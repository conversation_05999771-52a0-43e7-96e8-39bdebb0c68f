<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class DriverOcrRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'image_url' => 'required|URL',
        ];
    }

    public function messages()
    {
        return [
            'image_url.required' => '驾驶证图片地址必须上传',
            'image_url.URL' => '请填写正确的驾驶证图片'
        ];
    }
}
