<?php

namespace App\Admin\Actions\Form;

use App\Admin\Actions\Imports\SpuImport;
use App\Models\Common;
use App\Models\ShopSpu;
use Dcat\Admin\Widgets\Form;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Cell\Coordinate;
use PhpOffice\PhpSpreadsheet\IOFactory;

class SpuImportForm extends Form
{
    public function handle(array $input)
    {
        try {
            //上传文件位置，这里默认是在storage中，如有修改请对应替换
            $file = storage_path('app/public/' . $input['file']);
            $arr = explode(".", $file);
            $objRead = IOFactory::createReader(ucfirst(array_pop($arr)));
            $objSpreadsheet = $objRead->load($file);
            $objWorksheet = $objSpreadsheet->getSheet(0);
            $data = $objWorksheet->toArray();
            foreach ($objWorksheet->getDrawingCollection() as $drawing) {
                list($startColumn, $startRow) = Coordinate::coordinateFromString($drawing->getCoordinates());
                $imageName = "shop_spu_cover/".$drawing->getCoordinates() . create_uuid();
                switch ($drawing->getExtension()) {
                    case "jpg":
                    case "jpeg":
                    case "gif":
                    case "png":
                        $imageName .= ".".strtolower($drawing->getExtension());
                        break;
                    default:
                        throw new \Exception("图片类型仅支持：jpg|jpeg|gif|pgn");
                }
                if (!\Storage::disk('qiniu')->put($imageName, file_get_contents($drawing->getPath()))) {
                    throw new \Exception("图片上传失败");
                }
                $startColumn = $this->abc2decimal($startColumn);
                $data[$startRow - 1][$startColumn] = $imageName;
            }
            $statusMap = [];
            foreach (Common::StatusMap as $k => $v) {
                $statusMap[$v] = $k;
            }
            foreach ($data as $k => $v) {
                if ($k < 1) continue;
                $id = intval($v[0] ?? 0);
                $item = [
                    "name" => $v[1] ?? "",
                    "cover" => $v[2] ?? "",
                    "shop_id" => $v[3] ?? 0,
                    "description" => $v[5] ?? "",
                    "price" => $v[6] ?? 0,
                    "discount_price" => $v[7] ?? 0,
                    "sell_tags" => explode("|", ($v[8] ?? "")),
                    "stock" => $v[9] ?? 0,
                    "sales" => $v[10] ?? 0,
                    "is_signboard" => ($v[11] ?? "否") == "是",
                    "status" => $statusMap[$v[12]?? ""] ?? Common::STATUS_INIT,
                    "sort" => $v[13] ?? 0,
                ];
                if(empty($item["name"]) || empty($item["shop_id"])) {
                    continue;
                }
                $spu = null;
                if($id) $spu = ShopSpu::query()->find($id);
                if($spu){
                    $spu->update($item);
                }else{
                    ShopSpu::query()->create($item);
                }
            }
            unlink($file);
            return $this->response()->success('数据导入成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    private function abc2decimal($abc)
    {
        $ten = 0;
        $len = strlen($abc);
        for ($i = 1; $i <= $len; $i++) {
            $char = substr($abc, 0 - $i, 1);
            $int = ord($char);
            $ten += ($int - 65) * pow(26, $i - 1);
        }
        return $ten;
    }

    public function form()
    {
        $this->file('file', '上传数据（Excel）')->rules('required', ['required' => '文件不能为空'])->autoUpload()->disk('public');
    }
}
