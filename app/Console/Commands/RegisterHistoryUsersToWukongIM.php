<?php

namespace App\Console\Commands;

use App\Models\User;
use App\Services\WukongIMService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class RegisterHistoryUsersToWukongIM extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'wukongim:register-history-users {--chunk=100}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '将历史用户注册到悟空IM';

    /**
     * @var WukongIMService
     */
    protected $wukongIMService;

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct(WukongIMService $wukongIMService)
    {
        parent::__construct();
        $this->wukongIMService = $wukongIMService;
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $chunkSize = $this->option('chunk');
        $this->info('开始注册历史用户到悟空IM...');
        $this->info('当前环境: ' . app()->environment());
        
        // 只处理有recommend_code的用户
        $totalUsers = User::whereNotNull('recommend_code')->count();
        if ($totalUsers === 0) {
            $this->error('没有找到有推荐码的用户!');
            return Command::FAILURE;
        }

        $bar = $this->output->createProgressBar($totalUsers);
        $successCount = 0;
        $failCount = 0;
        $skippedCount = 0;

        // 分批处理用户
        User::whereNotNull('recommend_code')
            ->chunk($chunkSize, function ($users) use ($bar, &$successCount, &$failCount, &$skippedCount) {
                foreach ($users as $user) {
                    try {
                        $prev = app()->environment() == 'production' ? 1 : 2;
                        $uid = $prev . '_' . $user->recommend_code;
                        
                        if ($this->wukongIMService->registerUser($user)) {
                            $successCount++;
                            $this->info("\n用户注册成功: {$uid}");
                        } else {
                            $failCount++;
                            Log::warning('注册历史用户到悟空IM失败', [
                                'user_id' => $user->id,
                                'uid' => $uid,
                                'recommend_code' => $user->recommend_code
                            ]);
                            $this->error("\n用户注册失败: {$uid}");
                        }
                    } catch (\Exception $e) {
                        $failCount++;
                        $uid = $prev . '_' . $user->recommend_code;
                        Log::error('注册历史用户到悟空IM异常', [
                            'user_id' => $user->id,
                            'uid' => $uid,
                            'recommend_code' => $user->recommend_code,
                            'error' => $e->getMessage()
                        ]);
                        $this->error("\n用户注册异常: {$uid}, 错误: {$e->getMessage()}");
                    }
                    $bar->advance();
                }
            });

        $bar->finish();
        $this->newLine();
        
        $this->info("处理完成!");
        $this->info("成功: {$successCount}");
        $this->info("失败: {$failCount}");
        $this->info("跳过: {$skippedCount}");
        $this->info("总计: {$totalUsers}");

        return Command::SUCCESS;
    }
} 