<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\RechargeOrderRequest;
use App\Jobs\CloseRechargeOrder;
use App\Models\EarnestRechargeOrder;
use App\Models\RechargeOrder;
use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use Illuminate\Support\Facades\DB;

class RechargeOrderController extends Controller
{
    //
    public function store(RechargeOrderRequest $request)
    {
        $user = $request->user();
        if ($request->amount <= 0) {
            $this->errorResponse(500, '充值金额必须大于0');
        }

        $order = RechargeOrder::create([
            'user_id' => $user->id,
            'order_amount' => yuantofen($request->amount),
            'actual_amount' => yuantofen($request->amount),
            'reduce_amount' => 0,
            'refund_status' => RechargeOrder::REFUND_STATUS_PENDING,
        ]);

        $order->order_amount = fentoyuan($order->order_amount);
        $order->actual_amount = fentoyuan($order->actual_amount);
        $order->reduce_amount = fentoyuan($order->reduce_amount);

        dispatch(new CloseRechargeOrder($order->order_no, config('app.order_ttl')));

        return $this->success('success', $order);
    }

    public function earnestRecharge(RechargeOrderRequest $request)
    {
        $user = $request->user();
        if ($request->amount <= 0) {
            $this->errorResponse(500, '充值金额必须大于0');
        }

        $order = EarnestRechargeOrder::create([
            'user_id' => $user->id,
            'order_amount' => yuantofen($request->amount),
            'actual_amount' => yuantofen($request->amount),
            'refund_status' => EarnestRechargeOrder::REFUND_STATUS_PENDING,
        ]);

        $order->order_amount = fentoyuan($order->order_amount);
        $order->actual_amount = fentoyuan($order->actual_amount);

        dispatch(new CloseRechargeOrder($order->order_no, config('app.order_ttl')));

        return $this->success('success', $order);
    }

    public function earnestTransfer(RechargeOrderRequest $request)
    {
        $user = $request->user();
        if ($request->amount <= 0) {
            $this->errorResponse(500, '提现金额必须大于0');
        }

        $amount = yuantofen($request->amount);
        DB::beginTransaction();
        try {
            $earnestAccount = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 2)->first();
            if ($earnestAccount->amount < $amount) {
                $this->errorResponse(500, '保证金账户余额不足');
            }
            $earnestAccount->decrease($amount, RiderAccountFlow::BUSINESS_TYPE_TRANS_TO_AMOUNT, '', '退保证金到余额');

            $riderAccount = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 1)->first();
            $riderAccount->add($amount, RiderAccountFlow::BUSINESS_TYPE_TRANS_TO_AMOUNT, '', '退保证金到余额');
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorResponse(500, $e->getMessage());
        }

        $earnestAccount = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 2)->first();
        return $this->success('success', ['amount' => fentoyuan($earnestAccount->amount)]);
    }
}
