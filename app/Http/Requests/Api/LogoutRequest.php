<?php

namespace App\Http\Requests\Api;

use App\Models\SystemConfig;
use Illuminate\Validation\Rule;

class LogoutRequest extends FormRequest
{
    public function rules()
    {
        return [
            'verification_key' => 'required|string',
            'verification_code' => 'required|string',
            'reason' => 'required|string',
            'platform' => ['required', 'integer', Rule::in([SystemConfig::PlatformSQ, SystemConfig::PlatformPT])]
        ];
    }

    public function attributes()
    {
        return [
            'verification_key' => '短信验证码 key',
            'verification_code' => '短信验证码',
        ];
    }
}
