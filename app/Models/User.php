<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\Sanctum\HasApiTokens;
use PHPOpenSourceSaver\JWTAuth\Contracts\JWTSubject;


class User extends Authenticatable implements JWTSubject
{
    use HasApiTokens, HasFactory, Notifiable, HasDateTimeFormatter;

    const NORMAL_LEVEL = 1;

    const NORMAL_VIP = 2;

    // 只有白名单用户才可以使用社区食堂下单，注册后会根据user_white_lists表判断是否是已登记的白名单用户
    const CHANNEL_WHITE_LIST = 'WHITE LIST';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'phone',
        'nickname',
        'avatar',
        'name',
        'password',
        'certified',
        'level',
        'id_card',
        'birthday',
        'channel',
        'scene',
        'ip',
        'active_shequ',
        'active_paotui',
        'last_active_time',
        'device_id',
        'device_type',
        'recommend_code',
        'parent_id',
        'alipay_account',
        'mp_openid',
    ];

    protected $appends = ['easemob_user_id', 'easemob_rider_id'];

    protected $casts = ['active_shequ' => 'boolean', 'active_paotui' => 'boolean'];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array<int, string>
     */
    protected $hidden = [
        'password',
        'id_card',
        'device_id',
        'device_type'
    ];

    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    public function getJWTCustomClaims()
    {
        return [];
    }

    public function profile()
    {
        return $this->hasOne(UserProfile::class, 'user_id');
    }

    public function userAccount()
    {
        return $this->hasOne(UserAccount::class, 'user_id');
    }

    public function rider()
    {
        return $this->hasOne(Rider::class, 'user_id');
    }

    public function riderProfile()
    {
        return $this->hasOne(RiderProfile::class, 'user_id');
    }

    public function riderAccount()
    {
        return $this->hasOne(RiderAccount::class, 'user_id')->where('account_type', 1);
    }

    public function riderSetting()
    {
        return $this->hasOne(RiderSetting::class, 'user_id');
    }

    public function idcard()
    {
        return $this->hasOne(UserIdCard::class, 'user_id');
    }

    public function getEasemobUserIdAttribute()
    {
        return 'u_' . strtolower($this->recommend_code);
    }

    public function getEasemobRiderIdAttribute()
    {
        return 'r_' . strtolower($this->recommend_code);
    }

    public static function GenInviteCode(): string
    {
        $chars = "123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
        $res = "";

        while (true) {
            for ($i = 0; $i < 5; $i++) {
                $res .= $chars[mt_rand(0, strlen($chars) - 1)];
            }
            $user = User::where('recommend_code', $res)->first();
            if (!$user) {
                break;
            }
        }

        return $res;
    }

    public static function ParseRecommendCode($code)
    {
        if (!$code) return 0;
        $user = User::where('recommend_code', $code)->first();
        if ($user) {
            return $user->id;
        } else {
            return 0;
        }
    }

}
