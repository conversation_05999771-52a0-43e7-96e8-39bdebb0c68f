<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Article;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ArticleController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Article(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->column("id");
            $grid->column('title');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('title')->width(3);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Article(), function (Form $form) {
            $form->disableViewButton();
            $form->disableDeleteButton();
            $form->disableViewCheck();
            $form->display('id');
            $form->text('title')->required();
            $form->image('cover')->autoUpload()->autoSave(false)->uniqueName();
            $form->editor("content")->required();
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
