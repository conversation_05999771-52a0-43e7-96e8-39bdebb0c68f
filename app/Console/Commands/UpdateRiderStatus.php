<?php

namespace App\Console\Commands;

use App\Models\Rider;
use App\Services\RiderStatusService;
use Illuminate\Console\Command;

class UpdateRiderStatus extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'rider:update-status';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '更新骑手状态';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(RiderStatusService $riderStatusService)
    {
        // 获取所有在线骑手
        $riders = Rider::query()
            ->where('status', Rider::STATUS_UP)
            ->get();
            
        // 批量检查并更新骑手状态
        $riderStatusService->batchCheckStatus($riders->pluck('id')->toArray());
        
        $this->info('骑手状态更新完成');
        return Command::SUCCESS;
    }
}
