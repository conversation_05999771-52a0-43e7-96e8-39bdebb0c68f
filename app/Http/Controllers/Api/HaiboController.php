<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\HaiboService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class HaiboController extends Controller
{
    protected HaiboService $haiboService;

    public function __construct(HaiboService $haiboService)
    {
        $this->haiboService = $haiboService;
    }

    /**
     * 创建/修改配送商门店接口
     *
     * POST /api/haibo/store
     * Content-Type: application/x-www-form-urlencoded
     */
    public function createOrUpdateStore(Request $request)
    {
        try {
            // 记录海博请求信息
            Log::channel('haibo')->info('海博创建/修改配送商门店请求', [
                'method' => $request->method(),
                'content_type' => $request->header('Content-Type'),
                'data' => $request->all()
            ]);

            // 验证请求参数
            $validator = Validator::make($request->all(), [
                'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
                'address' => 'required|string|max:255',
                'province' => 'required|string|max:50',
                'city' => 'required|string|max:50',
                'district' => 'required|string|max:50',
                'city_code' => 'required|string|max:20',
                'shop_id' => 'nullable|string|max:50',
                'contact_name' => 'nullable|string|max:50',
                'email' => 'nullable|email|max:100',
                'merchant_type' => 'nullable|string|max:50',
                'password' => 'nullable|string|min:6',
                'access_token' => 'nullable|string',
                'refresh_token' => 'nullable|string',
            ], [
                'shop_name.required' => '门店名称不能为空',
                'phone.required' => '联系电话不能为空',
                'phone.regex' => '手机号格式不正确',
                'address.required' => '详细地址不能为空',
                'province.required' => '省份不能为空',
                'city.required' => '城市不能为空',
                'district.required' => '区县不能为空',
                'city_code.required' => '城市编码不能为空',
            ]);

            if ($validator->fails()) {
                Log::channel('haibo')->warning('海博请求参数验证失败', [
                    'errors' => $validator->errors()->toArray(),
                    'data' => $request->all()
                ]);

                return response()->json([
                    'code' => 400,
                    'message' => '参数验证失败: ' . $validator->errors()->first(),
                    'data' => null
                ], 400);
            }

            // 调用服务处理业务逻辑
            $result = $this->haiboService->createOrUpdateStore($request->all());

            Log::channel('haibo')->info('海博门店操作成功', $result);

            return response()->json([
                'code' => 200,
                'message' => $result['message'],
                'data' => $result['data']
            ]);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博门店操作异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'request_data' => $request->all()
            ]);

            return response()->json([
                'code' => 500,
                'message' => '操作失败: ' . $e->getMessage(),
                'data' => null
            ], 500);
        }
    }

    /**
     * 海博回调处理接口
     *
     * POST /api/haibo/callback/{command}
     * Content-Type: application/x-www-form-urlencoded
     */
    public function callback(Request $request, $command)
    {
        Log::channel('haibo')->info("海博回调", [
            'command' => $command,
            'data' => $request->all()
        ]);

        try {
            $postData = $request->all();
            $userId = 0;
            $merchantId = 0;
            $shopId = $postData["shop_id"] ?? '';

            // 获取商家信息
            if (!empty($shopId)) {
                $merchantInfo = $this->haiboService->getMerchantInfoByShopId($shopId);

                if ($merchantInfo) {
                    $userId = $merchantInfo['user_id'] ?? 0;
                    $merchantId = $merchantInfo['merchant_id'] ?? 0;

                    Log::channel('haibo')->info('获取到商家信息', [
                        'shop_id' => $shopId,
                        'merchant_id' => $merchantId,
                        'user_id' => $userId
                    ]);
                } else {
                    Log::channel('haibo')->warning('未找到对应商家信息', ['shop_id' => $shopId]);
                    throw new \Exception("该门店未授权");
                }
            }

            $result = [];

            switch ($command) {
                case "ping":
                    // 健康检查接口
                    $result = [
                        "status" => "ok",
                        "timestamp" => time(),
                        "version" => "1.0"
                    ];
                    break;

                case "store_info":
                    // 获取门店信息
                    if (!$merchantId) {
                        throw new \Exception("未找到门店信息");
                    }

                    $merchant = \App\Models\Merchant::find($merchantId);
                    if (!$merchant) {
                        throw new \Exception("门店不存在");
                    }

                    $result = [
                        "shop_id" => $shopId,
                        "shop_name" => $merchant->shop_name,
                        "phone" => $merchant->phone,
                        "address" => $merchant->address,
                        "province" => $merchant->province,
                        "city" => $merchant->city,
                        "district" => $merchant->district,
                        "city_code" => $merchant->city_code,
                        "status" => $merchant->status,
                        "balance" => $merchant->balance,
                        "created_at" => $merchant->created_at->timestamp,
                        "updated_at" => $merchant->updated_at->timestamp,
                    ];
                    break;

                case "balance":
                    // 查询门店余额
                    if (!$merchantId) {
                        throw new \Exception("未找到门店信息");
                    }

                    $merchant = \App\Models\Merchant::find($merchantId);
                    if (!$merchant) {
                        throw new \Exception("门店不存在");
                    }

                    $result = [
                        "shop_id" => $shopId,
                        "balance" => $merchant->balance,
                        "at_time" => time(),
                    ];

                    Log::channel('haibo')->info('查询门店余额', [
                        'shop_id' => $shopId,
                        'merchant_id' => $merchantId,
                        'balance' => $merchant->balance
                    ]);
                    break;

                default:
                    Log::channel('haibo')->warning('不支持的回调命令', ['command' => $command]);
                    return response()->json([
                        "code" => 400,
                        "message" => "The '$command' is not supported.",
                        "data" => ""
                    ]);
            }

            Log::channel('haibo')->info('海博回调处理成功', [
                'command' => $command,
                'result' => $result
            ]);

            return response()->json([
                "code" => 200,
                "message" => "SUCCESS",
                "data" => $result ? json_encode($result) : ""
            ]);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博回调异常', [
                'command' => $command ?? 'unknown',
                'shop_id' => $shopId ?? '',
                'merchant_id' => $merchantId ?? 0,
                'user_id' => $userId ?? 0,
                'request_data' => $request->all(),
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                "code" => 500,
                "message" => $e->getMessage(),
                "data" => ""
            ]);
        }
    }

    /**
     * 海博授权回调接口
     *
     * GET /api/haibo/auth/callback
     */
    public function authCallback(Request $request)
    {
        try {
            // 记录海博授权回调信息
            Log::channel('haibo')->info('海博授权回调', $request->all());

            $code = $request->get('code');
            $redirectUrl = $request->get('redirect_uri');
            $state = $request->get('state');

            // 这里可以根据需要处理授权逻辑
            // 例如重定向到商家登录页面或直接处理授权

            return response()->json([
                'code' => 200,
                'message' => '授权回调处理成功',
                'data' => [
                    'code' => $code,
                    'redirect_uri' => $redirectUrl,
                    'state' => $state
                ]
            ]);

        } catch (\Exception $e) {
            Log::channel('haibo')->error('海博授权回调异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                "code" => 500,
                "message" => $e->getMessage(),
                "data" => ""
            ]);
        }
    }
}
