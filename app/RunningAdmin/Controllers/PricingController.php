<?php

namespace App\RunningAdmin\Controllers;

use App\Models\Site;
use App\RunningAdmin\Repositories\Pricing;
use Dcat\Admin\Admin;
use Dcat\Admin\Color;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class PricingController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Pricing(), function (Grid $grid) {
            $grid->model()->with(["site"])->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->column('id');
            $grid->column("type")->using(\App\Models\Pricing::TypeMap)->label([
                1 => "info",
                2 => "danger",
            ]);
            $grid->column('title');
            $grid->column("site.name", "站点");
            $grid->column('base_price');
            $grid->column('is_special')->switch();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('type')->radio(\App\Models\Pricing::TypeMap)->width(3);
                $filter->like('title')->width(3);
                $filter->equal('site_id')->select(Site::query()->pluck('name', 'id'))->width(3);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        Admin::style(
            <<<CSS
    div.has-many-table-distance_price input.field_egt {
       width: 90px!important;
    }
    div.has-many-table-distance_price input.field_base {
       width: 90px!important;
    }
    div.has-many-table-distance_price input.field_unit {
       width: 90px!important;
    }
    div.has-many-table-period_price span.select2 {
       width: 90px!important;
    }
    div.has-many-table-period_price input.field_from {
       width: 90px!important;
    }
    div.has-many-table-period_price input.field_to {
       width: 90px!important;
    }
    div.has-many-table-period_price input.field_price {
       width: 90px!important;
    }
CSS
        );
        return Form::make(new Pricing(), function (Form $form) {
            $form->display('id');
            $form->select('type')->options(\App\Models\Pricing::TypeMap)->required();
            $form->text('title')->required();
            $form->select('site_id')->options(Site::query()->pluck("name", "id"))->required();
            $form->decimal('base_price')->required();
            $form->table('distance_price', function ($table) {
                $table->decimal("egt", "起始公里")->default(2);
                $table->decimal("base", "基础费/元")->default(2);
                $table->decimal("unit", "单位公里费/元")->default(1);
            });
            $form->table('period_price', function ($table) {
                $table->select('ft', "开始时间")->disableClearButton()->options([\App\Models\Pricing::COMPARE_EGT => '≥', \App\Models\Pricing::COMPARE_GT => '>'])->default(\App\Models\Pricing::COMPARE_EGT);
                $table->time("from", "--:--")->format('HH:mm');
                $table->select('tt', "结束时间")->disableClearButton()->options([\App\Models\Pricing::COMPARE_LT => '<', \App\Models\Pricing::COMPARE_ELT => '≤'])->default(\App\Models\Pricing::COMPARE_LT);
                $table->time("to", "--:--")->format('HH:mm');
                $table->decimal("price", "收费/元")->default(2);
            });
            $form->decimal('weather_price');
            $form->switch('is_special')->default(false);
            
            // 雨骑士定价策略
            $form->switch('use_yuqishi_pricing', '启用雨骑士定价')->default(false)->help('启用后将使用雨骑士复杂定价策略');
            
            $form->table('normal_pricing', '正常时间+正常天气定价', function ($table) {
                $table->text('distance_range', '距离区间')->placeholder('例如：0-1 或 3+')->help('格式：0-1表示0到1公里，3+表示3公里以上');
                $table->decimal('base_price', '基础价格/元')->help('该距离区间的基础配送费');
                $table->decimal('extra_price', '超出单价/元')->default(0)->help('超出该区间后每公里的费用');
                $table->text('weight_pricing', '重量定价')->placeholder('JSON格式，如：[{"weight_range":"0-3","base_price":0},{"weight_range":"3-5","base_price":2},{"weight_range":"5+","base_price":5,"extra_price":1}]')->help('重量阶梯价格：0-3kg免费，3-5kg加2元，5kg以上基础5元+每kg加1元');
            })->when('use_yuqishi_pricing', 1);
            
            $form->table('night_pricing', '夜间时间+正常天气定价', function ($table) {
                $table->text('distance_range', '距离区间')->placeholder('例如：0-1 或 3+');
                $table->decimal('base_price', '基础价格/元');
                $table->decimal('extra_price', '超出单价/元')->default(0);
                $table->text('weight_pricing', '重量定价')->placeholder('JSON格式')->help('重量阶梯价格配置');
            })->when('use_yuqishi_pricing', 1);
            
            $form->table('weather_normal_pricing', '正常时间+恶劣天气定价', function ($table) {
                $table->text('distance_range', '距离区间')->placeholder('例如：0-1 或 3+');
                $table->decimal('base_price', '基础价格/元');
                $table->decimal('extra_price', '超出单价/元')->default(0);
                $table->text('weight_pricing', '重量定价')->placeholder('JSON格式')->help('重量阶梯价格配置');
            })->when('use_yuqishi_pricing', 1);
            
            $form->table('weather_night_pricing', '夜间时间+恶劣天气定价', function ($table) {
                $table->text('distance_range', '距离区间')->placeholder('例如：0-1 或 3+');
                $table->decimal('base_price', '基础价格/元');
                $table->decimal('extra_price', '超出单价/元')->default(0);
                $table->text('weight_pricing', '重量定价')->placeholder('JSON格式')->help('重量阶梯价格配置');
            })->when('use_yuqishi_pricing', 1);
            
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
