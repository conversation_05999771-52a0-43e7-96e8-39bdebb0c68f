<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;


class Community extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const GEO_KEY = 'community';

    public static function GetCommunityFromRedis($lng, $lat, $distance)
    {
        return Redis::georadius(self::GEO_KEY, $lng, $lat, $distance, 'km', 'WITHDIST', 'ASC');
    }

    public static function PushLocation()
    {
        $communities = Community::query()->where("is_open", 1)->get();
        foreach ($communities as $community) {
            Redis::geoadd(self::GEO_KEY, $community->longitude, $community->latitude, $community->id);
        }
    }

    public function region() {
        return $this->belongsTo(Region::class, 'region_id');
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::saved(function ($community) {
            if ($community->is_open) {
                Redis::geoadd(self::GEO_KEY, $community->longitude, $community->latitude, $community->id);
            } else {
                Redis::zrem(self::GEO_KEY, $community->id);
            }
        });
        static::deleted(function ($model) {
            Redis::zrem(self::GEO_KEY, $model->id);
        });
    }
}
