<?php

namespace Tests\Feature\Controllers\Api;

use App\Models\O2oErrandOrder;
use App\Services\Amap\GaodeService;
use App\Services\O2oErrandOrderService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;
use Mockery;

class MaiYaTianControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        // 可以在这里添加一些通用的测试数据准备
    }

    public function testValuatingCommand()
    {
        // Mock GaodeService
        $mockGaodeService = Mockery::mock(GaodeService::class);
        $mockGaodeService->shouldReceive('riding')
            ->once()
            ->andReturn([
                'result' => [
                    'routes' => [
                        [
                            'duration' => 1200 // 20分钟
                        ]
                    ]
                ]
            ]);
        $this->app->instance(GaodeService::class, $mockGaodeService);

        // 构造测试数据
        $testData = [
            'command' => 'valuating',
            'data' => json_encode([
                'source_order_no' => 'TEST_ORDER_'.time(),
                'sender' => [
                    'longitude' => '120.123456',
                    'latitude' => '30.123456'
                ],
                'receiver' => [
                    'longitude' => '120.234567',
                    'latitude' => '30.234567'
                ],
                'tips' => 200, // 2元小费
                'expect_finish_time' => time() + 3600, // 1小时后
                'order_info' => [
                    'paid_fee' => 5000, // 50元商品费
                    'weight' => 1, // 1kg
                    'goods_list' => [
                        ['name' => '测试商品']
                    ]
                ]
            ])
        ];

        // 发送请求
        $response = $this->postJson('/api/maiyatian/callback/valuating', $testData);

        // 验证响应
        $response->assertStatus(200)
            ->assertJson([
                'code' => 200,
                'message' => 'SUCCESS'
            ]);

        // 解析返回的data
        $responseData = json_decode($response->json('data'), true);
        
        // 验证返回数据结构
        $this->assertArrayHasKey('pay_amount', $responseData);
        $this->assertArrayHasKey('distance', $responseData);
        $this->assertArrayHasKey('tips', $responseData);
        $this->assertArrayHasKey('expect_time', $responseData);
        $this->assertArrayHasKey('source_order_no', $responseData);
        
        // 验证具体数值
        $this->assertEquals(200, $responseData['tips']); // 验证小费金额
        $this->assertEquals(1000, $responseData['weight']); // 验证重量(g)
        $this->assertEquals($testData['data']['source_order_no'], $responseData['source_order_no']); // 验证订单号
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }
} 