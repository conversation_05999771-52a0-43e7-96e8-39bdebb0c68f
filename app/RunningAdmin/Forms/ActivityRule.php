<?php


namespace App\RunningAdmin\Forms;


use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class ActivityRule extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $rule = new \App\Models\ActivityRule();
        $rule->activity_id = $input['activity_id'];
        $rule->start = $input['start'];
        $rule->end = $input['end'];
        $rule->order_count = $input['order_count'];
        $rule->reward = yuantofen($input['reward']);
        $rule->save();

        return $this->response()->success('创建成功')->refresh();

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->hidden('activity_id')->value($this->payload['activity_id']);
        $this->range('start', 'end', '奖励区间')->help('左闭右开')->required();
        $this->number('order_count', '订单数')->required();
        $this->decimal('reward', '奖励金额')->required();
    }
}
