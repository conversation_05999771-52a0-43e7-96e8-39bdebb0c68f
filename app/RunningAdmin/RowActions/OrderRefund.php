<?php
namespace App\RunningAdmin\RowActions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use App\RunningAdmin\Forms\OrderRefundForm;

class OrderRefund extends RowAction
{
    protected $title = '退款';

    public function render()
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->body(OrderRefundForm::make()->payload(['order_no' => $this->getRow()->order_no]))
            ->button($this->title);
    }
}
