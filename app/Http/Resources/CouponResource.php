<?php

namespace App\Http\Resources;

use App\Models\Coupon;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;

class CouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'type' => $this->type,
            'type_txt' => Coupon::TypeMap[$this->type],
            'discount_price' => $this->discount_price,
            'start_price' => $this->start_price,
            'validity' => $this->validity,
            'rules' => $this->rules,
            'stock' => $this->stock,
            "sales" => $this->sales,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
