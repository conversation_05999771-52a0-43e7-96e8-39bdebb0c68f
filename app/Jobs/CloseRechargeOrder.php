<?php

namespace App\Jobs;

use App\Models\EarnestRechargeOrder;
use App\Models\RechargeOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

// 代表这个类需要被放到队列中执行，而不是触发时立即执行
class CloseRechargeOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected string $orderNo;

    public function __construct($orderNo, $delay)
    {
        $this->orderNo = $orderNo;
        // 设置延迟的时间，delay() 方法的参数代表多少秒之后执行
        $this->delay($delay);
    }

    // 定义这个任务类具体的执行逻辑
    // 当队列处理器从队列中取出任务时，会调用 handle() 方法
    public function handle()
    {
        // 查询订单 判断订单合法性以及获取金额
        $orderPrefix = substr($this->orderNo, 0, 2);

        switch ($orderPrefix) {
            case RechargeOrder::RECHARGE_ORDER_PREFIX:
                $rechargeOrder = RechargeOrder::query()->where('order_no', $this->orderNo)->first();
                $rechargeOrder->closed = 1;
                $rechargeOrder->save();
                break;
            case EarnestRechargeOrder::EARNEST_RECHARGE_ORDER_PREFIX:
                $rechargeOrder = EarnestRechargeOrder::query()->where('order_no', $this->orderNo)->first();
                $rechargeOrder->closed = 1;
                $rechargeOrder->save();
                break;
        }


    }
}
