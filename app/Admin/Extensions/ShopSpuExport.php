<?php


namespace App\Admin\Extensions;

use App\Models\ShopSpu;
use Dcat\Admin\Grid\Exporters\AbstractExporter;
use Illuminate\Contracts\View\View;
use Maatwebsite\Excel\Concerns\Exportable;
use Maatwebsite\Excel\Concerns\FromView;
use Maatwebsite\Excel\Concerns\RegistersEventListeners;
use Maatwebsite\Excel\Concerns\WithDrawings;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;

class ShopSpuExport extends AbstractExporter implements FromView, WithDrawings
{
    use Exportable;

    protected $data;

    protected $fileName = '商品导出.xlsx';

    public function __construct($query)
    {
        parent::__construct();
        $this->fileName = '商品导出_' . date('Y-m-d') . '.xlsx';
        $this->data = $this->getData($query);
    }

    private function getData($q)
    {
        $query = ShopSpu::query()->with(['shop']);
        if (isset($q['name'])) {
            $query->where('name', "like", "%" . $q['name'] . "%");
        }
        if (isset($q['shop_id'])) {
            $query->whereIn('shop_id', $q['shop_id']);
        }
        if (isset($q['cat_id'])) {
            $query->whereIn('cat_id', $q['cat_id']);
        }
        if (isset($q['status'])) {
            $query->whereIn('status', $q['status']);
        }
        return $query->orderBy("id", "desc")->get();
    }

    public function view(): View
    {
        return view('shop_spu', ['spus' => $this->data]);
    }

    public function drawings()
    {
        $result = [];
        foreach ($this->data as $k => $v) {
            if (empty($v->cover)) continue;
            $arr = explode(".", $v->cover);
            $coverName = sprintf("shop_spu_cover_%s.%s", $v->id, array_pop($arr));
            if (!\Storage::disk('local')->put($coverName, file_get_contents(img_url($v->cover)))) {
                continue;
            }
            ${'drawing' . $k} = new Drawing();
            ${'drawing' . $k}->setName($coverName);
            ${'drawing' . $k}->setDescription($coverName);
            ${'drawing' . $k}->setPath(storage_path('app/' . $coverName));
            ${'drawing' . $k}->setHeight(90);
            ${'drawing' . $k}->setOffsetX(5);
            ${'drawing' . $k}->setOffsetY(5);
            ${'drawing' . $k}->setCoordinates('C' . ($k + 2));
            $result[] = ${'drawing' . $k};
        }
        return $result;
    }

    public function export()
    {
        return $this->download($this->fileName)->prepare(request())->send();
    }

}
