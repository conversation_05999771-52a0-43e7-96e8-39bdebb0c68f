<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Cache;

class KefuService
{
    protected $baseUrl;
    protected $customerId;
    protected $secretKey;
    
    public function __construct()
    {
        $this->baseUrl = config('services.kefu.base_url', 'https://kefu.fengqishi.com.cn/');
        $this->customerId = config('services.kefu.customer_id', 1);
        $this->secretKey = config('services.kefu.secret_key', 'yuqishi');
    }

    /**
     * 计算签名
     * 
     * @param string $userId 用户ID
     * @return string
     */
    protected function calculateSign(string $userId): string 
    {
        $signStr = $this->customerId . $userId . $this->secretKey;
        return md5($signStr);
    }

    /**
     * 客服系统静默登录
     * 
     * @param string $userId 用户ID
     * @param string $username 用户名称
     * @return array 包含token和聊天地址的数组
     * @throws \Exception
     */
    public function silentLogin(string $userId, string $username)
    {
        try {
            $sign = $this->calculateSign($userId);
            
            $params = [
                'customer_id' => $this->customerId,
                'user_id' => $userId,
                'username' => '',
                'sign' => $sign
            ];

            $response = Http::post($this->baseUrl . '/api/user/silent-login', $params);

            if (!$response->successful()) {
                Log::error('客服系统静默登录失败', [
                    'params' => $params,
                    'response' => $response->json(),
                    'status' => $response->status()
                ]);
                throw new \Exception('客服系统登录失败');
            }

            $data = $response->json();
            if ($data['code'] !== 0) {
                Log::error('客服系统静默登录业务异常', [
                    'params' => $params,
                    'response' => $data
                ]);
                throw new \Exception($data['message'] ?? '客服系统登录异常');
            }
            return [
                'token' => $data['data']['token'],
                'chatUrl' => str_replace('https://kefu.fengqishi.com.cn/', 'https://kefu.fengqishi.com.cn/pages/login/index/', $data['data']['chat_url'])
            ];

        } catch (\Exception $e) {
            Log::error('客服系统静默登录异常', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取客服聊天链接
     * 
     * @param string $userId 用户ID
     * @param string $username 用户名称
     * @return string 完整的客服聊天链接
     * @throws \Exception
     */
    public function getChatUrl(string $userId, string $username): string
    {
        try {
            // 获取登录token
            $loginInfo = $this->silentLogin($userId, $username);
            
            return $loginInfo['chatUrl'];

        } catch (\Exception $e) {
            Log::error('获取客服聊天链接异常', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 获取带缓存的客服聊天链接
     * 
     * @param string $userId 用户ID
     * @param string $username 用户名称
     * @return string 客服聊天链接
     */
    public function getCachedChatUrl(string $userId, string $username): string
    {
        $cacheKey = 'kefu_chat_urls_prod_v2_' . $userId;
        
        // // 先尝试从缓存获取
        // $cachedUrl = Cache::get($cacheKey);
        // if (!empty($cachedUrl)) {
        //     return $cachedUrl;
        // }
        
        // 缓存中没有或为空时重新获取
        try {
            $chatUrl = $this->getChatUrl($userId, $username);
            // 只有在链接不为空时才缓存
            if (!empty($chatUrl)) {
                Cache::put($cacheKey, $chatUrl, 60 * 24);
            }
            return $chatUrl;
        } catch (\Exception $e) {
            Log::error('获取客服聊天链接缓存异常', [
                'userId' => $userId,
                'error' => $e->getMessage()
            ]);
            return '';
        }
    }
} 