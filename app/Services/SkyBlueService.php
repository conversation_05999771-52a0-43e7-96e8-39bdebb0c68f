<?php

namespace App\Services;

use Exception;
use Ixudra\Curl\Facades\Curl;

class SkyBlueService
{
    const MobileQueryUrl = "https://api.253.com/open/flashsdk/mobile-query";
    const IdcardAuthUrl = "https://api.253.com/open/idcard/id-card-auth/vs";
    const IdOcrCl = "https://api.253.com/open/i/ocr/id-ocr-cl";
    const DrivingLicense = "https://api.253.com/open/i/ocr/driving-license";
    const CiRxBD = "https://api.253.com/open/idmatch/idmatch-new";
    const VehicleLicense = "https://api.253.com/open/i/ocr/vehicle-license";

    protected string $appId;
    protected string $appKey;

    public function __construct()
    {
        $this->appId = "vQAtCZj3";
        $this->appKey = "PmJSDPvM";
//        $this->appKey = '8xYcBJkz';
//        $this->appId = 'JhPikztt';
    }

    /**
     * 生成签名
     * @param array $params 请求参数
     * @param string $algo 加密方式
     * @return string
     */
    private function parseSign(array $params, string $algo = "sha1"): string
    {
        $params["appId"] = $this->appId;
        ksort($params);
        $dataStr = "";
        foreach ($params as $key => $value) {
            $dataStr .= $key . $value;
        }
        return match ($algo) {
            "sha1" => base64_encode(hash_hmac($algo, $dataStr, $this->appKey, true)),
            "sha256" => hash_hmac($algo, $dataStr, $this->appKey),
            default => md5($dataStr . $this->appKey),
        };
    }

    /**
     * 发起http请求
     * @param string $requestUrl 请求url
     * @param array $params 请求参数
     * @param array $headers
     * @return mixed
     * @throws Exception
     */
    private function do(string $requestUrl, array $params, array $headers = ["Content-Type" => "application/x-www-form-urlencoded"]): mixed
    {
        try {
            $params["sign"] = $this->parseSign($params);
            $params['appId'] = $this->appId;
            $res = Curl::to($requestUrl)->withData($params)->withHeaders($headers)->asJsonResponse(true)->post();
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
        if ($res["code"] != "200000") {
            throw new Exception($res["code"] . ":" . $res["message"]);
        }
        return $res["data"];
    }

    /**
     * 一键登录V2（获取手机号码）
     * @param string $token SDK返回的运营商TOKEN，有效期：移动2分钟、电信10分钟、联通30分钟，一次有效
     * @param int $encryptType 手机号码加密方式，值包含：0（AES加密）、1（RSA加密）
     * @param array $otherParams 其他参数：clientIp，outId
     * @return mixed
     * @throws Exception
     */
    public function mobileQuery(string $token, int $encryptType = 0, array $otherParams = []): mixed
    {
        $otherParams["token"] = $token;
        $otherParams["encryptType"] = $encryptType;
        $res = $this->do(self::MobileQueryUrl, $otherParams);
        switch ($encryptType) {
            case 1: // RSA
                openssl_private_decrypt($res["mobileName"], $res["mobile"], "");
                break;
            default: // AES
                $res["mobile"] = openssl_decrypt($res["mobileName"], "AES-128-ECB", $this->appId, 0);

        }
        return $res;
    }

    /**
     * 实名认证-身份证校验V2（签名版）
     * @param string $name 姓名
     * @param string $idNum 身份证号码
     * @return mixed
     * @throws Exception
     */
    public function idcardAuth(string $name, string $idNum): mixed
    {
        return $this->do(self::IdcardAuthUrl, ["name" => $name, "idNum" => $idNum], ["multipart/form-data"]);
    }

    /**
     * 身份证识别
     * @param string $img
     * @param $ocrType
     * @param $detectRisk
     * @return mixed
     * @throws Exception
     */
    public function idOcrCl(string $img, $ocrType, $detectRisk)
    {
        $params = [
            'image' => $img,
            'imageType' => 'URL',
            'ocrType' => $ocrType,
            'detectRisk' => $detectRisk,
            'appKey' => $this->appKey
        ];
        return $this->do(self::IdOcrCl, $params);
    }

    /**
     * 驾驶证OCR
     * @param string $image
     * @return mixed
     * @throws Exception
     */
    public function drivingLicense(string $image)
    {
        $params = [
            'appKey' => $this->appKey,
            'image' => $image,
            'imageType' => 'URL'
        ];
        return $this->do(self::DrivingLicense, $params);
    }

    /**
     * 行驶证OCR
     * @param string $image
     * @return mixed
     * @throws Exception
     */
    public function vehicleLicense(string $image)
    {
        $params = [
            'appKey' => $this->appKey,
            'image' => $image,
            'imageType' => 'URL'
        ];
        return $this->do(self::VehicleLicense, $params);
    }

    public function faceCompare($idCard, $name, $image)
    {
        $params = [
            'appKey' => $this->appKey,
            'image' => $image,
            'idNum' => $idCard,
            'name' => $name
        ];

        return $this->do(self::CiRxBD, $params);
    }
}
