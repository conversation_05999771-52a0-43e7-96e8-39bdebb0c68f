<?php

namespace App\Http\Requests\Api;

use App\Models\SystemConfig;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ConfigRequest extends FormRequest
{
    public function rules()
    {
        return [
            'platform' => ['required', 'integer', Rule::in([SystemConfig::PlatformSQ, SystemConfig::PlatformPT])],
            'name' => 'required|string',
        ];
    }

    public function attributes()
    {
        return [
            'platform' => "平台",
            'name' => "参数名",
        ];
    }
}
