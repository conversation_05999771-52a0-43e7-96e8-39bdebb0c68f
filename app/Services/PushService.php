<?php

namespace App\Services;

use Illuminate\Support\Facades\Log;
use JPush\Client as JPush;

class PushService
{
    private $appKey;

    private $appSecret;


    public function __construct()
    {
        $this->appKey = 'b4c7de30fcac8c86156ee100';
        $this->appSecret = 'bcf40b5d84a69b9dd96253f5';
    }


    public function sendOrderMessage($order, $rid)
    {
        $client = new JPush($this->appKey, $this->appSecret);
        try {
            $response = $client->push()
                ->setPlatform(array('ios', 'android'))
                ->addRegistrationId($rid)
                ->setNotificationAlert('订单通知')
                ->iosNotification('您有新的订单', array(
                    'sound' => 'order_come.caf',
                    // 'badge' => '+1',
                    // 'content-available' => true,
                    // 'mutable-content' => true,
                    'category' => 'jiguang',
                    'extras' => [
                        'type' => 'order_notify',
                        'data' => [
                            'order_no' => $order->order_no,
                            'deliver_address' => $order->deliver_address,
                            'pickup_address' => $order->pickup_address,
                            'type' => $order->type,
                        ]
                    ],
                ))
                ->androidNotification('您有新的订单', array(
                    'title' => '订单通知',
                    // 'builder_id' => 2,
                    'extras' => [
                        'type' => 'order_notify',
                        'data' => [
                            'order_no' => $order->order_no,
                            'deliver_address' => $order->deliver_address,
                            'pickup_address' => $order->pickup_address,
                            'type' => $order->type,
                        ]

                    ],
                    'intent' => [
                        'url' => 'intent:#Intent;action=android.intent.action.MAIN;end'
                    ],
                    'sound' => '/raw/order_come'
                ))->options([
                    'time_to_live' => 0,
                    'apns_production' => true,
                    'third_party_channel' => [
                        'huawei' => [
                            'sound' => '/raw/order_come',
                            'default_sound' => false,
                            'category' => 'EXPRESS',
//                            "importance" => "NORMAL",//表示消息为服务与通讯类型
//                            'target_user_type' => 1,
                        ],
                        'xiaomi' => [
                            'channel_id' => '113242'
                        ]
                    ],
                ])
                ->send();
        } catch (\Exception $e) {
            Log::error($e->getMessage() . $e->getFile() . $e->getLine());
        }

    }
}

