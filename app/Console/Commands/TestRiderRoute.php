<?php

namespace App\Console\Commands;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Services\RouteOptimizationService;
use Illuminate\Console\Command;

class TestRiderRoute extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'rider:route-test {rider_id? : 骑手ID} {--lat=31.230416 : 当前纬度} {--lng=121.473701 : 当前经度}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试骑手多订单路径规划功能';

    /**
     * 路径规划服务
     *
     * @var RouteOptimizationService
     */
    protected $routeOptimizationService;

    /**
     * 构造函数
     *
     * @param RouteOptimizationService $routeOptimizationService
     */
    public function __construct(RouteOptimizationService $routeOptimizationService)
    {
        parent::__construct();
        $this->routeOptimizationService = $routeOptimizationService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        // 获取参数
        $riderId = $this->argument('rider_id');
        $lat = $this->option('lat');
        $lng = $this->option('lng');

        // 如果未指定骑手ID，提供选择
        if (!$riderId) {
            $riders = $this->getRidersWithOrders();
            if (empty($riders)) {
                $this->error('系统中没有有效订单的骑手！');
                return 1;
            }

            $choices = [];
            foreach ($riders as $rider) {
                $choices[$rider->id] = "ID: {$rider->id}, 姓名: {$rider->name}, 当前订单数: {$rider->order_count}";
            }

            $riderId = $this->choice('请选择要测试的骑手:', $choices);
        }

        $riderLocation = [
            'lat' => $lat,
            'lng' => $lng
        ];

        $this->info("开始为骑手ID: {$riderId} 进行路径规划测试，当前位置: 纬度 {$lat}, 经度 {$lng}");

        // 获取骑手信息
        $rider = Rider::find($riderId);
        if (!$rider) {
            $this->error("找不到ID为 {$riderId} 的骑手");
            return 1;
        }

        // 获取骑手当前订单
        $orders = O2oErrandOrder::query()
            ->where('rider_id', $riderId)
            ->whereIn('order_status', [
                O2oErrandOrder::STATUS_PICKUP, 
                O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT,
                O2oErrandOrder::STATUS_DELIVERY
            ])
            ->where('refund_status', O2oErrandOrder::REFUND_STATUS_INIT)
            ->get();

        // 打印订单信息
        $this->info("\n骑手订单信息:");
        if ($orders->isEmpty()) {
            $this->warn("骑手当前没有进行中的订单");
            return 0;
        }
        
        foreach ($orders as $index => $order) {
            $this->info("订单 #" . ($index + 1) . " (订单号: " . $order->order_no . "):");
            $this->info("  订单状态: " . ($order->order_status == O2oErrandOrder::STATUS_PICKUP ? '待取货' : 
                ($order->order_status == O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT ? '已到达取货点' : '派送中')));
            
            if ($order->order_status == O2oErrandOrder::STATUS_PICKUP || $order->order_status == O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                $this->info("  取货点: " . $order->pickup_address . " (纬度 " . $order->pickup_lat . ", 经度 " . $order->pickup_lng . ")");
            }
            
            $this->info("  送货点: " . $order->deliver_address . " (纬度 " . $order->deliver_lat . ", 经度 " . $order->deliver_lng . ")");
        }

        // 计算路径规划
        $startTime = microtime(true);
        $routeResult = $this->routeOptimizationService->getRiderOrdersRoute($riderId, $riderLocation);
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        // 打印结果
        $this->info("\n规划结果:");
        $this->info("计算耗时: " . $executionTime . " 秒");
        $this->info("路径总距离: " . $routeResult['total_distance'] . " 米");
        $this->info("预计送达时间: " . $this->formatSeconds($routeResult['estimated_time']));
        
        $this->info("\n路径详情:");
        foreach ($routeResult['route'] as $index => $point) {
            $type = $point['type'];
            $typeText = $type === 'rider' ? '骑手位置' : ($type === 'pickup' ? '取货点' : '送货点');
            $orderText = $point['order_id'] ? " (订单号: " . $point['order_id'] . ")" : "";
            
            $this->info(($index + 1) . ". " . $typeText . $orderText);
        }

        return 0;
    }

    /**
     * 获取有订单的骑手列表
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    private function getRidersWithOrders()
    {
        $riders = Rider::query()
            ->select('riders.*')
            ->selectRaw('COUNT(o2o_errand_orders.id) as order_count')
            ->join('o2o_errand_orders', 'riders.id', '=', 'o2o_errand_orders.rider_id')
            ->whereIn('o2o_errand_orders.order_status', [
                O2oErrandOrder::STATUS_PICKUP, 
                O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT,
                O2oErrandOrder::STATUS_DELIVERY
            ])
            ->where('o2o_errand_orders.refund_status', O2oErrandOrder::REFUND_STATUS_INIT)
            ->groupBy('riders.id')
            ->having('order_count', '>', 0)
            ->get();
            
        return $riders;
    }

    /**
     * 格式化秒数为时分秒
     *
     * @param int $seconds 秒数
     * @return string 格式化后的时间
     */
    private function formatSeconds(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;
        
        $result = '';
        if ($hours > 0) {
            $result .= $hours . '小时';
        }
        if ($minutes > 0) {
            $result .= $minutes . '分钟';
        }
        if ($secs > 0 || $result === '') {
            $result .= $secs . '秒';
        }
        
        return $result;
    }
} 