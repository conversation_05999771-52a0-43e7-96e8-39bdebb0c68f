<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\HealthyReportRequest;
use App\Models\HealthyReport;
use Carbon\Carbon;

class HealthyReportController extends Controller
{
    public function store(HealthyReportRequest $request)
    {
        $input = $request->only(['temperature', 'check_time', 'check_img']);

        $user = $request->user();

        $healthyReport = new HealthyReport();
        $healthyReport->user_id = $user->id;
        $healthyReport->temperature = $input['temperature'];
        $healthyReport->check_time = Carbon::parse($input['check_time']);
        $healthyReport->check_img = $input['check_img'];
        $healthyReport->save();

        return $this->success('success', []);
    }
}
