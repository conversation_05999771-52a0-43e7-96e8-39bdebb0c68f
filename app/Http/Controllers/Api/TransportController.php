<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\DriverLiscenseRequest;
use App\Http\Requests\Api\DrivingLiscenseRequest;
use App\Http\Requests\Api\TransportRequest;
use App\Models\DriverLicense;
use App\Models\DrivingLicense;
use App\Models\Rider;
use App\Models\TransportTool;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TransportController extends Controller
{
    public function show(Request $request, $type)
    {
        $transportTools = TransportTool::query()
            ->where('user_id', $request->user()->id)
            ->where('type', $type)
            ->first();
        $driverLicenses = null;
        $drivingLicenses = null;
        if ($type == 2) {
            $driverLicenses = DriverLicense::query()->where('user_id', $request->user()->id)->first();
            $drivingLicenses = DrivingLicense::query()->where('user_id', $request->user()->id)->first();
        }

        if (!$transportTools) {
            return $this->success('返回成功', null);
        }
        $result = [
            'type' => $transportTools->type,
            'card_type' => $transportTools->card_type,
            'card_no' => $transportTools->card_no,
            'image' => $transportTools->image,
            'image2' => $transportTools->image2,
            'status' => $transportTools->status,
        ];
        if (!$driverLicenses) {
            $result['driver_licenses'] = null;
        } else {
            $result['driver_licenses'] = [
                'cover' => $driverLicenses->cover,
                'obverse' => $driverLicenses->obverse,
                'card_no' => $driverLicenses->card_no,
                'name' => $driverLicenses->name,
                'vehicle_type' => $driverLicenses->vehicle_type,
                'exp_time' => $driverLicenses->exp_time,
                'end_time' => $driverLicenses->end_time,
                'create_time' => $driverLicenses->create_time
            ];
        }

        if (!$drivingLicenses) {
            $result['driving_licenses'] = null;
        } else {
            $result['driving_licenses'] = [
                'cover' => $drivingLicenses->cover,
                'obverse' => $drivingLicenses->obverse,
                'name' => $drivingLicenses->name,
                'card_no' => $drivingLicenses->car_no,
                'type' => $drivingLicenses->type
            ];
        }
        return $this->success('success', $result);
    }

    public function driverLicenses(Request $request)
    {
        return $this->success('success', DriverLicense::query()->where('user_id', $request->user()->id)->first());
    }

    public function drivingLicense(Request $request)
    {
        return $this->success('success', DrivingLicense::query()->where('user_id', $request->user()->id)->first());
    }

    public function driverLicensesStore(DriverLiscenseRequest $request)
    {
        $input = $request->only('cover', 'obverse', 'card_no', 'name', 'vehicle_type', 'exp_time_type',
            'start_time', 'end_time', 'create_time');

        if ($input['exp_time_type'] == '非永久') {
            $input['exp_time_type'] = DriverLicense::ExpTimeNotForever;
        } else {
            $input['exp_time_type'] = DriverLicense::ExpTimeForever;
        }

        $license = DriverLicense::query()->where('user_id', $request->user()->id)->first();
        if (!$license) {
            $input['user_id'] = $request->user()->id;
            DriverLicense::create($input);
        } else {
            $license->update($input);
        }

        return $this->success('success', $license);

    }

    public function drivingLicensesStore(DrivingLiscenseRequest $request)
    {
        $input = $request->only(['cover', 'obverse', 'name', 'car_no', 'type']);

        $license = DrivingLicense::query()->where('user_id', $request->user()->id)->first();
        if (!$license) {
            $input['user_id'] = $request->user()->id;
            DrivingLicense::create($input);
        } else {
            $license->update($input);
        }

        return $this->success('success', $license);

    }

    public function store(TransportRequest $request)
    {
        $input = $request->only(['type', 'card_no','card_type', 'image', 'image2']);

        $transport = TransportTool::query()
            ->where('user_id', $request->user()->id)
            ->where('type', $request->type)
            ->first();

        if (!$transport) {
            $input['user_id'] = $request->user()->id;
            $input['status'] = 0;
            TransportTool::create($input);
        } else {
            $transport->update($input);
        }
        $this->updateTransportStatus($request->user()->id);

        return $this->success('success', $transport);
    }

    private function updateTransportStatus($userId) {
        $count = TransportTool::query()->where('user_id', $userId)->count();
        if ($count > 0) {
            Rider::query()->where('user_id', $userId)->update(['transport_status' => 1]);
        }
    }
}
