<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class DrivingLiscenseRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cover' => 'required',
            'obverse' => 'required',
            'name' => 'required',
            'car_no' => 'required',
            'type' => 'required',
        ];
    }
}
