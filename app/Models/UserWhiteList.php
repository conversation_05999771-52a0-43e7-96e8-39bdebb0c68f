<?php

namespace App\Models;

use App\Events\ImportWhiteList;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserWhiteList extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = ['name', 'user_id', 'tel', 'province', 'city', 'county', 'address_detail', 'active',
        'sign_date'];

    protected $casts = ['active' => 'boolean'];

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::created(function ($model) {
            $user = User::query()->where('phone', $model->tel)->first();
            event(new ImportWhiteList($user));
        });
    }
}
