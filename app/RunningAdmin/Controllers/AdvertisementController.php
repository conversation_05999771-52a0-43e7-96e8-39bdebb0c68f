<?php

namespace App\RunningAdmin\Controllers;

use App\Models\Common;
use App\RunningAdmin\Repositories\Advertisement;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Config;

class AdvertisementController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Advertisement(), function (Grid $grid) {
            $grid->model()->orderBy('id');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->column('id');
            $grid->column('title');
            $grid->column('cover')->image(Config::get('app.img_url_prefix'), 200);
            $grid->column('type')->using(\App\Models\Advertisement::TypeMap)->label();
//            $grid->column('start_time', "有效期")->display(function ($startTime) {
//                $txt = "永久";
//                if ($startTime && $this->end_time) {
//                    $txt = sprintf("%s ~ %s", $startTime, $this->end_time);
//                } elseif ($startTime) {
//                    $txt = sprintf("%s 开始", $startTime);
//                } elseif ($this->end_time) {
//                    $txt = sprintf("直到 %s", $this->end_time);
//                }
//                return $txt;
//            });

            $grid->column('target_type')->using(\App\Models\Advertisement::TargetTypeMap)->label();
            $grid->column('status')->switch();
            $grid->column('sort')->orderable();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('title')->width(3);
                $filter->in('type')->multipleSelect(\App\Models\Advertisement::TypeMap)->width(3);
                $filter->in('target_type')->multipleSelect(\App\Models\Advertisement::TargetTypeMap)->width(3);
                $filter->in('status')->multipleSelect(Common::StatusMap)->width(3);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Advertisement(), function (Form $form) {
            $form->display('id');
            $form->text('title')->required();
            $form->radio('type')->options(\App\Models\Advertisement::TypeMap)->required()->default(1);
            $form->image('cover')->uniqueName()->required()->autoUpload()->autoSave(false);
//            $form->dateRange("start_time", "end_time", '有效期');
            $form->radio('target_type')->options(\App\Models\Advertisement::TargetTypeMap)->required()->default(1);
            $form->embeds('target', function ($form) {
                $form->text('path', "跳转路径");
                $form->text('wx_appid', "微信小程序-appid");
                $form->text('wx_orgid', "微信小程序-原始ID");
                $form->text('wx_path', "微信小程序-路径");
            });
            $form->switch('status');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
