<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    protected $title = '用户';
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new User(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->column('id')->sortable();
            $grid->column('avatar')->image('', 60);
            $grid->column('phone');
            $grid->column('nickname');
            $grid->column('name');
            $grid->column('certified')->bool();;
            $grid->column('level');
            $grid->column('birthday');
            $grid->column('channel')->label();
            $grid->column('ip');
            $grid->column('active_shequ')->bool();
            $grid->column('active_paotui')->bool();;
            $grid->column('last_active_time');
            $grid->column('device_id');
            $grid->column('device_type');
            $grid->column('created_at', '注册时间');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->expand(true);
                $filter->equal('phone')->width(4);
                $filter->like('name')->width(4);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new User(), function (Show $show) {
            $show->field('id');
            $show->field('phone');
            $show->field('nickname');
            $show->field('avatar');
            $show->field('name');
            $show->field('password');
            $show->field('certified');
            $show->field('level');
            $show->field('id_card');
            $show->field('birthday');
            $show->field('channel');
            $show->field('scene');
            $show->field('ip');
            $show->field('easemob_uuid');
            $show->field('easemob_activated');
            $show->field('active_shequ');
            $show->field('active_paotui');
            $show->field('last_active_time');
            $show->field('device_id');
            $show->field('device_type');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new User(), function (Form $form) {
            $form->display('id');
            $form->text('phone');
            $form->text('nickname');
            $form->text('avatar');
            $form->text('name');
            $form->text('password');
            $form->text('certified');
            $form->text('level');
            $form->text('id_card');
            $form->text('birthday');
            $form->text('channel');
            $form->text('scene');
            $form->text('ip');
            $form->text('easemob_uuid');
            $form->text('easemob_activated');
            $form->text('active_shequ');
            $form->text('active_paotui');
            $form->text('last_active_time');
            $form->text('device_id');
            $form->text('device_type');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
