<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\CooperateRequest;
use App\Http\Resources\CooperateResource;
use App\Models\Cooperate;
use Illuminate\Http\Request;

class CooperateController extends Controller
{
    // 获取企业合作信息
    public function info(Request $request)
    {
        $info = Cooperate::query()->where("user_id", $request->user()->id)->first();
        return $this->success("success", $info ? new CooperateResource($info) : null);
    }

    // 企业合作填写
    public function store(CooperateRequest $request)
    {
        $info = Cooperate::query()->where("user_id", $request->user()->id)->first();
        $data = $request->only(['company', 'type', 'province', 'city', 'district', 'address', 'name', 'phone']);
        if ($info) {
            $info->update($data);
        } else {
            $data["user_id"] = $request->user()->id;
            $info = Cooperate::query()->create($data);
        }
        return $this->success("success", new CooperateResource($info));
    }
}
