<?php

namespace App\Http\Resources;

use App\Models\Category;
use App\Models\Common;
use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\OrderPayRec;
use App\Services\RiderLocationService;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;
use Illuminate\Support\Facades\Redis;
use function PHPUnit\Framework\isNull;

class O2oErrandOrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $buyImgs = [];
        if ($this->buy_imgs) {
            foreach ($this->buy_imgs as $k => $v) {
                $buyImgs[] = img_url($v);
            }
        }
        $pos = null;
        if ($this->rider_id && $this->order_status < O2oErrandOrder::STATUS_FINISH) {
            $riderLocationService = app(RiderLocationService::class);
            $pos = $riderLocationService->getRiderLocationCoordinates($this->rider_id);
        }
        $orderStatus = $this->order_status;
        if($this->refund_status != O2oErrandOrder::REFUND_STATUS_INIT){
            $orderStatus = O2oErrandOrder::STATUS_CANCEL;
        }
        $res = [
            'type' => $this->type,
            'type_text' => O2oErrandOrder::TypeMap[$this->type] ?? "",
            'order_no' => $this->order_no,
            'title' => $this->title,
            'user_id' => $this->user_id,
            'rider_id' => $this->rider_id,
            'rider_pos' => $pos,
            'rider_info' => [
                "id" => $this->rider_id,
                'name' => "",
                'phone' => "",
                'avatar' => "",
            ],
            'order_status' => $orderStatus,
            'order_status_text' => O2oErrandOrder::StatusMap[$orderStatus] ?? "",
            'order_status_desc' => $this->order_status_desc,
            'coupon_id' => $this->coupon_id,
            'gratuity' => fentoyuan($this->gratuity),
            'reward' => fentoyuan($this->reward),
            'remark' => $this->remark,
            'hide_address' => $this->hide_address,
            'is_special' => $this->is_special,
            'need_incubator' => $this->need_incubator,
            'close_reason' => $this->close_reason ? $this->close_reason : '',
            'is_trans' => $this->is_trans,
            'ori_rider_id' => $this->ori_rider_id,
            'distance' => $this->distance,
            'pickup_name' => $this->pickup_name,
            'pickup_address_id' => $this->pickup_address_id,
            'pickup_address' => $this->pickup_address,
            'pickup_phone' => $this->pickup_phone,
            'pickup_lng' => $this->pickup_lng,
            'pickup_lat' => $this->pickup_lat,
            'pickup_region_id' => $this->pickup_region_id,
            'pickup_code' => $this->pickup_code,
            'pickup_code_mode' => $this->pickup_code_mode,
            'deliver_name' => $this->deliver_name,
            'deliver_address_id' => $this->deliver_address_id,
            'deliver_address' => $this->deliver_address,
            'deliver_phone' => $this->deliver_phone,
            'deliver_lng' => $this->deliver_lng,
            'deliver_lat' => $this->deliver_lat,
            'deliver_region_id' => $this->deliver_region_id,
            'receive_code' => $this->receive_code,
            'receive_code_mode' => $this->receive_code_mode,
            'goods_desc' => $this->goods_desc,
            'goods_imgs' => img_url_array($this->goods_imgs),
            'buy_imgs' => $buyImgs,
            'goods_price_pay_order_no' => "",
            'goods_price_paid_at' => "",
            "goods_pre_price" => fentoyuan($this->detail["goods_price"] ?? $this->goods_price),
            'goods_price' => fentoyuan($this->goods_price),
            'goods_protected_price' => fentoyuan($this->goods_protected_price),
            'goods_category_id' => $this->goods_category_id,
            'category_id' => $this->category_id,
            'weight' => $this->weight,
            'volume' => $this->volume,
            'order_amount' => fentoyuan($this->order_amount),
            'actual_amount' => fentoyuan($this->actual_amount),
            'coupon_amount' => fentoyuan($this->coupon_amount),
            'freight' => fentoyuan($this->freight),
            'distance_price' => fentoyuan($this->distance_price),
            'time_price' => fentoyuan($this->time_price),
            'weather_price' => fentoyuan($this->weather_price),
            'weight_price' => fentoyuan($this->weight_price),
            'pay_method' => $this->pay_method,
            'pay_method_text' => Common::PayMethodMap[$this->pay_method] ?? "",
            'paid_at' => $this->paid_at ? $this->paid_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'payment_no' => $this->payment_no,
            'refund_amount' => fentoyuan($this->refund_amount),
            'refund_status' => $this->refund_status,
            'refund_no' => $this->refund_no,
            'refund_at' => $this->refund_at ? $this->refund_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'timeline' => $this->timeline,
            'create_time' => $this->create_time ? $this->create_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'appointment_start_time' => $this->appointment_start_time ? $this->appointment_start_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'appointment_end_time' => $this->appointment_end_time ? $this->appointment_end_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'pickup_at' => $this->pickup_at ? $this->pickup_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'arrive_at' => $this->arrive_at ? $this->arrive_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'receipt_time' => $this->receipt_time ? $this->receipt_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'finish_time' => $this->finish_time ? $this->finish_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'estimated_delivery_time' => $this->estimated_delivery_time ? $this->estimated_delivery_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
        $amountInfo = [
            ['label' => '基础配送费', 'amount' => $res["freight"]],
            ['label' => '距离附加费', 'amount' => $res["distance_price"]],
        ];
        if ($res['time_price']) {
            $amountInfo[] = ['label' => '特殊时段费', 'amount' => $res["time_price"]];
        }
        if ($res['weather_price']) {
            $amountInfo[] = ['label' => '恶劣天气费', 'amount' => $res["weather_price"]];
        }
        if ($res['weight_price']) {
            $amountInfo[] = ['label' => '重量附加费', 'amount' => $res["weight_price"]];
        }
        if ($res['coupon_amount']) {
            $amountInfo[] = ['label' => '优惠券', 'amount' => "-" . $res["coupon_amount"]];
        }
        if ($res['gratuity']) {
            $amountInfo[] = ['label' => '小费', 'amount' => $res["gratuity"]];
        }
        if ($res["goods_protected_price"]) {
            $amountInfo[] = ['label' => '保费', 'amount' => $res["goods_protected_price"]];
        }

        $res["amount_info"] = $amountInfo;
        $res["total_amount"] = fentoyuan($this->actual_amount + $this->gratuity + $this->goods_protected_price);
        if (!($this->whenLoaded('rider') instanceof MissingValue)) {
            if ($this->rider) {
                $res["rider_info"] = [
                    'id' => $this->rider->id,
                    'name' => $this->rider->name,
                    'phone' => $this->rider->phone,
                    'avatar' => $this->rider->avatar ? img_url($this->rider->avatar) : "",
                ];
            }
        }
        if (!($this->whenLoaded('goodsCategory') instanceof MissingValue)) {
            $res["goods_category"] = new CategoryResource($this->goodsCategory);
        }
        if (!isset($res['goods_category']) || isNull($res['goods_category'])) {
            $goodsCategory = Category::find(14);
            $res['goods_category'] = new CategoryResource($goodsCategory);
        }
        if (!($this->whenLoaded('payRecs') instanceof MissingValue)) {
            if ($this->payRecs->count() > 0) {
                foreach ($this->payRecs as $v) {
                    if ($v->type == OrderPayRec::TYPE_GOODS) { //获取骑手垫付的商品费支付单号及是否支付标识
                        $res["goods_price_pay_order_no"] = $v->order_no;
                        $res["goods_price_paid_at"] = $v->paid_at ? $v->paid_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "";
                        break;
                    }
                }
            }
        }
        if (!($this->whenLoaded('evaluate') instanceof MissingValue)) {
            $res["evaluate"] = new OrderEvaluateRecResource($this->evaluate);
        }
        return $res;
    }
}
