<?php

namespace App\Http\Controllers\Api;

use App\Models\Site;
use App\Models\SiteRegion;
use Illuminate\Http\Request;

class SiteController extends Controller
{
    public function show($id)
    {
        $site = Site::with('regions')->find($id);
        $regions = [];
        foreach ($site->regions as $region) {
            $p = [];
            foreach ($region->points as $point) {
                $p[] = [$point['lng'], $point['lat']];
            }
            $regions[] = [
                'id' => $region->id,
                'site_id' => $region->site_id,
                'name' => $region->region_name,
                'points' => $p
            ];
        }

        $result = [
            'site' => [
                'id' => $site->id,
                'name' => $site->name,
                'latitude' => $site->latitude,
                'longitude' => $site->longitude,
            ],
            'regions' => $regions
        ];

        return $this->success('success', $result);
    }

    public function saveSiteRegion(Request $request)
    {
        $polygons = $request->polygons;
        $siteId = $request->site_id;

        if(!$polygons) return $this->errorResponse(400, "请框选范围");
        $areaIds = [];
        foreach ($polygons as $polygon) {
            $siteRegion = SiteRegion::query()->where("site_id", $siteId)->where('region_name', $polygon['name'])->first();
            if ($siteRegion) {
                $siteRegion->update(['points' => $polygon['pointList']]);
            } else {
                $siteRegion = SiteRegion::create([
                    'region_name' => $polygon['name'],
                    'points' => $polygon['pointList'],
                    'site_id' => $siteId
                ]);
            }
            $areaIds[] = $siteRegion->id;
        }
        SiteRegion::query()->where("site_id", $siteId)->whereNotIn("id", $areaIds)->delete();

        return $this->success('success', []);
    }
}
