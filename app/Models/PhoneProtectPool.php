<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;


class PhoneProtectPool extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const VENDOR_NONE = 0;
    const VENDOR_YD = 1;
    const VENDOR_LT = 2;
    const VENDOR_DX = 3;
    const VendorMap = [
        self::VENDOR_NONE => '未知',
        self::VENDOR_YD => '移动',
        self::VENDOR_LT => "联通",
        self::VENDOR_DX => "电信",
    ];

    const STATUS_ACTIVE = 1;
    const STATUS_DISABLE = 0;
    const StatusMap = [
        self::STATUS_ACTIVE => '启用',
        self::STATUS_DISABLE => '禁用'
    ];

    protected $dates = ['purchase_time'];

    protected $fillable = [
        'phone', 'purchase_time', 'region', "vendor", 'status',
    ];

    public function logs(){
        return $this->hasMany(PhoneProtectLog::class, "phone_x", "phone");
    }
}
