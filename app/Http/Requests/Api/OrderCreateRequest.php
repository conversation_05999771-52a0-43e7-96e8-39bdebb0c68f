<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class OrderCreateRequest extends FormRequest
{
    public function rules()
    {
        return [
            'shop_id' => 'required|integer|min:1',
            'address_id' => 'required|integer|min:1',
            'sending_time' => 'required',
            'is_tel_protect' => ['required', 'integer', Rule::in([0, 1])]
        ];
    }

    public function attributes()
    {
        return [
            'shop_id' => "店铺",
            'address_id' => '收货地址',
            'sending_time' => '配送时间',
            'is_tel_protect' => '号码保护'
        ];
    }
}
