<?php

namespace App\RunningAdmin\Controllers;

use App\Models\RiderLevelSetting;
use App\RunningAdmin\Repositories\RiderRight;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RiderRightController extends AdminController
{
    protected $title = '骑手权益';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RiderRight(), function (Grid $grid) {
            $grid->model()->orderBy('view_order');
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('icon')->image('', 50);
            $grid->column('remark');
            $grid->column('level');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RiderRight(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('icon');
            $show->field('remark');
            $show->field('levels');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RiderRight(), function (Form $form) {
            $form->display('id');
            $form->text('name')->required();
            $form->image('icon')->required()->autoUpload()->uniqueName();
            $form->text('remark')->required()->maxLength(5);
            $options = RiderLevelSetting::query()->pluck('name', 'level');
            $form->select('level', '骑手等级')->options($options);
            $form->number('view_order', '排序')->default(1);
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
