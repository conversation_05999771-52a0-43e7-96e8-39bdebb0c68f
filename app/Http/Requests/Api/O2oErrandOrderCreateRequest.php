<?php

namespace App\Http\Requests\Api;

use App\Models\O2oErrandOrder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class O2oErrandOrderCreateRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            "type" => ['required', 'integer', Rule::in([O2oErrandOrder::TYPE_SEND, O2oErrandOrder::TYPE_TAKE, O2oErrandOrder::TYPE_BUY, O2oErrandOrder::TYPE_HELP])],
            "gratuity" => "numeric|min:0",
//            "remark" => "string",
            "appointment_time" => "required|string",
            "coupon_id" => "integer|min:0",
            "hide_address" => 'required|boolean',
            "is_special" => 'required|boolean',
            "need_incubator" => 'required|boolean',
            "goods_info.category_id" => "integer|min:0",
            "goods_info.goods_category_id" => "integer|min:0",
//            "goods_info.desc" => "string",
            "goods_info.imgs" => "array",
            "goods_info.price" => "numeric|min:0",
            "goods_info.is_protect_price" => "required|boolean",
            "goods_info.weight" => "integer|min:0",
//            "goods_info.volume" => "string",
            "start_point.mode" => ['required', 'integer', Rule::in([0, 1, 2])], //0-id,1-指定，2-就近
            "start_point.pickup_code" => "required|boolean",
            "start_point.pickup_code_mode" => ['required', 'integer', Rule::in([0, O2oErrandOrder::VALIDATE_SPEAK, O2oErrandOrder::VALIDATE_INPUT])],
            "end_point.address_id" => "required|integer|min:1",
            "end_point.receive_code" => "required|boolean",
            "end_point.receive_code_mode" => ['required', 'integer', Rule::in([0, O2oErrandOrder::VALIDATE_SPEAK, O2oErrandOrder::VALIDATE_INPUT])],
        ];
        $startPoint = $this->input("start_point");
        if ($startPoint["mode"] == 0) {
            $rules["start_point.address_id"] = "required|integer|min:1";
        } elseif ($startPoint["mode"] == 1) {
            $rules["start_point.area"] = "required|string";
            $rules["start_point.address"] = "required|string";
            $rules["start_point.lat"] = "required|numeric";
            $rules["start_point.lng"] = "required|numeric";
        }
        return $rules;
    }

    public function attributes()
    {
        return [
            "type" => "类型",
            'gratuity' => "小费",
            'remark' => "备注",
            'appointment_time' => "预约时间",
            'coupon_id' => "优惠券",
            'hide_address' => "是否隐藏发货地",
            'is_special' => "是否专人配送",
            'need_incubator' => "是否需要保温箱",

            "goods_info" => "商品信息",
            "goods_info.category_id" => "商品一级类目",
            "goods_info.goods_category_id" => "商品二级类目",
            "goods_info.desc" => "商品描述",
            "goods_info.imgs" => "商品图",
            "goods_info.price" => "商品预估价",
            "goods_info.is_protect_price" => "是否保价",
            "goods_info.weight" => "重量",
            "goods_info.volume" => "体积",

            "start_point" => "起点信息",
            "start_point.mode" => "起点类型",
            "start_point.address_id" => "起点地址",
            "start_point.pickup_code" => "是否开启取货码",
            "start_point.pickup_code_mode" => "取货码验证方式",
            "start_point.area" => "指定店铺省市区",
            "start_point.address" => "指定店铺详细地址",
            "start_point.lat" => "指定店铺纬度",
            "start_point.lng" => "指定店铺经度",

            "end_point" => "终点信息",
            "end_point.address_id" => "终点地址",
            "end_point.receive_code" => "是否开启收货码",
            "end_point.receive_code_mode" => "收货码验证方式",
        ];
    }
}
