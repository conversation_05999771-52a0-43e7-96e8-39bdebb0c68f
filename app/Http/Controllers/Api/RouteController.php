<?php

namespace App\Http\Controllers\Api;

use App\Models\Rider;
use App\Services\RouteOptimizationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RouteController extends Controller
{
    /**
     * 路径规划服务
     *
     * @var RouteOptimizationService
     */
    protected $routeOptimizationService;

    /**
     * 构造函数
     *
     * @param RouteOptimizationService $routeOptimizationService
     */
    public function __construct(RouteOptimizationService $routeOptimizationService)
    {
        $this->routeOptimizationService = $routeOptimizationService;
    }

    /**
     * 获取最优路径（不考虑取送货顺序约束）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function optimize(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'rider_location.lat' => 'required|numeric',
            'rider_location.lng' => 'required|numeric',
            'orders' => 'required|array',
            'orders.*.id' => 'required|string',
            'orders.*.pickup.lat' => 'required|numeric',
            'orders.*.pickup.lng' => 'required|numeric',
            'orders.*.delivery.lat' => 'required|numeric',
            'orders.*.delivery.lng' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取路径规划详细信息
        $result = $this->routeOptimizationService->getRouteDetails(
            $request->input('rider_location'),
            $request->input('orders')
        );

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => $result
        ]);
    }

    /**
     * 获取考虑约束的最优路径（取货必须在送货之前）
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function constrained(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'rider_location.lat' => 'required|numeric',
            'rider_location.lng' => 'required|numeric',
            'orders' => 'required|array',
            'orders.*.id' => 'required|string',
            'orders.*.pickup.lat' => 'required|numeric',
            'orders.*.pickup.lng' => 'required|numeric',
            'orders.*.delivery.lat' => 'required|numeric',
            'orders.*.delivery.lng' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取考虑约束的路径规划
        $route = $this->routeOptimizationService->getConstrainedRoute(
            $request->input('rider_location'),
            $request->input('orders')
        );

        // 计算总距离和时间
        $totalDistance = 0;
        $estimatedTime = 0;
        $waypoints = [];

        for ($i = 0; $i < count($route) - 1; $i++) {
            $from = $route[$i];
            $to = $route[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = app()->make('App\\Services\\MapService')->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                // 检查是否存在duration字段
                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;

                $totalDistance += $distance;
                $estimatedTime += $duration;

                // 构建导航点
                $waypoints[] = [
                    'from' => $from,
                    'to' => $to,
                    'distance' => $distance,
                    'duration' => $duration
                ];
            }
        }

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => [
                'route' => $route,
                'total_distance' => $totalDistance,
                'estimated_time' => $estimatedTime,
                'waypoints' => $waypoints
            ]
        ]);
    }

    /**
     * 获取骑手当前订单的最优路径规划
     * 支持高德地图SDK直接使用
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function riderRoute(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取当前登录骑手信息
        $user = $request->user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 获取路径规划
        $riderLocation = [
            'lat' => $request->input('lat'),
            'lng' => $request->input('lng')
        ];

        $result = $this->routeOptimizationService->getRiderOrdersRoute($rider->id, $riderLocation);

        // 为高德地图SDK格式化结果
        $formattedResult = [
            'total_distance' => $result['total_distance'],
            'estimated_time' => $result['estimated_time'],
            'waypoints' => $result['waypoints'],
            'route' => [],
            // 高德地图路径规划所需数据
            'amap' => [
                'origin' => $riderLocation['lng'] . ',' . $riderLocation['lat'],
                'destination' => '',
                'waypoints' => [],
            ],
            'orders' => []
        ];

        // 处理路径详情，同时构建高德地图所需的waypoints字符串
        $amapWaypoints = [];
        $lastDeliveryPoint = null;
        $orderInfo = [];

        foreach ($result['route'] as $index => $point) {
            $type = $point['type'];
            $orderNo = $point['order_id'];

            // 跳过起点（骑手位置）
            if ($type !== 'rider' && $index > 0) {
                $amapWaypoints[] = $point['lng'] . ',' . $point['lat'];

                // 记录最后一个送货点作为终点
                if ($type === 'delivery') {
                    $lastDeliveryPoint = $point;
                }
            }

            $pointInfo = [
                'type' => $type,
                'order_no' => $orderNo,
                'lat' => $point['lat'],
                'lng' => $point['lng'],
                'address' => null,
                'name' => null,
                'phone' => null
            ];

            // 添加额外信息
            if ($orderNo) {
                foreach ($result['orders'] as $order) {
                    if ($order->order_no === $orderNo) {
                        if ($type === 'pickup') {
                            $pointInfo['address'] = $order->pickup_address;
                            $pointInfo['name'] = $order->pickup_name;
                            $pointInfo['phone'] = $order->pickup_phone;

                            // 记录订单基本信息
                            if (!isset($orderInfo[$orderNo])) {
                                $orderInfo[$orderNo] = [
                                    'order_no' => $orderNo,
                                    'order_status' => $order->order_status,
                                    'type' => $order->type,
                                    'pickup' => [
                                        'address' => $order->pickup_address,
                                        'name' => $order->pickup_name,
                                        'phone' => $order->pickup_phone,
                                        'lat' => $order->pickup_lat,
                                        'lng' => $order->pickup_lng,
                                    ],
                                    'delivery' => [
                                        'address' => $order->deliver_address,
                                        'name' => $order->deliver_name,
                                        'phone' => $order->deliver_phone,
                                        'lat' => $order->deliver_lat,
                                        'lng' => $order->deliver_lng,
                                    ]
                                ];
                            }
                        } else if ($type === 'delivery') {
                            $pointInfo['address'] = $order->deliver_address;
                            $pointInfo['name'] = $order->deliver_name;
                            $pointInfo['phone'] = $order->deliver_phone;
                        }
                        break;
                    }
                }
            }

            $formattedResult['route'][] = $pointInfo;
        }

        // 如果有路径点，设置终点为最后一个送货点，否则终点就是骑手当前位置
        if ($lastDeliveryPoint) {
            $formattedResult['amap']['destination'] = $lastDeliveryPoint['lng'] . ',' . $lastDeliveryPoint['lat'];
        } else {
            $formattedResult['amap']['destination'] = $riderLocation['lng'] . ',' . $riderLocation['lat'];
        }

        // 设置途经点
        if (!empty($amapWaypoints)) {
            // 移除最后一个点，因为它是终点
            if (count($amapWaypoints) > 1) {
                array_pop($amapWaypoints);
            }
            $formattedResult['amap']['waypoints'] = implode(';', $amapWaypoints);
        }

        // 添加订单信息
        foreach ($orderInfo as $info) {
            $formattedResult['orders'][] = $info;
        }

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => $formattedResult
        ]);
    }

    /**
     * 获取骑手多任务的最优路径规划
     * 系统自动根据时间窗口和位置关联性来确定需要进行路径优化的任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function multiTaskRouteV2(Request $request)
    {
        // 验证请求数据，只需要骑手位置，不再需要传入任务IDs
        $validator = Validator::make($request->all(), [
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取当前登录骑手信息
        $user = $request->user();
        $rider = Rider::where('user_id', $user->id)->first();
        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 获取骑手位置
        $riderLocation = [
            'lat' => strval($request->input('lat')),
            'lng' => strval($request->input('lng')),
            'time_window' => $request->input('time_window', 120)
        ];


        // 获取骑手当前所有活跃任务
        $allActiveTasks = $this->routeOptimizationService->getRiderActiveTasks($rider->id, $rider->role);
        // 没有任务或只有一个任务时，无需优化
        if (count($allActiveTasks) < 2) {
            return response()->json([
                'code' => 0,
                'message' => '当前任务数量不足，无需路径优化',
                'data' => []
            ]);
        }


        $result = $this->routeOptimizationService->getMultiTaskRouteV2($allActiveTasks, $riderLocation);

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => $result
        ]);

//        // 计算优化节省的时间和距离
//        $savings = $this->routeOptimizationService->calculateSavings($result);
//
//        // 保存路径规划结果到数据库
//        $routeId = $this->routeOptimizationService->saveRouteResult($rider->id, $result, $savings);
//
//        return response()->json([
//            'code' => 0,
//            'message' => '成功',
//            'data' => [
//                'route_id' => $routeId,
//                'total_distance' => $result['total_distance'],
//                'estimated_time' => $result['estimated_time'],
//                'waypoints' => $result['waypoints'],
//                'savings' => $savings,
//                'orders' => $result['tasks'],
//                'task_count' => count($optimizableTasks),
//                'total_task_count' => count($allActiveTasks)
//            ]
//        ]);
    }

    /**
     * 获取骑手多任务的最优路径规划
     * 系统自动根据时间窗口和位置关联性来确定需要进行路径优化的任务
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function multiTaskRoute(Request $request)
    {
        // 验证请求数据，只需要骑手位置，不再需要传入任务IDs
        $validator = Validator::make($request->all(), [
            'lat' => 'required|numeric',
            'lng' => 'required|numeric',
            'time_window' => 'nullable|integer|min:15|max:120', // 可选参数，时间窗口大小(分钟)
            'max_distance' => 'nullable|integer|min:1000|max:20000' // 可选参数，最大距离(米)
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取当前登录骑手信息
        $user = $request->user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 获取骑手位置
        $riderLocation = [
            'lat' => $request->input('lat'),
            'lng' => $request->input('lng')
        ];

        // 获取时间窗口设置（默认60分钟）
        $timeWindow = $request->input('time_window', 60);

        // 获取最大距离设置（默认5000米）
        $maxDistance = $request->input('max_distance', 5000);

        // 获取骑手当前所有活跃任务
        $allActiveTasks = $this->routeOptimizationService->getRiderActiveTasks($rider->id, $rider->role);

        // 没有任务或只有一个任务时，无需优化
        if (count($allActiveTasks) < 2) {
            return response()->json([
                'code' => 400,
                'message' => '当前任务数量不足，无需路径优化',
                'data' => [
                    'has_optimization' => false,
                    'task_count' => count($allActiveTasks)
                ]
            ]);
        }
        // 根据时间窗口和位置关联性筛选需要优化的任务
        $optimizableTasks = $this->routeOptimizationService->filterOptimizableTasks(
            $allActiveTasks,
            $riderLocation,
            $timeWindow,
            $maxDistance
        );
        // 如果筛选后的任务数量不足，无需优化
        if (count($optimizableTasks) < 2) {
            return response()->json([
                'code' => 400,
                'message' => '没有找到足够的相关任务，无需路径优化',
                'data' => [
                    'has_optimization' => false,
                    'task_count' => count($optimizableTasks)
                ]
            ]);
        }

        // 获取筛选后任务的ID数组
        $taskIds = collect($optimizableTasks)->pluck('id')->toArray();

        // 获取指定任务的最优路径
        $result = $this->routeOptimizationService->getMultiTaskRoute(
            $rider->id,
            $riderLocation,
            $taskIds
        );

        // 计算优化节省的时间和距离
        $savings = $this->routeOptimizationService->calculateSavings($result);

        // 保存路径规划结果到数据库
        $routeId = $this->routeOptimizationService->saveRouteResult($rider->id, $result, $savings);

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => [
                'route_id' => $routeId,
                'total_distance' => $result['total_distance'],
                'estimated_time' => $result['estimated_time'],
                'waypoints' => $result['waypoints'],
                'savings' => $savings,
                'orders' => $result['tasks'],
                'task_count' => count($optimizableTasks),
                'total_task_count' => count($allActiveTasks)
            ]
        ]);
    }

    /**
     * 获取路径规划详情
     *
     * @param int $id 路径规划ID
     * @return \Illuminate\Http\JsonResponse
     */
    public function routeDetail($id)
    {
        // 获取当前登录骑手信息
        $user = auth()->user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 获取路径规划详情
        $routeDetail = $this->routeOptimizationService->getRouteById($id, $rider->id);

        if (!$routeDetail) {
            return response()->json([
                'code' => 404,
                'message' => '未找到路径规划信息',
                'data' => null
            ], 404);
        }

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => $routeDetail
        ]);
    }

    /**
     * 保存用户选择的路径
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function saveRoute(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'route_id' => 'required|integer',
            'is_accepted' => 'required|boolean'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取当前登录骑手信息
        $user = $request->user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 更新路径状态
        $result = $this->routeOptimizationService->updateRouteStatus(
            $request->input('route_id'),
            $rider->id,
            $request->input('is_accepted')
        );

        if (!$result) {
            return response()->json([
                'code' => 404,
                'message' => '未找到路径规划信息或无权操作',
                'data' => null
            ], 404);
        }

        return response()->json([
            'code' => 0,
            'message' => '保存成功',
            'data' => null
        ]);
    }

    /**
     * 开始导航
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function startNavigation(Request $request)
    {
        // 验证请求数据
        $validator = Validator::make($request->all(), [
            'route_id' => 'required|integer',
            'lat' => 'required|numeric',
            'lng' => 'required|numeric'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'code' => 422,
                'message' => '参数错误',
                'data' => $validator->errors()
            ], 422);
        }

        // 获取当前登录骑手信息
        $user = $request->user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 获取路径规划详情
        $routeDetail = $this->routeOptimizationService->getRouteById($request->input('route_id'), $rider->id);

        if (!$routeDetail) {
            return response()->json([
                'code' => 404,
                'message' => '未找到路径规划信息',
                'data' => null
            ], 404);
        }

        // 获取当前位置
        $currentLocation = [
            'lat' => $request->input('lat'),
            'lng' => $request->input('lng')
        ];

        // 获取导航信息
        $navigationInfo = $this->routeOptimizationService->getNavigationInfo(
            $routeDetail,
            $currentLocation
        );

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => $navigationInfo
        ]);
    }

    /**
     * 查询路径优化节省信息
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function routeSavings(Request $request)
    {
        // 获取当前登录骑手信息
        $user = $request->user();
        $rider = Rider::where('user_id', $user->id)->first();

        if (!$rider) {
            return response()->json([
                'code' => 403,
                'message' => '您不是骑手，无法使用此功能',
                'data' => null
            ], 403);
        }

        // 获取骑手当前活跃任务
        $activeTasks = $this->routeOptimizationService->getRiderActiveTasks($rider->id, $rider->role);

        if (count($activeTasks) < 2) {
            return response()->json([
                'code' => 400,
                'message' => '当前任务数量不足，无需路径优化',
                'data' => [
                    'has_optimization' => false,
                    'task_count' => count($activeTasks)
                ]
            ]);
        }

        // 计算可能的优化节省
        $savings = $this->routeOptimizationService->calculatePotentialSavings($rider->id, $activeTasks);

        return response()->json([
            'code' => 0,
            'message' => '成功',
            'data' => [
                'has_optimization' => true,
                'task_count' => count($activeTasks),
                'time_savings' => $savings['time_savings'],
                'distance_savings' => $savings['distance_savings']
            ]
        ]);
    }
}
