<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class Merchant extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'merchants';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'shop_name',
        'phone',
        'password',
        'province',
        'city',
        'district',
        'city_code',
        'address',
        'contact_name',
        'email',
        'merchant_type',
        'license_number',
        'balance',
        'status',
        'user_id',
    ];

    /**
     * 隐藏的属性
     *
     * @var array
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * 应该被转换成日期的属性
     *
     * @var array
     */
    protected $dates = [
        'approved_at',
        'last_login_at',
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    /**
     * 属性的默认值
     *
     * @var array
     */
    protected $attributes = [
        'status' => 0, // 默认为待审核状态
        'balance' => 0, // 默认余额为0
    ];

    /**
     * 属性类型转换
     *
     * @var array
     */
    protected $casts = [
        'balance' => 'float',
    ];

    /**
     * 获取审核人
     */
    public function approver()
    {
        return $this->belongsTo(User::class, 'approved_by');
    }

    /**
     * 检查商家是否已审核通过
     *
     * @return bool
     */
    public function isApproved()
    {
        return $this->status === 1;
    }

    /**
     * 获取状态文本
     *
     * @return string
     */
    public function getStatusTextAttribute()
    {
        $statusMap = [
            0 => '待审核',
            1 => '已审核通过',
            2 => '审核拒绝',
            3 => '暂停服务',
            4 => '账号禁用',
        ];

        return $statusMap[$this->status] ?? '未知状态';
    }


    /**
     * 获取关联的用户账号
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
