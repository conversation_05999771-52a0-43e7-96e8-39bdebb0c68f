<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class CartChangeRequest extends FormRequest
{
    public function rules()
    {
        return [
            'spu_id' => 'required|integer|min:1',
            'quantity' => 'required|integer|min:0'
        ];
    }

    public function attributes()
    {
        return [
            'spu_id' => "商品",
            'quantity' => "商品数量",
        ];
    }
}
