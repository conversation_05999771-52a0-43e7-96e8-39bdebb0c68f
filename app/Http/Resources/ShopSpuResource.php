<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class ShopSpuResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'shop_id' => $this->shop_id,
            'cat_id' => $this->cat_id,
            'name' => $this->name,
            'cover' => $this->cover ? img_url($this->cover) : "",
            'description' => $this->description,
            'price' => $this->price,
            'discount_price' => $this->discount_price,
            'sell_tags' => $this->sell_tags ?: [],
            'stock' => $this->stock,
            'sales' => $this->sales,
            'is_signboard' => $this->is_signboard,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
