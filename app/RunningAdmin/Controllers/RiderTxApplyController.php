<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Actions\Grid\RiderTxApplyReject;
use App\RunningAdmin\Repositories\RiderTxApply;
use App\RunningAdmin\RowActions\RiderTxApplyAgree;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RiderTxApplyController extends AdminController
{
    protected $title = '提现申请表';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RiderTxApply(), function (Grid $grid) {
            $grid->model()->with(['user', 'user.rider'])->orderByDesc('id');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->column('id')->sortable();
            $grid->column('user_id');
            $grid->column('order_no');
            $grid->column('amount');
            $grid->column('status')->using(\App\Models\RiderTxApply::StatusMap)->label(
                [
                    0 => 'warning',
                    1 => 'success',
                    2 => 'danger',
                ],
                'primary'
            );
            $grid->column('trans_status')->using(\App\Models\RiderTxApply::TransferStatusMap)->label(
                [
                    0 => 'warning',
                    1 => 'success',
                    2 => 'danger',
                ],
                'primary'
            );
            $grid->column('remark');
            $grid->column('trans_message');
            $grid->column('account_type')->using(\App\Models\RiderTxApply::AccountTypeMap)->label();
            $grid->column('bank_name');
            $grid->column('bank_account');
            $grid->column('real_name');
            $grid->column('created_at', '申请时间');
            $grid->column('check_time');
            $grid->column('transfer_time');
            $grid->quickSearch(['remark', 'real_name']);

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->status == 0) {
                    $actions->append(new RiderTxApplyAgree());
                    $actions->append(new RiderTxApplyReject());
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('todo', '待办')->where('status', 0);
                $filter->scope('pass', '通过')->where('status', 1);
                $filter->scope('reject', '拒绝')->where('status', 2);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RiderTxApply(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('order_no');
            $show->field('amount');
            $show->field('status');
            $show->field('trans_status');
            $show->field('remark');
            $show->field('trans_message');
            $show->field('account_type');
            $show->field('bank_name');
            $show->field('bank_account');
            $show->field('real_name');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RiderTxApply(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('order_no');
            $form->text('amount');
            $form->text('status');
            $form->text('trans_status');
            $form->text('remark');
            $form->text('trans_message');
            $form->text('account_type');
            $form->text('bank_name');
            $form->text('bank_account');
            $form->text('real_name');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
