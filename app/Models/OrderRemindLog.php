<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderRemindLog extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'order_id', 'user_id', 'rider_id'
    ];

    public function order(){
        return $this->belongsTo(O2oErrandOrder::class, "order_id");
    }

    public function user(){
        return $this->belongsTo(User::class, "user_id");
    }

    public function rider(){
        return $this->belongsTo(Rider::class, "rider_id");
    }
}
