<?php

namespace App\Services;

use App\Jobs\CloseOrder;
use App\Models\Cart;
use App\Models\Common;
use App\Models\Order;
use App\Models\OrderDetail;
use App\Models\OrderRefund;
use App\Models\ShopSpu;
use App\Models\User;
use App\Models\UserAccount;
use App\Models\UserAccountFlow;
use App\Models\UserAddress;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yansongda\Pay\Pay;

class OrderService
{

    //预下单，参数：shop_id、address_id、sending_time、is_tel_protect、remark
    public function preOrder($user, array $params)
    {
        if ($user->channel != User::CHANNEL_WHITE_LIST) throw new \Exception("本「社区食堂」仅供内部白名单人员使用，如需购买请前往「雨骑士」使用帮我买进行下单～");
        $cartItems = Cart::query()->with(["spu"])->where("user_id", $user->id)->where("shop_id", $params['shop_id'])->get();
        if ($cartItems->count() <= 0) {
            throw new \Exception("你的购物车是空的");
        }
        $address = UserAddress::query()->where("user_id", $user->id)->where("id", $params["address_id"])->first();
        if (!$address) throw new \Exception("该收货地址无效");
        if (!$address->is_white_list) throw new \Exception("本「社区食堂」仅供内部白名单人员使用，如需购买请前往「雨骑士」使用帮我买进行下单～");
        $orderAmount = 0;
        $payAmount = 0;
        foreach ($cartItems as $item) {
            if ($item->quantity <= 0) continue;
            if (!$item->spu) throw new \Exception("该商品不存在");
            if ($item->spu->status != Common::STATUS_UP) throw new \Exception("该商品已下架");
            if ($item->spu->stock < $item->quantity) throw new \Exception("该商品库存不足");
            $orderAmount += yuantofen($item->spu->price * $item->quantity);
            $payAmount += yuantofen($item->spu->discount_price * $item->quantity);
        }
        try {
            $timeArr = explode("|", $params["sending_time"]);
            DB::beginTransaction();
            $order = Order::query()->create([
                'user_id' => $user->id,
                'shop_id' => $params["shop_id"],
                'order_amount' => $orderAmount,
                'reduce_amount' => $orderAmount - $payAmount,
                "pay_amount" => $payAmount,
                'receiver_name' => $address->name,
                'receiver_tel' => $address->tel,
                'receiver_area' => implode("-", [$address->province, $address->city, $address->county]),
                'receiver_address' => $address->province . $address->city . $address->county . $address->address_detail . $address->address_remark,
                'sending_start_time' => $timeArr[0] ?? "",
                'sending_end_time' => $timeArr[1] ?? "",
                'remark' => $params["remark"],
                'is_tel_protect' => $params["is_tel_protect"] > 0,
            ]);
            foreach ($cartItems as $item) {
                if ($item->quantity <= 0) continue;
                $num = $item->quantity;
                $rowsAffected = ShopSpu::query()->where("id", $item->spu_id)->where("stock", ">=", $num)
                    ->update(["stock" => DB::raw("stock-{$num}"), "sales" => DB::raw("sales+{$num}")]);
                if ($rowsAffected <= 0) {
                    throw new \Exception("该商品库存不足");
                }
                OrderDetail::query()->create([
                    'order_id' => $order->id,
                    'spu_id' => $item->spu_id,
                    'spu_name' => $item->spu->name,
                    'spu_cover' => $item->spu->cover,
                    'price' => yuantofen($item->spu->price),
                    "discount_price" => yuantofen($item->spu->discount_price),
                    'quantity' => $item->quantity
                ]);
            }
            Cart::query()->where("user_id", $user->id)->where("shop_id", $params['shop_id'])->delete();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        dispatch(new CloseOrder($order->id, config('app.order_ttl')));
        return $order;
    }

    //取消订单
    public function cancelOrder($orderId, $userId = null)
    {
        $order = Order::query()->with('details')->find($orderId);
        if (!$order) throw new \Exception("该订单不存在");
        if (!is_null($userId)) {
            if ($order->user_id != $userId) throw new \Exception("该订单不存在");
        }
        if ($order->closed) {
            throw new \Exception("该订单已取消");
        }
        if ($order->paid_at) {
            throw new \Exception("该订单已支付");
        }
        try {
            DB::beginTransaction();
            $order->update(["closed" => true]);
            foreach ($order->details as $item) {
                $num = $item->quantity;
                ShopSpu::query()->where("id", $item->spu_id)->update(["stock" => DB::raw("stock+{$num}"), "sales" => DB::raw("sales-{$num}")]);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        return $order;
    }

    //预计配送前30分钟前取消订单 & 申请售后，参数：order_id、refund_reason、refund_remark、refund_pics、refund_items
    public function refundOrder(array $params, $userId = null)
    {
        $order = Order::query()->with(['details', "refunds"])->find($params["order_id"]);
        if (!$order) throw new \Exception("该订单不存在");
        if (!is_null($userId)) {
            if ($order->user_id != $userId) throw new \Exception("该订单不存在");
        }
        if ($order->closed) {
            throw new \Exception("该订单已取消");
        }
        if (!$order->paid_at) {
            throw new \Exception("该订单未支付");
        }
        if (in_array($order->refund_type, [-1, OrderRefund::REFUND_STATUS_APPLY + 10, OrderRefund::REFUND_STATUS_APPROVED + 10, OrderRefund::REFUND_STATUS_SUCCESS + 10])) {
            throw new \Exception("该订单已发起退款");
        }

        //已退款数量
        $hasRefundItems = [];
        if ($order->refunds) {
            foreach ($order->refunds as $v) {
                if (!isset($hasRefundItems[$v->spu_info["spu_id"]])) $hasRefundItems[$v->spu_info["spu_id"]] = 0;
                $hasRefundItems[$v->spu_info["spu_id"]] += $v->spu_info['quantity'];
            }
        }

        $refundData = [
            'order_id' => $order->id,
            'no' => 'T' . $order->order_no,
            'refund_reason' => $params["refund_reason"],
            "refund_remark" => $params["refund_remark"] ?: '取消订单',
            "refund_pics" => $params["refund_pics"] ?: [],
        ];
        $refundType = -1;
        if ($order->status >= Order::STATUS_DELIVERY) { //申请售后
            if (empty($params["refund_items"])) throw new \Exception("请选择退款商品");
            $refundType = OrderRefund::REFUND_STATUS_APPLY + 10;
            $refundData["refund_status"] = OrderRefund::REFUND_STATUS_APPLY;
            $refundData["refund_amount"] = 0;
            $refundData["spu_info"] = [];
            foreach ($params["refund_items"] as $v) {
                foreach ($order->details as $item) {
                    if ($v["spu_id"] == $item->spu_id) {
                        if ($v["quantity"] > $item->quantity - ($hasRefundItems[$item->spu_id] ?? 0)) {
                            throw new \Exception("退款数量超出");
                        }
                        $refundData["spu_info"][] = $v;
                        $refundData["refund_amount"] += $item->discount_price * $v["quantity"];
                        break;
                    }
                }
            }
        } else { //预计配送前30分钟前取消订单
            $refundData["refund_status"] = OrderRefund::REFUND_STATUS_APPROVED;
            $refundData["refund_amount"] = $order->pay_amount;
            $refundData["spu_info"] = [];
            foreach ($order->details as $item) {
                $refundData["spu_info"][] = ["spu_id" => $item->spu_id, "quantity" => $item->quantity];
            }
        }
        try {
            DB::beginTransaction();
            $refund = OrderRefund::query()->create($refundData);
            $order->update(["refund_type" => $refundType]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        $this->refundToUser($refund);
        return $order;
    }

    //审核通过退款
    public function refundToUser(OrderRefund $orderRefund)
    {
        if ($orderRefund->refund_status != OrderRefund::REFUND_STATUS_APPROVED) return;
        $order = Order::query()->find($orderRefund->order_id);
        if (!$order) return;

        $refundAmount = $orderRefund->refund_amount;
        $payAmount = $order->pay_amount;
        if (app()->environment() != 'production') {
            $refundAmount = 1;
            $payAmount = 1;
        }
        Pay::config(config("pay"));

        $refundNo = "";
        $refundAt = Carbon::now();
        try {
            switch ($order->pay_method) {
                case Common::PAY_METHOD_ALIPAY:
                    $result = Pay::alipay()->refund([
                        'out_trade_no' => $order->order_no,
                        'refund_amount' => fentoyuan($refundAmount),
                        'out_request_no' => $orderRefund->no . $orderRefund->id
                    ]);
                    if ($result->code != 10000) {
                        throw new \Exception($result->code . "-" . $result->msg . "(" . $result->sub_code . "-" . $result->sub_msg . ")");
                    }
                    $refundNo = $result->trade_no;
                    $refundAt = $result->gmt_refund_pay ?? Carbon::now();
                    break;
                case Common::PAY_METHOD_WECHAT:
                    $result = Pay::wechat()->refund([
                        'out_trade_no' => $order->order_no,
                        'out_refund_no' => $orderRefund->no . $orderRefund->id,
                        'amount' => [
                            'refund' => $refundAmount,
                            'total' => $payAmount,
                            'currency' => 'CNY',
                        ],
                    ]);
                    if ($result->has("code")){
                        throw new \Exception($result->code . "-" . $result->message);
                    }
                    $refundNo = $result->refund_id;
                    $refundAt = $result->success_time ?? Carbon::now();
                    break;
                case Common::PAY_METHOD_CLOUD_FLASH:
                    //TODO 云闪付退款
                    break;
                case Common::PAY_METHOD_BALANCE:
                    $account = UserAccount::query()->firstOrCreate(['user_id' => $order->user_id]);
                    DB::transaction(function () use ($order, $account, $orderRefund) {
                        $account->add($orderRefund->refund_amount, UserAccountFlow::BUSINESS_TYPE_REFUND, $order->order_no, '订单退款');
                        $orderRefund->update(["refund_status" => OrderRefund::REFUND_STATUS_SUCCESS, "refund_at" => Carbon::now(), "refund_no" => ""]);
                        if ($order->refund_type > 0) {
                            $order->update(["refund_type" => OrderRefund::REFUND_STATUS_SUCCESS+10]);
                        }
                    });
                    return ;
            }
            DB::transaction(function () use ($order, $orderRefund, $refundNo, $refundAt) {
                $orderRefund->update(["refund_status" => OrderRefund::REFUND_STATUS_SUCCESS, "refund_at" => $refundAt, "refund_no" => $refundNo]);
                if ($order->refund_type > 0) {
                    $order->update(["refund_type" => OrderRefund::REFUND_STATUS_SUCCESS+10]);
                }
            });
        }catch(\Exception $e){
            DB::transaction(function () use ($order, $orderRefund) {
                $orderRefund->update(["refund_status" => OrderRefund::REFUND_STATUS_FAIL]);
                if ($order->refund_type > 0) {
                    $order->update(["refund_type" => OrderRefund::REFUND_STATUS_FAIL + 10]);
                }
            });
            Log::error("社区食堂退款单（{$orderRefund->id}）退款失败：".json_encode(["code" => $e->getCode(), "message" => $e->getMessage(), "file" => $e->getFile(), "line" => $e->getLine()]));
        }
    }
}
