<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;

class AdvertisementResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'cover' => $this->cover ? img_url($this->cover) : "",
            'type' => $this->type,
            "start_time" => $this->start_time ? $this->start_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'end_time' => $this->end_time ? $this->end_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'target_type' => $this->target_type,
            'target' => $this->target,
            'status' => $this->status,
        ];
    }
}
