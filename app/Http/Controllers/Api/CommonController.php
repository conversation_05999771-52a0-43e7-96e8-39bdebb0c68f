<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\ConfigRequest;
use App\Http\Requests\Api\DriverOcrRequest;
use App\Http\Requests\Api\DrivingOcrRequest;
use App\Models\DriverLicense;
use App\Models\O2oErrandOrder;
use App\Http\Requests\Api\IdCardOcrRequest;
use App\Models\DataModel\Option;
use App\Models\SystemConfig;
use App\Models\Version;
use App\Services\SkyBlueService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CommonController extends Controller
{
    public function config(ConfigRequest $request)
    {
        $config = SystemConfig::getCacheConfigValue($request->platform, $request->name);
        if ($request->name == SystemConfig::PARAM_SENDING_TIME) {
            $data = [];
            if ($config) {
                for ($i = 0; $i < 2; $i++) {
                    $day = Carbon::now()->addDays($i)->format("Y-m-d");
                    $times = [];
                    foreach ($config as $v) {
                        $reserveEndTime = $day . " " . $v['reserve_end'] . ":00";
                        if ($v['reserve_end'] > $v['sending_start']) {
                            $reserveEndTime = Carbon::now()->addDays($i - 1)->format("Y-m-d") . " " . $v['reserve_end'] . ":00";
                        }
                        if ($i == 0 && $reserveEndTime <= Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT)) {
                            continue;
                        }
                        $times[] = [
                            "label" => $v['sending_start'] . "-" . $v['sending_end'],
                            "value" => $day . " " . $v['sending_start'] . ":00|" . $day . " " . $v['sending_end'] . ":00",
                        ];
                    }
                    if ($times) {
                        $data[] = ["day" => ["今天", "明天"][$i], "times" => $times];
                    }
                }
            }
            $config = $data;
        }

        return $this->success("success", $config);
    }

    public function common()
    {
        return $this->success("success", [
            "tel_protect" => route('article', 1),
            "privacy_policy" => route('article', 2),
            'third_sdk' => route('article', 7),
            'logout_agreement' => route('article', 6),
            'user_agreement' => route('article', 5),
            'business_license' => '',
            'icp' => '',
            'register_agreement' => route('article', 2),
            'recharge_agreement' => route('article', 3),
            'personal_info_list' => '',
            'about_us' => '',
            'audit_version' => true,
        ]);

    }

    public function checkVersion()
    {
        $version = Version::query()
            ->where('system', $this->system)
            ->where('current', true)->first();
        if ($version) {
            $result = version_compare($this->version, $version->version);
            if ($result == -1) {
                $result = [
                    'version' => $version->version,
                    'remark' => $version->remark,
                    'download_path' => img_url($version->download_path),
                ];
                return $this->success('success', $result);
            }
        }

        return $this->success('success', false);
    }

    //预约时间
    public function appointTime(Request $request)
    {
        $timeRange = ["07:00:00", "23:59:59"];
        $data = [];
        for ($i = 0; $i < 2; $i++) {
            $day = Carbon::now()->addDays($i)->format("Y-m-d");
            $times = [];
            $start = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $day . " " . $timeRange[0]);
            $end = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $day . " " . $timeRange[1]);
            if ($i == 0) {
                $now = Carbon::now();
                if ($now->timestamp >= $start->timestamp && $now->timestamp < $end->timestamp) {
                    $times[] = [
                        "label" => $request->type == O2oErrandOrder::TYPE_BUY ? "立即送达" : "立即取件",
                        "value" => "NOW",
                    ];
                }
            }
            for ($j = 0; ; $j++) {
                $st = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $day . " " . $timeRange[0])->addMinutes(30 * $j);
                if ($st->timestamp >= $end->timestamp) break;
                if ($st->timestamp < Carbon::now()->timestamp) continue;
                $et = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $day . " " . $timeRange[0])->addMinutes(30 * ($j + 1));
                if ($et->timestamp > $end->timestamp) $et = $end;
                $times[] = [
                    "label" => $st->format("H:i") . "-" . $et->format("H:i"),
                    "value" => $st->format(Carbon::DEFAULT_TO_STRING_FORMAT) . "|" . $et->format(Carbon::DEFAULT_TO_STRING_FORMAT),
                ];
            }
            if ($times) {
                $data[] = ["day" => ["今天", "明天"][$i], "times" => $times];
            }
        }
        return $this->success("SUCCESS", $data);
    }

    public function idCardOcr(IdCardOcrRequest $request, SkyBlueService $service)
    {
        $res = $service->idOcrCl($request->image_url, $request->oct_type, 1);
        if ($res['riskType'] == 'copy') $this->errorResponse(500, '请上传真实的身份证');
        switch ($res['imageStatus']) {
            case 'reversed_side':
                $this->errorResponse(500, '身份证正反面颠倒');
                break;
            case 'non_idcard':
                $this->errorResponse(500, '上传的图片中不包含身份证');
                break;
            case 'blurred':
                $this->errorResponse(500, '身份证模糊');
                break;
            case 'not_complete':
                $this->errorResponse(500, '身份证不完整');
                break;
            case 'over_dark':
                $this->errorResponse(500, '欠曝');
                break;
            case 'over_exposure':
                $this->errorResponse(500, '过曝');
                break;
            case 'unknown':
                $this->errorResponse(500, '未知问题，请确认上传图片是否正确');
                break;
        }

        $result = [
            'address' => $res['address'] ?? '',
            'name' => $res['name'] ?? '',
            'birth' => $res['birth'] ?? '',
            'id_card' => $res['cardNum'] ?? '',
            'sex' => $res['sex'] ?? '',
            'nation' => $res['nation'] ?? '',
            'issuing_authority' => $res['issuingAuthority'],
            'issuing_date' => $res['issuingDate'],
            'expiry_date' => $res['expiryDate'],
        ];


        return $this->success('success', $result);
    }

    public function driverCardOcr(DriverOcrRequest $request, SkyBlueService $service)
    {
        $result = $service->drivingLicense($request->image_url);

        if ($result['code'] == '0') {
            $times = explode('-', $result['expiryTime']);
            $startTime = trim($times[0]);
            $endTime = trim($times[1]);
            $res = [
                'card_no' => $result['number'],
                'name' => $result['name'],
                'vehicle_type' => $result['carType'],
                'exp_time_type' => '非永久',
                'start_time' => $startTime,
                'end_time' => $endTime,
                'create_time' => $result['firstGetCard']
            ];

            return $this->success('success', $res);
        } else {
            $this->errorResponse(500, $result['message']);
        }

    }

    public function drivingCardOcr(DrivingOcrRequest $request, SkyBlueService $service)
    {
        $result = $service->vehicleLicense($request->image_url);
        if ($result['code'] == '0') {
            $res = [
                'name' => $result['possessor'],
                'car_no' => $result['plateNo'],
                'type' => $result['vehicleType']
            ];
            return $this->success('success', $res);
        } else {
            $this->errorResponse(500, $result['message']);
        }
    }

    public function options(Request $request)
    {
        $type = $request->input('type', '');
        switch ($type) {
            case Option::TypeTransport;
                $options = Option::TypeTransportOptions;
                break;
            case Option::TypeEdu:
                $options = Option::TypeEduOptions;
                break;
            case Option::TypeJob:
                $options = Option::TypeJobOptions;
                break;
            case Option::TypeMarriage:
                $options = Option::TypeMarriageOptions;
                break;
            case Option::TypeChildren:
                $options = Option::TypeChildrenOptions;
                break;
            default:
                $options = [];
        }

        $result = [];
        foreach ($options as $key => $value) {
            $result[] = [
                'id' => $key,
                'name' => $value,
            ];
        }

        return $this->success('success', $result);
    }
}
