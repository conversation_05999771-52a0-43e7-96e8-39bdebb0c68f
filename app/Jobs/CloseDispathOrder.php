<?php

namespace App\Jobs;

use App\Models\O2oErrandOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CloseDispathOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $id;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($id, $delay)
    {
        $this->id = $id;
        // 设置延迟的时间，delay() 方法的参数代表多少秒之后执行
        $this->delay($delay);
    }


    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $dispatchOrder = \App\Models\DispatchOrder::find($this->id);
        if (!$dispatchOrder) {
            return;
        }
        $o2oOrder = O2oErrandOrder::query()->where('id', $dispatchOrder->order_id)->first();

        $dispatchOrder->status = 0;
        $dispatchOrder->closed = 1;
        $dispatchOrder->save();

        $o2oOrder->status = O2oErrandOrder::DISPATCH_STATUS_FAILED;
        $o2oOrder->save();

    }
}
