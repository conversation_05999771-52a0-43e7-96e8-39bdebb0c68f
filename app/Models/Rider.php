<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Rider extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const STATUS_UP = 1;
    const STATUS_DOWN = 0;
    const STATUS_REST = 2;
    const StatusMap = [
        self::STATUS_UP => '在线',
        self::STATUS_DOWN => '离线',
        self::STATUS_REST => '小休'
    ];

    const TYPE_STATUS_NO_UPLOAD = 0;
    const TYPE_STATUS_IN_APPROVAL = 1;
    const TYPE_STATUS_PASS = 2;
    const TYPE_STATUS_REJECT = 3;
    const TypeStatusMap = [
        self::TYPE_STATUS_NO_UPLOAD => '未上传',
        self::TYPE_STATUS_IN_APPROVAL => '审核中',
        self::TYPE_STATUS_PASS => '审核通过',
        self::TYPE_STATUS_REJECT => '审核失败',
    ];

    const LEVEL_ONE = 1;
    const LEVEL_TWO = 2;
    const LEVEL_THREE = 3;
    const LEVEL_FORE = 4;

    const LEVEL_MAP = [
        self::LEVEL_ONE => '白银骑士',
        self::LEVEL_TWO => '黄金骑士',
        self::LEVEL_THREE => '先锋骑士',
        self::LEVEL_FORE => '精英骑士',
    ];

    const RolePartTime = 1;
    const RoleFullTime = 2;

    const RoleMap = [
        self::RolePartTime => '兼职骑手',
        self::RoleFullTime => '全职骑手',
    ];


    protected $dates = ['start_time', "end_time"];

    protected $fillable = [
        'user_id',
        'name',
        'status',
        'phone',
        'avatar',
        'parent_id',
        'start_time',
        'end_time',
        'level',
        'score',
        'verified',
        'health_status',
        'transport_status',
        'order_limit',
        'trans_limit'
    ];

    protected $appends = ['avatar_url'];

    public function user()
    {
        return $this->belongsTo(User::class, "user_id");
    }

    public function parent()
    {
        return $this->belongsTo(Rider::class, "parent_id");
    }

    public function site() {
        return $this->belongsTo(Site::class, 'site_id');
    }

    public function accountBzj() {
        return $this->hasOne(RiderAccount::class, 'user_id', 'user_id')->where('account_type', 2);
    }

    public function getAvatarUrlAttribute()
    {
        if (!$this->avatar) {
            return img_url('/resource/rider_header.png');
        } else {
            return img_url($this->avatar);
        }
    }

    public function getOrderCount()
    {
        return O2oErrandOrder::query()->where('rider_id', $this->id)
            ->whereIn('order_status', [O2oErrandOrder::STATUS_PICKUP,
                O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT,
                O2oErrandOrder::STATUS_DELIVERY])
            ->where('refund_status', O2oErrandOrder::REFUND_STATUS_INIT)
            ->count();
    }
}
