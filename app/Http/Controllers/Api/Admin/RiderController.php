<?php


namespace App\Http\Controllers\Api\Admin;


use App\Http\Controllers\Api\Controller;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use Illuminate\Http\Request;

class RiderController extends Controller
{
    public function index(Request $request)
    {

        $orderNo = $request->input('order_no');

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if (!$order) {
            $this->errorResponse(200, '订单不存在', -1);
        }

        $query = Rider::query()->where('status', Rider::STATUS_UP);

        if ($siteId = $request->input('site_id')) {
            $query->where('site_id', $siteId);
        }


    }
}
