<?php

namespace App\RunningAdmin\Forms;

use App\Models\Common;
use App\Models\Coupon;
use App\Models\UserCoupon;
use App\Services\O2oErrandOrderService;
use Carbon\Carbon;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class SendCouponForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $userId = $this->payload['id'] ?? 0;
        if (empty($userId)) return $this->response()->error("请选择用户");

        $couponId = intval($input['coupon_id'] ?? 0);
        $num = intval($input['num'] ?? 0);
        if ($couponId <= 0 || $num <= 0) return $this->response()->error("请选择优惠券或填写发放数量");

        $coupon = Coupon::query()->findOrFail($couponId);
        if ($coupon->status != Common::STATUS_UP) {
            return $this->response()->error('该优惠券未上架～');
        }
        if ($coupon->validity["type"] == Coupon::VALIDITY_TYPE_RANGE && $coupon->validity["end"] <= Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT)) {
            return $this->response()->error('该优惠券已过期～');
        }
        if ($coupon->stock < $num) {
            return $this->response()->error('该优惠券库存不足～');
        }

        try {
            if ($coupon->validity['type'] == Coupon::VALIDITY_TYPE_RANGE) {
                $start = $coupon->validity['start'];
                $end = $coupon->validity['end'];
            } else {
                $start = Carbon::now();
                $end = Carbon::now()->addDays($coupon->validity["day"]);
            }
            DB::beginTransaction();
            //创建领取记录、增加销量、较少库存
            $rowsAffected = Coupon::query()->where("id", $coupon->id)->where("stock", ">=", $num)
                ->update(["stock" => DB::raw("stock-{$num}"), "sales" => DB::raw("sales+{$num}")]);
            if ($rowsAffected <= 0) {
                throw new \Exception("该优惠券库存不足～");
            }
            for ($i = 1; $i <= $num; $i++) {
                UserCoupon::query()->create([
                    'coupon_id' => $coupon->id,
                    'user_id' => $userId,
                    'start_time' => $start,
                    'end_time' => $end,
                ]);
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error($e->getMessage());
        }
        return $this->response()->success('发券成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $coupons = Coupon::query()
            ->where("status", Common::STATUS_UP)
            ->where('stock', '>', 0)
            ->where(function ($query) {
                $query->where("validity->type", "<>", Coupon::VALIDITY_TYPE_RANGE)->orWhere(function ($query) {
                    $query->where('validity->type', Coupon::VALIDITY_TYPE_RANGE)
                        ->where('validity->end', '>', Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT));
                });
            })->pluck("title", "id");
        $this->select("coupon_id", "优惠券")->options($coupons)->required();
        $this->number('num', "数量")->attribute('min', 1)->required();
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'coupon_id' => null,
            "num" => 1
        ];
    }
}
