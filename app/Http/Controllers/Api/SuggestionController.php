<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\SuggestionRequest;
use App\Models\Suggestion;
use App\Models\SuggestionCategory;
use Illuminate\Http\Request;

class SuggestionController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        $category_options = SuggestionCategory::query()
            ->where('platform', 1)
            ->orderBy('view_order')
            ->get();

        $options = [];
        foreach ($category_options as $option) {
            $options[] = [
                'id' => $option->id,
                'name' => $option->name,
            ];
        }

        $result = [
            'options' => $options,
            'phone' => $user->phone,
            'name' => $user->name
        ];

        return $this->success('success', $result);
    }

    public function store(SuggestionRequest $request)
    {
        $inputs = $request->only(['category_id', 'content', 'phone', 'contact', 'platform']);

        $suggestion = Suggestion::create([
            'user_id' => $request->user()->id,
            'suggestion_cid' => $inputs['category_id'],
            'content' => $inputs['content'],
            'platform' => $inputs['platform'],
            'phone' => $inputs['phone'],
            'contact' => $inputs['contact']
        ]);

        return $this->success('success', $suggestion);


    }
}
