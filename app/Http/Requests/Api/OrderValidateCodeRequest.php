<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class OrderValidateCodeRequest extends FormRequest
{
    public function rules()
    {
        return [
            'order_no' => 'required',
            'code_type' => ['required', 'integer', Rule::in([1, 2])],
            'code' => 'required|string',
        ];
    }

    public function attributes()
    {
        return [
            'order_no' => "订单号",
            'code_type' => "码类型",
            'code' => '码',
        ];
    }
}
