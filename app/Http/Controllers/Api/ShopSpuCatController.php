<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\ShopSpuCatRequest;
use App\Http\Resources\ShopSpuCatResource;
use App\Models\Common;
use App\Models\ShopSpuCat;

class ShopSpuCatController extends Controller
{
    public function index(ShopSpuCatRequest $request)
    {
        //shop_id(必填)
        $cats = ShopSpuCat::query()
            ->where("status", Common::STATUS_UP)
            ->where("shop_id", $request->shop_id)
            ->orderBy("sort", "asc")
            ->orderBy("id", "asc")
            ->get();
        return $this->success("success", ShopSpuCatResource::collection($this->recursiveCats($cats)));
    }

    private function recursiveCats($cats, $pid = 0)
    {
        $res = [];
        if ($cats) {
            foreach ($cats as $v) {
                if ($v->pid == $pid) {
                    $item = $v;
                    $item->children = $this->recursiveCats($cats, $v->id);
                    $res[] = $item;
                }
            }
        }
        return $res;
    }
}
