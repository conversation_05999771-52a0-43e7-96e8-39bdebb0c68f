<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class RiderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'status' => $this->status,
            'phone' => $this->phone,
            'avatar' => $this->avatar ? img_url($this->avatar) : "",
            'parent_id' => $this->parent_id,
            'start_time' => $this->start_time ? $this->start_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'end_time' => $this->end_time ? $this->end_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'level' => $this->level,
            'score' => $this->score,
            'verified' => $this->verified,
            'health_status' => $this->health_status,
            'transport_status' => $this->transport_status,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
