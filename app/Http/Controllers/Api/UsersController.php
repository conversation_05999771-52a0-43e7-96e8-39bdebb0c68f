<?php

namespace App\Http\Controllers\Api;

use App\Events\AfterRegister;
use App\Http\Requests\Api\IdCardRequest;
use App\Http\Requests\Api\LogoutRequest;
use App\Http\Requests\Api\UserAuthRequest;
use App\Http\Requests\Api\ValidChangePhoneRequest;
use App\Jobs\DispatchHandleNewUserActivity;
use App\Models\Image;
use App\Models\LogoutRec;
use App\Models\Rider;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserAccount;
use App\Models\UserAuth;
use App\Models\UserAuthRel;
use App\Models\UserIdCard;
use App\Models\UserProfile;
use App\Models\UserRid;
use App\Services\IdCardService;
use App\Services\SkyBlueService;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\Api\UserRequest;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Overtrue\LaravelWeChat\EasyWeChat;
use SimpleSoftwareIO\QrCode\Facades\QrCode;
use App\Services\KefuService;

class UsersController extends Controller
{
    public function store(UserRequest $request, UserService $service)
    {
        $cacheKey = 'verificationCode_' . $request->verification_key;
        $verifyData = Cache::get($cacheKey);

        if (!$verifyData) {
            $this->errorResponse(403, '验证码已失效');
        }

        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(500, '验证码错误');
        }

        $user = User::query()->where('phone', $verifyData['phone'])->first();

        if ($user) {
            $this->errorResponse(500, '您已注册，直接去登录吧～');
        } else {
            $user = $service->registerUser($verifyData['phone'], $request->password, $request->invite_code, $this->platform);
        }

        // 清除验证码缓存
        Cache::forget($cacheKey);

        return $this->success("注册成功", $user);
    }

    public function miniProgramStore(Request $request, UserService $service)
    {
        $app = EasyWeChat::miniApp();
        $api = $app->getClient();

        $response = $api->postJson('/wxa/business/getuserphonenumber?access_token=' . $app->getAccessToken()->getToken(), [
            'code' => $request->code,
        ]);
        $resp = json_decode($response->getContent(), true);
        if (isset($resp['errcode']) && $resp['errcode'] != 0) {
            throw new AuthenticationException($resp['errmsg']);
        }

        $phone = $resp['phone_info']['phoneNumber'];
        $user = User::query()->where('phone', $phone)->first();
        if (!$user) {
            $user = $service->registerUser($phone, '', $request->invite_code, $this->platform);
        }
        $ttl = 365 * 24 * 60;
        $token = auth('api')->setTTL($ttl)->login($user);

        return $this->respondWithToken($token);
    }

    public function sync(Request $request, UserService $service)
    {
        $user = $service->registerUser($request->phone, $request->password, '', $request->platform, 2);

        return $this->success("注册成功", $user);
    }

    public function me(Request $request)
    {
        return $this->success('success', $this->formatUserInfo($request->user()));
    }

    public function update(UserRequest $request)
    {
        $user = $request->user();

        $attributes = $request->only(['name', 'id_card', 'nickname', 'birthday']);

        $attributes = array_filter($attributes);

        if ($request->avatar_image_id) {
            $image = Image::find($request->avatar_image_id);
            if ($image) $attributes['avatar'] = $image->path;
        }

        if ($request->code) {
            $app = EasyWeChat::miniApp();
            $util = $app->getUtils();
            $session = $util->codeToSession($request->code);
            if (isset($session['openid'])) {
                $attributes['mp_openid'] = $session['openid'];
            }
        }

        if ($request->sex) {
            $user->profile->sex = $request->sex;
            $user->profile->save();
        }
        $user->update($attributes);

        $user = User::query()->find($user->id);

        return $this->success('success', $this->formatUserInfo($user));
    }

    public function idCardAuth(UserAuthRequest $request, SkyBlueService $skyService, IdCardService $idCardService)
    {
        Log::info('idCardAuth', $request->all());
        $user = $request->user();
        if ($user->id_card && $user->certified) {
            return $this->success('您已认证，无需再次认证', []);
        }
        $idCardNo = $request->id_card_no;
        $name = $request->name;
        if (!$idCardService->isChinaIDCard($idCardNo)) {
            $this->errorResponse(500, '请核实您的姓名与身份证号是否正确');
        }
        $sex = $idCardService->getChinaIDCardSex($idCardNo);
        $sx = $idCardService->getChinaIDCardSX($idCardNo);
        $xz = $idCardService->getChinaIDCardXZ($idCardNo);
        $birthday = $idCardService->getBirthday($idCardNo);

        // 调用创蓝接口验证实名
        if (config('app.open_idcard_auth')) {
            $validResult = $skyService->idcardAuth($name, $idCardNo);
            $vaildFlag = false;
            // 成功
            switch ($validResult['result']) {
                case '01':
                    // 认证成功
                    $vaildFlag = true;
                    break;
                case '02':
                    // 认证不一致
                    $this->errorResponse(500, '请核实您的姓名与身份证号是否正确');
                    break;
                case '03':
                    // 认证不确定，暂且认为成功
                    $vaildFlag = true;
                    break;
                case '04':
                    // 认证失败
                    $this->errorResponse(500, '身份认证失败');
                    break;
            }
        } else {
            $vaildFlag = true;
        }

        if ($vaildFlag) {
            $user->id_card = $idCardNo;
            $user->name = $name;
            $user->certified = true;
            $user->birthday = $birthday;
            $user->save();

            UserProfile::updateOrCreate([
                'user_id' => $user->id,
                'sex' => $sex == '男' ? 1 : 2,
                'constellation' => $xz,
                'shu_xiang' => $sx
            ]);
        }

        return $this->success('恭喜您身份认证成功', []);


    }

    public function validChangePhone(ValidChangePhoneRequest $request)
    {
        $cacheKey = 'verificationCode_' . $request->verification_key;
        $verifyData = Cache::get($cacheKey);

        if (!$verifyData) {
            $this->errorResponse(403, '验证码已失效');
        }

        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(500, '验证码错误');
        }

        if ($request->user()) {
            $user = $request->user();
            if ($user->phone != $verifyData['phone']) {
                $this->errorResponse(500, '手机号和登录用户不一致');
            }
        }

        return $this->success('success', []);

    }

    public function changePhone(ValidChangePhoneRequest $request)
    {
        $cacheKey = 'verificationCode_' . $request->verification_key;
        $verifyData = Cache::get($cacheKey);

        if (!$verifyData) {
            $this->errorResponse(403, '验证码已失效');
        }

        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(500, '验证码错误');
        }
        $phone = $verifyData['phone'];
        // 判断手机号是否注册
        $existUser = User::query()->where('phone', $phone)->first();
        if ($existUser) {
            $this->errorResponse(500, '新手机号已注册，无法更换。请换个手机号换绑吧');
        }

        $user = $request->user();
        $user->phone = $phone;
        $user->save();

        return $this->success('success', $this->formatUserInfo($user));
    }

    public function logout(LogoutRequest $request)
    {
        $cacheKey = 'verificationCode_' . $request->verification_key;
        $verifyData = Cache::get($cacheKey);

        if (!$verifyData) {
            $this->errorResponse(403, '验证码已失效');
        }

        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(500, '验证码错误');
        }

        $user = $request->user();

        $rec = new LogoutRec();
        $rec->user_id = $user->id;
        $rec->reason = $request->reason;
        $rec->platform = $request->platform;
        $rec->save();

        if ($rec->platform == SystemConfig::PlatformSQ) {
            $user->log_out_sq = 1;
        }
        if ($rec->platform == SystemConfig::PlatformPT) {
            $user->log_out_pt = 1;
        }
        $user->save();

        return $this->success('注销成功', $this->formatUserInfo($user));
    }

    public function idCardStore(IdCardRequest $request)
    {
        $input = $request->only(['id_card_image', 'id_card_image_over', 'name', 'nation', 'address', 'id_card',
            'issuing_authority', 'birth', 'issuing_date', 'expiry_date', 'sex']);

        $input['birth'] = Carbon::parse($input['birth']);
        $input['issuing_date'] = Carbon::parse($input['issuing_date']);
        $input['expiry_date'] = Carbon::parse($input['expiry_date']);
        $userIdCard = UserIdCard::query()->where('user_id', $request->user()->id)->first();
        if (!$userIdCard) {
            $input['user_id'] = $request->user()->id;
            UserIdCard::create($input);
        } else {
            $userIdCard->update($input);
        }

        $rider = Rider::query()->where('user_id', $request->user()->id)->first();
        if ($rider && $rider->id_card_certified == 0) {
            $rider->id_card_certified = 1;
            $rider->save();
        }

        return $this->success('success', []);
    }

    public function faceCompare(Request $request, SkyBlueService $service)
    {
        $image = $request->image;
        $userIdCard = UserIdCard::query()->where('user_id', $request->user()->id)->first();
        if (!$userIdCard) $this->errorResponse(500, "请先完成身份证认证");
        $res = $service->faceCompare($userIdCard->id_card, $userIdCard->name, $image);
        if ($res && isset($res['photoScore']) && $res['photoScore'] > 70) {
            $user = $request->user();
            $user->face_certified = 1;
            $user->save();
            return $this->success('success', ['result' => true, 'score' => $res['photoScore']]);
        } else {
            return $this->success('success', ['result' => false, 'score' => $res['photoScore']]);
        }
    }

    public function share(Request $request)
    {
        $user = $request->user();

        $shareUrl = route('m.share', ['code' => $user->recommend_code]);

        $qrCode = QrCode::size(300)->errorCorrection('H')->format('png')->generate($shareUrl);

        $fileName = 'share/qrcode/' . Carbon::now()->format('Ym') . '/' . md5($qrCode) . '.png';

        Storage::put($fileName, $qrCode);

        $result = [
            'share_url' => $shareUrl,
            'share_qrcode' => img_url($fileName),
            'title' => '雨骑士拉新用户注册，前往下载即可获得更多福利。',
            'image' => 'http://storage.fengqishi.com.cn/resource/yuqishi_logo_108.png',
            'desc' => '立刻前往>>',
        ];

        return $this->success('success', $result);

    }

    public function RidUpload(Request $request)
    {
        $user = $request->user();
        $rid = $request->input('rid', '');
        if ($rid) {
            $userRid = UserRid::query()->where('rid', $rid)->first();
            if (!$userRid) {
                UserRid::create([
                    'user_id' => $user->id,
                    'rid' => $rid
                ]);
            } else {
                if ($userRid->user_id != $user->id) {
                    $userRid->user_id = $user->id;
                    $userRid->save();
                } else {
                    $userRid->updated_at = Carbon::now();
                    $userRid->save();
                }
            }
        }

        return $this->success('success', []);

    }

    private function formatUserInfo($user)
    {
        $prev = app()->environment() == 'production' ? 1 : 2;
        // 获取客服链接
        $kfUrl = '';
        try {
            $kefuService = app(KefuService::class);
            $kfUrl = $kefuService->getCachedChatUrl($user->recommend_code, $user->nickname ?: '访客');
        } catch (\Exception $e) {
            Log::error('获取用户客服链接异常', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }

        return [
            'id' => $user->id,
            'phone' => $user->phone,
            'nickname' => $user->nickname,
            'level' => $user->level,
            'avatar' => $user->avatar,
            'certified' => $user->certified,
            'amount' => $user->userAccount ? fentoyuan($user->userAccount->amount) : 0,
            'sex' => $user->profile->sex,
            "is_white_list" => $user->channel == User::CHANNEL_WHITE_LIST,
            'birthday' => $user->birthday,
            'name' => $user->name,
            'id_card' => substr_replace($user->id_card, '***************', 3, 13),
            'recommend_code' => $user->recommend_code,
            'easemob_user_id' => $user->easemob_user_id,
            'easemob_uuid' => $user->easemob_uuid,
            'open_id' => $user->mp_openid,
            'alias' => $prev . '_' . $user->recommend_code,
            'kf_url' => $kfUrl,
        ];
    }

    protected function respondWithToken($token)
    {
        return $this->success('登录成功', [
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_in' => Auth::guard('api')->factory()->getTTL() * 60
        ]);
    }
}
