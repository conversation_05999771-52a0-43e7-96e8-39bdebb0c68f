<?php

namespace App\Http\Middleware;

use App\Models\OpenApiAccount;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;


class VerifyRiderPermission
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();

        $rider = $user->rider;

        if (!$rider) {
            return $next($request);
        } else {
            if ($rider->black) {
                return $this->errorResponse(401, '骑手被禁用');
            }
        }

        return $next($request);
    }

    public function errorResponse($statusCode, $message = null, $code = 0)
    {
        throw new HttpException($statusCode, $message, null, [], $code);
    }
}
