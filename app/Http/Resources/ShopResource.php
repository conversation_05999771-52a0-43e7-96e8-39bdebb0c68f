<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class ShopResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {

        return [
            'id' => $this->id,
            "community_id" => $this->community_id,
            'name' => $this->name,
            'promotion_info' => $this->promotion_info,
            'province' => $this->regions[0] ?? $this->province,
            'city' => $this->regions[1] ?? $this->city,
            'district' => $this->regions[2] ?? $this->district,
            'address_detail' => $this->address_detail,
            'lng' => $this->lng,
            'lat' => $this->lat,
            'logo' => $this->logo ? img_url($this->logo) : "",
            'cover' => $this->cover ? img_url($this->cover) : "",
            'tel' => $this->tel,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'distance' => $this->distance ?? null,
        ];
    }
}
