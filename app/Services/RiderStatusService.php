<?php

namespace App\Services;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class RiderStatusService
{
    const RIDER_HEARTBEAT_KEY = 'rider_heartbeat:';
    const HEARTBEAT_TIMEOUT = 120; // 心跳超时时间(秒)
    
    /**
     * 更新骑手心跳
     */
    public function updateHeartbeat($riderId)
    {
        $key = self::RIDER_HEARTBEAT_KEY . $riderId;
        Redis::setex($key, self::HEARTBEAT_TIMEOUT + 30, Carbon::now()->timestamp);
        
        // 检查并更新骑手状态
        $this->checkAndUpdateStatus($riderId);
    }
    
    /**
     * 检查并更新骑手状态
     */
    public function checkAndUpdateStatus($riderId)
    {
        $rider = Rider::query()->find($riderId);
        if (!$rider) {
            return;
        }
        
        $key = self::RIDER_HEARTBEAT_KEY . $riderId;
        $lastHeartbeat = Redis::get($key);
        $now = Carbon::now()->timestamp;
        
        // 检查是否有进行中的订单
        $hasActiveOrder = O2oErrandOrder::query()
            ->where("rider_id", $riderId)
            ->where("order_status", ">", O2oErrandOrder::STATUS_PAID)
            ->where("order_status", "<", O2oErrandOrder::STATUS_FINISH)
            ->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT)
            ->exists();
            
        // 判断状态
        $newStatus = $rider->status;
        if (!$lastHeartbeat || ($now - $lastHeartbeat) > self::HEARTBEAT_TIMEOUT) {
            if (!$hasActiveOrder) {
                $newStatus = Rider::STATUS_DOWN;
            } else {
                // 有进行中订单但心跳超时,记录日志
                Log::warning("骑手{$riderId}心跳超时但有进行中订单");
            }
        }
        
        // 更新状态
        if ($newStatus != $rider->status) {
            $rider->status = $newStatus;
            $rider->save();
            
            // 记录状态变更日志
            Log::info("骑手{$riderId}状态变更", [
                'old_status' => $rider->status,
                'new_status' => $newStatus,
                'has_active_order' => $hasActiveOrder,
                'last_heartbeat' => $lastHeartbeat
            ]);
        }
        
        return $newStatus;
    }
    
    /**
     * 手动更新骑手状态
     */
    public function updateStatus($riderId, $status)
    {
        $rider = Rider::query()->find($riderId);
        if (!$rider) {
            return false;
        }
        
        // 上线时的检查
        if (in_array($status, [Rider::STATUS_UP, Rider::STATUS_REST])) {
            if (!$rider->verified) {
                throw new \Exception("请完成骑手认证");
            }
            
            // 其他资质检查...
        }
        
        $rider->status = $status;
        $rider->save();
        
        // 如果是下线状态,清除心跳记录
        if ($status == Rider::STATUS_DOWN) {
            Redis::del(self::RIDER_HEARTBEAT_KEY . $riderId);
        }
        
        return true;
    }
    
    /**
     * 批量检查骑手状态
     */
    public function batchCheckStatus($riderIds = null)
    {
        $query = Rider::query()->where("status", Rider::STATUS_UP);
        if ($riderIds) {
            $query->whereIn('id', $riderIds);
        }
        
        $query->chunk(100, function ($riders) {
            foreach ($riders as $rider) {
                $this->checkAndUpdateStatus($rider->id);
            }
        });
    }
} 