<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Coupon extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const TYPE_MANJIAN = 1;
    const TYPE_ZHEKOU = 2;
    const TypeMap = [
        self::TYPE_MANJIAN => '满减券',
        self::TYPE_ZHEKOU => '折扣券',
    ];

    const VALIDITY_TYPE_DAY = 1;
    const VALIDITY_TYPE_RANGE = 2;
    const ValidityTypeMap = [
        self::VALIDITY_TYPE_DAY => '领取后x天有效',
        self::VALIDITY_TYPE_RANGE => '时间范围'
    ];

    protected $casts = [
        'validity' => 'json',
    ];

    protected $fillable = [
        'title', 'type', 'discount_price', "start_price", 'validity', 'rules', 'stock', 'sales', 'status', "sort"
    ];

    public function user_coupons(){
        return $this->hasMany(UserCoupon::class, "coupon_id");
    }
}
