/* Box sizing rules */
*,
*::before,
*::after {
    box-sizing: border-box;
}

/* Remove default margin */
body,
h1,
h2,
h3,
h4,
p,
figure,
blockquote,
dl,
dd {
    margin: 0;
}

/* Remove list styles on ul, ol elements with a list role, which suggests default styling will be removed */
ul[role='list'],
ol[role='list'] {
    list-style: none;
}

/* Set core root defaults */
html:focus-within {
    scroll-behavior: smooth;
}

/* Set core body defaults */
body {
    text-rendering: optimizeSpeed;
    line-height: 1.4;
}

/* A elements that don't have a class get default styles */
a:not([class]) {
    text-decoration-skip-ink: auto;
    color: inherit;
}

/* Make images easier to work with */
img,
picture {
    max-width: 100%;
    display: block;
}

/* Inherit fonts for inputs and buttons */
input,
button,
textarea,
select {
    font: inherit;
}

:root {
    font-size: 49.9999875PX;
    font-size: 13.33333vw;
}
body {
    font-size: 14px;
    max-width: 640PX;
    margin: 0 auto !important;
}
@media (max-width: 320px) {
    :root {
        font-size: 42.666656PX;
    }
}
@media (min-width: 640px) {
    :root {
        font-size: 85.333312PX;
    }
}
