<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderEvaluateRec extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $casts = ['is_anonymous' => 'boolean', 'is_satisfied' => 'boolean', 'reason' => 'json', 'imgs' => 'json'];

    protected $fillable = [
        'order_id', 'user_id', 'rider_id', 'is_anonymous', 'is_satisfied', 'reason', 'remark', 'imgs', "pszs_star", 'cdgf_star', 'ybzj_star', 'hpwh_star', 'lmrq_star'
    ];

    public function order(){
        return $this->belongsTo(O2oErrandOrder::class, "order_id");
    }

    public function user(){
        return $this->belongsTo(User::class, "user_id");
    }

    public function rider(){
        return $this->belongsTo(Rider::class, "rider_id");
    }
}
