<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShopSpuCat extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = ['shop_id', 'name', 'cover', 'pid', 'sort', 'status'];

    public function shop(){
        return $this->belongsTo(Shop::class, "shop_id");
    }

    public function spus(){
        return $this->hasMany(ShopSpu::class, "cat_id");
    }

    public function parent(){
        return $this->belongsTo(ShopSpuCat::class, "pid");
    }

    public function children(){
        return $this->hasMany(ShopSpuCat::class, "pid");
    }
}
