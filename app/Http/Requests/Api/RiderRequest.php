<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class RiderRequest extends FormRequest
{
    public function rules()
    {
        $rules = match (request()->method) {
            'POST' => [
                'verification_key' => 'required|string',
                'verification_code' => 'required|string',
            ],
            default => [],
        };


        return $rules;
    }
}
