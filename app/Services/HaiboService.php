<?php

namespace App\Services;

use App\Models\Merchant;
use App\Models\MerchantToken;
use App\Models\O2oErrandOrder;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;

class HaiboService
{
    protected string $appKey;
    protected string $appSecret;
    protected string $host;

    public function __construct()
    {
        // 海博平台的配置信息 - 这些需要根据实际的海博平台配置进行调整
        $this->appKey = "haibo_app_key";
        $this->appSecret = "haibo_app_secret";
        
        if (app()->environment() == 'production') {
            $this->host = "https://api-open.hiboos.com";
        } else {
            $this->host = "https://api-open-test.hiboos.com";
        }
    }

    /**
     * 创建或修改配送商门店
     *
     * @param array $data 门店数据
     * @return array 处理结果
     * @throws \Exception
     */
    public function createOrUpdateStore(array $data): array
    {
        Log::channel('haibo')->info('海博创建/修改配送商门店请求', $data);

        try {
            // 验证必要参数
            $this->validateStoreData($data);

            // 开始数据库事务
            DB::beginTransaction();

            // 检查是否已存在该门店
            $existingMerchant = $this->findExistingMerchant($data);

            if ($existingMerchant) {
                // 更新现有门店
                $result = $this->updateExistingStore($existingMerchant, $data);
            } else {
                // 创建新门店
                $result = $this->createNewStore($data);
            }

            DB::commit();

            Log::channel('haibo')->info('海博门店操作成功', [
                'operation' => $existingMerchant ? 'update' : 'create',
                'merchant_id' => $result['merchant_id'],
                'user_id' => $result['user_id']
            ]);

            return [
                'success' => true,
                'message' => $existingMerchant ? '门店更新成功' : '门店创建成功',
                'data' => $result
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            
            Log::channel('haibo')->error('海博门店操作失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'data' => $data
            ]);

            throw $e;
        }
    }

    /**
     * 验证门店数据
     *
     * @param array $data
     * @throws \Exception
     */
    private function validateStoreData(array $data): void
    {
        $requiredFields = [
            'shop_name',    // 门店名称
            'phone',        // 联系电话
            'address',      // 详细地址
            'province',     // 省份
            'city',         // 城市
            'district',     // 区县
            'city_code',    // 城市编码
        ];

        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new \Exception("缺少必要参数: {$field}");
            }
        }

        // 验证手机号格式
        if (!preg_match('/^1[3-9]\d{9}$/', $data['phone'])) {
            throw new \Exception("手机号格式不正确");
        }
    }

    /**
     * 查找已存在的商家
     *
     * @param array $data
     * @return Merchant|null
     */
    private function findExistingMerchant(array $data): ?Merchant
    {
        // 根据海博的shop_id查找
        if (!empty($data['shop_id'])) {
            $token = MerchantToken::where('platform', 'haibo')
                ->where('shop_id', $data['shop_id'])
                ->first();
            
            if ($token && $token->merchant_id) {
                return Merchant::find($token->merchant_id);
            }
        }

        // 根据手机号查找
        return Merchant::where('phone', $data['phone'])->first();
    }

    /**
     * 创建新门店
     *
     * @param array $data
     * @return array
     * @throws \Exception
     */
    private function createNewStore(array $data): array
    {
        // 检查该手机号是否已有用户账号
        $user = User::where('phone', $data['phone'])->first();
        
        // 如果没有用户账号，则创建一个
        if (!$user) {
            $userService = app(UserService::class);
            $user = $userService->registerUser(
                $data['phone'],
                $data['password'] ?? Hash::make('123456'), // 默认密码
                '', // 空邀请码
                \App\Models\SystemConfig::PlatformPT // 平台标识为跑腿平台
            );
            
            Log::channel('haibo')->info("为海博商家创建关联用户账号成功", [
                'user_id' => $user->id,
                'phone' => $data['phone']
            ]);
        }

        // 创建商家账户
        $merchant = Merchant::create([
            'shop_name' => $data['shop_name'],
            'phone' => $data['phone'],
            'password' => Hash::make($data['password'] ?? '123456'),
            'province' => $data['province'],
            'city' => $data['city'],
            'district' => $data['district'],
            'city_code' => $data['city_code'],
            'address' => $data['address'],
            'contact_name' => $data['contact_name'] ?? '',
            'email' => $data['email'] ?? '',
            'merchant_type' => $data['merchant_type'] ?? 'haibo',
            'status' => 1, // 海博商家默认审核通过
            'balance' => 0,
            'user_id' => $user->id,
        ]);

        // 创建海博平台token记录
        if (!empty($data['shop_id'])) {
            MerchantToken::create([
                'merchant_id' => $merchant->id,
                'user_id' => $user->id,
                'platform' => 'haibo',
                'access_token' => $data['access_token'] ?? '',
                'refresh_token' => $data['refresh_token'] ?? '',
                'shop_id' => $data['shop_id'],
                'extra_data' => [
                    'created_by' => 'haibo_api',
                    'created_at' => now()->toDateTimeString()
                ]
            ]);
        }

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'shop_id' => $data['shop_id'] ?? '',
            'operation' => 'create'
        ];
    }

    /**
     * 更新现有门店
     *
     * @param Merchant $merchant
     * @param array $data
     * @return array
     */
    private function updateExistingStore(Merchant $merchant, array $data): array
    {
        // 更新商家信息
        $merchant->update([
            'shop_name' => $data['shop_name'],
            'province' => $data['province'],
            'city' => $data['city'],
            'district' => $data['district'],
            'city_code' => $data['city_code'],
            'address' => $data['address'],
            'contact_name' => $data['contact_name'] ?? $merchant->contact_name,
            'email' => $data['email'] ?? $merchant->email,
        ]);

        // 更新或创建token记录
        if (!empty($data['shop_id'])) {
            MerchantToken::updateOrCreate(
                [
                    'merchant_id' => $merchant->id,
                    'platform' => 'haibo'
                ],
                [
                    'user_id' => $merchant->user_id,
                    'access_token' => $data['access_token'] ?? '',
                    'refresh_token' => $data['refresh_token'] ?? '',
                    'shop_id' => $data['shop_id'],
                    'extra_data' => [
                        'updated_by' => 'haibo_api',
                        'updated_at' => now()->toDateTimeString()
                    ]
                ]
            );
        }

        return [
            'merchant_id' => $merchant->id,
            'user_id' => $merchant->user_id,
            'shop_id' => $data['shop_id'] ?? '',
            'operation' => 'update'
        ];
    }

    /**
     * 通过海博的shop_id获取对应的商家信息和用户ID
     *
     * @param string $shopId 海博平台的商铺ID
     * @return array|null 包含merchant_id和user_id的数组，未找到时返回null
     */
    public function getMerchantInfoByShopId(string $shopId): ?array
    {
        $token = MerchantToken::where('platform', 'haibo')
            ->where('shop_id', $shopId)
            ->first();

        if (!$token) {
            Log::channel('haibo')->warning('未找到shop_id对应的商家记录', ['shop_id' => $shopId]);
            return null;
        }

        $merchantId = $token->merchant_id;
        $userId = $token->user_id;

        // 如果token记录中没有user_id，尝试从商家表获取
        if (!$userId && $merchantId) {
            $merchant = Merchant::find($merchantId);
            $userId = $merchant ? $merchant->user_id : null;
        }

        $result = [
            'merchant_id' => $merchantId,
            'user_id' => $userId,
            'token' => $token
        ];

        Log::channel('haibo')->info('获取商家信息成功', [
            'shop_id' => $shopId,
            'merchant_id' => $merchantId,
            'user_id' => $userId
        ]);

        return $result;
    }
}
