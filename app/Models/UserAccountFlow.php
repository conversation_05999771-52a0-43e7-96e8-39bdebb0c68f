<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAccountFlow extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const TYPE_IN = 'in';
    const TYPE_OUT = 'out';

    const BUSINESS_TYPE_PAYMENT = 1001;
    const BUSINESS_TYPE_REFUND = 1002;
    const BUSINESS_TYPE_RECHARGE_ALIPAY = 1003;
    const BUSINESS_TYPE_RECHARGE_WX = 1004;
    const BUSINESS_TYPE_RECHARGE_YSF = 1005;

    const BUSINESS_TYPE_MAP = [
        self::BUSINESS_TYPE_PAYMENT => '余额支付',
        self::BUSINESS_TYPE_REFUND => '余额退款',
        self::BUSINESS_TYPE_RECHARGE_ALIPAY => '支付宝充值',
        self::BUSINESS_TYPE_RECHARGE_WX => '微信充值',
        self::BUSINESS_TYPE_RECHARGE_YSF => '云闪付充值'
    ];

    protected $fillable = [
        'user_id',
        'user_account_id',
        'chg_amount',
        'before_amount',
        'after_amount',
        'type',
        'business_type',
        'no',
        'remark'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
