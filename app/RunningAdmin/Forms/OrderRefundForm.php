<?php

namespace App\RunningAdmin\Forms;

use App\Services\O2oErrandOrderService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class OrderRefundForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $orderNo = $this->payload['order_no'] ?? "";
        if (empty($orderNo)) return $this->response()->error("请选择订单");
        $reason = $input['reason'] ?? "";
        if (empty($reason)) return $this->response()->error("请填写退款原因");
        try {
            $order = app(O2oErrandOrderService::class)->refundOrder($orderNo, $reason, null, true);
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
        return $this->response()->success('退款中...')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->textarea("reason", "退款原因")->rows(3);
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'reason' => "",
        ];
    }
}
