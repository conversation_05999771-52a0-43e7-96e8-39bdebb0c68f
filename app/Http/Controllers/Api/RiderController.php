<?php

namespace App\Http\Controllers\Api;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderLevelSetting;
use App\Models\RiderProfile;
use App\Models\RiderRight;
use App\Models\UserIdCard;
use App\Services\CommonService;
use App\Services\RiderStatusService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;

class RiderController extends Controller
{

    public function me(Request $request)
    {
        $user = $request->user();

        $rider = $user->rider;

        if (!$rider) {
            $this->errorResponse(500, '骑手信息未审核');
        }

        $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 1)->first();
        $count = O2oErrandOrder::query()->where('rider_id', $rider->id)
            ->whereIn('order_status', [O2oErrandOrder::STATUS_FINISH,O2oErrandOrder::STATUS_PAID, O2oErrandOrder::STATUS_DELIVERY, O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT])
            ->count();
        $idcard = UserIdCard::query()->where('user_id', $user->id)->first();

        $card = RiderLevelSetting::query()->where('level', $rider->level)->first();

        $rights = RiderRight::query()->where('level', $rider->level)->get();
        $rightList = [];
        foreach ($rights as $right) {
            $rightList[] = [
                'name' => $right->name,
                'remark' => $right->remark,
                'icon' => img_url($right->icon),
            ];
        }

        $riderInfo = [
            'amount' => fentoyuan($account->amount),
            'order_count' => $count,
            'avatar' => $user->avatar,
            'card_no' => $idcard ? substr_replace($idcard->id_card, '***************', 3, 13) : '',
            'phone' => $rider->phone,
            'name' => $rider->name,
            'status' => $rider->status,
            'level' => $rider->level,
            'level_name' => $card ? $card->name : '白银骑士',
            'level_background' => $card ? img_url($card->card_background) : '',
            'level_start_time' => Carbon::parse($rider->start_time)->format('Y/m/d'),
            'level_end_time' => Carbon::parse($rider->end_time)->format('Y/m/d'),
            'level_score' => $rider->score,
            'level_max' => $card ? $card->max_score : 0,
            'level_remain' => $card->max_score - $rider->score,
            'rights' => $rightList,
            'alipay_account' => $user->alipay_account,
            'verified' => $rider->face_certified,
            'health_status' => $rider->health_status,
            'transport_status' => $rider->transport_status,
            'easemob_rider_id' => $user->easemob_rider_id,
            'register_time' => $user->created_at->format('Y-m-d H:i:s'),
        ];

        return $this->success('success', $riderInfo);
    }

    public function profile(Request $request)
    {
        $user = $request->user();

        $profile = RiderProfile::query()->where('user_id', $user->id)->first();

        $result = [
            'user_id' => $profile->user_id,
            'transport_type' => $profile->transport_type,
            'edu_background' => $profile->edu_background,
            'address' => $profile->address,
            'job' => $profile->job,
            'marriage' => $profile->marriage,
            'children_count' => $profile->children_count,
            'emergency_contact' => $profile->emergency_contact,
            'emergency_mobile' => $profile->emergency_mobile,
        ];

        return $this->success('success', $result);

    }

    public function updateProfile(Request $request)
    {
        $user = $request->user();

        $input = $request->only('transport_type', 'edu_background', 'address', 'job', 'children_count', 'emergency_contact', 'emergency_mobile');
        $input = array_filter($input, function ($value) {
            return !is_null($value);
        });

        $profile = RiderProfile::query()->where('user_id', $user->id)->first();

        $profile->update($input);

        $result = [
            'user_id' => $profile->user_id,
            'transport_type' => $profile->transport_type,
            'edu_background' => $profile->edu_background,
            'address' => $profile->address,
            'job' => $profile->job,
            'marriage' => $profile->marriage,
            'children_count' => $profile->children_count,
            'emergency_contact' => $profile->emergency_contact,
            'emergency_mobile' => $profile->emergency_mobile,
        ];

        return $this->success('success', $result);
    }

    public function updateStatus(Request $request, RiderStatusService $riderStatusService)
    {
        $rider = Rider::query()->where("user_id", $request->user()->id)->first();
        if(!$rider) return $this->success("success", null);

        // 使用RiderStatusService更新状态
        $riderStatusService->updateStatus($rider->id, $request->post("status"));

        return $this->success("success", null);
    }
}
