<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SystemConfig;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class SystemConfigController extends AdminController
{

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SystemConfig(), function (Grid $grid) {
            $grid->model()->where('platform', \App\Models\SystemConfig::PlatformSQ)->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->disableEditButton();
            $grid->disableCreateButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();

            $grid->column("id");
            $grid->column("name");
            $grid->column("remark")->width(200);
            $grid->column("value");

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('name')->width(2);
                $filter->like('remark')->width(2);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        Admin::style(
            <<<CSS
    div.has-many-table-value span.select2 {
       width: 120px!important;
    }
    div.has-many-table-value input.field_reserve_start {
       width: 100px!important;
    }
    div.has-many-table-value input.field_reserve_end {
       width: 100px!important;
    }
    div.has-many-table-value input.field_sending_start {
       width: 100px!important;
    }
    div.has-many-table-value input.field_sending_end {
       width: 100px!important;
    }
CSS
        );
        return Form::make(new SystemConfig(), function (Form $form) {
            $form->display('id');
            $form->hidden('platform');
            $form->hidden('last_update_user');
            $form->select('name')->options(\App\Models\SystemConfig::ParamRemarkMapForSq)->required();
            switch ($form->model()->name) {
                case \App\Models\SystemConfig::PARAM_CANCEL_ORDER_REASON:
                case \App\Models\SystemConfig::PARAM_REFUND_ORDER_REASON:
                    $form->hidden("is_json")->default(1);
                    $form->list('value')->saving(function ($v) {
                        return json_encode($v, JSON_UNESCAPED_UNICODE);
                    });
                    break;
                case \App\Models\SystemConfig::PARAM_SENDING_TIME:
                    $form->hidden("is_json")->default(1);
                    $form->table('value', function ($table) {
                        $table->select('meal', "一日三餐")->options(["早餐" => '早餐', "中餐" => '中餐', "晚餐" => '晚餐']);
                        $table->time("reserve_start", "预约(开始)")->format('HH:mm');
                        $table->time("reserve_end", "预约(结束)")->format('HH:mm');
                        $table->time("sending_start", "配送(开始)")->format('HH:mm');
                        $table->time("sending_end", "配送(结束)")->format('HH:mm');
                    })->saving(function ($v) {
                        return json_encode($v, JSON_UNESCAPED_UNICODE);
                    });
                    break;
                default:
                    $form->hidden("is_json")->default(0);
                    $form->textarea("value")->rows(3);
            }
            $form->hidden("remark");
            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function (Form $form) {
            $form->remark = \App\Models\SystemConfig::ParamRemarkMapForSq[$form->name];
            $form->platform = \App\Models\SystemConfig::PlatformSQ;
            $form->last_update_user = Admin::user()->username;
        });
    }
}
