<?php


namespace App\RunningAdmin\RowActions;

use App\Services\UserService;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;

class RiderApplyAgree extends RowAction
{
    /**
     * 标题
     *
     * @return string
     */
    public function title(): string
    {
        return '通过';
    }

    /**
     * 设置确认弹窗信息，如果返回空值，则不会弹出弹窗
     *
     * 允许返回字符串或数组类型
     *
     * @return array|string|void
     */
    public function confirm()
    {
        return [];
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return \Dcat\Admin\Actions\Response
     */
    public function handle(Request $request)
    {
        // 获取当前行ID
        $id = $this->getKey();
        $service = app(UserService::class);

        try {
            $service->upToRider($id);
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage())->refresh();
        }


        return $this->response()->success("审核成功")->refresh();

    }


    /**
     * 设置要POST到接口的数据
     *
     * @return array
     */
    public function parameters()
    {
        return [
        ];
    }

}
