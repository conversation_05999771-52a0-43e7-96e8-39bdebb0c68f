<?php

use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Config;


function fentoyuan($money)
{
    // 分转元
    return (string)floatval(sprintf("%.2f", $money / 100));
}

function yuantofen($price)
{
    // 元转分
    return intval(bcmul($price, 100));
}

function litoyuan($money)
{
    // 厘转元
    return sprintf("%.2f", $money / 1000);
}

function yuantoli($price)
{
    // 元转厘
    return intval(bcmul($price, 1000));
}

if (!function_exists('img_url')) {
    function img_url($imgpath, $width = 0)
    {
        if (!$imgpath) {
            return $imgpath;
        }
        if (Str::startsWith($imgpath, '//')) {
            return $imgpath;
        }
        if (Str::startsWith($imgpath, 'http://')) {
            return $imgpath;
        }
        if (Str::startsWith($imgpath, 'https://')) {
            return $imgpath;
        }

        if (Config::get('app.img_url_prefix')) {
            $img = Config::get('app.img_url_prefix') . '/' . trim($imgpath, '/');
        } else {
            $img = Config::get('app.url') . '/' . trim($imgpath, '/');
        }
        if ($width) {
            $img .= '?imageView2/2/w/' . $width;
        }

        return $img;
    }
}

function img_url_array($imgs, $width = 0)
{
    if (empty($imgs)) return [];
    foreach ($imgs as $k => $v) {
        $imgs[$k] = $v ? img_url($v, $width) : "";
    }
    return $imgs;
}

function base64url_encode($data)
{
    return rtrim(strtr(base64_encode($data), '+/', '-_'), '=');
}

function base64url_decode($data)
{
    return base64_decode(str_pad(strtr($data, '-_', '+/'), strlen($data) % 4, '=', STR_PAD_RIGHT));
}

function ngrok_url($routeName, $parameters = [])
{
    // 开发环境，并且配置了 NGROK_URL
    if (app()->environment('local') && $url = config('app.ngrok_url')) {
        // route() 函数第三个参数代表是否绝对路径
        return $url . route($routeName, $parameters, false);
    }

    return route($routeName, $parameters);
}


function fileLocalize($filePath, $fileExt = "")
{
    $arr = pathinfo($filePath);
    if (empty($fileExt)) {
        $fileExt = $arr['extension'];
    }
    $fileName = $arr['filename'] . "." . $fileExt;
    if (!file_exists(storage_path('app/' . $fileName))) {
        if (!Storage::disk('local')->put($fileName, file_get_contents($filePath))) {
            throw new \Exception('文件本地化失败');
        }
    }
    return storage_path('app/' . $fileName);
}

function formatDistance($distance)
{
    if (!$distance) return '';

    if ($distance > 1) {
        return round($distance, 2) . 'km';
    } else {
        return intval($distance * 1000) . 'm';
    }
}

function getdistance($lng1, $lat1, $lng2, $lat2)
{
    // 将角度转为狐度
    $radLat1 = deg2rad($lat1); //deg2rad()函数将角度转换为弧度
    $radLat2 = deg2rad($lat2);
    $radLng1 = deg2rad($lng1);
    $radLng2 = deg2rad($lng2);
    $a = $radLat1 - $radLat2;
    $b = $radLng1 - $radLng2;
    $s = 2 * asin(sqrt(pow(sin($a / 2), 2) + cos($radLat1) * cos($radLat2) * pow(sin($b / 2), 2))) * 6378.137 * 1000;
    return $s / 1000;
}


function is_point_in_polygon($point, $pts)
{
    $N = count($pts);
    $boundOrVertex = true; //如果点位于多边形的顶点或边上，也算做点在多边形内，直接返回true
    $intersectCount = 0;//cross points count of x
    $precision = 2e-10; //浮点类型计算时候与0比较时候的容差
    $p1 = 0;//neighbour bound vertices
    $p2 = 0;
    $p = $point; //测试点

    $p1 = $pts[0];//left vertex
    for ($i = 1; $i <= $N; ++$i) {//check all rays
        // dump($p1);
        if ($p['lng'] == $p1['lng'] && $p['lat'] == $p1['lat']) {
            return $boundOrVertex;//p is an vertex
        }

        $p2 = $pts[$i % $N];//right vertex
        if ($p['lat'] < min($p1['lat'], $p2['lat']) || $p['lat'] > max($p1['lat'], $p2['lat'])) {//ray is outside of our interests
            $p1 = $p2;
            continue;//next ray left point
        }

        if ($p['lat'] > min($p1['lat'], $p2['lat']) && $p['lat'] < max($p1['lat'], $p2['lat'])) {//ray is crossing over by the algorithm (common part of)
            if ($p['lng'] <= max($p1['lng'], $p2['lng'])) {//x is before of ray
                if ($p1['lat'] == $p2['lat'] && $p['lng'] >= min($p1['lng'], $p2['lng'])) {//overlies on a horizontal ray
                    return $boundOrVertex;
                }

                if ($p1['lng'] == $p2['lng']) {//ray is vertical
                    if ($p1['lng'] == $p['lng']) {//overlies on a vertical ray
                        return $boundOrVertex;
                    } else {//before ray
                        ++$intersectCount;
                    }
                } else {//cross point on the left side
                    $xinters = ($p['lat'] - $p1['lat']) * ($p2['lng'] - $p1['lng']) / ($p2['lat'] - $p1['lat']) + $p1['lng'];//cross point of lng
                    if (abs($p['lng'] - $xinters) < $precision) {//overlies on a ray
                        return $boundOrVertex;
                    }

                    if ($p['lng'] < $xinters) {//before ray
                        ++$intersectCount;
                    }
                }
            }
        } else {//special case when ray is crossing through the vertex
            if ($p['lat'] == $p2['lat'] && $p['lng'] <= $p2['lng']) {//p crossing over p2
                $p3 = $pts[($i + 1) % $N]; //next vertex
                if ($p['lat'] >= min($p1['lat'], $p3['lat']) && $p['lat'] <= max($p1['lat'], $p3['lat'])) { //p.lat lies between p1.lat & p3.lat
                    ++$intersectCount;
                } else {
                    $intersectCount += 2;
                }
            }
        }
        $p1 = $p2;//next ray left point
    }

    if ($intersectCount % 2 == 0) {//偶数在多边形外
        return false;
    } else { //奇数在多边形内
        return true;
    }
}

function common_admin_color($status)
{
    $colors = [
        0 => 'info',
        1 => 'success',
        2 => 'danger',
        3 => 'primary',
    ];

    return $colors[$status] ?? 'primary';
}

function common_admin_label($text, $status)
{
    $adminColor = app(\Dcat\Admin\Color::class);
    $color = common_admin_color($status);
    $spanColor = $adminColor->get($color);
    return '<span class="label" style="background:' . $spanColor . '">' . $text . '</span>';
}


function create_uuid($prefix = "")
{    //可以指定前缀
    $str = md5(uniqid(mt_rand(), true));
    $uuid = substr($str, 0, 8) . '-';
    $uuid .= substr($str, 8, 4) . '-';
    $uuid .= substr($str, 12, 4) . '-';
    $uuid .= substr($str, 16, 4) . '-';
    $uuid .= substr($str, 20, 12);
    return $prefix . $uuid;
}

function rad($d)
{
    return $d * PI() / 180.0;
}


function getAngle($lon1, $lat1, $lon2, $lat2, $mode = 1)
{
    $lat1 = rad($lat1);
    $lat2 = rad($lat2);
    $lon1 = rad($lon1);
    $lon2 = rad($lon2);
    $angle = sin($lat1) * sin($lat2) + cos($lat1)
        * cos($lat2) * cos($lon2 - $lon1);
    $angle = sqrt(1 - $angle * $angle);
    $angle = cos($lat2) * sin($lon2 - $lon1) / $angle;
    $angle = asin($angle) * 180 / PI();


    if (is_nan($angle)) {
        if ($lon1 < $lon2) {
            $angle = 90.0;
        } else {
            $angle = 270.0;
        }
        return $angle;
    }
    if (($lat1 < $lat2) && ($lon1 <= $lon2)) {
        //一象限
        $angle = $lon1 == $lon2 ? 0 : $angle;

    } elseif (($lat1 >= $lat2) && ($lon1 < $lon2)) {
        //二象限
        $angle = 180 - $angle;
    } elseif (($lat1 > $lat2) && ($lon1 >= $lon2)) {
        //三象限
        $angle = abs($angle) + 180;
    } else {
        //四象限
        $angle = $lat1 == $lat2 ? $angle : 360 - abs($angle);
    }
    if ($mode == 2) {
        return toDirStr($angle);
    }
    return $angle;
}

function toDirStr($num)
{
    $num = round($num, 2);
    $N = '北';
    $E = '东';
    $S = '南';
    $W = '西';
    $dir = '';

    if ($num == 0 || $num == 360) {
        $dir = '正' . $N;
    } else if ($num < 90 && $num > 0) {
        if ($num < 45) {
            $dir = $N . '偏' . $E . $num . ' °';
        } else if ($num == 45) {
            $dir = $E . $N . $num . ' °';
        } else if ($num > 45) {
            $dir = $E . '偏' . $N . (90 - $num) . ' °';
        }
    } else if ($num == 90) {
        $dir = '正' . $E;
    } else if ($num < 180 && $num > 90) {

        if ($num < 135) {
            $dir = $E . '偏' . $S . ($num - 90) + ' °';
        } else if ($num == 135) {
            $dir = $E . $S . ($num - 90) . ' °';
        } else if ($num > 135) {
            $dir = $S . '偏' . $E . (180 - $num) . ' °';
        }
    } else if ($num == 180) {
        $dir = '正' . $S;
    } else if ($num < 270 && $num > 180) {

        if ($num < 225) {
            $dir = $S . '偏' . $W . ($num - 180) + ' °';
        } else if ($num == 225) {
            $dir = $W . $S . ($num - 180) . ' °';
        } else if ($num > 225) {
            $dir = $W . '偏' . $S . (270 - $num) . ' °';
        }
    } else if ($num == 270) {
        $dir = '正' . $W;
    } else if ($num < 360 && $num > 270) {
        if ($num < 315) {
            $dir = $W . '偏' . $N . ($num - 270) . ' °';
        } else if ($num == 315) {
            $dir = $W . $N . ($num - 270) . ' °';
        } else if ($num > 315) {
            $dir = $S . '偏' . $W . (360 - $num) . ' °';
        }
    }

    return $dir;
}

function get_device_type()
{
    //全部变成小写字母
    $agent = strtolower(request()->userAgent());
    $type = 'other';
    //分别进行判断
    if (strpos($agent, 'iphone') || strpos($agent, 'ipad')) {
        $type = 'ios';
    }

    if (strpos($agent, 'android')) {
        $type = 'android';
    }
    return $type;
}

function getVersion()
{
    return request()->header('version');
}

function gtVersion($version)
{
    $result = version_compare(getVersion(), $version);
    return $result > 0;

}

function ltVersion($version)
{
    $result = version_compare(getVersion(), $version);
    return $result == -1;
}

### WGS84转GCJ02

function wgs84togcj02($lng, $lat)
{
    $lat = +$lat;
    $lng = +$lng;
    // Krasovsky 1940
    //
    // a = 6378245.0, 1/f = 298.3
    // b = a * (1 - f)
    // ee = (a^2 - b^2) / a^2;
    $a = 6378245.0;
    $ee = 0.00669342162296594323;
    $dLat = transformLat($lng - 105.0, $lat - 35.0);
    $dLng = transformLng($lng - 105.0, $lat - 35.0);
    $radLat = $lat / 180.0 * pi();
    $magic = sin($radLat);
    $magic = 1 - $ee * $magic * $magic;
    $sqrtMagic = sqrt($magic);
    $dLat = ($dLat * 180.0) / (($a * (1 - $ee)) / ($magic * $sqrtMagic) * pi());
    $dLng = ($dLng * 180.0) / ($a / $sqrtMagic * cos($radLat) * pi());
    $mgLat = $lat + $dLat;
    $mgLng = $lng + $dLng;
    return array('lng' => $mgLng, 'lat' => $mgLat);
}

### GCJ02 转换为 WGS84
function gcj02towgs84($lng, $lat)
{
    $lat = +$lat;
    $lng = +$lng;
    // Krasovsky 1940
    //
    // a = 6378245.0, 1/f = 298.3
    // b = a * (1 - f)
    // ee = (a^2 - b^2) / a^2;
    $a = 6378245.0;
    $ee = 0.00669342162296594323;
    $dLat = transformLat($lng - 105.0, $lat - 35.0);
    $dLng = transformLng($lng - 105.0, $lat - 35.0);
    $radLat = $lat / 180.0 * pi();
    $magic = sin($radLat);
    $magic = 1 - $ee * $magic * $magic;
    $sqrtMagic = sqrt($magic);
    $dLat = ($dLat * 180.0) / (($a * (1 - $ee)) / ($magic * $sqrtMagic) * pi());
    $dLng = ($dLng * 180.0) / ($a / $sqrtMagic * cos($radLat) * pi());
    $mgLat = $lat + $dLat;
    $mgLng = $lng + $dLng;
    return array('lng' => $lng * 2 - $mgLng, 'lat' => $lat * 2 - $mgLat);
}

function transformlat($lng, $lat)
{
    $lat = +$lat;
    $lng = +$lng;
    $ret = -100.0 + 2.0 * $lng + 3.0 * $lat + 0.2 * $lat * $lat + 0.1 * $lng * $lat + 0.2 * sqrt(abs($lng));
    $ret += (20.0 * sin(6.0 * $lng * pi()) + 20.0 * sin(2.0 * $lng * pi())) * 2.0 / 3.0;
    $ret += (20.0 * sin($lat * pi()) + 40.0 * sin($lat / 3.0 * pi())) * 2.0 / 3.0;
    $ret += (160.0 * sin($lat / 12.0 * pi()) + 320 * sin($lat * pi() / 30.0)) * 2.0 / 3.0;
    return $ret;
}

function transformLng($lng, $lat)
{
    $lat = +$lat;
    $lng = +$lng;
    $ret = 300.0 + $lng + 2.0 * $lat + 0.1 * $lng * $lng + 0.1 * $lng * $lat + 0.1 * sqrt(abs($lng));
    $ret += (20.0 * sin(6.0 * $lng * pi()) + 20.0 * sin(2.0 * $lng * pi())) * 2.0 / 3.0;
    $ret += (20.0 * sin($lng * pi()) + 40.0 * sin($lng / 3.0 * pi())) * 2.0 / 3.0;
    $ret += (150.0 * sin($lng / 12.0 * pi()) + 300.0 * sin($lng / 30.0 * pi())) * 2.0 / 3.0;
    return $ret;
}
