<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAccount extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'amount'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public function add($amount, $businessType, $orderNo, $remark = '')
    {
        $beforeAmt = $this->amount;

        $this->increment('amount', $amount);

        $flow = [
            'user_id' => $this->user_id,
            'user_account_id' => $this->id,
            'chg_amount' => $amount,
            'before_amount' => $beforeAmt,
            'after_amount' => $this->amount,
            'type' => UserAccountFlow::TYPE_IN,
            'business_type' => $businessType,
            'no' => $orderNo,
            'remark' => $remark
        ];
        UserAccountFlow::create($flow);
    }

    public function decrease($amount, $businessType, $orderNo, $remark = '')
    {
        $beforeAmt = $this->amount;

        $this->newQuery()->where('id', $this->id)->decrement('amount', $amount);

        $flow = [
            'user_id' => $this->user_id,
            'user_account_id' => $this->id,
            'chg_amount' => $amount,
            'before_amount' => $beforeAmt,
            'after_amount' => $this->amount,
            'type' => UserAccountFlow::TYPE_OUT,
            'business_type' => $businessType,
            'no' => $orderNo,
            'remark' => $remark
        ];
        UserAccountFlow::create($flow);
    }

}
