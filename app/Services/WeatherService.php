<?php

namespace App\Services;

use App\Services\Amap\GaodeService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class WeatherService
{
    protected GaodeService $gaodeService;
    
    /**
     * 恶劣天气关键词定义
     * 基于高德天气API返回的weather字段和天气现象表
     * 只包含会显著影响骑手配送安全和效率的天气条件
     */
    const BAD_WEATHER_KEYWORDS = [
        // 强降雨类（影响视线和路面安全）
        '大雨', '暴雨', '大暴雨', '特大暴雨', 
        '强阵雨', '强雷阵雨', '极端降雨',
        '中雨-大雨', '大雨-暴雨', '暴雨-大暴雨', '大暴雨-特大暴雨',
        
        // 雷电天气（安全风险）
        '雷阵雨', '雷阵雨并伴有冰雹', '强雷阵雨',
        
        // 中到大雪类（影响行驶安全）
        '中雪', '大雪', '暴雪',
        '小雪-中雪', '中雪-大雪', '大雪-暴雪',
        
        // 强风天气（影响行驶稳定性）
        '强风', '劲风', '疾风', '大风', '烈风', 
        '风暴', '狂爆风', '飓风', '热带风暴',
        
        // 沙尘天气（影响视线）
        '沙尘暴', '强沙尘暴',
        
        // 极端天气
        '龙卷风',
        
        // 严重影响视线的雾
        '浓雾', '强浓雾', '大雾', '特强浓雾',
        
        // 冰冻天气（路面危险）
        '冻雨', '雨夹雪', '降雨夹雪',
        
        // 严重空气污染
        '重度霾', '严重霾'
    ];
    
    /**
     * 轻微天气条件（不影响配送）
     * 这些天气虽然不是完全晴朗，但不会显著影响配送安全和效率
     */
    const MILD_WEATHER_KEYWORDS = [
        // 晴朗天气
        '晴', '少云', '晴间多云', '多云', '阴',
        
        // 轻微降水
        '小雨', '毛毛雨', '细雨', '雨', '小雪', '阵雪', '中雨',
        
        // 轻微风力
        '平静', '微风', '和风', '清风', '有风',
        
        // 轻微雾霾
        '雾', '轻雾', '霾', '中度霾', '扬沙', '浮尘',
        
        // 温度相关（单纯温度不算恶劣天气）
        '热', '冷',
        
        // 未知天气默认为正常
        '未知'
    ];
    
    // 缓存时间（分钟）
    const CACHE_MINUTES = 30;

    public function __construct()
    {
        $this->gaodeService = new GaodeService();
    }

    /**
     * 根据经纬度判断是否为恶劣天气
     * @param float $lng 经度
     * @param float $lat 纬度
     * @return bool
     */
    public function isBadWeatherByLocation(float $lng, float $lat): bool
    {
        try {
            // 1. 先通过逆地理编码获取adcode
            $adcode = $this->getAdcodeByLocation($lng, $lat);
            if (!$adcode) {
                Log::warning('无法获取地区编码', ['lng' => $lng, 'lat' => $lat]);
                return false; // 获取不到地区编码时默认为正常天气
            }
            
            // 2. 根据adcode获取天气信息
            $weatherInfo = $this->getWeatherByAdcode($adcode);
            if (!$weatherInfo) {
                Log::warning('无法获取天气信息', ['adcode' => $adcode]);
                return false; // 获取不到天气信息时默认为正常天气
            }
            
            // 3. 判断是否为恶劣天气
            $isBadWeather = $this->isWeatherBad($weatherInfo);
            
            Log::info('天气判断结果', [
                'lng' => $lng,
                'lat' => $lat,
                'adcode' => $adcode,
                'weather' => $weatherInfo['weather'] ?? 'unknown',
                'is_bad_weather' => $isBadWeather
            ]);
            
            return $isBadWeather;
            
        } catch (\Exception $e) {
            Log::error('天气判断异常', [
                'lng' => $lng,
                'lat' => $lat,
                'error' => $e->getMessage()
            ]);
            return false; // 异常时默认为正常天气
        }
    }

    /**
     * 根据地址判断是否为恶劣天气
     * @param string $address 地址
     * @param string $city 城市（可选）
     * @return bool
     */
    public function isBadWeatherByAddress(string $address, string $city = ''): bool
    {
        try {
            // 1. 通过地理编码获取经纬度
            $geoResult = $this->gaodeService->geoQuery($address, $city);
            if (!isset($geoResult['geocodes'][0])) {
                Log::warning('地理编码失败', ['address' => $address, 'city' => $city]);
                return false;
            }
            
            $geocode = $geoResult['geocodes'][0];
            $adcode = $geocode['adcode'];
            
            // 2. 根据adcode获取天气信息
            $weatherInfo = $this->getWeatherByAdcode($adcode);
            if (!$weatherInfo) {
                Log::warning('无法获取天气信息', ['adcode' => $adcode]);
                return false;
            }
            
            // 3. 判断是否为恶劣天气
            $isBadWeather = $this->isWeatherBad($weatherInfo);
            
            Log::info('天气判断结果', [
                'address' => $address,
                'city' => $city,
                'adcode' => $adcode,
                'weather' => $weatherInfo['weather'] ?? 'unknown',
                'is_bad_weather' => $isBadWeather
            ]);
            
            return $isBadWeather;
            
        } catch (\Exception $e) {
            Log::error('天气判断异常', [
                'address' => $address,
                'city' => $city,
                'error' => $e->getMessage()
            ]);
            return false; // 异常时默认为正常天气
        }
    }

    /**
     * 根据经纬度获取地区编码
     * @param float $lng 经度
     * @param float $lat 纬度
     * @return string|null
     */
    private function getAdcodeByLocation(float $lng, float $lat): ?string
    {
        $cacheKey = "adcode_{$lng}_{$lat}";
        
        return Cache::remember($cacheKey, self::CACHE_MINUTES, function () use ($lng, $lat) {
            try {
                $result = $this->gaodeService->reGeoQuery((string)$lng, (string)$lat);
                return $result['regeocode']['addressComponent']['adcode'] ?? null;
            } catch (\Exception $e) {
                Log::error('获取地区编码失败', [
                    'lng' => $lng,
                    'lat' => $lat,
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        });
    }

    /**
     * 根据地区编码获取天气信息
     * @param string $adcode 地区编码
     * @return array|null
     */
    private function getWeatherByAdcode(string $adcode): ?array
    {
        $cacheKey = "weather_{$adcode}";
        
        return Cache::remember($cacheKey, self::CACHE_MINUTES, function () use ($adcode) {
            try {
                $result = $this->gaodeService->weatherQuery($adcode);
                
                // 高德天气API返回的是数组，取第一个实时天气
                $lives = $result['lives'] ?? [];
                if (empty($lives)) {
                    return null;
                }
                
                return $lives[0]; // 返回实时天气信息
            } catch (\Exception $e) {
                Log::error('获取天气信息失败', [
                    'adcode' => $adcode,
                    'error' => $e->getMessage()
                ]);
                return null;
            }
        });
    }

    /**
     * 判断天气信息是否为恶劣天气
     * @param array $weatherInfo 天气信息
     * @return bool
     */
    private function isWeatherBad(array $weatherInfo): bool
    {
        $weather = $weatherInfo['weather'] ?? '';
        
        // 1. 优先进行精确匹配（完全匹配天气描述）
        if (in_array($weather, self::BAD_WEATHER_KEYWORDS)) {
            Log::info('天气精确匹配为恶劣天气', ['weather' => $weather]);
            return true;
        }
        
        // 2. 检查是否在轻微天气列表中（明确为正常天气）
        if (in_array($weather, self::MILD_WEATHER_KEYWORDS)) {
            Log::info('天气匹配为轻微天气', ['weather' => $weather]);
            return false;
        }
        
        // 3. 模糊匹配恶劣天气关键词
        foreach (self::BAD_WEATHER_KEYWORDS as $keyword) {
            if (str_contains($weather, $keyword)) {
                Log::info('天气模糊匹配为恶劣天气', ['weather' => $weather, 'keyword' => $keyword]);
                return true;
            }
        }
        
        // 4. 检查风力等级（7级以上为大风）
        $windpower = $weatherInfo['windpower'] ?? '';
        if ($windpower && preg_match('/(\d+)级/', $windpower, $matches)) {
            $windLevel = intval($matches[1]);
            if ($windLevel >= 7) {
                Log::info('风力等级判定为恶劣天气', ['windpower' => $windpower, 'level' => $windLevel]);
                return true;
            }
        }
        
        // 5. 检查极端温度（高温40度以上或低温-15度以下）
        $temperature = $weatherInfo['temperature'] ?? '';
        if ($temperature && is_numeric($temperature)) {
            $temp = floatval($temperature);
            if ($temp >= 40 || $temp <= -15) {
                Log::info('极端温度判定为恶劣天气', ['temperature' => $temp]);
                return true;
            }
        }
        
        // 6. 未匹配到任何条件，默认为正常天气
        Log::info('天气未匹配任何恶劣条件，判定为正常天气', ['weather' => $weather]);
        return false;
    }

    /**
     * 获取天气详细信息（用于调试和展示）
     * @param float $lng 经度
     * @param float $lat 纬度
     * @return array
     */
    public function getWeatherDetails(float $lng, float $lat): array
    {
        try {
            $adcode = $this->getAdcodeByLocation($lng, $lat);
            if (!$adcode) {
                return ['error' => '无法获取地区编码'];
            }
            
            $weatherInfo = $this->getWeatherByAdcode($adcode);
            if (!$weatherInfo) {
                return ['error' => '无法获取天气信息'];
            }
            
            $isBadWeather = $this->isWeatherBad($weatherInfo);
            
            return [
                'adcode' => $adcode,
                'weather' => $weatherInfo['weather'] ?? 'unknown',
                'temperature' => $weatherInfo['temperature'] ?? 'unknown',
                'windpower' => $weatherInfo['windpower'] ?? 'unknown',
                'humidity' => $weatherInfo['humidity'] ?? 'unknown',
                'reporttime' => $weatherInfo['reporttime'] ?? 'unknown',
                'is_bad_weather' => $isBadWeather,
                'matched_keywords' => $this->getMatchedKeywords($weatherInfo['weather'] ?? '')
            ];
            
        } catch (\Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * 获取匹配的恶劣天气关键词
     * @param string $weather 天气描述
     * @return array
     */
    private function getMatchedKeywords(string $weather): array
    {
        $matched = [];
        foreach (self::BAD_WEATHER_KEYWORDS as $keyword) {
            if (str_contains($weather, $keyword)) {
                $matched[] = $keyword;
            }
        }
        return $matched;
    }

    /**
     * 清除天气缓存
     * @param float|null $lng 经度（可选，不传则清除所有天气缓存）
     * @param float|null $lat 纬度（可选，不传则清除所有天气缓存）
     */
    public function clearWeatherCache(?float $lng = null, ?float $lat = null): void
    {
        if ($lng && $lat) {
            // 清除指定位置的缓存
            $adcode = $this->getAdcodeByLocation($lng, $lat);
            if ($adcode) {
                Cache::forget("weather_{$adcode}");
                Cache::forget("adcode_{$lng}_{$lat}");
            }
        } else {
            // 清除所有天气相关缓存
            $pattern = ['weather_*', 'adcode_*'];
            foreach ($pattern as $p) {
                Cache::forget($p);
            }
        }
    }
} 