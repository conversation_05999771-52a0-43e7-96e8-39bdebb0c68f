<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Yan<PERSON>gda\Pay\Pay;

class TestAlipayConfig extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:alipay-config';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '测试支付宝配置是否正确';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('开始测试支付宝配置...');

        try {
            // 检查配置
            $config = config('pay.alipay.default');
            $this->info('支付宝配置:');
            $this->table(['配置项', '值'], [
                ['app_id', $config['app_id'] ?? 'NULL'],
                ['notify_url', $config['notify_url'] ?? 'NULL'],
                ['mode', $config['mode'] ?? 'NULL'],
            ]);

            // 检查证书文件
            $this->info('检查证书文件...');
            $certFiles = [
                'app_public_cert_path' => $config['app_public_cert_path'] ?? '',
                'alipay_public_cert_path' => $config['alipay_public_cert_path'] ?? '',
                'alipay_root_cert_path' => $config['alipay_root_cert_path'] ?? '',
            ];

            foreach ($certFiles as $name => $path) {
                if (file_exists($path)) {
                    $this->info("✓ {$name}: {$path}");
                } else {
                    $this->error("✗ {$name}: {$path} (文件不存在)");
                }
            }

            // 测试Pay配置
            $this->info('测试Pay SDK配置...');
            Pay::config(config("pay"));
            $this->info('✓ Pay SDK配置成功');

            $this->info('支付宝配置测试完成!');
            return Command::SUCCESS;

        } catch (\Exception $e) {
            $this->error('配置测试失败: ' . $e->getMessage());
            $this->error('文件: ' . $e->getFile());
            $this->error('行号: ' . $e->getLine());
            return Command::FAILURE;
        }
    }
} 