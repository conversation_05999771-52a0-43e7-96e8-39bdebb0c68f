<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Version;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class VersionController extends AdminController
{
    protected $title = '版本管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Version(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('version');
            $grid->column('status')->using(\App\Models\Version::StatusMap)->label();
            $grid->column('system')->using(\App\Models\Version::SystemMap)->label();
            $grid->column('app_type', '应用类型')->using(\App\Models\Version::AppTypeMap)->label();
            $grid->column('current', '当前开启')->switch();
            $grid->column('download_path', '文件');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Version(), function (Show $show) {
            $show->field('id');
            $show->field('version');
            $show->field('status');
            $show->field('system');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Version(), function (Form $form) {
            $form->display('id');
            $form->text('version')->required();
            $form->radio('status')->options(\App\Models\Version::StatusMap)->default(1)->required();
            $form->text('system')->options(\App\Models\Version::SystemMap)->default(1)->required();
            $form->radio('app_type', '应用类型')->options(\App\Models\Version::AppTypeMap)->default(1)->required();
            $form->switch('current', '当前开启');
            $form->url('download_path', '文件');
            $form->textarea('remark', '升级说明');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
