<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class CooperateRequest extends FormRequest
{
    public function rules()
    {
        $rules = match (request()->method) {
            'POST' => [
                'name' => 'required|string',
                'phone' => 'required|string',
            ],
            default => [],
        };
        return $rules;
    }
}
