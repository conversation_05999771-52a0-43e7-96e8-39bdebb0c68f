<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class OrderEvaluateRequest extends FormRequest
{
    public function rules()
    {
        return [
            'order_no' => 'required',
            'is_anonymous' => 'required|boolean',
            'is_satisfied' => 'required|boolean',
            'reason' => 'required_if:is_satisfied,false|array',
            'remark' => 'required_if:is_satisfied,false|string',
            'imgs' => 'array',
            'pszs_star' => 'required_if:is_satisfied,true|integer|min:0|max:5',
            'cdgf_star' => 'required_if:is_satisfied,true|integer|min:0|max:5',
            'ybzj_star' => 'required_if:is_satisfied,true|integer|min:0|max:5',
            'hpwh_star' => 'required_if:is_satisfied,true|integer|min:0|max:5',
            'lmrq_star' => 'required_if:is_satisfied,true|integer|min:0|max:5',
        ];
    }

    public function attributes()
    {
        return [
            'order_no' => "订单号",
            'is_anonymous' => '是否匿名',
            'is_satisfied' => '是否满意',
            'reason' => '原因',
            'remark' => "备注",
            'imgs' => '图片',
            'pszs_star' => '配送准时',
            'cdgf_star' => '穿戴工服',
            'ybzj_star' => '仪表整洁',
            'hpwh_star' => '货品完好',
            'lmrq_star' => '礼貌热情',
        ];
    }
}
