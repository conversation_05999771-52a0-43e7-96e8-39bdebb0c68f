<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Modal\SpuImportModal;
use App\Admin\Extensions\ShopSpuExport;
use App\Admin\Repositories\ShopSpu;
use App\Models\Common;
use App\Models\Shop;
use App\Models\ShopSpuCat;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Config;

class ShopSpuController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ShopSpu(), function (Grid $grid) {
            $grid->model()->with(["shop", "cat"])->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new SpuImportModal());
            });

            $grid->column("id");
            $grid->column('name');
            $grid->column('cover')->image(Config::get('app.img_url_prefix'), 80);
            $grid->column("shop.name", "店铺")->filter();
            $grid->column('price');
            $grid->column('discount_price');
            $grid->column('stock');
            $grid->column('sales');
            $grid->column('is_signboard')->switch();
            $grid->column('sort')->sortable();
            $grid->column('status')->using(Common::StatusMap)->label(Common::StatusLabelMap);
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->export(new ShopSpuExport(request()->all()));

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('name')->width(3);
                $filter->in('shop_id')->multipleSelect(Shop::query()->pluck("name", "id"))->width(2);
                $filter->in('cat_id')->multipleSelect(ShopSpuCat::query()->pluck("name", "id"))->width(2);
                $filter->in('status')->multipleSelect(Common::StatusMap)->width(2);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ShopSpu(), function (Form $form) {
            $form->display('id');
            $form->text('name')->required();
            $form->image('cover')->uniqueName()->required()->autoUpload()->autoSave(false);
            $form->select('shop_id')->options(Shop::query()->pluck("name", "id"))->required();
//            $form->select('cat_id')->options(ShopSpuCat::query()->pluck("name", "id"));
            $form->textarea('description');
            $form->decimal("price")->required();
            $form->decimal("discount_price")->required();
            $form->tags('sell_tags')->help("单个标签输入完成再输入一个空格，回车即可");
            $form->number('stock')->default(0)->attribute('min', 0)->required();
            $form->switch('is_signboard')->default(false);
            $form->select('status')->options(Common::StatusMap)->default(Common::STATUS_INIT)->required();
            $form->number('sort')->default(0)->attribute('min', 0)->required();
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
