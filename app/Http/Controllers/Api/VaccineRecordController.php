<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\VaccineRequest;
use App\Models\VaccineRecord;

class VaccineRecordController extends Controller
{
    public function store(VaccineRequest $request)
    {
        $input = $request->only(['vaccine_times', 'last_vaccine_time', 'check_imgs']);
        $user = $request->user();

        $vaccineRecords = VaccineRecord::query()->where('user_id', $user->id)->first();
        if (!$vaccineRecords) {
            $vaccineRecords = new VaccineRecord();
            $vaccineRecords->user_id = $user->id;
            $vaccineRecords->vaccine_times = $input['vaccine_times'];
            $vaccineRecords->last_vaccine_time = $input['last_vaccine_time'];
            $vaccineRecords->check_imgs = $input['check_imgs'];
            $vaccineRecords->save();
        }

        return $this->success('success', $vaccineRecords);

    }
}
