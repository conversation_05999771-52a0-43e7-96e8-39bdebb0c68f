<?php

namespace App\Listeners;

use App\Events\AfterRegister;
use App\Services\EasemobService;
use Illuminate\Contracts\Queue\ShouldQueue;

class RegisterEasemob implements ShouldQueue
{
    /**
     * 任务将被发送到的队列的名称。
     *
     * @var string|null
     */
    public $queue = 'listeners';

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\AfterRegister $event
     * @return void
     */
    public function handle(AfterRegister $event)
    {
        $user = $event->user;

        if (config('app.app_mode') == 1) {
            // 融合模式
            $easemob = app('easemob_sq')->getUser($user->easemob_user_id);
            if (!$easemob) {
                app('easemob_sq')->authorizationRegistration($user->easemob_user_id);
                $service = new EasemobService('easemob_sq');
                $service->initEUserInfo($user);
            }
            // 添加好友
            app('easemob_sq')->addFriend($user->easemob_user_id, EasemobService::SQ_SYSTEM_USER);

            // 查询是否注册环信
            $easemob = app('easemob_pt')->getUser($user->easemob_user_id);
            if (!$easemob) {
                app('easemob_pt')->authorizationRegistration($user->easemob_user_id);
                $service = new EasemobService('easemob_sq');
                $service->initEUserInfo($user);
            }
            app('easemob_sq')->addFriend($user->easemob_user_id, EasemobService::SQ_SYSTEM_USER);
            $txt = "欢迎使用社区食堂";
            app('easemob_sq')->sendMessageText([$user->easemob_user_id], $target_type = 'users', $message = $txt, $send_user = EasemobService::SQ_SYSTEM_USER, $ext = []);
        } else {
            if ($user->active_shequ) {
                $easemob = app('easemob_sq')->getUser($user->easemob_user_id);
                if (!$easemob) {
                    app('easemob_sq')->authorizationRegistration($user->easemob_user_id);
                    $service = new EasemobService('easemob_sq');
                    $service->initEUserInfo($user);
                }
                // 添加好友
                app('easemob_sq')->addFriend($user->easemob_user_id, EasemobService::SQ_SYSTEM_USER);
                $txt = "欢迎使用社区食堂";
                app('easemob_sq')->sendMessageText([$user->easemob_user_id], $target_type = 'users', $message = $txt, $send_user = EasemobService::SQ_SYSTEM_USER, $ext = []);
            } else {
                $easemob = app('easemob_pt')->getUser($user->easemob_user_id);
                if (!$easemob) {
                    app('easemob_pt')->authorizationRegistration($user->easemob_user_id);
                    $service = new EasemobService('easemob_sq');
                    $service->initEUserInfo($user);
                }
                app('easemob_pt')->addFriend($user->easemob_user_id, EasemobService::PT_SYSTEM_USER);
                $txt = "欢迎使用雨骑士";
                app('easemob_pt')->sendMessageText([$user->easemob_user_id], $target_type = 'users', $message = $txt, $send_user = EasemobService::PT_SYSTEM_USER, $ext = []);
            }
        }
    }
}
