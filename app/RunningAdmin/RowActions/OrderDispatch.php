<?php
namespace App\RunningAdmin\RowActions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use App\RunningAdmin\Forms\OrderDispatchForm;

class OrderDispatch extends RowAction
{
    protected $title = '派单';

    public function render()
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->body(OrderDispatchForm::make()->payload(['id' => $this->getKey()]))
            ->button($this->title);
    }
}
