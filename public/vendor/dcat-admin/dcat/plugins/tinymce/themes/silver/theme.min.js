/**
 * Copyright (c) Tiny Technologies, Inc. All rights reserved.
 * Licensed under the LGPL or a commercial license.
 * For LGPL see License.txt in the project root for license information.
 * For commercial licenses see https://www.tiny.cloud/
 *
 * Version: 5.8.0 (2021-05-06)
 */
!function(){"use strict";var n,t=function(o){return function(t){return e=typeof(n=t),(null===n?"null":"object"==e&&(Array.prototype.isPrototypeOf(n)||n.constructor&&"Array"===n.constructor.name)?"array":"object"==e&&(String.prototype.isPrototypeOf(n)||n.constructor&&"String"===n.constructor.name)?"string":e)===o;var n,e}},e=function(n){return function(t){return typeof t===n}},x=t("string"),w=t("object"),c=t("array"),S=e("boolean"),o=(n=undefined,function(t){return n===t}),i=function(t){return!(null===(n=t)||n===undefined);var n},y=e("function"),at=e("number"),s=function(t,n){if(c(t)){for(var e=0,o=t.length;e<o;++e)if(!n(t[e]))return!1;return!0}return!1},ct=function(){},u=function(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e(o.apply(null,t))}},st=function(t){return function(){return t}},lt=function(t){return t};function k(o){for(var r=[],t=1;t<arguments.length;t++)r[t-1]=arguments[t];return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var e=r.concat(t);return o.apply(null,e)}}var C=function(n){return function(t){return!n(t)}},a=function(t){return function(){throw new Error(t)}},l=st(!1),O=st(!0),r=tinymce.util.Tools.resolve("tinymce.ThemeManager"),ft=function(){return(ft=Object.assign||function(t){for(var n,e=1,o=arguments.length;e<o;e++)for(var r in n=arguments[e])Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r]);return t}).apply(this,arguments)};function _(t,n){var e={};for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&n.indexOf(o)<0&&(e[o]=t[o]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols)for(var r=0,o=Object.getOwnPropertySymbols(t);r<o.length;r++)n.indexOf(o[r])<0&&Object.prototype.propertyIsEnumerable.call(t,o[r])&&(e[o[r]]=t[o[r]]);return e}function T(){for(var t=0,n=0,e=arguments.length;n<e;n++)t+=arguments[n].length;for(var o=Array(t),r=0,n=0;n<e;n++)for(var i=arguments[n],u=0,a=i.length;u<a;u++,r++)o[r]=i[u];return o}var f,d,m,g,p,h=function(){return v},v=(f=function(t){return t.isNone()},{fold:function(t,n){return t()},is:l,isSome:l,isNone:O,getOr:m=function(t){return t},getOrThunk:d=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:st(null),getOrUndefined:st(undefined),or:m,orThunk:d,map:h,each:ct,bind:h,exists:l,forall:O,filter:h,equals:f,equals_:f,toArray:function(){return[]},toString:st("none()")}),b=function(e){var t=st(e),n=function(){return r},o=function(t){return t(e)},r={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:O,isNone:l,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return b(t(e))},each:function(t){t(e)},bind:o,exists:o,forall:o,filter:function(t){return t(e)?r:v},toArray:function(){return[e]},toString:function(){return"some("+e+")"},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(l,function(t){return n(e,t)})}};return r},dt={some:b,none:h,from:function(t){return null===t||t===undefined?v:b(t)}},E=Array.prototype.slice,D=Array.prototype.indexOf,B=Array.prototype.push,A=function(t,n){return D.call(t,n)},M=function(t,n){return-1<A(t,n)},F=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return!0}return!1},I=function(t,n){for(var e=[],o=0;o<t;o++)e.push(n(o));return e},R=function(t,n){for(var e=[],o=0;o<t.length;o+=n){var r=E.call(t,o,o+n);e.push(r)}return e},V=function(t,n){for(var e=t.length,o=new Array(e),r=0;r<e;r++){var i=t[r];o[r]=n(i,r)}return o},mt=function(t,n){for(var e=0,o=t.length;e<o;e++){n(t[e],e)}},P=function(t,n){for(var e=[],o=[],r=0,i=t.length;r<i;r++){var u=t[r];(n(u,r)?e:o).push(u)}return{pass:e,fail:o}},H=function(t,n){for(var e=[],o=0,r=t.length;o<r;o++){var i=t[o];n(i,o)&&e.push(i)}return e},z=function(t,n,e){return function(t,n){for(var e=t.length-1;0<=e;e--){n(t[e],e)}}(t,function(t){e=n(e,t)}),e},N=function(t,n,e){return mt(t,function(t){e=n(e,t)}),e},L=function(t,n){return function(t,n,e){for(var o=0,r=t.length;o<r;o++){var i=t[o];if(n(i,o))return dt.some(i);if(e(i,o))break}return dt.none()}(t,n,l)},j=function(t,n){for(var e=0,o=t.length;e<o;e++){if(n(t[e],e))return dt.some(e)}return dt.none()},gt=function(t){for(var n=[],e=0,o=t.length;e<o;++e){if(!c(t[e]))throw new Error("Arr.flatten item "+e+" was not an array, input: "+t);B.apply(n,t[e])}return n},U=function(t,n){return gt(V(t,n))},W=function(t,n){for(var e=0,o=t.length;e<o;++e){if(!0!==n(t[e],e))return!1}return!0},G=function(t){var n=E.call(t,0);return n.reverse(),n},X=function(t,n){return H(t,function(t){return!M(n,t)})},Y=function(t,n){var e=E.call(t,0);return e.sort(n),e},q=function(t,n){return 0<=n&&n<t.length?dt.some(t[n]):dt.none()},K=function(t){return q(t,0)},J=function(t){return q(t,t.length-1)},$=y(Array.from)?Array.from:function(t){return E.call(t)},Q=function(t,n){for(var e=0;e<t.length;e++){var o=n(t[e],e);if(o.isSome())return o}return dt.none()},Z=function(e){return{is:function(t){return e===t},isValue:O,isError:l,getOr:st(e),getOrThunk:st(e),getOrDie:st(e),or:function(t){return Z(e)},orThunk:function(t){return Z(e)},fold:function(t,n){return n(e)},map:function(t){return Z(t(e))},mapError:function(t){return Z(e)},each:function(t){t(e)},bind:function(t){return t(e)},exists:function(t){return t(e)},forall:function(t){return t(e)},toOptional:function(){return dt.some(e)}}},tt=function(e){return{is:l,isValue:l,isError:O,getOr:lt,getOrThunk:function(t){return t()},getOrDie:function(){return a(String(e))()},or:function(t){return t},orThunk:function(t){return t()},fold:function(t,n){return t(e)},map:function(t){return tt(e)},mapError:function(t){return tt(t(e))},each:ct,bind:function(t){return tt(e)},exists:l,forall:O,toOptional:dt.none}},pt={value:Z,error:tt,fromOption:function(t,n){return t.fold(function(){return tt(n)},Z)}};(p=g=g||{})[p.Error=0]="Error",p[p.Value=1]="Value";var nt,et,ot=function(t,n,e){return t.stype===g.Error?n(t.serror):e(t.svalue)},rt=function(t){return{stype:g.Value,svalue:t}},it=function(t){return{stype:g.Error,serror:t}},ut=function(t){return t.fold(it,rt)},ht=function(t){return ot(t,pt.error,pt.value)},vt=rt,bt=function(t){var n=[],e=[];return mt(t,function(t){ot(t,function(t){return e.push(t)},function(t){return n.push(t)})}),{values:n,errors:e}},yt=it,xt=function(t,n){return t.stype===g.Value?n(t.svalue):t},wt=function(t,n){return t.stype===g.Error?n(t.serror):t},St=function(t,n){return t.stype===g.Value?{stype:g.Value,svalue:n(t.svalue)}:t},kt=function(t,n){return t.stype===g.Error?{stype:g.Error,serror:n(t.serror)}:t},Ct=Object.keys,Ot=Object.hasOwnProperty,_t=function(t,n){for(var e=Ct(t),o=0,r=e.length;o<r;o++){var i=e[o];n(t[i],i)}},Tt=function(t,e){return Et(t,function(t,n){return{k:n,v:e(t,n)}})},Et=function(t,o){var r={};return _t(t,function(t,n){var e=o(t,n);r[e.k]=e.v}),r},Dt=function(t,n){var e,o,r,i,u={};return e=n,i=u,o=function(t,n){i[n]=t},r=ct,_t(t,function(t,n){(e(t,n)?o:r)(t,n)}),u},Bt=function(t,e){var o=[];return _t(t,function(t,n){o.push(e(t,n))}),o},At=function(t,n){for(var e=Ct(t),o=0,r=e.length;o<r;o++){var i=e[o],u=t[i];if(n(u,i,t))return dt.some(u)}return dt.none()},Mt=function(t){return Bt(t,function(t){return t})},Ft=function(t,n){return It(t,n)?dt.from(t[n]):dt.none()},It=function(t,n){return Ot.call(t,n)},Rt=function(t,n){return It(t,n)&&t[n]!==undefined&&null!==t[n]},Vt=function(u){if(!c(u))throw new Error("cases must be an array");if(0===u.length)throw new Error("there must be at least one case");var a=[],e={};return mt(u,function(t,o){var n=Ct(t);if(1!==n.length)throw new Error("one and only one name per case");var r=n[0],i=t[r];if(e[r]!==undefined)throw new Error("duplicate key detected:"+r);if("cata"===r)throw new Error("cannot have a case named cata (sorry)");if(!c(i))throw new Error("case arguments must be an array");a.push(r),e[r]=function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=e.length;if(n!==i.length)throw new Error("Wrong number of arguments to case "+r+". Expected "+i.length+" ("+i+"), got "+n);return{fold:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(t.length!==u.length)throw new Error("Wrong number of arguments to fold. Expected "+u.length+", got "+t.length);return t[o].apply(null,e)},match:function(t){var n=Ct(t);if(a.length!==n.length)throw new Error("Wrong number of arguments to match. Expected: "+a.join(",")+"\nActual: "+n.join(","));if(!W(a,function(t){return M(n,t)}))throw new Error("Not all branches were specified when using match. Specified: "+n.join(", ")+"\nRequired: "+a.join(", "));return t[r].apply(null,e)},log:function(t){console.log(t,{constructors:a,constructor:r,params:e})}}}}),e},Pt=Object.prototype.hasOwnProperty,Ht=function(u){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];if(0===t.length)throw new Error("Can't merge zero objects");for(var e={},o=0;o<t.length;o++){var r=t[o];for(var i in r)Pt.call(r,i)&&(e[i]=u(e[i],r[i]))}return e}},zt=Ht(function(t,n){return w(t)&&w(n)?zt(t,n):n}),Nt=Ht(function(t,n){return n}),Lt=function(e){var o,r=!1;return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return r||(r=!0,o=e.apply(null,t)),o}},jt=Vt([{strict:[]},{defaultedThunk:["fallbackThunk"]},{asOption:[]},{asDefaultedOptionThunk:["fallbackThunk"]},{mergeWithThunk:["baseThunk"]}]),Ut=function(t){return jt.defaultedThunk(st(t))},Wt=jt.strict,Gt=jt.asOption,Xt=jt.defaultedThunk,Yt=(jt.asDefaultedOptionThunk,jt.mergeWithThunk),qt=(Vt([{bothErrors:["error1","error2"]},{firstError:["error1","value2"]},{secondError:["value1","error2"]},{bothValues:["value1","value2"]}]),function(t,n){var e={};return e[t]=n,e}),Kt=function(t,n){return e=n,o={},_t(t,function(t,n){M(e,n)||(o[n]=t)}),o;var e,o},Jt=qt,$t=function(t){return n={},mt(t,function(t){n[t.key]=t.value}),n;var n},Qt=function(t,n){var e,o,r,i,u,a=(e=[],o=[],mt(t,function(t){t.fold(function(t){e.push(t)},function(t){o.push(t)})}),{errors:e,values:o});return 0<a.errors.length?(u=a.errors,pt.error(gt(u))):(i=n,0===(r=a.values).length?pt.value(i):pt.value(zt(i,Nt.apply(undefined,r))))},Zt=function(t){return u(yt,gt)(t)},tn=function(t,n){var e,o,r=bt(t);return 0<r.errors.length?Zt(r.errors):(e=r.values,o=n,0<e.length?vt(zt(o,Nt.apply(undefined,e))):vt(o))},nn=function(t){var n=bt(t);return 0<n.errors.length?Zt(n.errors):vt(n.values)},en=function(t){return w(t)&&100<Ct(t).length?" removed due to size":JSON.stringify(t,null,2)},on=function(t,n){return yt([{path:t,getErrorInfo:n}])},rn=Vt([{field:["key","okey","presence","prop"]},{state:["okey","instantiator"]}]),un=function(e,o,r){return Ft(o,r).fold(function(){return t=r,n=o,on(e,function(){return'Could not find valid *strict* value for "'+t+'" in '+en(n)});var t,n},vt)},an=function(t,n,e){var o=Ft(t,n).fold(function(){return e(t)},lt);return vt(o)},cn=function(a,c,t,s){return t.fold(function(r,e,t,o){var i=function(t){var n=o.extract(a.concat([r]),s,t);return St(n,function(t){return qt(e,s(t))})},u=function(t){return t.fold(function(){var t=qt(e,s(dt.none()));return vt(t)},function(t){var n=o.extract(a.concat([r]),s,t);return St(n,function(t){return qt(e,s(dt.some(t)))})})};return t.fold(function(){return xt(un(a,c,r),i)},function(t){return xt(an(c,r,t),i)},function(){return xt(vt(Ft(c,r)),u)},function(t){return xt((e=t,o=Ft(n=c,r).map(function(t){return!0===t?e(n):t}),vt(o)),u);var n,e,o},function(t){var n=t(c),e=St(an(c,r,st({})),function(t){return zt(n,t)});return xt(e,i)})},function(t,n){var e=n(c);return vt(qt(t,s(e)))})},sn=function(o){return{extract:function(e,t,n){return wt(o(n,t),function(t){return n=t,on(e,function(){return n});var n})},toString:function(){return"val"}}},ln=function(t){var u=fn(t),a=z(t,function(n,t){return t.fold(function(t){return zt(n,Jt(t,!0))},st(n))},{});return{extract:function(t,n,e){var o,r=S(e)?[]:Ct(Dt(e,function(t){return t!==undefined&&null!==t})),i=H(r,function(t){return!Rt(a,t)});return 0===i.length?u.extract(t,n,e):(o=i,on(t,function(){return"There are unsupported fields: ["+o.join(", ")+"] specified"}))},toString:u.toString}},fn=function(a){return{extract:function(t,n,e){return o=t,r=e,i=n,u=V(a,function(t){return cn(o,r,t,i)}),tn(u,{});var o,r,i,u},toString:function(){return"obj{\n"+V(a,function(t){return t.fold(function(t,n,e,o){return t+" -> "+o.toString()},function(t,n){return"state("+t+")"})}).join("\n")+"}"}}},dn=function(r){return{extract:function(e,o,t){var n=V(t,function(t,n){return r.extract(e.concat(["["+n+"]"]),o,t)});return nn(n)},toString:function(){return"array("+r.toString()+")"}}},mn=function(a){return{extract:function(t,n,e){for(var o=[],r=0,i=a;r<i.length;r++){var u=i[r].extract(t,n,e);if(u.stype===g.Value)return u;o.push(u)}return nn(o)},toString:function(){return"oneOf("+V(a,function(t){return t.toString()}).join(", ")+")"}}},gn=function(a,c){return{extract:function(e,o,r){var t,n,i=Ct(r),u=(t=e,n=i,dn(sn(a)).extract(t,lt,n));return xt(u,function(t){var n=V(t,function(t){return rn.field(t,t,Wt(),c)});return fn(n).extract(e,o,r)})},toString:function(){return"setOf("+c.toString()+")"}}},pn=st(sn(vt)),hn=u(dn,fn),vn=rn.state,bn=rn.field,yn=function(e,n,o,r,i){return Ft(r,i).fold(function(){return t=r,n=i,on(e,function(){return'The chosen schema: "'+n+'" did not exist in branches: '+en(t)});var t,n},function(t){return t.extract(e.concat(["branch: "+i]),n,o)})},xn=function(r,i){return{extract:function(n,e,o){return Ft(o,r).fold(function(){return t=r,on(n,function(){return'Choice schema did not contain choice key: "'+t+'"'});var t},function(t){return yn(n,e,o,i,t)})},toString:function(){return"chooseOn("+r+"). Possible values: "+Ct(i)}}},wn=sn(vt),Sn=function(t){return hn(t)},kn=function(o){return{extract:function(t,n,e){return o().extract(t,n,e)},toString:function(){return o().toString()}}},Cn=function(n){return sn(function(t){return n(t).fold(yt,vt)})},On=function(n,t){return gn(function(t){return ut(n(t))},t)},_n=function(t,n,e){return ht((o=t,r=lt,i=e,u=n.extract([o],r,i),kt(u,function(t){return{input:i,errors:t}})));var o,r,i,u},Tn=function(t){return t.fold(function(t){throw new Error(Dn(t))},lt)},En=function(t,n,e){return Tn(_n(t,n,e))},Dn=function(t){return"Errors: \n"+(n=t.errors,e=10<n.length?n.slice(0,10).concat([{path:[],getErrorInfo:function(){return"... (only showing first ten failures)"}}]):n,V(e,function(t){return"Failed path: ("+t.path.join(" > ")+")\n"+t.getErrorInfo()}).join("\n"))+"\n\nInput object: "+en(t.input);var n,e},Bn=xn,An=function(t,n){return xn(t,Tt(n,fn))},Mn=st(wn),Fn=function(e,o){return sn(function(t){var n=typeof t;return e(t)?vt(t):yt("Expected type: "+o+" but got: "+n)})},In=Fn(at,"number"),Rn=Fn(x,"string"),Vn=Fn(S,"boolean"),Pn=Fn(y,"function"),Hn=function(n){var t=function(t,n){for(var e=t.next();!e.done;){if(!n(e.value))return!1;e=t.next()}return!0};if(Object(n)!==n)return!0;switch({}.toString.call(n).slice(8,-1)){case"Boolean":case"Number":case"String":case"Date":case"RegExp":case"Blob":case"FileList":case"ImageData":case"ImageBitmap":case"ArrayBuffer":return!0;case"Array":case"Object":return Object.keys(n).every(function(t){return Hn(n[t])});case"Map":return t(n.keys(),Hn)&&t(n.values(),Hn);case"Set":return t(n.keys(),Hn);default:return!1}},zn=sn(function(t){return Hn(t)?vt(t):yt("Expected value to be acceptable for sending via postMessage")}),Nn=function(n){return Cn(function(t){return M(n,t)?pt.value(t):pt.error('Unsupported value: "'+t+'", choose one of "'+n.join(", ")+'".')})},Ln=function(t){return bn(t,t,Wt(),pn())},jn=function(t,n){return bn(t,t,Wt(),n)},Un=function(t){return jn(t,Rn)},Wn=function(t,n){return bn(t,t,Wt(),Nn(n))},Gn=function(t){return jn(t,Pn)},Xn=function(t,n){return bn(t,t,Wt(),fn(n))},Yn=function(t,n){return bn(t,t,Wt(),hn(n))},qn=function(t,n){return bn(t,t,Wt(),dn(n))},Kn=function(t){return bn(t,t,Gt(),pn())},Jn=function(t,n){return bn(t,t,Gt(),n)},$n=function(t){return Jn(t,In)},Qn=function(t){return Jn(t,Rn)},Zn=function(t){return Jn(t,Pn)},te=function(t,n){return Jn(t,fn(n))},ne=function(t,n){return bn(t,t,Ut(n),pn())},ee=function(t,n,e){return bn(t,t,Ut(n),e)},oe=function(t,n){return ee(t,n,In)},re=function(t,n){return ee(t,n,Rn)},ie=function(t,n,e){return ee(t,n,Nn(e))},ue=function(t,n){return ee(t,n,Vn)},ae=function(t,n){return ee(t,n,Pn)},ce=function(t,n,e){return ee(t,n,dn(e))},se=function(t,n,e){return ee(t,n,fn(e))},le=function(t,n){return vn(t,n)},fe=function(t){var n=t;return{get:function(){return n},set:function(t){n=t}}},de=function(t){if(null===t||t===undefined)throw new Error("Node cannot be null or undefined");return{dom:t}},me={fromHtml:function(t,n){var e=(n||document).createElement("div");if(e.innerHTML=t,!e.hasChildNodes()||1<e.childNodes.length)throw console.error("HTML does not have a single root node",t),new Error("HTML must have a single root node");return de(e.childNodes[0])},fromTag:function(t,n){var e=(n||document).createElement(t);return de(e)},fromText:function(t,n){var e=(n||document).createTextNode(t);return de(e)},fromDom:de,fromPoint:function(t,n,e){return dt.from(t.dom.elementFromPoint(n,e)).map(de)}},ge=function(t,n){var e=function(t,n){for(var e=0;e<t.length;e++){var o=t[e];if(o.test(n))return o}return undefined}(t,n);if(!e)return{major:0,minor:0};var o=function(t){return Number(n.replace(e,"$"+t))};return he(o(1),o(2))},pe=function(){return he(0,0)},he=function(t,n){return{major:t,minor:n}},ve={nu:he,detect:function(t,n){var e=String(n).toLowerCase();return 0===t.length?pe():ge(t,e)},unknown:pe},be=function(t,n){var e=String(n).toLowerCase();return L(t,function(t){return t.search(e)})},ye=function(t,e){return be(t,e).map(function(t){var n=ve.detect(t.versionRegexes,e);return{current:t.name,version:n}})},xe=function(t,e){return be(t,e).map(function(t){var n=ve.detect(t.versionRegexes,e);return{current:t.name,version:n}})},we=function(t,n){return-1!==t.indexOf(n)},Se=function(t,n){return o=n,r=(e=t).length-n.length,""===o||e.length>=o.length&&e.substr(r,r+o.length)===o;var e,o,r},ke=(nt=/^\s+|\s+$/g,function(t){return t.replace(nt,"")}),Ce=/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,Oe=function(n){return function(t){return we(t,n)}},_e=[{name:"Edge",versionRegexes:[/.*?edge\/ ?([0-9]+)\.([0-9]+)$/],search:function(t){return we(t,"edge/")&&we(t,"chrome")&&we(t,"safari")&&we(t,"applewebkit")}},{name:"Chrome",versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/,Ce],search:function(t){return we(t,"chrome")&&!we(t,"chromeframe")}},{name:"IE",versionRegexes:[/.*?msie\ ?([0-9]+)\.([0-9]+).*/,/.*?rv:([0-9]+)\.([0-9]+).*/],search:function(t){return we(t,"msie")||we(t,"trident")}},{name:"Opera",versionRegexes:[Ce,/.*?opera\/([0-9]+)\.([0-9]+).*/],search:Oe("opera")},{name:"Firefox",versionRegexes:[/.*?firefox\/\ ?([0-9]+)\.([0-9]+).*/],search:Oe("firefox")},{name:"Safari",versionRegexes:[Ce,/.*?cpu os ([0-9]+)_([0-9]+).*/],search:function(t){return(we(t,"safari")||we(t,"mobile/"))&&we(t,"applewebkit")}}],Te=[{name:"Windows",search:Oe("win"),versionRegexes:[/.*?windows\ nt\ ?([0-9]+)\.([0-9]+).*/]},{name:"iOS",search:function(t){return we(t,"iphone")||we(t,"ipad")},versionRegexes:[/.*?version\/\ ?([0-9]+)\.([0-9]+).*/,/.*cpu os ([0-9]+)_([0-9]+).*/,/.*cpu iphone os ([0-9]+)_([0-9]+).*/]},{name:"Android",search:Oe("android"),versionRegexes:[/.*?android\ ?([0-9]+)\.([0-9]+).*/]},{name:"OSX",search:Oe("mac os x"),versionRegexes:[/.*?mac\ os\ x\ ?([0-9]+)_([0-9]+).*/]},{name:"Linux",search:Oe("linux"),versionRegexes:[]},{name:"Solaris",search:Oe("sunos"),versionRegexes:[]},{name:"FreeBSD",search:Oe("freebsd"),versionRegexes:[]},{name:"ChromeOS",search:Oe("cros"),versionRegexes:[/.*?chrome\/([0-9]+)\.([0-9]+).*/]}],Ee={browsers:st(_e),oses:st(Te)},De="Firefox",Be=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isEdge:o("Edge"),isChrome:o("Chrome"),isIE:o("IE"),isOpera:o("Opera"),isFirefox:o(De),isSafari:o("Safari")}},Ae={unknown:function(){return Be({current:undefined,version:ve.unknown()})},nu:Be,edge:st("Edge"),chrome:st("Chrome"),ie:st("IE"),opera:st("Opera"),firefox:st(De),safari:st("Safari")},Me="Windows",Fe="Android",Ie="Solaris",Re="FreeBSD",Ve="ChromeOS",Pe=function(t){var n=t.current,e=t.version,o=function(t){return function(){return n===t}};return{current:n,version:e,isWindows:o(Me),isiOS:o("iOS"),isAndroid:o(Fe),isOSX:o("OSX"),isLinux:o("Linux"),isSolaris:o(Ie),isFreeBSD:o(Re),isChromeOS:o(Ve)}},He={unknown:function(){return Pe({current:undefined,version:ve.unknown()})},nu:Pe,windows:st(Me),ios:st("iOS"),android:st(Fe),linux:st("Linux"),osx:st("OSX"),solaris:st(Ie),freebsd:st(Re),chromeos:st(Ve)},ze=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g=Ee.browsers(),p=Ee.oses(),h=ye(g,t).fold(Ae.unknown,Ae.nu),v=xe(p,t).fold(He.unknown,He.nu);return{browser:h,os:v,deviceType:(o=h,r=t,i=n,u=(e=v).isiOS()&&!0===/ipad/i.test(r),a=e.isiOS()&&!u,c=e.isiOS()||e.isAndroid(),s=c||i("(pointer:coarse)"),l=u||!a&&c&&i("(min-device-width:768px)"),f=a||c&&!l,d=o.isSafari()&&e.isiOS()&&!1===/safari/i.test(r),m=!f&&!l&&!d,{isiPad:st(u),isiPhone:st(a),isTablet:st(l),isPhone:st(f),isTouch:st(s),isAndroid:e.isAndroid,isiOS:e.isiOS,isWebView:st(d),isDesktop:st(m)})}},Ne=function(t){return window.matchMedia(t).matches},Le=Lt(function(){return ze(navigator.userAgent,Ne)}),je=function(){return Le()},Ue=function(t,n){var e=t.dom;if(1!==e.nodeType)return!1;var o=e;if(o.matches!==undefined)return o.matches(n);if(o.msMatchesSelector!==undefined)return o.msMatchesSelector(n);if(o.webkitMatchesSelector!==undefined)return o.webkitMatchesSelector(n);if(o.mozMatchesSelector!==undefined)return o.mozMatchesSelector(n);throw new Error("Browser lacks native selectors")},We=function(t){return 1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType||0===t.childElementCount},Ge=function(t,n){return t.dom===n.dom},Xe=function(t,n){return e=t.dom,o=n.dom,r=e,i=o,u=Node.DOCUMENT_POSITION_CONTAINED_BY,0!=(r.compareDocumentPosition(i)&u);var e,o,r,i,u},Ye=function(t,n){return je().browser.isIE()?Xe(t,n):(e=n,o=t.dom,r=e.dom,o!==r&&o.contains(r));var e,o,r},qe=function(t){return y(t)?t:l},Ke=function(t,n,e){for(var o=t.dom,r=qe(e);o.parentNode;){o=o.parentNode;var i=me.fromDom(o),u=n(i);if(u.isSome())return u;if(r(i))break}return dt.none()},Je=function(t,n,e){var o=n(t),r=qe(e);return o.orThunk(function(){return r(t)?dt.none():Ke(t,n,r)})},$e=function(t,n){return Ge(t.element,n.event.target)},Qe=function(t){if(!Rt(t,"can")&&!Rt(t,"abort")&&!Rt(t,"run"))throw new Error("EventHandler defined by: "+JSON.stringify(t,null,2)+" does not have can, abort, or run!");return En("Extracting event.handler",ln([ne("can",O),ne("abort",l),ne("run",ct)]),t)},Ze=function(e){var n,o,r,i,t=(o=function(t){return t.can},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(n,function(t,n){return t&&o(n).apply(undefined,e)},!0)}),u=(r=n=e,i=function(t){return t.abort},function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return N(r,function(t,n){return t||i(n).apply(undefined,e)},!1)});return Qe({can:t,abort:u,run:function(){for(var n=[],t=0;t<arguments.length;t++)n[t]=arguments[t];mt(e,function(t){t.run.apply(undefined,n)})}})},to=st,no=to("touchstart"),eo=to("touchmove"),oo=to("touchend"),ro=to("touchcancel"),io=to("mousedown"),uo=to("mousemove"),ao=to("mouseout"),co=to("mouseup"),so=to("mouseover"),lo=to("focusin"),fo=to("focusout"),mo=to("keydown"),go=to("keyup"),po=to("input"),ho=to("change"),vo=to("click"),bo=to("transitionend"),yo=to("selectstart"),xo=function(t){return st("alloy."+t)},wo={tap:xo("tap")},So=xo("focus"),ko=xo("blur.post"),Co=xo("paste.post"),Oo=xo("receive"),_o=xo("execute"),To=xo("focus.item"),Eo=wo.tap,Do=xo("longpress"),Bo=xo("sandbox.close"),Ao=xo("typeahead.cancel"),Mo=xo("system.init"),Fo=xo("system.touchmove"),Io=xo("system.touchend"),Ro=xo("system.scroll"),Vo=xo("system.resize"),Po=xo("system.attached"),Ho=xo("system.detached"),zo=xo("system.dismissRequested"),No=xo("system.repositionRequested"),Lo=xo("focusmanager.shifted"),jo=xo("slotcontainer.visibility"),Uo=xo("change.tab"),Wo=xo("dismiss.tab"),Go=xo("highlight"),Xo=xo("dehighlight"),Yo=function(t,n){$o(t,t.element,n,{})},qo=function(t,n,e){$o(t,t.element,n,e)},Ko=function(t){Yo(t,_o())},Jo=function(t,n,e){$o(t,n,e,{})},$o=function(t,n,e,o){var r=ft({target:n},o);t.getSystem().triggerEvent(e,n,r)},Qo=function(t,n,e,o){t.getSystem().triggerEvent(e,n,o.event)},Zo=$t,tr=function(t,n){return{key:t,value:Qe({abort:n})}},nr=function(t){return{key:t,value:Qe({run:function(t,n){n.event.prevent()}})}},er=function(t,n){return{key:t,value:Qe({run:n})}},or=function(t,e,o){return{key:t,value:Qe({run:function(t,n){e.apply(undefined,[t,n].concat(o))}})}},rr=function(t){return function(e){return{key:t,value:Qe({run:function(t,n){$e(t,n)&&e(t,n)}})}}},ir=function(t,n,e){var o,r,i=n.partUids[e];return r=i,er(o=t,function(t,n){t.getSystem().getByUid(r).each(function(t){Qo(t,t.element,o,n)})})},ur=function(t,r){return er(t,function(n,t){var e=t.event,o=n.getSystem().getByDom(e.target).fold(function(){return Je(e.target,function(t){return n.getSystem().getByDom(t).toOptional()},l).getOr(n)},function(t){return t});r(n,o,t)})},ar=function(t){return er(t,function(t,n){n.cut()})},cr=function(t,n){return rr(t)(n)},sr=rr(Po()),lr=rr(Ho()),fr=rr(Mo()),dr=(et=_o(),function(t){return er(et,t)}),mr=("undefined"!=typeof window||Function("return this;")(),function(t){return t.dom.nodeName.toLowerCase()}),gr=function(n){return function(t){return t.dom.nodeType===n}},pr=gr(1),hr=gr(3),vr=gr(9),br=gr(11),yr=function(t){return me.fromDom(t.dom.ownerDocument)},xr=function(t){return vr(t)?t:yr(t)},wr=function(t){return me.fromDom(xr(t).dom.documentElement)},Sr=function(t){return me.fromDom(xr(t).dom.defaultView)},kr=function(t){return dt.from(t.dom.parentNode).map(me.fromDom)},Cr=kr,Or=function(t){return dt.from(t.dom.offsetParent).map(me.fromDom)},_r=function(t){return V(t.dom.childNodes,me.fromDom)},Tr=function(t,n){var e=t.dom.childNodes;return dt.from(e[n]).map(me.fromDom)},Er=function(t){return br(t)&&i(t.dom.host)},Dr=y(Element.prototype.attachShadow)&&y(Node.prototype.getRootNode),Br=st(Dr),Ar=Dr?function(t){return me.fromDom(t.dom.getRootNode())}:xr,Mr=function(t){return Er(t)?t:me.fromDom(xr(t).dom.body)},Fr=function(t){var n=Ar(t);return Er(n)?dt.some(n):dt.none()},Ir=function(t){return me.fromDom(t.dom.host)},Rr=function(t){return i(t.dom.shadowRoot)},Vr=function(n,e){kr(n).each(function(t){t.dom.insertBefore(e.dom,n.dom)})},Pr=function(t,n){var e;(e=t,dt.from(e.dom.nextSibling).map(me.fromDom)).fold(function(){kr(t).each(function(t){zr(t,n)})},function(t){Vr(t,n)})},Hr=function(n,e){Tr(n,0).fold(function(){zr(n,e)},function(t){n.dom.insertBefore(e.dom,t.dom)})},zr=function(t,n){t.dom.appendChild(n.dom)},Nr=function(n,t){mt(t,function(t){zr(n,t)})},Lr=function(t){t.dom.textContent="",mt(_r(t),function(t){jr(t)})},jr=function(t){var n=t.dom;null!==n.parentNode&&n.parentNode.removeChild(n)},Ur=function(t){var n,e=_r(t);0<e.length&&(n=t,mt(e,function(t){Vr(n,t)})),jr(t)},Wr=function(t){return t.dom.innerHTML},Gr=function(t,n){var e,o,r=yr(t).dom,i=me.fromDom(r.createDocumentFragment()),u=(e=n,(o=(r||document).createElement("div")).innerHTML=e,_r(me.fromDom(o)));Nr(i,u),Lr(t),zr(t,i)},Xr=function(t,n,e){if(!(x(e)||S(e)||at(e)))throw console.error("Invalid call to Attribute.set. Key ",n,":: Value ",e,":: Element ",t),new Error("Attribute value was not simple");t.setAttribute(n,e+"")},Yr=function(t,n,e){Xr(t.dom,n,e)},qr=function(t,n){var e=t.dom.getAttribute(n);return null===e?undefined:e},Kr=function(t,n){return dt.from(qr(t,n))},Jr=function(t,n){var e=t.dom;return!(!e||!e.hasAttribute)&&e.hasAttribute(n)},$r=function(t,n){t.dom.removeAttribute(n)},Qr=function(t){return n=t,e=!1,me.fromDom(n.dom.cloneNode(e));var n,e},Zr=function(t){if(Er(t))return"#shadow-root";var n,e,o,r=Qr(t);return n=r,e=me.fromTag("div"),o=me.fromDom(n.dom.cloneNode(!0)),zr(e,o),Wr(e)},ti=Zo([{key:So(),value:Qe({can:function(t,n){var e,o,r=n.event,i=r.originator,u=r.target;return o=u,!(Ge(e=i,t.element)&&!Ge(e,o))||(console.warn(So()+" did not get interpreted by the desired target. \nOriginator: "+Zr(i)+"\nTarget: "+Zr(u)+"\nCheck the "+So()+" event handlers"),!1)}})}]),ni=/* */Object.freeze({__proto__:null,events:ti}),ei=0,oi=function(t){var n=(new Date).getTime();return t+"_"+Math.floor(1e9*Math.random())+ ++ei+String(n)},ri=st("alloy-id-"),ii=st("data-alloy-id"),ui=ri(),ai=ii(),ci=function(t,n){Object.defineProperty(t.dom,ai,{value:n,writable:!0})},si=function(t){var n=pr(t)?t.dom[ai]:null;return dt.from(n)},li=oi,fi=lt,di=function(n){var t=function(t){return function(){throw new Error("The component must be in a context to send: "+t+(n?"\n"+Zr(n().element)+" is not in context.":""))}};return{debugInfo:st("fake"),triggerEvent:t("triggerEvent"),triggerFocus:t("triggerFocus"),triggerEscape:t("triggerEscape"),build:t("build"),addToWorld:t("addToWorld"),removeFromWorld:t("removeFromWorld"),addToGui:t("addToGui"),removeFromGui:t("removeFromGui"),getByUid:t("getByUid"),getByDom:t("getByDom"),broadcast:t("broadcast"),broadcastOn:t("broadcastOn"),broadcastEvent:t("broadcastEvent"),isConnected:l}},mi=di(),gi=function(t){return V(t,function(t){return Se(t,"/*")?t.substring(0,t.length-"/*".length):t})},pi=function(t,n){var e=t.toString(),o=e.indexOf(")")+1,r=e.indexOf("("),i=e.substring(r+1,o-1).split(/,\s*/);return t.toFunctionAnnotation=function(){return{name:n,parameters:gi(i)}},t},hi=oi("alloy-premade"),vi=function(t){return Jt(hi,t)},bi=function(o){return t=function(t){for(var n=[],e=1;e<arguments.length;e++)n[e-1]=arguments[e];return o.apply(void 0,T([t.getApis(),t],n))},n=o.toString(),e=n.indexOf(")")+1,r=n.indexOf("("),i=n.substring(r+1,e-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:"OVERRIDE",parameters:gi(i.slice(1))}},t;var t,n,e,r,i},yi={init:function(){return xi({readState:function(){return"No State required"}})}},xi=function(t){return t},wi=function(t,r){var i={};return _t(t,function(t,o){_t(t,function(t,n){var e=Ft(i,n).getOr([]);i[n]=e.concat([r(o,t)])})}),i},Si=function(t){return{classes:t.classes!==undefined?t.classes:[],attributes:t.attributes!==undefined?t.attributes:{},styles:t.styles!==undefined?t.styles:{}}},ki=function(t){return t.cHandler},Ci=function(t,n){return{name:t,handler:n}},Oi=function(t,n,e){var o,r,i=ft(ft({},e),(o=t,r={},mt(n,function(t){r[t.name()]=t.handlers(o)}),r));return wi(i,Ci)},_i=function(t){var n,i=y(n=t)?{can:O,abort:l,run:n}:n;return function(t,n){for(var e=[],o=2;o<arguments.length;o++)e[o-2]=arguments[o];var r=[t,n].concat(e);i.abort.apply(undefined,r)?n.stop():i.can.apply(undefined,r)&&i.run.apply(undefined,r)}},Ti=function(t,n,e){var o,r,i=n[e];return i?function(u,a,t,c){try{var n=Y(t,function(t,n){var e=t[a],o=n[a],r=c.indexOf(e),i=c.indexOf(o);if(-1===r)throw new Error("The ordering for "+u+" does not have an entry for "+e+".\nOrder specified: "+JSON.stringify(c,null,2));if(-1===i)throw new Error("The ordering for "+u+" does not have an entry for "+o+".\nOrder specified: "+JSON.stringify(c,null,2));return r<i?-1:i<r?1:0});return pt.value(n)}catch(e){return pt.error([e])}}("Event: "+e,"name",t,i).map(function(t){var n=V(t,function(t){return t.handler});return Ze(n)}):(o=e,r=t,pt.error(["The event ("+o+') has more than one behaviour that listens to it.\nWhen this occurs, you must specify an event ordering for the behaviours in your spec (e.g. [ "listing", "toggling" ]).\nThe behaviours that can trigger it are: '+JSON.stringify(V(r,function(t){return t.name}),null,2)]))},Ei=function(t,i){var n=Bt(t,function(o,r){return(1===o.length?pt.value(o[0].handler):Ti(o,i,r)).map(function(t){var n=_i(t),e=1<o.length?H(i[r],function(n){return F(o,function(t){return t.name===n})}).join(" > "):o[0].name;return Jt(r,{handler:n,purpose:e})})});return Qt(n,{})},Di="alloy.base.behaviour",Bi=function(t){var n,e;return _n("custom.definition",fn([bn("dom","dom",Wt(),fn([Ln("tag"),ne("styles",{}),ne("classes",[]),ne("attributes",{}),Kn("value"),Kn("innerHtml")])),Ln("components"),Ln("uid"),ne("events",{}),ne("apis",{}),bn("eventOrder","eventOrder",((n={})[_o()]=["disabling",Di,"toggling","typeaheadevents"],n[So()]=[Di,"focusing","keying"],n[Mo()]=[Di,"disabling","toggling","representing"],n[po()]=[Di,"representing","streaming","invalidating"],n[Ho()]=[Di,"representing","item-events","tooltipping"],n[io()]=["focusing",Di,"item-type-events"],n[no()]=["focusing",Di,"item-type-events"],n[so()]=["item-type-events","tooltipping"],n[Oo()]=["receiving","reflecting","tooltipping"],e=n,jt.mergeWithThunk(st(e))),Mn()),Kn("domModification")]),t)},Ai=function(t,n){var e=qr(t,n);return e===undefined||""===e?[]:e.split(" ")},Mi=function(t){return t.dom.classList!==undefined},Fi=function(t,n){return r=n,i=Ai(e=t,o="class").concat([r]),Yr(e,o,i.join(" ")),!0;var e,o,r,i},Ii=function(t,n){return r=n,0<(i=H(Ai(e=t,o="class"),function(t){return t!==r})).length?Yr(e,o,i.join(" ")):$r(e,o),!1;var e,o,r,i},Ri=function(t,n){Mi(t)?t.dom.classList.add(n):Fi(t,n)},Vi=function(t){0===(Mi(t)?t.dom.classList:Ai(t,"class")).length&&$r(t,"class")},Pi=function(t,n){Mi(t)?t.dom.classList.remove(n):Ii(t,n),Vi(t)},Hi=function(t,n){return Mi(t)&&t.dom.classList.contains(n)},zi=function(n,t){mt(t,function(t){Ri(n,t)})},Ni=function(n,t){mt(t,function(t){Pi(n,t)})},Li=function(t){return t.style!==undefined&&y(t.style.getPropertyValue)},ji=function(t){var n=hr(t)?t.dom.parentNode:t.dom;if(n===undefined||null===n||null===n.ownerDocument)return!1;var e,o,r=n.ownerDocument;return Fr(me.fromDom(n)).fold(function(){return r.body.contains(n)},(e=ji,o=Ir,function(t){return e(o(t))}))},Ui=function(){return Wi(me.fromDom(document))},Wi=function(t){var n=t.dom.body;if(null===n||n===undefined)throw new Error("Body is not available yet");return me.fromDom(n)},Gi=function(t,n,e){if(!x(e))throw console.error("Invalid call to CSS.set. Property ",n,":: Value ",e,":: Element ",t),new Error("CSS value must be a string: "+e);Li(t)&&t.style.setProperty(n,e)},Xi=function(t,n){Li(t)&&t.style.removeProperty(n)},Yi=function(t,n,e){var o=t.dom;Gi(o,n,e)},qi=function(t,n){var e=t.dom;_t(n,function(t,n){Gi(e,n,t)})},Ki=function(t,n){var e=t.dom;_t(n,function(t,n){t.fold(function(){Xi(e,n)},function(t){Gi(e,n,t)})})},Ji=function(t,n){var e=t.dom,o=window.getComputedStyle(e).getPropertyValue(n);return""!==o||ji(t)?o:$i(e,n)},$i=function(t,n){return Li(t)?t.style.getPropertyValue(n):""},Qi=function(t,n){var e=t.dom,o=$i(e,n);return dt.from(o).filter(function(t){return 0<t.length})},Zi=function(t,n,e){var o=me.fromTag(t);return Yi(o,n,e),Qi(o,n).isSome()},tu=function(t,n){var e=t.dom;Xi(e,n),Kr(t,"style").map(ke).is("")&&$r(t,"style")},nu=function(t){return t.dom.offsetWidth},eu=function(t){return t.dom.value},ou=function(t,n){if(n===undefined)throw new Error("Value.set was undefined");t.dom.value=n},ru=function(t){var n,e,o,r=me.fromTag(t.tag);n=r,e=t.attributes,o=n.dom,_t(e,function(t,n){Xr(o,n,t)}),zi(r,t.classes),qi(r,t.styles),t.innerHtml.each(function(t){return Gr(r,t)});var i=t.domChildren;return Nr(r,i),t.value.each(function(t){ou(r,t)}),t.uid,ci(r,t.uid),r},iu=function(t,n){return e=t,r=V(o=n,function(t){return te(t.name(),[Ln("config"),ne("state",yi)])}),i=_n("component.behaviours",fn(r),e.behaviours).fold(function(t){throw new Error(Dn(t)+"\nComplete spec:\n"+JSON.stringify(e,null,2))},function(t){return t}),{list:o,data:Tt(i,function(t){var n=t.map(function(t){return{config:t.config,state:t.state.init(t.config)}});return function(){return n}})};var e,o,r,i},uu=function(t){var n,e,o=(n=Ft(t,"behaviours").getOr({}),e=H(Ct(n),function(t){return n[t]!==undefined}),V(e,function(t){return n[t].me}));return iu(t,o)},au=function(t,n,e){var o,r,i,u=ft(ft({},(o=t).dom),{uid:o.uid,domChildren:V(o.components,function(t){return t.element})}),a=t.domModification.fold(function(){return Si({})},Si),c={"alloy.base.modification":a},s=0<n.length?function(n,t,e,o){var r=ft({},t);mt(e,function(t){r[t.name()]=t.exhibit(n,o)});var i=wi(r,function(t,n){return{name:t,modification:n}}),u=function(t){return z(t,function(t,n){return ft(ft({},n.modification),t)},{})},a=z(i.classes,function(t,n){return n.modification.concat(t)},[]),c=u(i.attributes),s=u(i.styles);return Si({classes:a,attributes:c,styles:s})}(e,c,n,u):a;return i=s,ft(ft({},r=u),{attributes:ft(ft({},r.attributes),i.attributes),styles:ft(ft({},r.styles),i.styles),classes:r.classes.concat(i.classes)})},cu=function(t,n,e){var o,r,i,u={"alloy.base.behaviour":t.events};return o=e,r=t.eventOrder,i=Oi(o,n,u),Ei(i,r).getOrDie()},su=function(t){var n,e,o,r,i,u,a,c,s,l,f,d,m,g=fi(t),p=g.events,h=_(g,["events"]),v=(n=Ft(h,"components").getOr([]),V(n,mu)),b=ft(ft({},h),{events:ft(ft({},ni),p),components:v});return pt.value((o=function(){return m},r=fe(mi),i=Tn(Bi(e=b)),u=uu(e),a=u.list,c=u.data,s=au(i,a,c),l=ru(s),f=cu(i,a,c),d=fe(i.components),m={getSystem:r.get,config:function(t){var n=c;return(y(n[t.name()])?n[t.name()]:function(){throw new Error("Could not find "+t.name()+" in "+JSON.stringify(e,null,2))})()},hasConfigured:function(t){return y(c[t.name()])},spec:e,readState:function(t){return c[t]().map(function(t){return t.state.readState()}).getOr("not enabled")},getApis:function(){return i.apis},connect:function(t){r.set(t)},disconnect:function(){r.set(di(o))},element:l,syncComponents:function(){var t=_r(l),n=U(t,function(t){return r.get().getByDom(t).fold(function(){return[]},function(t){return[t]})});d.set(n)},components:d.get,events:f}))},lu=function(t){var n=me.fromText(t);return fu({element:n})},fu=function(t){var n=En("external.component",ln([Ln("element"),Kn("uid")]),t),e=fe(di());n.uid.each(function(t){ci(n.element,t)});var o={getSystem:e.get,config:dt.none,hasConfigured:l,connect:function(t){e.set(t)},disconnect:function(){e.set(di(function(){return o}))},getApis:function(){return{}},element:n.element,spec:t,readState:st("No state"),syncComponents:ct,components:st([]),events:{}};return vi(o)},du=li,mu=function(n){return Ft(n,hi).fold(function(){var t=n.hasOwnProperty("uid")?n:ft({uid:du("")},n);return su(t).getOrDie()},function(t){return t})},gu=vi,pu=function(o,r){var t=function(t){var n=r(t);if(n<=0||null===n){var e=Ji(t,o);return parseFloat(e)||0}return n},i=function(r,t){return N(t,function(t,n){var e=Ji(r,n),o=e===undefined?0:parseInt(e,10);return isNaN(o)?t:t+o},0)};return{set:function(t,n){if(!at(n)&&!n.match(/^[0-9]+$/))throw new Error(o+".set accepts only positive integer values. Value was "+n);var e=t.dom;Li(e)&&(e.style[o]=n+"px")},get:t,getOuter:t,aggregate:i,max:function(t,n,e){var o=i(t,e);return o<n?n-o:0}}},hu=pu("height",function(t){var n=t.dom;return ji(t)?n.getBoundingClientRect().height:n.offsetHeight}),vu=function(t){return hu.get(t)},bu=function(t){return hu.getOuter(t)},yu=function(e,o){return{left:e,top:o,translate:function(t,n){return yu(e+t,o+n)}}},xu=yu,wu=function(t,n){return t!==undefined?t:n!==undefined?n:0},Su=function(t){var n=t.dom.ownerDocument,e=n.body,o=n.defaultView,r=n.documentElement;if(e===t.dom)return xu(e.offsetLeft,e.offsetTop);var i=wu(null==o?void 0:o.pageYOffset,r.scrollTop),u=wu(null==o?void 0:o.pageXOffset,r.scrollLeft),a=wu(r.clientTop,e.clientTop),c=wu(r.clientLeft,e.clientLeft);return ku(t).translate(u-c,i-a)},ku=function(t){var n,e=t.dom,o=e.ownerDocument.body;return o===e?xu(o.offsetLeft,o.offsetTop):ji(t)?(n=e.getBoundingClientRect(),xu(n.left,n.top)):xu(0,0)},Cu=pu("width",function(t){return t.dom.offsetWidth}),Ou=function(t){return Cu.get(t)},_u=function(t){return Cu.getOuter(t)},Tu=function(t){var n=me.fromDom(function(t){if(Br()&&i(t.target)){var n=me.fromDom(t.target);if(pr(n)&&Rr(n)&&t.composed&&t.composedPath){var e=t.composedPath();if(e)return K(e)}}return dt.from(t.target)}(t).getOr(t.target)),e=function(){return t.stopPropagation()},o=function(){return t.preventDefault()},r=u(o,e);return{target:n,x:t.clientX,y:t.clientY,stop:e,prevent:o,kill:r,raw:t}},Eu=function(t,n,e,o,r){var i,u,a=(i=e,u=o,function(t){i(t)&&u(Tu(t))});return t.dom.addEventListener(n,a,r),{unbind:k(Du,t,n,a,r)}},Du=function(t,n,e,o){t.dom.removeEventListener(n,e,o)},Bu=function(t){var n=t!==undefined?t.dom:document,e=n.body.scrollLeft||n.documentElement.scrollLeft,o=n.body.scrollTop||n.documentElement.scrollTop;return xu(e,o)},Au=function(t,n,e){var o=(e!==undefined?e.dom:document).defaultView;o&&o.scrollTo(t,n)},Mu=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Fu=function(t){var n,e,o=t===undefined?window:t,r=o.document,i=Bu(me.fromDom(r));return e=(n=o)===undefined?window:n,dt.from(e.visualViewport).fold(function(){var t=o.document.documentElement,n=t.clientWidth,e=t.clientHeight;return Mu(i.left,i.top,n,e)},function(t){return Mu(Math.max(t.pageLeft,i.left),Math.max(t.pageTop,i.top),t.width,t.height)})},Iu=function(o,t){return o.view(t).fold(st([]),function(t){var n=o.owner(t),e=Iu(o,n);return[t].concat(e)})},Ru=/* */Object.freeze({__proto__:null,view:function(t){var n;return(t.dom===document?dt.none():dt.from(null===(n=t.dom.defaultView)||void 0===n?void 0:n.frameElement)).map(me.fromDom)},owner:yr}),Vu=function(o){var t,n,e,r,i=me.fromDom(document),u=Bu(i);return(t=o,e=(n=Ru).owner(t),r=Iu(n,e),dt.some(r)).fold(k(Su,o),function(t){var n=ku(o),e=z(t,function(t,n){var e=ku(n);return{left:t.left+e.left,top:t.top+e.top}},{left:0,top:0});return xu(e.left+n.left+u.left,e.top+n.top+u.top)})},Pu=function(t,n,e,o){return{x:t,y:n,width:e,height:o,right:t+e,bottom:n+o}},Hu=function(t){var n=Su(t),e=_u(t),o=bu(t);return Pu(n.left,n.top,e,o)},zu=function(t){var n=Vu(t),e=_u(t),o=bu(t);return Pu(n.left,n.top,e,o)},Nu=function(){return Fu(window)};function Lu(t,n,e,o,r){return t(e,o)?dt.some(e):y(r)&&r(e)?dt.none():n(e,o,r)}var ju,Uu,Wu=function(t,n,e){for(var o=t.dom,r=y(e)?e:l;o.parentNode;){o=o.parentNode;var i=me.fromDom(o);if(n(i))return dt.some(i);if(r(i))break}return dt.none()},Gu=function(t,n,e){return Lu(function(t,n){return n(t)},Wu,t,n,e)},Xu=function(t,n,e){return Gu(t,n,e).isSome()},Yu=function(t,n,e){return Wu(t,function(t){return Ue(t,n)},e)},qu=function(t,n){return e=n,r=(o=t)===undefined?document:o.dom,We(r)?dt.none():dt.from(r.querySelector(e)).map(me.fromDom);var e,o,r},Ku=function(t,n,e){return Lu(Ue,Yu,t,n,e)},Ju=function(){var n=oi("aria-owns");return{id:n,link:function(t){Yr(t,"aria-owns",n)},unlink:function(t){$r(t,"aria-owns")}}},$u=function(n,t){return Gu(t,function(t){if(!pr(t))return!1;var n=qr(t,"id");return n!==undefined&&-1<n.indexOf("aria-owns")}).bind(function(t){var n=qr(t,"id"),e=Ar(t);return qu(e,'[aria-owns="'+n+'"]')}).exists(function(t){return Qu(n,t)})},Qu=function(n,t){return Xu(t,function(t){return Ge(t,n.element)},l)||$u(n,t)},Zu="unknown";(Uu=ju=ju||{})[Uu.STOP=0]="STOP",Uu[Uu.NORMAL=1]="NORMAL",Uu[Uu.LOGGING=2]="LOGGING";var ta,na,ea=fe({}),oa=["alloy/data/Fields","alloy/debugging/Debugging"],ra=function(n,t,e){var o,r,i,u;switch(Ft(ea.get(),n).orThunk(function(){var t=Ct(ea.get());return Q(t,function(t){return-1<n.indexOf(t)?dt.some(ea.get()[t]):dt.none()})}).getOr(ju.NORMAL)){case ju.NORMAL:return e(ia());case ju.LOGGING:var a=(o=n,r=t,i=[],u=(new Date).getTime(),{logEventCut:function(t,n,e){i.push({outcome:"cut",target:n,purpose:e})},logEventStopped:function(t,n,e){i.push({outcome:"stopped",target:n,purpose:e})},logNoParent:function(t,n,e){i.push({outcome:"no-parent",target:n,purpose:e})},logEventNoHandlers:function(t,n){i.push({outcome:"no-handlers-left",target:n})},logEventResponse:function(t,n,e){i.push({outcome:"response",purpose:e,target:n})},write:function(){var t=(new Date).getTime();M(["mousemove","mouseover","mouseout",Mo()],o)||console.log(o,{event:o,time:t-u,target:r.dom,sequence:V(i,function(t){return M(["cut","stopped","response"],t.outcome)?"{"+t.purpose+"} "+t.outcome+" at ("+Zr(t.target)+")":t.outcome})})}}),c=e(a);return a.write(),c;case ju.STOP:return!0}},ia=st({logEventCut:ct,logEventStopped:ct,logNoParent:ct,logEventNoHandlers:ct,logEventResponse:ct,write:ct}),ua=st([Ln("menu"),Ln("selectedMenu")]),aa=st([Ln("item"),Ln("selectedItem")]),ca=(st(fn(aa().concat(ua()))),st(fn(aa()))),sa=Xn("initSize",[Ln("numColumns"),Ln("numRows")]),la=function(){return Xn("markers",[Ln("backgroundMenu")].concat(ua()).concat(aa()))},fa=function(t){return Xn("markers",V(t,Ln))},da=function(t,n,e){!function(){var t=new Error;if(t.stack===undefined)return;var n=t.stack.split("\n");L(n,function(n){return 0<n.indexOf("alloy")&&!F(oa,function(t){return-1<n.indexOf(t)})}).getOr(Zu)}();return bn(n,n,e,Cn(function(e){return pt.value(function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(undefined,t)})}))},ma=function(t){return da(0,t,Ut(ct))},ga=function(t){return da(0,t,Ut(dt.none))},pa=function(t){return da(0,t,Wt())},ha=function(t){return da(0,t,Wt())},va=function(t,n){return le(t,st(n))},ba=function(t){return le(t,lt)},ya=st(sa),xa=function(t,n,e,o,r,i){return{x:t,y:n,bubble:e,direction:o,boundsRestriction:r,label:i}},wa=Vt([{southeast:[]},{southwest:[]},{northeast:[]},{northwest:[]},{south:[]},{north:[]},{east:[]},{west:[]}]),Sa=wa.southeast,ka=wa.southwest,Ca=wa.northeast,Oa=wa.northwest,_a=wa.south,Ta=wa.north,Ea=wa.east,Da=wa.west,Ba=function(n,e){return function(t,n){for(var e={},o=0,r=t.length;o<r;o++){var i=t[o];e[String(i)]=n(i,o)}return e}(["left","right","top","bottom"],function(t){return Ft(e,t).map(function(t){return function(t,n){switch(n){case 1:return t.x;case 0:return t.x+t.width;case 2:return t.y;case 3:return t.y+t.height}}(n,t)})})},Aa=function(t){return t.x},Ma=function(t,n){return t.x+t.width/2-n.width/2},Fa=function(t,n){return t.x+t.width-n.width},Ia=function(t,n){return t.y-n.height},Ra=function(t){return t.y+t.height},Va=function(t,n){return t.y+t.height/2-n.height/2},Pa=function(t,n,e){return xa(Aa(t),Ra(t),e.southeast(),Sa(),Ba(t,{left:1,top:3}),"layout-se")},Ha=function(t,n,e){return xa(Fa(t,n),Ra(t),e.southwest(),ka(),Ba(t,{right:0,top:3}),"layout-sw")},za=function(t,n,e){return xa(Aa(t),Ia(t,n),e.northeast(),Ca(),Ba(t,{left:1,bottom:2}),"layout-ne")},Na=function(t,n,e){return xa(Fa(t,n),Ia(t,n),e.northwest(),Oa(),Ba(t,{right:0,bottom:2}),"layout-nw")},La=function(t,n,e){return xa(Ma(t,n),Ia(t,n),e.north(),Ta(),Ba(t,{bottom:2}),"layout-n")},ja=function(t,n,e){return xa(Ma(t,n),Ra(t),e.south(),_a(),Ba(t,{top:3}),"layout-s")},Ua=function(t,n,e){return xa((o=t).x+o.width,Va(t,n),e.east(),Ea(),Ba(t,{left:0}),"layout-e");var o},Wa=function(t,n,e){return xa((o=n,t.x-o.width),Va(t,n),e.west(),Da(),Ba(t,{right:1}),"layout-w");var o},Ga=function(){return[Pa,Ha,za,Na,ja,La,Ua,Wa]},Xa=function(){return[Ha,Pa,Na,za,ja,La,Ua,Wa]},Ya=function(){return[za,Na,Pa,Ha,La,ja]},qa=function(){return[Na,za,Ha,Pa,La,ja]},Ka=function(){return[Pa,Ha,za,Na,ja,La]},Ja=function(){return[Ha,Pa,Na,za,ja,La]},$a=/* */Object.freeze({__proto__:null,events:function(c){return Zo([er(Oo(),function(r,t){var n,e,i=c.channels,o=Ct(i),u=t,a=(n=o,(e=u).universal?n:H(n,function(t){return M(e.channels,t)}));mt(a,function(t){var n=i[t],e=n.schema,o=En("channel["+t+"] data\nReceiver: "+Zr(r.element),e,u.data);n.onReceive(r,o)})})])}}),Qa=[jn("channels",On(pt.value,ln([pa("onReceive"),ne("schema",Mn())])))],Za=function(e,o,r){return fr(function(t,n){r(t,e,o)})},tc=function(t,n,e,o,r,i){var u=ln(t),a=te(n,[Jn("config",ln(t))]);return oc(u,a,n,e,o,r,i)},nc=function(r,i,u){var t,n,e,o,a,c;return t=function(e){for(var t=[],n=1;n<arguments.length;n++)t[n-1]=arguments[n];var o=[e].concat(t);return e.config({name:st(r)}).fold(function(){throw new Error("We could not find any behaviour configuration for: "+r+". Using API: "+u)},function(t){var n=Array.prototype.slice.call(o,1);return i.apply(undefined,[e,t.config,t.state].concat(n))})},n=u,e=i.toString(),o=e.indexOf(")")+1,a=e.indexOf("("),c=e.substring(a+1,o-1).split(/,\s*/),t.toFunctionAnnotation=function(){return{name:n,parameters:gi(c.slice(0,1).concat(c.slice(3)))}},t},ec=function(t){return{key:t,value:undefined}},oc=function(e,t,o,r,n,i,u){var a=function(t){return Rt(t,o)?t[o]():dt.none()},c=Tt(n,function(t,n){return nc(o,t,n)}),s=Tt(i,pi),l=ft(ft(ft({},s),c),{revoke:k(ec,o),config:function(t){var n=En(o+"-config",e,t);return{key:o,value:{config:n,me:l,configAsRaw:Lt(function(){return En(o+"-config",e,t)}),initialConfig:t,state:u}}},schema:st(t),exhibit:function(t,e){return a(t).bind(function(n){return Ft(r,"exhibit").map(function(t){return t(e,n.config,n.state)})}).getOr(Si({}))},name:st(o),handlers:function(t){return a(t).map(function(t){return Ft(r,"events").getOr(function(){return{}})(t.config,t.state)}).getOr({})}});return l},rc=$t,ic=ln([Ln("fields"),Ln("name"),ne("active",{}),ne("apis",{}),ne("state",yi),ne("extra",{})]),uc=function(t){var n=En("Creating behaviour: "+t.name,ic,t);return tc(n.fields,n.name,n.active,n.apis,n.extra,n.state)},ac=ln([Ln("branchKey"),Ln("branches"),Ln("name"),ne("active",{}),ne("apis",{}),ne("state",yi),ne("extra",{})]),cc=function(t){var n,e,o,r,i,u,a,c,s=En("Creating behaviour: "+t.name,ac,t);return n=An(s.branchKey,s.branches),e=s.name,o=s.active,r=s.apis,i=s.extra,u=s.state,c=te(e,[Jn("config",a=n)]),oc(a,c,e,o,r,i,u)},sc=st(undefined),lc=uc({fields:Qa,name:"receiving",active:$a}),fc=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return Si({classes:[],styles:n.useFixed()?{}:{position:"relative"}})}}),dc=function(t){return t.dom.focus()},mc=function(t){return void 0===t&&(t=me.fromDom(document)),dt.from(t.dom.activeElement).map(me.fromDom)},gc=function(n){return mc(Ar(n)).filter(function(t){return n.dom.contains(t.dom)})},pc=function(t,e){var o=Ar(e),n=mc(o).bind(function(n){var r,i,t=function(t){return Ge(n,t)};return t(e)?dt.some(e):(r=t,(i=function(t){for(var n=0;n<t.childNodes.length;n++){var e=me.fromDom(t.childNodes[n]);if(r(e))return dt.some(e);var o=i(t.childNodes[n]);if(o.isSome())return o}return dt.none()})(e.dom))}),r=t(e);return n.each(function(n){mc(o).filter(function(t){return Ge(t,n)}).fold(function(){dc(n)},ct)}),r},hc=function(t,n,e,o,r){return{position:t,left:n,top:e,right:o,bottom:r}},vc=function(t,n){var e=function(t){return t+"px"};Ki(t,{position:dt.some(n.position),left:n.left.map(e),top:n.top.map(e),right:n.right.map(e),bottom:n.bottom.map(e)})},bc=Vt([{none:[]},{relative:["x","y","width","height"]},{fixed:["x","y","width","height"]}]),yc=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=n.x-e,h=n.y-o,v=r-(p+n.width),b=i-(h+n.height),y=dt.some(p),x=dt.some(h),w=dt.some(v),S=dt.some(b),k=dt.none();return u=n.direction,a=function(){return hc(t,y,x,k,k)},c=function(){return hc(t,k,x,w,k)},s=function(){return hc(t,y,k,k,S)},l=function(){return hc(t,k,k,w,S)},f=function(){return hc(t,y,x,k,k)},d=function(){return hc(t,y,k,k,S)},m=function(){return hc(t,y,x,k,k)},g=function(){return hc(t,k,x,w,k)},u.fold(a,c,s,l,f,d,m,g)},xc=function(t,n){var e=k(Vu,n),o=t.fold(e,e,function(){var t=Bu();return Vu(n).translate(-t.left,-t.top)}),r=_u(n),i=bu(n);return Pu(o.left,o.top,r,i)},wc=function(t,n,e){var o=xu(n,e);return t.fold(st(o),st(o),function(){var t=Bu();return o.translate(-t.left,-t.top)})},Sc=(bc.none,bc.relative),kc=bc.fixed,Cc=function(t,n,e,o){var r=t+n;return o<r?e:r<e?o:r},Oc=function(t,n,e){return Math.min(Math.max(t,n),e)},_c=Vt([{fit:["reposition"]},{nofit:["reposition","deltaW","deltaH"]}]),Tc=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m,g,p,h,v,b,y,x,w,S,k,C,O,_,T,E,D,B,A,M,F,I,R,V,P=t.x,H=t.y,z=t.bubble.offset,N=z.left,L=z.top,j=(r=o,i=t.boundsRestriction,u=z,c=(a=function(n,e){var o="top"===n||"bottom"===n?u.top:u.left;return Ft(i,n).bind(lt).bind(function(t){return"left"===n||"top"===n?e<=t?dt.some(t):dt.none():t<=e?dt.some(t):dt.none()}).map(function(t){return t+o}).getOr(e)})("left",r.x),s=a("top",r.y),l=a("right",r.right),f=a("bottom",r.bottom),Pu(c,s,l-c,f-s)),U=j.y,W=j.bottom,G=j.x,X=j.right,Y=H+L,q=(d=P+N,m=Y,g=n,p=e,v=(h=j).x,b=h.y,y=h.width,x=h.height,S=b<=m,k=(w=v<=d)&&S,C=d+g<=v+y&&m+p<=b+x,O=Math.abs(Math.min(g,w?v+y-d:v-(d+g))),_=Math.abs(Math.min(p,S?b+x-m:b-(m+p))),T=Math.max(h.x,h.right-g),E=Math.max(h.y,h.bottom-p),{originInBounds:k,sizeInBounds:C,limitX:Oc(d,h.x,T),limitY:Oc(m,h.y,E),deltaW:O,deltaH:_}),K=q.originInBounds,J=q.sizeInBounds,$=q.limitX,Q=q.limitY,Z=q.deltaW,tt=q.deltaH,nt=st(Q+tt-U),et=st(W-Q),ot=(D=t.direction,A=B=et,M=nt,D.fold(B,B,M,M,B,M,A,A)),rt=st($+Z-G),it=st(X-$),ut={x:$,y:Q,width:Z,height:tt,maxHeight:ot,maxWidth:(F=t.direction,R=I=it,V=rt,F.fold(I,V,I,V,R,R,I,V)),direction:t.direction,classes:{on:t.bubble.classesOn,off:t.bubble.classesOff},label:t.label,candidateYforTest:Y};return K&&J?_c.fit(ut):_c.nofit(ut,Z,tt)},Ec=function(t,n,e,o){tu(n,"max-height"),tu(n,"max-width");var r,i,u,a,c,s,l,f,d,m={width:_u(r=n),height:bu(r)};return i=o.preference,u=t,a=m,c=e,s=o.bounds,l=a.width,f=a.height,d=function(t,o,r,i){var n=t(u,a,c);return Tc(n,l,f,s).fold(_c.fit,function(t,n,e){return i<e||r<n?_c.nofit(t,n,e):_c.nofit(o,r,i)})},N(i,function(t,n){var e=k(d,n);return t.fold(_c.fit,e)},_c.nofit({x:u.x,y:u.y,width:a.width,height:a.height,maxHeight:a.height,maxWidth:a.width,direction:Sa(),classes:{on:[],off:[]},label:"none",candidateYforTest:u.y},-1,-1)).fold(lt,lt)},Dc=function(t,n,e){var o,r;vc(t,(o=e.origin,r=n,o.fold(function(){return hc("absolute",dt.some(r.x),dt.some(r.y),dt.none(),dt.none())},function(t,n,e,o){return yc("absolute",r,t,n,e,o)},function(t,n,e,o){return yc("fixed",r,t,n,e,o)})))},Bc=function(t,n){var e,o,r;e=t,o=Math.floor(n),r=hu.max(e,o,["margin-top","border-top-width","padding-top","padding-bottom","border-bottom-width","margin-bottom"]),Yi(e,"max-height",r+"px")},Ac=st(function(t,n){Bc(t,n),qi(t,{"overflow-x":"hidden","overflow-y":"auto"})}),Mc=st(function(t,n){Bc(t,n)}),Fc=function(t,n,e){return t[n]===undefined?e:t[n]},Ic=function(t,n,e,o,r,i){var u,a=Fc(i,"maxHeightFunction",Ac()),c=Fc(i,"maxWidthFunction",ct),s=t.anchorBox,l=t.origin,f={bounds:(u=l,r.fold(function(){return u.fold(Nu,Nu,Pu)},function(e){return u.fold(e,e,function(){var t=e(),n=wc(u,t.x,t.y);return Pu(n.left,n.top,t.width,t.height)})})),origin:l,preference:o,maxHeightFunction:a,maxWidthFunction:c};Rc(s,n,e,f)},Rc=function(t,n,e,o){var r,i,u,a,c,s,l=Ec(t,n,e,o);Dc(n,l,o),r=n,i=l.classes,Ni(r,i.off),zi(r,i.on),u=n,a=l,(0,o.maxHeightFunction)(u,a.maxHeight),c=n,s=l,(0,o.maxWidthFunction)(c,s.maxWidth)},Vc=["valignCentre","alignLeft","alignRight","alignCentre","top","bottom","left","right"],Pc=function(t,n,e){var r=function(t){return Ft(e,t).getOr([])},o=function(t,n,e){var o=X(Vc,e);return{offset:xu(t,n),classesOn:U(e,r),classesOff:U(o,r)}};return{southeast:function(){return o(-t,n,["top","alignLeft"])},southwest:function(){return o(t,n,["top","alignRight"])},south:function(){return o(-t/2,n,["top","alignCentre"])},northeast:function(){return o(-t,-n,["bottom","alignLeft"])},northwest:function(){return o(t,-n,["bottom","alignRight"])},north:function(){return o(-t/2,-n,["bottom","alignCentre"])},east:function(){return o(t,-n/2,["valignCentre","left"])},west:function(){return o(-t,-n/2,["valignCentre","right"])},innerNorthwest:function(){return o(-t,n,["top","alignRight"])},innerNortheast:function(){return o(t,n,["top","alignLeft"])},innerNorth:function(){return o(-t/2,n,["top","alignCentre"])},innerSouthwest:function(){return o(-t,-n,["bottom","alignRight"])},innerSoutheast:function(){return o(t,-n,["bottom","alignLeft"])},innerSouth:function(){return o(-t/2,-n,["bottom","alignCentre"])},innerWest:function(){return o(t,-n/2,["valignCentre","right"])},innerEast:function(){return o(-t,-n/2,["valignCentre","left"])}}},Hc=function(){return Pc(0,0,{})},zc=function(n,e){return function(t){return"rtl"===Nc(t)?e:n}},Nc=function(t){return"rtl"===Ji(t,"direction")?"rtl":"ltr"};(na=ta=ta||{}).TopToBottom="toptobottom",na.BottomToTop="bottomtotop";var Lc,jc,Uc,Wc,Gc,Xc="data-alloy-vertical-dir",Yc=function(t){return Xu(t,function(t){return pr(t)&&qr(t,"data-alloy-vertical-dir")===ta.BottomToTop})},qc=function(){return te("layouts",[Ln("onLtr"),Ln("onRtl"),Kn("onBottomLtr"),Kn("onBottomRtl")])},Kc=function(n,t,e,o,r,i,u){var a=u.map(Yc).getOr(!1),c=t.layouts.map(function(t){return t.onLtr(n)}),s=t.layouts.map(function(t){return t.onRtl(n)}),l=a?t.layouts.bind(function(t){return t.onBottomLtr.map(function(t){return t(n)})}).or(c).getOr(r):c.getOr(e),f=a?t.layouts.bind(function(t){return t.onBottomRtl.map(function(t){return t(n)})}).or(s).getOr(i):s.getOr(o);return zc(l,f)(n)},Jc=[Ln("hotspot"),Kn("bubble"),ne("overrides",{}),qc(),va("placement",function(t,n,e){var o=n.hotspot,r=xc(e,o.element),i=Kc(t.element,n,Ka(),Ja(),Ya(),qa(),dt.some(n.hotspot.element));return dt.some({anchorBox:r,bubble:n.bubble.getOr(Hc()),overrides:n.overrides,layouts:i,placer:dt.none()})})],$c=[Ln("x"),Ln("y"),ne("height",0),ne("width",0),ne("bubble",Hc()),ne("overrides",{}),qc(),va("placement",function(t,n,e){var o=wc(e,n.x,n.y),r=Pu(o.left,o.top,n.width,n.height),i=Kc(t.element,n,Ga(),Xa(),Ga(),Xa(),dt.none());return dt.some({anchorBox:r,bubble:n.bubble,overrides:n.overrides,layouts:i,placer:dt.none()})})],Qc=Vt([{screen:["point"]},{absolute:["point","scrollLeft","scrollTop"]}]),Zc=function(t){return t.fold(lt,function(t,n,e){return t.translate(-n,-e)})},ts=function(t){return t.fold(lt,lt)},ns=function(t){return N(t,function(t,n){return t.translate(n.left,n.top)},xu(0,0))},es=function(t){var n=V(t,ts);return ns(n)},os=Qc.screen,rs=Qc.absolute,is=function(t,n,e){var o,r,i=yr(t.element),u=Bu(i),a=(o=t,r=Sr(e.root).dom,dt.from(r.frameElement).map(me.fromDom).filter(function(t){var n=yr(t),e=yr(o.element);return Ge(n,e)}).map(Su).getOr(u));return rs(a,u.left,u.top)},us=function(t,n,e,o){var r=t,i=n,u=e,a=o;t<0&&(r=0,u=e+t),n<0&&(i=0,a=o+n);var c=os(xu(r,i));return dt.some({point:c,width:u,height:a})},as=function(t,l,f,d,m){return t.map(function(t){var n,e,o,r=[l,t.point],i=(n=function(){return es(r)},e=function(){return es(r)},o=function(){return t=V(r,Zc),ns(t);var t},d.fold(n,e,o)),u={x:i.left,y:i.top,width:t.width,height:t.height},a=(f.showAbove?Ya:Ka)(),c=(f.showAbove?qa:Ja)(),s=Kc(m,f,a,c,a,c,dt.none());return{anchorBox:u,bubble:f.bubble.getOr(Hc()),overrides:f.overrides,layouts:s,placer:dt.none()}})},cs=[Ln("node"),Ln("root"),Kn("bubble"),qc(),ne("overrides",{}),ne("showAbove",!1),va("placement",function(r,i,u){var a=is(r,0,i);return i.node.filter(ji).bind(function(t){var n=t.dom.getBoundingClientRect(),e=us(n.left,n.top,n.width,n.height),o=i.node.getOr(r.element);return as(e,a,i,u,o)})})],ss=function(t,n,e,o){return{start:t,soffset:n,finish:e,foffset:o}},ls=Vt([{before:["element"]},{on:["element","offset"]},{after:["element"]}]),fs=(ls.before,ls.on,ls.after,function(t){return t.fold(lt,lt,lt)}),ds=Vt([{domRange:["rng"]},{relative:["startSitu","finishSitu"]},{exact:["start","soffset","finish","foffset"]}]),ms={domRange:ds.domRange,relative:ds.relative,exact:ds.exact,exactFromRange:function(t){return ds.exact(t.start,t.soffset,t.finish,t.foffset)},getWin:function(t){var n=t.match({domRange:function(t){return me.fromDom(t.startContainer)},relative:function(t,n){return fs(t)},exact:function(t,n,e,o){return t}});return Sr(n)},range:ss},gs=function(t,n,e){var o,r,i=t.document.createRange();return o=i,n.fold(function(t){o.setStartBefore(t.dom)},function(t,n){o.setStart(t.dom,n)},function(t){o.setStartAfter(t.dom)}),r=i,e.fold(function(t){r.setEndBefore(t.dom)},function(t,n){r.setEnd(t.dom,n)},function(t){r.setEndAfter(t.dom)}),i},ps=function(t,n,e,o,r){var i=t.document.createRange();return i.setStart(n.dom,e),i.setEnd(o.dom,r),i},hs=function(t){return{left:t.left,top:t.top,right:t.right,bottom:t.bottom,width:t.width,height:t.height}},vs=Vt([{ltr:["start","soffset","finish","foffset"]},{rtl:["start","soffset","finish","foffset"]}]),bs=function(t,n,e){return n(me.fromDom(e.startContainer),e.startOffset,me.fromDom(e.endContainer),e.endOffset)},ys=function(t,n){var r,e,o,i=(r=t,n.match({domRange:function(t){return{ltr:st(t),rtl:dt.none}},relative:function(t,n){return{ltr:Lt(function(){return gs(r,t,n)}),rtl:Lt(function(){return dt.some(gs(r,n,t))})}},exact:function(t,n,e,o){return{ltr:Lt(function(){return ps(r,t,n,e,o)}),rtl:Lt(function(){return dt.some(ps(r,e,o,t,n))})}}}));return(o=(e=i).ltr()).collapsed?e.rtl().filter(function(t){return!1===t.collapsed}).map(function(t){return vs.rtl(me.fromDom(t.endContainer),t.endOffset,me.fromDom(t.startContainer),t.startOffset)}).getOrThunk(function(){return bs(0,vs.ltr,o)}):bs(0,vs.ltr,o)},xs=(vs.ltr,vs.rtl,Lc=hr,jc="text",{get:function(t){if(!Lc(t))throw new Error("Can only get "+jc+" value of a "+jc+" node");return Uc(t).getOr("")},getOption:Uc=function(t){return Lc(t)?dt.from(t.dom.nodeValue):dt.none()},set:function(t,n){if(!Lc(t))throw new Error("Can only set raw "+jc+" value of a "+jc+" node");t.dom.nodeValue=n}}),ws=function(t){return xs.getOption(t)},Ss=["img","br"],ks=function(t){return ws(t).filter(function(t){return 0!==t.trim().length||-1<t.indexOf("\xa0")}).isSome()||M(Ss,mr(t))},Cs=function(t,i){var u=function(t){for(var n=_r(t),e=n.length-1;0<=e;e--){var o=n[e];if(i(o))return dt.some(o);var r=u(o);if(r.isSome())return r}return dt.none()};return u(t)},Os=function(t,n){return e=n,r=(o=t)===undefined?document:o.dom,We(r)?[]:V(r.querySelectorAll(e),me.fromDom);var e,o,r},_s=function(t,n,e,o){var r,i,u,a,c,s=(i=n,u=e,a=o,(c=yr(r=t).dom.createRange()).setStart(r.dom,i),c.setEnd(u.dom,a),c),l=Ge(t,e)&&n===o;return s.collapsed&&!l},Ts=function(t){if(0<t.rangeCount){var n=t.getRangeAt(0),e=t.getRangeAt(t.rangeCount-1);return dt.some(ss(me.fromDom(n.startContainer),n.startOffset,me.fromDom(e.endContainer),e.endOffset))}return dt.none()},Es=function(t){if(null===t.anchorNode||null===t.focusNode)return Ts(t);var n=me.fromDom(t.anchorNode),e=me.fromDom(t.focusNode);return _s(n,t.anchorOffset,e,t.focusOffset)?dt.some(ss(n,t.anchorOffset,e,t.focusOffset)):Ts(t)},Ds=function(t){return n=t,dt.from(n.getSelection()).filter(function(t){return 0<t.rangeCount}).bind(Es);var n},Bs=function(t,n){var i,e,o,r,u=ys(i=t,n).match({ltr:function(t,n,e,o){var r=i.document.createRange();return r.setStart(t.dom,n),r.setEnd(e.dom,o),r},rtl:function(t,n,e,o){var r=i.document.createRange();return r.setStart(e.dom,o),r.setEnd(t.dom,n),r}});return o=(e=u).getClientRects(),0<(r=0<o.length?o[0]:e.getBoundingClientRect()).width||0<r.height?dt.some(r).map(hs):dt.none()},As=function(t,n){return{element:t,offset:n}},Ms=function(t,n){var e=_r(t);if(0===e.length)return As(t,n);if(n<e.length)return As(e[n],0);var o,r=e[e.length-1],i=hr(r)?(o=r,xs.get(o).length):_r(r).length;return As(r,i)},Fs=function(t,n){return(hr(t)?As:Ms)(t,n)},Is=function(t,n){return n.getSelection.getOrThunk(function(){return function(){return Ds(t)}})().map(function(t){var n=Fs(t.start,t.soffset),e=Fs(t.finish,t.foffset);return ms.range(n.element,n.offset,e.element,e.offset)})},Rs=[Kn("getSelection"),Ln("root"),Kn("bubble"),qc(),ne("overrides",{}),ne("showAbove",!1),va("placement",function(t,n,e){var o=Sr(n.root).dom,r=is(t,0,n),i=Is(o,n).bind(function(t){return Bs(o,ms.exactFromRange(t)).orThunk(function(){var n=me.fromText("\ufeff");return Vr(t.start,n),Bs(o,ms.exact(n,0,n,1)).map(function(t){return jr(n),t})}).bind(function(t){return us(t.left,t.top,t.width,t.height)})}),u=Is(o,n).bind(function(t){return pr(t.start)?dt.some(t.start):Cr(t.start)}).getOr(t.element);return as(i,r,n,e,u)})],Vs=function(t){return t.x+t.width},Ps=function(t,n){return t.x-n.width},Hs=function(t,n){return t.y-n.height+t.height},zs=function(t){return t.y},Ns=function(t,n,e){return xa(Vs(t),zs(t),e.southeast(),Sa(),Ba(t,{left:0,top:2}),"link-layout-se")},Ls=function(t,n,e){return xa(Ps(t,n),zs(t),e.southwest(),ka(),Ba(t,{right:1,top:2}),"link-layout-sw")},js=function(t,n,e){return xa(Vs(t),Hs(t,n),e.northeast(),Ca(),Ba(t,{left:0,bottom:3}),"link-layout-ne")},Us=function(t,n,e){return xa(Ps(t,n),Hs(t,n),e.northwest(),Oa(),Ba(t,{right:1,bottom:3}),"link-layout-nw")},Ws=function(){return[Ns,Ls,js,Us]},Gs=function(){return[Ls,Ns,Us,js]},Xs=[Ln("item"),qc(),ne("overrides",{}),va("placement",function(t,n,e){var o=xc(e,n.item.element),r=Kc(t.element,n,Ws(),Gs(),Ws(),Gs(),dt.none());return dt.some({anchorBox:o,bubble:Hc(),overrides:n.overrides,layouts:r,placer:dt.none()})})],Ys=An("anchor",{selection:Rs,node:cs,hotspot:Jc,submenu:Xs,makeshift:$c}),qs=function(t,n,e,o,r){var i={anchorBox:e.anchorBox,origin:n};Ic(i,r.element,e.bubble,e.layouts,o,e.overrides)},Ks=function(t,n,e,o,r,i){var u=i.map(Hu);return Js(t,n,e,o,r,u)},Js=function(c,s,t,n,l,f){var d=En("positioning anchor.info",Ys,n);pc(function(){Yi(l.element,"position","fixed");var t=Qi(l.element,"visibility");Yi(l.element,"visibility","hidden");var n,e,o,r,i=s.useFixed()?(r=document.documentElement,kc(0,0,r.clientWidth,r.clientHeight)):(e=Su((n=c).element),o=n.element.dom.getBoundingClientRect(),Sc(e.left,e.top,o.width,o.height)),u=d.placement,a=f.map(st).or(s.getBounds);u(c,d,i).each(function(t){t.placer.getOr(qs)(c,i,t,a,l)}),t.fold(function(){tu(l.element,"visibility")},function(t){Yi(l.element,"visibility",t)}),Qi(l.element,"left").isNone()&&Qi(l.element,"top").isNone()&&Qi(l.element,"right").isNone()&&Qi(l.element,"bottom").isNone()&&Qi(l.element,"position").is("fixed")&&tu(l.element,"position")},l.element)},$s=/* */Object.freeze({__proto__:null,position:function(t,n,e,o,r){Ks(t,n,e,o,r,dt.none())},positionWithin:Ks,positionWithinBounds:Js,getMode:function(t,n,e){return n.useFixed()?"fixed":"absolute"}}),Qs=[ne("useFixed",l),Kn("getBounds")],Zs=uc({fields:Qs,name:"positioning",active:fc,apis:$s}),tl=function(t){Yo(t,Ho());var n=t.components();mt(n,tl)},nl=function(t){var n=t.components();mt(n,nl),Yo(t,Po())},el=function(t,n){zr(t.element,n.element)},ol=function(n,t){var e,o=n.components();mt((e=n).components(),function(t){return jr(t.element)}),Lr(e.element),e.syncComponents();var r=X(o,t);mt(r,function(t){tl(t),n.getSystem().removeFromWorld(t)}),mt(t,function(t){t.getSystem().isConnected()?el(n,t):(n.getSystem().addToWorld(t),el(n,t),ji(n.element)&&nl(t)),n.syncComponents()})},rl=function(t,n){il(t,n,zr)},il=function(t,n,e){t.getSystem().addToWorld(n),e(t.element,n.element),ji(t.element)&&nl(n),t.syncComponents()},ul=function(t){tl(t),jr(t.element),t.getSystem().removeFromWorld(t)},al=function(n){var t=kr(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()});ul(n),t.each(function(t){t.syncComponents()})},cl=function(t){var n=t.components();mt(n,ul),Lr(t.element),t.syncComponents()},sl=function(t,n){ll(t,n,zr)},ll=function(t,n,e){e(t,n.element);var o=_r(n.element);mt(o,function(t){n.getByDom(t).each(nl)})},fl=function(n){var t=_r(n.element);mt(t,function(t){n.getByDom(t).each(tl)}),jr(n.element)},dl=function(n,t,e,o){e.get().each(function(t){cl(n)});var r=t.getAttachPoint(n);rl(r,n);var i=n.getSystem().build(o);return rl(n,i),e.set(i),i},ml=function(t,n,e,o){var r=dl(t,n,e,o);return n.onOpen(t,r),r},gl=function(n,e,o){o.get().each(function(t){cl(n),al(n),e.onClose(n,t),o.clear()})},pl=function(t,n,e){return e.isOpen()},hl=function(t,n,e){var o,r,i,u,a=n.getAttachPoint(t);Yi(t.element,"position",Zs.getMode(a)),o=t,r="visibility",i=n.cloakVisibilityAttr,u="hidden",Qi(o.element,r).fold(function(){$r(o.element,i)},function(t){Yr(o.element,i,t)}),Yi(o.element,r,u)},vl=function(t,n,e){var o,r,i,u;o=t.element,F(["top","left","right","bottom"],function(t){return Qi(o,t).isSome()})||tu(t.element,"position"),r=t,i="visibility",u=n.cloakVisibilityAttr,Kr(r.element,u).fold(function(){return tu(r.element,i)},function(t){return Yi(r.element,i,t)})},bl=/* */Object.freeze({__proto__:null,cloak:hl,decloak:vl,open:ml,openWhileCloaked:function(t,n,e,o,r){hl(t,n),ml(t,n,e,o),r(),vl(t,n)},close:gl,isOpen:pl,isPartOf:function(n,e,t,o){return pl(0,0,t)&&t.get().exists(function(t){return e.isPartOf(n,t,o)})},getState:function(t,n,e){return e.get()},setContent:function(t,n,e,o){return e.get().map(function(){return dl(t,n,e,o)})}}),yl=/* */Object.freeze({__proto__:null,events:function(e,o){return Zo([er(Bo(),function(t,n){gl(t,e,o)})])}}),xl=[ma("onOpen"),ma("onClose"),Ln("isPartOf"),Ln("getAttachPoint"),ne("cloakVisibilityAttr","data-precloak-visibility")],wl=uc({fields:xl,name:"sandboxing",active:yl,apis:bl,state:/* */Object.freeze({__proto__:null,init:function(){var n=fe(dt.none()),t=st("not-implemented");return xi({readState:t,isOpen:function(){return n.get().isSome()},clear:function(){n.set(dt.none())},set:function(t){n.set(dt.some(t))},get:function(){return n.get()}})}})}),Sl=st("dismiss.popups"),kl=st("reposition.popups"),Cl=st("mouse.released"),Ol=ln([ne("isExtraPart",l),te("fireEventInstead",[ne("event",zo())])]),_l=function(t){var e=En("Dismissal",Ol,t),n={};return n[Sl()]={schema:ln([Ln("target")]),onReceive:function(n,t){wl.isOpen(n)&&(wl.isPartOf(n,t.target)||e.isExtraPart(n,t.target)||e.fireEventInstead.fold(function(){return wl.close(n)},function(t){return Yo(n,t.event)}))}},n},Tl=ln([te("fireEventInstead",[ne("event",No())]),Gn("doReposition")]),El=function(t){var e=En("Reposition",Tl,t),n={};return n[kl()]={onReceive:function(n){wl.isOpen(n)&&e.fireEventInstead.fold(function(){return e.doReposition(n)},function(t){return Yo(n,t.event)})}},n},Dl=function(t,n,e){n.store.manager.onLoad(t,n,e)},Bl=function(t,n,e){n.store.manager.onUnload(t,n,e)},Al=/* */Object.freeze({__proto__:null,onLoad:Dl,onUnload:Bl,setValue:function(t,n,e,o){n.store.manager.setValue(t,n,e,o)},getValue:function(t,n,e){return n.store.manager.getValue(t,n,e)},getState:function(t,n,e){return e}}),Ml=/* */Object.freeze({__proto__:null,events:function(e,o){var t=e.resetOnDom?[sr(function(t,n){Dl(t,e,o)}),lr(function(t,n){Bl(t,e,o)})]:[Za(e,o,Dl)];return Zo(t)}}),Fl=function(){var t=fe(null);return xi({set:t.set,get:t.get,isNotSet:function(){return null===t.get()},clear:function(){t.set(null)},readState:function(){return{mode:"memory",value:t.get()}}})},Il=function(){var i=fe({}),u=fe({});return xi({readState:function(){return{mode:"dataset",dataByValue:i.get(),dataByText:u.get()}},lookup:function(t){return Ft(i.get(),t).orThunk(function(){return Ft(u.get(),t)})},update:function(t){var n=i.get(),e=u.get(),o={},r={};mt(t,function(n){o[n.value]=n,Ft(n,"meta").each(function(t){Ft(t,"text").each(function(t){r[t]=n})})}),i.set(ft(ft({},n),o)),u.set(ft(ft({},e),r))},clear:function(){i.set({}),u.set({})}})},Rl=/* */Object.freeze({__proto__:null,memory:Fl,dataset:Il,manual:function(){return xi({readState:ct})},init:function(t){return t.store.manager.state(t)}}),Vl=function(t,n,e,o){var r=n.store;e.update([o]),r.setValue(t,o),n.onSetValue(t,o)},Pl=[Kn("initialValue"),Ln("getFallbackEntry"),Ln("getDataKey"),Ln("setValue"),va("manager",{setValue:Vl,getValue:function(t,n,e){var o=n.store,r=o.getDataKey(t);return e.lookup(r).fold(function(){return o.getFallbackEntry(r)},function(t){return t})},onLoad:function(n,e,o){e.store.initialValue.each(function(t){Vl(n,e,o,t)})},onUnload:function(t,n,e){e.clear()},state:Il})],Hl=[Ln("getValue"),ne("setValue",ct),Kn("initialValue"),va("manager",{setValue:function(t,n,e,o){n.store.setValue(t,o),n.onSetValue(t,o)},getValue:function(t,n,e){return n.store.getValue(t)},onLoad:function(n,e,t){e.store.initialValue.each(function(t){e.store.setValue(n,t)})},onUnload:ct,state:yi.init})],zl=[Kn("initialValue"),va("manager",{setValue:function(t,n,e,o){e.set(o),n.onSetValue(t,o)},getValue:function(t,n,e){return e.get()},onLoad:function(t,n,e){n.store.initialValue.each(function(t){e.isNotSet()&&e.set(t)})},onUnload:function(t,n,e){e.clear()},state:Fl})],Nl=[ee("store",{mode:"memory"},An("mode",{memory:zl,manual:Hl,dataset:Pl})),ma("onSetValue"),ne("resetOnDom",!1)],Ll=uc({fields:Nl,name:"representing",active:Ml,apis:Al,extra:{setValueFrom:function(t,n){var e=Ll.getValue(n);Ll.setValue(t,e)}},state:Rl}),jl=function(o,t){return se(o,{},V(t,function(t){return n=t.name(),e="Cannot configure "+t.name()+" for "+o,bn(n,n,Gt(),sn(function(t){return yt("The field: "+n+" is forbidden. "+e)}));var n,e}).concat([le("dump",lt)]))},Ul=function(t){return t.dump},Wl=function(t,n){return ft(ft({},t.dump),rc(n))},Gl=jl,Xl=Wl,Yl="placeholder",ql=Vt([{single:["required","valueThunk"]},{multiple:["required","valueThunks"]}]),Kl=function(t){return It(t,"uiType")},Jl=function(t,n,e,o){return Kl(e)&&e.uiType===Yl?(i=e,u=o,(r=t).exists(function(t){return t!==i.owner})?ql.single(!0,st(i)):Ft(u,i.name).fold(function(){throw new Error("Unknown placeholder component: "+i.name+"\nKnown: ["+Ct(u)+"]\nNamespace: "+r.getOr("none")+"\nSpec: "+JSON.stringify(i,null,2))},function(t){return t.replace()})):ql.single(!1,st(e));var r,i,u},$l=function(i,u,a,c){return Jl(i,0,a,c).fold(function(t,n){var e=Kl(a)?n(u,a.config,a.validated):n(u),o=Ft(e,"components").getOr([]),r=U(o,function(t){return $l(i,u,t,c)});return[ft(ft({},e),{components:r})]},function(t,n){if(Kl(a)){var e=n(u,a.config,a.validated);return a.validated.preprocess.getOr(lt)(e)}return n(u)})},Ql=function(n,e,t,o){var r,i,u,a=Tt(o,function(t,n){return o=t,r=!1,{name:st(e=n),required:function(){return o.fold(function(t,n){return t},function(t,n){return t})},used:function(){return r},replace:function(){if(r)throw new Error("Trying to use the same placeholder more than once: "+e);return r=!0,o}};var e,o,r}),c=(r=n,i=e,u=a,U(t,function(t){return $l(r,i,t,u)}));return _t(a,function(t){if(!1===t.used()&&t.required())throw new Error("Placeholder: "+t.name()+" was not found in components list\nNamespace: "+n.getOr("none")+"\nComponents: "+JSON.stringify(e.components,null,2))}),c},Zl=ql.single,tf=ql.multiple,nf=st(Yl),ef=Vt([{required:["data"]},{external:["data"]},{optional:["data"]},{group:["data"]}]),of=ne("factory",{sketch:lt}),rf=ne("schema",[]),uf=Ln("name"),af=bn("pname","pname",Xt(function(t){return"<alloy."+oi(t.name)+">"}),Mn()),cf=le("schema",function(){return[Kn("preprocess")]}),sf=ne("defaults",st({})),lf=ne("overrides",st({})),ff=fn([of,rf,uf,af,sf,lf]),df=fn([of,rf,uf,sf,lf]),mf=fn([of,rf,uf,af,sf,lf]),gf=fn([of,cf,uf,Ln("unit"),af,sf,lf]),pf=function(t){return t.fold(dt.some,dt.none,dt.some,dt.some)},hf=function(t){var n=function(t){return t.name};return t.fold(n,n,n,n)},vf=function(e,o){return function(t){var n=En("Converting part type",o,t);return e(n)}},bf=vf(ef.required,ff),yf=vf(ef.external,df),xf=vf(ef.optional,mf),wf=vf(ef.group,gf),Sf=st("entirety"),kf=/* */Object.freeze({__proto__:null,required:bf,external:yf,optional:xf,group:wf,asNamedPart:pf,name:hf,asCommon:function(t){return t.fold(lt,lt,lt,lt)},original:Sf}),Cf=function(t,n,e,o){return zt(n.defaults(t,e,o),e,{uid:t.partUids[n.name]},n.overrides(t,e,o))},Of=function(r,t){var n={};return mt(t,function(t){pf(t).each(function(e){var o=_f(r,e.pname);n[e.name]=function(t){var n=En("Part: "+e.name+" in "+r,fn(e.schema),t);return ft(ft({},o),{config:t,validated:n})}})}),n},_f=function(t,n){return{uiType:nf(),owner:t,name:n}},Tf=function(t,n,e){return{uiType:nf(),owner:t,name:n,config:e,validated:{}}},Ef=function(t){return U(t,function(t){return t.fold(dt.none,dt.some,dt.none,dt.none).map(function(t){return Xn(t.name,t.schema.concat([ba(Sf())]))}).toArray()})},Df=function(t){return V(t,hf)},Bf=function(t,n,e){return o=n,i={},r={},mt(e,function(t){t.fold(function(o){i[o.pname]=Zl(!0,function(t,n,e){return o.factory.sketch(Cf(t,o,n,e))})},function(t){var n=o.parts[t.name];r[t.name]=st(t.factory.sketch(Cf(o,t,n[Sf()]),n))},function(o){i[o.pname]=Zl(!1,function(t,n,e){return o.factory.sketch(Cf(t,o,n,e))})},function(r){i[r.pname]=tf(!0,function(n,t,e){var o=n[r.name];return V(o,function(t){return r.factory.sketch(zt(r.defaults(n,t,e),t,r.overrides(n,t)))})})})}),{internals:st(i),externals:st(r)};var o,i,r},Af=function(t,n,e){return Ql(dt.some(t),n,n.components,e)},Mf=function(t,n,e){var o=n.partUids[e];return t.getSystem().getByUid(o).toOptional()},Ff=function(t,n,e){return Mf(t,n,e).getOrDie("Could not find part: "+e)},If=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return mt(e,function(t){o[t]=st(i.getByUid(r[t]))}),o},Rf=function(t,n){var e=t.getSystem();return Tt(n.partUids,function(t,n){return st(e.getByUid(t))})},Vf=function(t){return Ct(t.partUids)},Pf=function(t,n,e){var o={},r=n.partUids,i=t.getSystem();return mt(e,function(t){o[t]=st(i.getByUid(r[t]).getOrDie())}),o},Hf=function(n,t){var e=Df(t);return $t(V(e,function(t){return{key:t,value:n+"-"+t}}))},zf=function(n){return bn("partUids","partUids",Yt(function(t){return Hf(t.uid,n)}),Mn())},Nf=/* */Object.freeze({__proto__:null,generate:Of,generateOne:Tf,schemas:Ef,names:Df,substitutes:Bf,components:Af,defaultUids:Hf,defaultUidsSchema:zf,getAllParts:Rf,getAllPartNames:Vf,getPart:Mf,getPartOrDie:Ff,getParts:If,getPartsOrDie:Pf}),Lf=function(t,n,e,o,r){var i,u,a=(u=r,(0<(i=o).length?[Xn("parts",i)]:[]).concat([Ln("uid"),ne("dom",{}),ne("components",[]),ba("originalSpec"),ne("debug.sketcher",{})]).concat(u));return En(t+" [SpecSchema]",ln(a.concat(n)),e)},jf=function(t,n,e,o,r){var i=Uf(r),u=Ef(e),a=zf(e),c=Lf(t,n,i,u,[a]),s=Bf(0,c,e);return o(c,Af(t,c,s.internals()),i,s.externals())},Uf=function(t){return It(t,"uid")?t:ft(ft({},t),{uid:li("uid")})},Wf=ln([Ln("name"),Ln("factory"),Ln("configFields"),ne("apis",{}),ne("extraApis",{})]),Gf=ln([Ln("name"),Ln("factory"),Ln("configFields"),Ln("partFields"),ne("apis",{}),ne("extraApis",{})]),Xf=function(t){var i=En("Sketcher for "+t.name,Wf,t),n=Tt(i.apis,bi),e=Tt(i.extraApis,pi);return ft(ft({name:i.name,configFields:i.configFields,sketch:function(t){return n=i.name,e=i.configFields,o=i.factory,r=Uf(t),o(Lf(n,e,r,[],[]),r);var n,e,o,r}},n),e)},Yf=function(t){var n=En("Sketcher for "+t.name,Gf,t),e=Of(n.name,n.partFields),o=Tt(n.apis,bi),r=Tt(n.extraApis,pi);return ft(ft({name:n.name,partFields:n.partFields,configFields:n.configFields,sketch:function(t){return jf(n.name,n.configFields,n.partFields,n.factory,t)},parts:e},o),r)},qf=function(t){for(var n=[],e=function(t){n.push(t)},o=0;o<t.length;o++)t[o].each(e);return n},Kf=function(t,n){return t?dt.some(n):dt.none()},Jf=function(t){return"input"===mr(t)&&"radio"!==qr(t,"type")||"textarea"===mr(t)},$f=/* */Object.freeze({__proto__:null,getCurrent:function(t,n,e){return n.find(t)}}),Qf=[Ln("find")],Zf=uc({fields:Qf,name:"composing",apis:$f}),td=function(e,o,t,r){var n=Os(e.element,"."+o.highlightClass);mt(n,function(n){F(r,function(t){return t.element===n})||(Pi(n,o.highlightClass),e.getSystem().getByDom(n).each(function(t){o.onDehighlight(e,t),Yo(t,Xo())}))})},nd=function(t,n,e,o){td(t,n,0,[o]),ed(t,n,e,o)||(Ri(o.element,n.highlightClass),n.onHighlight(t,o),Yo(o,Go()))},ed=function(t,n,e,o){return Hi(o.element,n.highlightClass)},od=function(t,n,e,o){var r=Os(t.element,"."+n.itemClass);return dt.from(r[o]).fold(function(){return pt.error(new Error("No element found with index "+o))},t.getSystem().getByDom)},rd=function(n,t,e){return qu(n.element,"."+t.itemClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},id=function(n,t,e){var o=Os(n.element,"."+t.itemClass);return(0<o.length?dt.some(o[o.length-1]):dt.none()).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},ud=function(e,n,t,o){var r=Os(e.element,"."+n.itemClass);return j(r,function(t){return Hi(t,n.highlightClass)}).bind(function(t){var n=Cc(t,o,0,r.length-1);return e.getSystem().getByDom(r[n]).toOptional()})},ad=function(n,t,e){var o=Os(n.element,"."+t.itemClass);return qf(V(o,function(t){return n.getSystem().getByDom(t).toOptional()}))},cd=/* */Object.freeze({__proto__:null,dehighlightAll:function(t,n,e){return td(t,n,0,[])},dehighlight:function(t,n,e,o){ed(t,n,e,o)&&(Pi(o.element,n.highlightClass),n.onDehighlight(t,o),Yo(o,Xo()))},highlight:nd,highlightFirst:function(n,e,o){rd(n,e).each(function(t){nd(n,e,o,t)})},highlightLast:function(n,e,o){id(n,e).each(function(t){nd(n,e,o,t)})},highlightAt:function(n,e,o,t){od(n,e,o,t).fold(function(t){throw t},function(t){nd(n,e,o,t)})},highlightBy:function(n,e,o,t){var r=ad(n,e);L(r,t).each(function(t){nd(n,e,o,t)})},isHighlighted:ed,getHighlighted:function(n,t,e){return qu(n.element,"."+t.highlightClass).bind(function(t){return n.getSystem().getByDom(t).toOptional()})},getFirst:rd,getLast:id,getPrevious:function(t,n,e){return ud(t,n,0,-1)},getNext:function(t,n,e){return ud(t,n,0,1)},getCandidates:ad}),sd=[Ln("highlightClass"),Ln("itemClass"),ma("onHighlight"),ma("onDehighlight")],ld=uc({fields:sd,name:"highlighting",apis:cd}),fd=[8],dd=[9],md=[13],gd=[27],pd=[32],hd=[37],vd=[38],bd=[39],yd=[40],xd=function(t,n,e){var o=G(t.slice(0,n)),r=G(t.slice(n+1));return L(o.concat(r),e)},wd=function(t,n,e){var o=G(t.slice(0,n));return L(o,e)},Sd=function(t,n,e){var o=t.slice(0,n),r=t.slice(n+1);return L(r.concat(o),e)},kd=function(t,n,e){var o=t.slice(n+1);return L(o,e)},Cd=function(e){return function(t){var n=t.raw;return M(e,n.which)}},Od=function(t){return function(n){return W(t,function(t){return t(n)})}},_d=function(t){return!0===t.raw.shiftKey},Td=function(t){return!0===t.raw.ctrlKey},Ed=C(_d),Dd=function(t,n){return{matches:t,classification:n}},Bd=function(t,n,e){n.exists(function(n){return e.exists(function(t){return Ge(t,n)})})||qo(t,Lo(),{prevFocus:n,newFocus:e})},Ad=function(){var r=function(t){return gc(t.element)};return{get:r,set:function(t,n){var e=r(t);t.getSystem().triggerFocus(n,t.element);var o=r(t);Bd(t,e,o)}}},Md=function(){var r=function(t){return ld.getHighlighted(t).map(function(t){return t.element})};return{get:r,set:function(n,t){var e=r(n);n.getSystem().getByDom(t).fold(ct,function(t){ld.highlight(n,t)});var o=r(n);Bd(n,e,o)}}};(Gc=Wc=Wc||{}).OnFocusMode="onFocus",Gc.OnEnterOrSpaceMode="onEnterOrSpace",Gc.OnApiMode="onApi";var Fd,Id=function(t,n,e,o,a){var c=function(n,e,t,o,r){var i,u,a=t(n,e,o,r);return i=a,u=e.event,L(i,function(t){return t.matches(u)}).map(function(t){return t.classification}).bind(function(t){return t(n,e,o,r)})},r={schema:function(){return t.concat([ne("focusManager",Ad()),ee("focusInside","onFocus",Cn(function(t){return M(["onFocus","onEnterOrSpace","onApi"],t)?pt.value(t):pt.error("Invalid value for focusInside")})),va("handler",r),va("state",n),va("sendFocusIn",a)])},processKey:c,toEvents:function(i,u){var t=i.focusInside!==Wc.OnFocusMode?dt.none():a(i).map(function(e){return er(So(),function(t,n){e(t,i,u),n.stop()})}),n=[er(mo(),function(o,r){c(o,r,e,i,u).fold(function(){var n,e,t;n=o,e=r,t=Cd(pd.concat(md))(e.event),i.focusInside===Wc.OnEnterOrSpaceMode&&t&&$e(n,e)&&a(i).each(function(t){t(n,i,u),e.stop()})},function(t){r.stop()})}),er(go(),function(t,n){c(t,n,o,i,u).each(function(t){n.stop()})})];return Zo(t.toArray().concat(n))}};return r},Rd=function(t){var n=[Kn("onEscape"),Kn("onEnter"),ne("selector",'[data-alloy-tabstop="true"]:not(:disabled)'),ne("firstTabstop",0),ne("useTabstopAt",O),Kn("visibilitySelector")].concat([t]),u=function(t,n){var e=t.visibilitySelector.bind(function(t){return Ku(n,t)}).getOr(n);return 0<vu(e)},e=function(n,e,t){var o,r,i;o=e,r=Os(n.element,o.selector),i=H(r,function(t){return u(o,t)}),dt.from(i[o.firstTabstop]).each(function(t){e.focusManager.set(n,t)})},a=function(n,t,e,o,r){return r(t,e,function(t){return u(n=o,e=t)&&n.useTabstopAt(e);var n,e}).fold(function(){return o.cyclic?dt.some(!0):dt.none()},function(t){return o.focusManager.set(n,t),dt.some(!0)})},r=function(n,t,e,o){var r,i,u=Os(n.element,e.selector);return r=n,(i=e).focusManager.get(r).bind(function(t){return Ku(t,i.selector)}).bind(function(t){return j(u,k(Ge,t)).bind(function(t){return a(n,u,t,e,o)})})},o=st([Dd(Od([_d,Cd(dd)]),function(t,n,e){var o=e.cyclic?xd:wd;return r(t,0,e,o)}),Dd(Cd(dd),function(t,n,e){var o=e.cyclic?Sd:kd;return r(t,0,e,o)}),Dd(Cd(gd),function(n,e,t){return t.onEscape.bind(function(t){return t(n,e)})}),Dd(Od([Ed,Cd(md)]),function(n,e,t){return t.onEnter.bind(function(t){return t(n,e)})})]),i=st([]);return Id(n,yi.init,o,i,function(){return dt.some(e)})},Vd=Rd(le("cyclic",l)),Pd=Rd(le("cyclic",O)),Hd=function(t,n,e){return Jf(e)&&Cd(pd)(n.event)?dt.none():(Jo(t,e,_o()),dt.some(!0))},zd=function(t,n){return dt.some(!0)},Nd=[ne("execute",Hd),ne("useSpace",!1),ne("useEnter",!0),ne("useControlEnter",!1),ne("useDown",!1)],Ld=function(t,n,e){return e.execute(t,n,t.element)},jd=Id(Nd,yi.init,function(t,n,e,o){var r=e.useSpace&&!Jf(t.element)?pd:[],i=e.useEnter?md:[],u=e.useDown?yd:[],a=r.concat(i).concat(u);return[Dd(Cd(a),Ld)].concat(e.useControlEnter?[Dd(Od([Td,Cd(md)]),Ld)]:[])},function(t,n,e,o){return e.useSpace&&!Jf(t.element)?[Dd(Cd(pd),zd)]:[]},function(){return dt.none()}),Ud=function(){var e=fe(dt.none());return xi({readState:function(){return e.get().map(function(t){return{numRows:String(t.numRows),numColumns:String(t.numColumns)}}).getOr({numRows:"?",numColumns:"?"})},setGridSize:function(t,n){e.set(dt.some({numRows:t,numColumns:n}))},getNumRows:function(){return e.get().map(function(t){return t.numRows})},getNumColumns:function(){return e.get().map(function(t){return t.numColumns})}})},Wd=/* */Object.freeze({__proto__:null,flatgrid:Ud,init:function(t){return t.state(t)}}),Gd=function(i){return function(t,n,e,o){var r=i(t.element);return Kd(r,t,n,e,o)}},Xd=function(t,n){var e=zc(t,n);return Gd(e)},Yd=function(t,n){var e=zc(n,t);return Gd(e)},qd=function(r){return function(t,n,e,o){return Kd(r,t,n,e,o)}},Kd=function(n,e,t,o,r){return o.focusManager.get(e).bind(function(t){return n(e.element,t,o,r)}).map(function(t){return o.focusManager.set(e,t),!0})},Jd=qd,$d=qd,Qd=qd,Zd=function(t){return!((n=t.dom).offsetWidth<=0&&n.offsetHeight<=0);var n},tm=function(t,n,e){var o,r=Os(t,e),i=H(r,Zd);return j(o=i,function(t){return Ge(t,n)}).map(function(t){return{index:t,candidates:o}})},nm=function(t,n){return j(t,function(t){return Ge(n,t)})},em=function(e,t,o,n){return n(Math.floor(t/o),t%o).bind(function(t){var n=t.row*o+t.column;return 0<=n&&n<e.length?dt.some(e[n]):dt.none()})},om=function(r,t,i,u,a){return em(r,t,u,function(t,n){var e=t===i-1?r.length-t*u:u,o=Cc(n,a,0,e-1);return dt.some({row:t,column:o})})},rm=function(i,t,u,a,c){return em(i,t,a,function(t,n){var e=Cc(t,c,0,u-1),o=e===u-1?i.length-e*a:a,r=Oc(n,0,o-1);return dt.some({row:e,column:r})})},im=[Ln("selector"),ne("execute",Hd),ga("onEscape"),ne("captureTab",!1),ya()],um=function(n,e,t){qu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})},am=function(r){return function(t,n,e,o){return tm(t,n,e.selector).bind(function(t){return r(t.candidates,t.index,o.getNumRows().getOr(e.initSize.numRows),o.getNumColumns().getOr(e.initSize.numColumns))})}},cm=function(t,n,e){return e.captureTab?dt.some(!0):dt.none()},sm=am(function(t,n,e,o){return om(t,n,e,o,-1)}),lm=am(function(t,n,e,o){return om(t,n,e,o,1)}),fm=am(function(t,n,e,o){return rm(t,n,e,o,-1)}),dm=am(function(t,n,e,o){return rm(t,n,e,o,1)}),mm=st([Dd(Cd(hd),Xd(sm,lm)),Dd(Cd(bd),Yd(sm,lm)),Dd(Cd(vd),Jd(fm)),Dd(Cd(yd),$d(dm)),Dd(Od([_d,Cd(dd)]),cm),Dd(Od([Ed,Cd(dd)]),cm),Dd(Cd(gd),function(t,n,e){return e.onEscape(t,n)}),Dd(Cd(pd.concat(md)),function(n,e,o,t){return r=n,(i=o).focusManager.get(r).bind(function(t){return Ku(t,i.selector)}).bind(function(t){return o.execute(n,e,t)});var r,i})]),gm=st([Dd(Cd(pd),zd)]),pm=Id(im,Ud,mm,gm,function(){return dt.some(um)}),hm=function(t,n,e,i){var u=function(t,n,e){var o,r=Cc(n,i,0,e.length-1);return r===t?dt.none():(o=e[r],"button"===mr(o)&&"disabled"===qr(o,"disabled")?u(t,r,e):dt.from(e[r]))};return tm(t,e,n).bind(function(t){var n=t.index,e=t.candidates;return u(n,n,e)})},vm=[Ln("selector"),ne("getInitial",dt.none),ne("execute",Hd),ga("onEscape"),ne("executeOnMove",!1),ne("allowVertical",!0)],bm=function(n,e,o){return t=n,(r=o).focusManager.get(t).bind(function(t){return Ku(t,r.selector)}).bind(function(t){return o.execute(n,e,t)});var t,r},ym=function(n,e,t){e.getInitial(n).orThunk(function(){return qu(n.element,e.selector)}).each(function(t){e.focusManager.set(n,t)})},xm=function(t,n,e){return hm(t,e.selector,n,-1)},wm=function(t,n,e){return hm(t,e.selector,n,1)},Sm=function(r){return function(t,n,e,o){return r(t,n,e,o).bind(function(){return e.executeOnMove?bm(t,n,e):dt.some(!0)})}},km=function(t,n,e){return e.onEscape(t,n)},Cm=st([Dd(Cd(pd),zd)]),Om=Id(vm,yi.init,function(t,n,e,o){var r=hd.concat(e.allowVertical?vd:[]),i=bd.concat(e.allowVertical?yd:[]);return[Dd(Cd(r),Sm(Xd(xm,wm))),Dd(Cd(i),Sm(Yd(xm,wm))),Dd(Cd(md),bm),Dd(Cd(pd),bm),Dd(Cd(gd),km)]},Cm,function(){return dt.some(ym)}),_m=function(t,n,e){return dt.from(t[n]).bind(function(t){return dt.from(t[e]).map(function(t){return{rowIndex:n,columnIndex:e,cell:t}})})},Tm=function(t,n,e,o){var r=t[n].length,i=Cc(e,o,0,r-1);return _m(t,n,i)},Em=function(t,n,e,o){var r=Cc(e,o,0,t.length-1),i=t[r].length,u=Oc(n,0,i-1);return _m(t,r,u)},Dm=function(t,n,e,o){var r=t[n].length,i=Oc(e+o,0,r-1);return _m(t,n,i)},Bm=function(t,n,e,o){var r=Oc(e+o,0,t.length-1),i=t[r].length,u=Oc(n,0,i-1);return _m(t,r,u)},Am=[Xn("selectors",[Ln("row"),Ln("cell")]),ne("cycles",!0),ne("previousSelector",dt.none),ne("execute",Hd)],Mm=function(n,e,t){e.previousSelector(n).orThunk(function(){var t=e.selectors;return qu(n.element,t.cell)}).each(function(t){e.focusManager.set(n,t)})},Fm=function(t,n){return function(e,o,i){var u=i.cycles?t:n;return Ku(o,i.selectors.row).bind(function(t){var n=Os(t,i.selectors.cell);return nm(n,o).bind(function(o){var r=Os(e,i.selectors.row);return nm(r,t).bind(function(t){var n,e=(n=i,V(r,function(t){return Os(t,n.selectors.cell)}));return u(e,t,o).map(function(t){return t.cell})})})})}},Im=Fm(function(t,n,e){return Tm(t,n,e,-1)},function(t,n,e){return Dm(t,n,e,-1)}),Rm=Fm(function(t,n,e){return Tm(t,n,e,1)},function(t,n,e){return Dm(t,n,e,1)}),Vm=Fm(function(t,n,e){return Em(t,e,n,-1)},function(t,n,e){return Bm(t,e,n,-1)}),Pm=Fm(function(t,n,e){return Em(t,e,n,1)},function(t,n,e){return Bm(t,e,n,1)}),Hm=st([Dd(Cd(hd),Xd(Im,Rm)),Dd(Cd(bd),Yd(Im,Rm)),Dd(Cd(vd),Jd(Vm)),Dd(Cd(yd),$d(Pm)),Dd(Cd(pd.concat(md)),function(n,e,o){return gc(n.element).bind(function(t){return o.execute(n,e,t)})})]),zm=st([Dd(Cd(pd),zd)]),Nm=Id(Am,yi.init,Hm,zm,function(){return dt.some(Mm)}),Lm=[Ln("selector"),ne("execute",Hd),ne("moveOnTab",!1)],jm=function(n,e,o){return o.focusManager.get(n).bind(function(t){return o.execute(n,e,t)})},Um=function(n,e,t){qu(n.element,e.selector).each(function(t){e.focusManager.set(n,t)})},Wm=function(t,n,e){return hm(t,e.selector,n,-1)},Gm=function(t,n,e){return hm(t,e.selector,n,1)},Xm=st([Dd(Cd(vd),Qd(Wm)),Dd(Cd(yd),Qd(Gm)),Dd(Od([_d,Cd(dd)]),function(t,n,e,o){return e.moveOnTab?Qd(Wm)(t,n,e,o):dt.none()}),Dd(Od([Ed,Cd(dd)]),function(t,n,e,o){return e.moveOnTab?Qd(Gm)(t,n,e,o):dt.none()}),Dd(Cd(md),jm),Dd(Cd(pd),jm)]),Ym=st([Dd(Cd(pd),zd)]),qm=Id(Lm,yi.init,Xm,Ym,function(){return dt.some(Um)}),Km=[ga("onSpace"),ga("onEnter"),ga("onShiftEnter"),ga("onLeft"),ga("onRight"),ga("onTab"),ga("onShiftTab"),ga("onUp"),ga("onDown"),ga("onEscape"),ne("stopSpaceKeyup",!1),Kn("focusIn")],Jm=Id(Km,yi.init,function(t,n,e){return[Dd(Cd(pd),e.onSpace),Dd(Od([Ed,Cd(md)]),e.onEnter),Dd(Od([_d,Cd(md)]),e.onShiftEnter),Dd(Od([_d,Cd(dd)]),e.onShiftTab),Dd(Od([Ed,Cd(dd)]),e.onTab),Dd(Cd(vd),e.onUp),Dd(Cd(yd),e.onDown),Dd(Cd(hd),e.onLeft),Dd(Cd(bd),e.onRight),Dd(Cd(pd),e.onSpace),Dd(Cd(gd),e.onEscape)]},function(t,n,e){return e.stopSpaceKeyup?[Dd(Cd(pd),zd)]:[]},function(t){return t.focusIn}),$m=Vd.schema(),Qm=Pd.schema(),Zm=Om.schema(),tg=pm.schema(),ng=Nm.schema(),eg=jd.schema(),og=qm.schema(),rg=Jm.schema(),ig=cc({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,acyclic:$m,cyclic:Qm,flow:Zm,flatgrid:tg,matrix:ng,execution:eg,menu:og,special:rg}),name:"keying",active:{events:function(t,n){return t.handler.toEvents(t,n)}},apis:{focusIn:function(n,e,o){e.sendFocusIn(e).fold(function(){n.getSystem().triggerFocus(n.element,n.element)},function(t){t(n,e,o)})},setGridSize:function(t,n,e,o,r){Rt(e,"setGridSize")?e.setGridSize(o,r):console.error("Layout does not support setGridSize")}},state:Wd}),ug=function(t,n,e,o){var r=t.getSystem().build(o);il(t,r,e)},ag=function(t,n,e,o){var r=cg(t);L(r,function(t){return Ge(o.element,t.element)}).each(al)},cg=function(t,n){return t.components()},sg=function(n,t,e,r,o){var i=cg(n);return dt.from(i[r]).map(function(t){return ag(n,0,0,t),o.each(function(t){ug(n,0,function(t,n){var e,o;o=n,Tr(e=t,r).fold(function(){zr(e,o)},function(t){Vr(t,o)})},t)}),t})},lg=uc({fields:[],name:"replacing",apis:/* */Object.freeze({__proto__:null,append:function(t,n,e,o){ug(t,0,zr,o)},prepend:function(t,n,e,o){ug(t,0,Hr,o)},remove:ag,replaceAt:sg,replaceBy:function(n,t,e,o,r){var i=cg(n);return j(i,o).bind(function(t){return sg(n,0,0,t,r)})},set:function(n,t,e,o){pc(function(){var t=V(o,n.getSystem().build);ol(n,t)},n.element)},contents:cg})}),fg=function(t,n){var e,o;return{key:t,value:{config:{},me:(e=t,o=Zo(n),uc({fields:[Ln("enabled")],name:e,active:{events:st(o)}})),configAsRaw:st({}),initialConfig:{},state:yi}}},dg=function(t,n){n.ignore||(dc(t.element),n.onFocus(t))},mg=/* */Object.freeze({__proto__:null,focus:dg,blur:function(t,n){n.ignore||t.element.dom.blur()},isFocused:function(t){return n=t.element,e=Ar(n).dom,n.dom===e.activeElement;var n,e}}),gg=/* */Object.freeze({__proto__:null,exhibit:function(t,n){var e=n.ignore?{}:{attributes:{tabindex:"-1"}};return Si(e)},events:function(e){return Zo([er(So(),function(t,n){dg(t,e),n.stop()})].concat(e.stopMousedown?[er(io(),function(t,n){n.event.prevent()})]:[]))}}),pg=[ma("onFocus"),ne("stopMousedown",!1),ne("ignore",!1)],hg=uc({fields:pg,name:"focusing",active:gg,apis:mg}),vg=function(t,n,e){var o=n.aria;o.update(t,o,e.get())},bg=function(n,t,e){t.toggleClass.each(function(t){(e.get()?Ri:Pi)(n.element,t)})},yg=function(t,n,e){Sg(t,n,e,!e.get())},xg=function(t,n,e){e.set(!0),bg(t,n,e),vg(t,n,e)},wg=function(t,n,e){e.set(!1),bg(t,n,e),vg(t,n,e)},Sg=function(t,n,e,o){(o?xg:wg)(t,n,e)},kg=function(t,n,e){Sg(t,n,e,n.selected)},Cg=/* */Object.freeze({__proto__:null,onLoad:kg,toggle:yg,isOn:function(t,n,e){return e.get()},on:xg,off:wg,set:Sg}),Og=/* */Object.freeze({__proto__:null,exhibit:function(){return Si({})},events:function(t,n){var e,o,r,i=(e=t,o=n,r=yg,dr(function(t){r(t,e,o)})),u=Za(t,n,kg);return Zo(gt([t.toggleOnExecute?[i]:[],[u]]))}}),_g=function(t,n,e){Yr(t.element,"aria-expanded",e)},Tg=[ne("selected",!1),Kn("toggleClass"),ne("toggleOnExecute",!0),ee("aria",{mode:"none"},An("mode",{pressed:[ne("syncWithExpanded",!1),va("update",function(t,n,e){Yr(t.element,"aria-pressed",e),n.syncWithExpanded&&_g(t,n,e)})],checked:[va("update",function(t,n,e){Yr(t.element,"aria-checked",e)})],expanded:[va("update",_g)],selected:[va("update",function(t,n,e){Yr(t.element,"aria-selected",e)})],none:[va("update",ct)]}))],Eg=uc({fields:Tg,name:"toggling",active:Og,apis:Cg,state:(Fd=!1,{init:function(){var n=fe(Fd);return{get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(Fd)},readState:function(){return n.get()}}}})}),Dg=function(){var t=function(t,n){n.stop(),Ko(t)};return[er(vo(),t),er(Eo(),t),ar(no()),ar(io())]},Bg=function(t){return Zo(gt([t.map(function(e){return dr(function(t,n){e(t),n.stop()})}).toArray(),Dg()]))},Ag="alloy.item-hover",Mg="alloy.item-focus",Fg=function(t){(gc(t.element).isNone()||hg.isFocused(t))&&(hg.isFocused(t)||hg.focus(t),qo(t,Ag,{item:t}))},Ig=function(t){qo(t,Mg,{item:t})},Rg=st(Ag),Vg=st(Mg),Pg=[Ln("data"),Ln("components"),Ln("dom"),ne("hasSubmenu",!1),Kn("toggling"),Gl("itemBehaviours",[Eg,hg,ig,Ll]),ne("ignoreFocus",!1),ne("domModification",{}),va("builder",function(t){return{dom:t.dom,domModification:ft(ft({},t.domModification),{attributes:ft(ft(ft({role:t.toggling.isSome()?"menuitemcheckbox":"menuitem"},t.domModification.attributes),{"aria-haspopup":t.hasSubmenu}),t.hasSubmenu?{"aria-expanded":!1}:{})}),behaviours:Xl(t.itemBehaviours,[t.toggling.fold(Eg.revoke,function(t){return Eg.config(ft({aria:{mode:"checked"}},t))}),hg.config({ignore:t.ignoreFocus,stopMousedown:t.ignoreFocus,onFocus:function(t){Ig(t)}}),ig.config({mode:"execution"}),Ll.config({store:{mode:"memory",initialValue:t.data}}),fg("item-type-events",T(Dg(),[er(so(),Fg),er(To(),hg.focus)]))]),components:t.components,eventOrder:t.eventOrder}}),ne("eventOrder",{})],Hg=[Ln("dom"),Ln("components"),va("builder",function(t){return{dom:t.dom,components:t.components,events:Zo([(n=To(),er(n,function(t,n){n.stop()}))])};var n})],zg=st("item-widget"),Ng=st([bf({name:"widget",overrides:function(n){return{behaviours:rc([Ll.config({store:{mode:"manual",getValue:function(t){return n.data},setValue:ct}})])}}})]),Lg=[Ln("uid"),Ln("data"),Ln("components"),Ln("dom"),ne("autofocus",!1),ne("ignoreFocus",!1),Gl("widgetBehaviours",[Ll,hg,ig]),ne("domModification",{}),zf(Ng()),va("builder",function(e){var t=Bf(zg(),e,Ng()),n=Af(zg(),e,t.internals()),o=function(t){return Mf(t,e,"widget").map(function(t){return ig.focusIn(t),t})},r=function(t,n){return Jf(n.event.target)||e.autofocus&&n.setSource(t.element),dt.none()};return{dom:e.dom,components:n,domModification:e.domModification,events:Zo([dr(function(t,n){o(t).each(function(t){n.stop()})}),er(so(),Fg),er(To(),function(t,n){e.autofocus?o(t):hg.focus(t)})]),behaviours:Xl(e.widgetBehaviours,[Ll.config({store:{mode:"memory",initialValue:e.data}}),hg.config({ignore:e.ignoreFocus,onFocus:function(t){Ig(t)}}),ig.config({mode:"special",focusIn:e.autofocus?function(t){o(t)}:sc(),onLeft:r,onRight:r,onEscape:function(t,n){return hg.isFocused(t)||e.autofocus?(e.autofocus&&n.setSource(t.element),dt.none()):(hg.focus(t),dt.some(!0))}})])}})],jg=An("type",{widget:Lg,item:Pg,separator:Hg}),Ug=st([wf({factory:{sketch:function(t){var n=En("menu.spec item",jg,t);return n.builder(n)}},name:"items",unit:"item",defaults:function(t,n){return n.hasOwnProperty("uid")?n:ft(ft({},n),{uid:li("item")})},overrides:function(t,n){return{type:n.type,ignoreFocus:t.fakeFocus,domModification:{classes:[t.markers.item]}}}})]),Wg=st([Ln("value"),Ln("items"),Ln("dom"),Ln("components"),ne("eventOrder",{}),jl("menuBehaviours",[ld,Ll,Zf,ig]),ee("movement",{mode:"menu",moveOnTab:!0},An("mode",{grid:[ya(),va("config",function(t,n){return{mode:"flatgrid",selector:"."+t.markers.item,initSize:{numColumns:n.initSize.numColumns,numRows:n.initSize.numRows},focusManager:t.focusManager}})],matrix:[va("config",function(t,n){return{mode:"matrix",selectors:{row:n.rowSelector,cell:"."+t.markers.item},focusManager:t.focusManager}}),Ln("rowSelector")],menu:[ne("moveOnTab",!0),va("config",function(t,n){return{mode:"menu",selector:"."+t.markers.item,moveOnTab:n.moveOnTab,focusManager:t.focusManager}})]})),jn("markers",ca()),ne("fakeFocus",!1),ne("focusManager",Ad()),ma("onHighlight")]),Gg=st("alloy.menu-focus"),Xg=Yf({name:"Menu",configFields:Wg(),partFields:Ug(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,markers:t.markers,behaviours:Wl(t.menuBehaviours,[ld.config({highlightClass:t.markers.selectedItem,itemClass:t.markers.item,onHighlight:t.onHighlight}),Ll.config({store:{mode:"memory",initialValue:t.value}}),Zf.config({find:dt.some}),ig.config(t.movement.config(t,t.movement))]),events:Zo([er(Vg(),function(n,e){var t=e.event;n.getSystem().getByDom(t.target).each(function(t){ld.highlight(n,t),e.stop(),qo(n,Gg(),{menu:n,item:t})})}),er(Rg(),function(t,n){var e=n.event.item;ld.highlight(t,e)})]),components:n,eventOrder:t.eventOrder,domModification:{attributes:{role:"menu"}}}}}),Yg=function(e,o,r,t){return Ft(r,t).bind(function(t){return Ft(e,t).bind(function(t){var n=Yg(e,o,r,t);return dt.some([t].concat(n))})}).getOr([])},qg=function(t,n){var e={};_t(t,function(t,n){mt(t,function(t){e[t]=n})});var o=n,r=Et(n,function(t,n){return{k:t,v:n}}),i=Tt(r,function(t,n){return[n].concat(Yg(e,o,r,n))});return Tt(e,function(t){return Ft(i,t).getOr([t])})},Kg=function(t){return"prepared"===t.type?dt.some(t.menu):dt.none()},Jg={init:function(){var i=fe({}),u=fe({}),a=fe({}),c=fe(dt.none()),s=fe({}),r=function(t,o,r){return e(t).bind(function(n){return e=t,At(i.get(),function(t,n){return t===e}).bind(function(t){return o(t).map(function(t){return{triggeredMenu:n,triggeringItem:t,triggeringPath:r}})});var e})},e=function(t){return n(t).bind(Kg)},n=function(t){return Ft(u.get(),t)},l=function(t){return Ft(i.get(),t)};return{setMenuBuilt:function(t,n){var e;u.set(ft(ft({},u.get()),((e={})[t]={type:"prepared",menu:n},e)))},setContents:function(t,n,e,o){c.set(dt.some(t)),i.set(e),u.set(n),s.set(o);var r=qg(o,e);a.set(r)},expand:function(e){return Ft(i.get(),e).map(function(t){var n=Ft(a.get(),e).getOr([]);return[t].concat(n)})},refresh:function(t){return Ft(a.get(),t)},collapse:function(t){return Ft(a.get(),t).bind(function(t){return 1<t.length?dt.some(t.slice(1)):dt.none()})},lookupMenu:n,lookupItem:l,otherMenus:function(t){var n=s.get();return X(Ct(n),t)},getPrimary:function(){return c.get().bind(e)},getMenus:function(){return u.get()},clear:function(){i.set({}),u.set({}),a.set({}),c.set(dt.none())},isClear:function(){return c.get().isNone()},getTriggeringPath:function(t,o){var n=H(l(t).toArray(),function(t){return e(t).isSome()});return Ft(a.get(),t).bind(function(t){var e=G(n.concat(t));return function(t){for(var n=[],e=0;e<t.length;e++){var o=t[e];if(!o.isSome())return dt.none();n.push(o.getOrDie())}return dt.some(n)}(U(e,function(t,n){return r(t,o,e.slice(0,n+1)).fold(function(){return c.get().is(t)?[]:[dt.none()]},function(t){return[dt.some(t)]})}))})}}},extractPreparedMenu:Kg},$g=st("collapse-item"),Qg=Xf({name:"TieredMenu",configFields:[ha("onExecute"),ha("onEscape"),pa("onOpenMenu"),pa("onOpenSubmenu"),ma("onRepositionMenu"),ma("onCollapseMenu"),ne("highlightImmediately",!0),Xn("data",[Ln("primary"),Ln("menus"),Ln("expansions")]),ne("fakeFocus",!1),ma("onHighlight"),ma("onHover"),la(),Ln("dom"),ne("navigateOnHover",!0),ne("stayInDom",!1),jl("tmenuBehaviours",[ig,ld,Zf,lg]),ne("eventOrder",{})],apis:{collapseMenu:function(t,n){t.collapseMenu(n)},highlightPrimary:function(t,n){t.highlightPrimary(n)},repositionMenus:function(t,n){t.repositionMenus(n)}},factory:function(a,t){var c,n,i=fe(dt.none()),s=Jg.init(),e=function(t){var o,r,n,e=(o=t,r=a.data.primary,n=a.data.menus,Tt(n,function(t,n){var e=function(){return Xg.sketch(ft(ft({},t),{value:n,markers:a.markers,fakeFocus:a.fakeFocus,onHighlight:a.onHighlight,focusManager:(a.fakeFocus?Md:Ad)()}))};return n===r?{type:"prepared",menu:o.getSystem().build(e())}:{type:"notbuilt",nbMenu:e}})),i=u();return s.setContents(a.data.primary,e,a.data.expansions,i),s.getPrimary()},l=function(t){return Ll.getValue(t).value},u=function(t){return Tt(a.data.menus,function(t,n){return U(t.items,function(t){return"separator"===t.type?[]:[t.data.value]})})},f=function(n,t){ld.highlight(n,t),ld.getHighlighted(t).orThunk(function(){return ld.getFirst(t)}).each(function(t){Jo(n,t.element,To())})},d=function(n,t){return qf(V(t,function(t){return n.lookupMenu(t).bind(function(t){return"prepared"===t.type?dt.some(t.menu):dt.none()})}))},m=function(n,t,e){var o=d(t,t.otherMenus(e));mt(o,function(t){Ni(t.element,[a.markers.backgroundMenu]),a.stayInDom||lg.remove(n,t)})},g=function(t,o){var r,n=(r=t,i.get().getOrThunk(function(){var e={},t=Os(r.element,"."+a.markers.item),n=H(t,function(t){return"true"===qr(t,"aria-haspopup")});return mt(n,function(t){r.getSystem().getByDom(t).each(function(t){var n=l(t);e[n]=t})}),i.set(dt.some(e)),e}));_t(n,function(t,n){var e=M(o,n);Yr(t.element,"aria-expanded",e)})},p=function(o,r,i){return dt.from(i[0]).bind(function(t){return r.lookupMenu(t).bind(function(t){if("notbuilt"===t.type)return dt.none();var n=t.menu,e=d(r,i.slice(1));return mt(e,function(t){Ri(t.element,a.markers.backgroundMenu)}),ji(n.element)||lg.append(o,gu(n)),Ni(n.element,[a.markers.backgroundMenu]),f(o,n),m(o,r,i),dt.some(n)})})};(n=c=c||{})[n.HighlightSubmenu=0]="HighlightSubmenu",n[n.HighlightParent=1]="HighlightParent";var h=function(r,i,u){void 0===u&&(u=c.HighlightSubmenu);var t=l(i);return s.expand(t).bind(function(o){return g(r,o),dt.from(o[0]).bind(function(e){return s.lookupMenu(e).bind(function(t){var n=function(t,n,e){if("notbuilt"!==e.type)return e.menu;var o=t.getSystem().build(e.nbMenu());return s.setMenuBuilt(n,o),o}(r,e,t);return ji(n.element)||lg.append(r,gu(n)),a.onOpenSubmenu(r,i,n,G(o)),u===c.HighlightSubmenu?(ld.highlightFirst(n),p(r,s,o)):(ld.dehighlightAll(n),dt.some(i))})})})},o=function(n,e){var t=l(e);return s.collapse(t).bind(function(t){return g(n,t),p(n,s,t).map(function(t){return a.onCollapseMenu(n,e,t),t})})},r=function(e){return function(n,t){return Ku(t.getSource(),"."+a.markers.item).bind(function(t){return n.getSystem().getByDom(t).toOptional().bind(function(t){return e(n,t).map(O)})})}},v=Zo([er(Gg(),function(e,o){var t=o.event.item;s.lookupItem(l(t)).each(function(){var t=o.event.menu;ld.highlight(e,t);var n=l(o.event.item);s.refresh(n).each(function(t){return m(e,s,t)})})}),dr(function(n,t){var e=t.event.target;n.getSystem().getByDom(e).each(function(t){0===l(t).indexOf("collapse-item")&&o(n,t),h(n,t,c.HighlightSubmenu).fold(function(){a.onExecute(n,t)},ct)})}),sr(function(n,t){e(n).each(function(t){lg.append(n,gu(t)),a.onOpenMenu(n,t),a.highlightImmediately&&f(n,t)})})].concat(a.navigateOnHover?[er(Rg(),function(t,n){var e,o,r=n.event.item;e=t,o=l(r),s.refresh(o).bind(function(t){return g(e,t),p(e,s,t)}),h(t,r,c.HighlightParent),a.onHover(t,r)})]:[])),b=function(t){return ld.getHighlighted(t).bind(ld.getHighlighted)},y={collapseMenu:function(n){b(n).each(function(t){o(n,t)})},highlightPrimary:function(n){s.getPrimary().each(function(t){f(n,t)})},repositionMenus:function(o){s.getPrimary().bind(function(n){return b(o).bind(function(t){var n=l(t),e=Mt(s.getMenus()),o=qf(V(e,Jg.extractPreparedMenu));return s.getTriggeringPath(n,function(t){return e=t,Q(o,function(t){if(!t.getSystem().isConnected())return dt.none();var n=ld.getCandidates(t);return L(n,function(t){return l(t)===e})});var e})}).map(function(t){return{primary:n,triggeringPath:t}})}).fold(function(){var t;t=o,dt.from(t.components()[0]).filter(function(t){return"menu"===qr(t.element,"role")}).each(function(t){a.onRepositionMenu(o,t,[])})},function(t){var n=t.primary,e=t.triggeringPath;a.onRepositionMenu(o,n,e)})}};return{uid:a.uid,dom:a.dom,markers:a.markers,behaviours:Wl(a.tmenuBehaviours,[ig.config({mode:"special",onRight:r(function(t,n){return Jf(n.element)?dt.none():h(t,n,c.HighlightSubmenu)}),onLeft:r(function(t,n){return Jf(n.element)?dt.none():o(t,n)}),onEscape:r(function(t,n){return o(t,n).orThunk(function(){return a.onEscape(t,n).map(function(){return t})})}),focusIn:function(n,t){s.getPrimary().each(function(t){Jo(n,t.element,To())})}}),ld.config({highlightClass:a.markers.selectedMenu,itemClass:a.markers.menu}),Zf.config({find:function(t){return ld.getHighlighted(t)}}),lg.config({})]),eventOrder:a.eventOrder,apis:y,events:v}},extraApis:{tieredData:function(t,n,e){return{primary:t,menus:n,expansions:e}},singleData:function(t,n){return{primary:t,menus:Jt(t,n),expansions:{}}},collapseItem:function(t){return{value:oi($g()),meta:{text:t}}}}}),Zg=Xf({name:"InlineView",configFields:[Ln("lazySink"),ma("onShow"),ma("onHide"),Zn("onEscape"),jl("inlineBehaviours",[wl,Ll,lc]),te("fireDismissalEventInstead",[ne("event",zo())]),te("fireRepositionEventInstead",[ne("event",No())]),ne("getRelated",dt.none),ne("isExtraPart",l),ne("eventOrder",dt.none)],factory:function(m,t){var o=function(t,n,e,o){r(t,n,e,function(){return o.map(Hu)})},r=function(t,n,e,o){var r=m.lazySink(t).getOrDie();wl.openWhileCloaked(t,e,function(){return Zs.positionWithinBounds(r,n,t,o())}),Ll.setValue(t,dt.some({mode:"position",anchor:n,getBounds:o}))},i=function(t,n,e,o){var r,i,u,a,c,s,l,f,d=(r=m,i=t,u=n,c=o,s=function(){return r.lazySink(i)},l="horizontal"===(a=e).type?{layouts:{onLtr:Ka,onRtl:Ja}}:{},f=function(t){return 2===t.length?l:{}},Qg.sketch({dom:{tag:"div"},data:a.data,markers:a.menu.markers,highlightImmediately:a.menu.highlightImmediately,onEscape:function(){return wl.close(i),r.onEscape.map(function(t){return t(i)}),dt.some(!0)},onExecute:function(){return dt.some(!0)},onOpenMenu:function(t,n){Zs.positionWithinBounds(s().getOrDie(),u,n,c())},onOpenSubmenu:function(t,n,e,o){var r=s().getOrDie();Zs.position(r,ft({anchor:"submenu",item:n},f(o)),e)},onRepositionMenu:function(t,n,e){var o=s().getOrDie();Zs.positionWithinBounds(o,u,n,c()),mt(e,function(t){var n=f(t.triggeringPath);Zs.position(o,ft({anchor:"submenu",item:t.triggeringItem},n),t.triggeredMenu)})}}));wl.open(t,d),Ll.setValue(t,dt.some({mode:"menu",menu:d}))},n=function(e){wl.isOpen(e)&&Ll.getValue(e).each(function(t){switch(t.mode){case"menu":wl.getState(e).each(function(t){Qg.repositionMenus(t)});break;case"position":var n=m.lazySink(e).getOrDie();Zs.positionWithinBounds(n,t.anchor,e,t.getBounds())}})},e={setContent:function(t,n){wl.setContent(t,n)},showAt:function(t,n,e){o(t,n,e,dt.none())},showWithin:o,showWithinBounds:r,showMenuAt:function(t,n,e){i(t,n,e,function(){return dt.none()})},showMenuWithinBounds:i,hide:function(t){wl.isOpen(t)&&(Ll.setValue(t,dt.none()),wl.close(t))},getContent:function(t){return wl.getState(t)},reposition:n,isOpen:wl.isOpen};return{uid:m.uid,dom:m.dom,behaviours:Wl(m.inlineBehaviours,[wl.config({isPartOf:function(t,n,e){return Qu(n,e)||(o=t,r=e,m.getRelated(o).exists(function(t){return Qu(t,r)}));var o,r},getAttachPoint:function(t){return m.lazySink(t).getOrDie()},onOpen:function(t){m.onShow(t)},onClose:function(t){m.onHide(t)}}),Ll.config({store:{mode:"memory",initialValue:dt.none()}}),lc.config({channels:ft(ft({},_l(ft({isExtraPart:t.isExtraPart},m.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),El(ft(ft({},m.fireRepositionEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})),{doReposition:n})))})]),eventOrder:m.eventOrder,apis:e}},apis:{showAt:function(t,n,e,o){t.showAt(n,e,o)},showWithin:function(t,n,e,o,r){t.showWithin(n,e,o,r)},showWithinBounds:function(t,n,e,o,r){t.showWithinBounds(n,e,o,r)},showMenuAt:function(t,n,e,o){t.showMenuAt(n,e,o)},showMenuWithinBounds:function(t,n,e,o,r){t.showMenuWithinBounds(n,e,o,r)},hide:function(t,n){t.hide(n)},isOpen:function(t,n){return t.isOpen(n)},getContent:function(t,n){return t.getContent(n)},setContent:function(t,n,e){t.setContent(n,e)},reposition:function(t,n){t.reposition(n)}}}),tp=function(t){return t.x},np=function(t,n){return t.x+t.width/2-n.width/2},ep=function(t,n){return t.x+t.width-n.width},op=function(t){return t.y},rp=function(t,n){return t.y+t.height-n.height},ip=function(t,n,e){return xa(ep(t,n),rp(t,n),e.innerSoutheast(),Oa(),Ba(t,{right:0,bottom:3}),"layout-inner-se")},up=function(t,n,e){return xa(tp(t),rp(t,n),e.innerSouthwest(),Ca(),Ba(t,{left:1,bottom:3}),"layout-inner-sw")},ap=function(t,n,e){return xa(ep(t,n),op(t),e.innerNortheast(),ka(),Ba(t,{right:0,top:2}),"layout-inner-ne")},cp=function(t,n,e){return xa(tp(t),op(t),e.innerNorthwest(),Sa(),Ba(t,{left:1,top:2}),"layout-inner-nw")},sp=function(t,n,e){return xa(np(t,n),op(t),e.innerNorth(),_a(),Ba(t,{top:2}),"layout-inner-n")},lp=function(t,n,e){return xa(np(t,n),rp(t,n),e.innerSouth(),Ta(),Ba(t,{bottom:3}),"layout-inner-s")},fp=tinymce.util.Tools.resolve("tinymce.util.Delay"),dp=Xf({name:"Button",factory:function(t){var n=Bg(t.action),e=t.dom.tag,o=function(n){return Ft(t.dom,"attributes").bind(function(t){return Ft(t,n)})};return{uid:t.uid,dom:t.dom,components:t.components,events:n,behaviours:Xl(t.buttonBehaviours,[hg.config({}),ig.config({mode:"execution",useSpace:!0,useEnter:!0})]),domModification:{attributes:function(){if("button"!==e)return{role:o("role").getOr("button")};var t=o("type").getOr("button"),n=o("role").map(function(t){return{role:t}}).getOr({});return ft({type:t},n)}()},eventOrder:t.eventOrder}},configFields:[ne("uid",undefined),Ln("dom"),ne("components",[]),Gl("buttonBehaviours",[hg,ig]),Kn("action"),Kn("role"),ne("eventOrder",{})]}),mp=function(t){var n=t.uid!==undefined&&Rt(t,"uid")?t.uid:li("memento");return{get:function(t){return t.getSystem().getByUid(n).getOrDie()},getOpt:function(t){return t.getSystem().getByUid(n).toOptional()},asSpec:function(){return ft(ft({},t),{uid:n})}}},gp=function(t){return dt.from(t()["temporary-placeholder"]).getOr("!not found!")},pp=function(t,n){return dt.from(n()[t.toLowerCase()]).getOrThunk(function(){return gp(n)})},hp={success:"checkmark",error:"warning",err:"error",warning:"warning",warn:"warning",info:"info"},vp=Xf({name:"Notification",factory:function(n){var t,e,o=mp({dom:{tag:"p",innerHtml:n.translationProvider(n.text)},behaviours:rc([lg.config({})])}),r=function(t){return{dom:{tag:"div",classes:["tox-bar"],attributes:{style:"width: "+t+"%"}}}},i=function(t){return{dom:{tag:"div",classes:["tox-text"],innerHtml:t+"%"}}},u=mp({dom:{tag:"div",classes:n.progress?["tox-progress-bar","tox-progress-indicator"]:["tox-progress-bar"]},components:[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(0)]},i(0)],behaviours:rc([lg.config({})])}),a={updateProgress:function(t,n){t.getSystem().isConnected()&&u.getOpt(t).each(function(t){lg.set(t,[{dom:{tag:"div",classes:["tox-bar-container"]},components:[r(n)]},i(n)])})},updateText:function(t,n){var e;t.getSystem().isConnected()&&(e=o.get(t),lg.set(e,[lu(n)]))}},c=gt([n.icon.toArray(),n.level.toArray(),n.level.bind(function(t){return dt.from(hp[t])}).toArray()]),s=mp(dp.sketch({dom:{tag:"button",classes:["tox-notification__dismiss","tox-button","tox-button--naked","tox-button--icon"]},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:pp("close",n.iconProvider),attributes:{"aria-label":n.translationProvider("Close")}}}],action:function(t){n.onAction(t)}})),l=[{dom:{tag:"div",classes:["tox-notification__icon"],innerHtml:(t=c,e=n.iconProvider,Q(t,function(t){return dt.from(e()[t.toLowerCase()])}).getOrThunk(function(){return gp(e)}))}},{dom:{tag:"div",classes:["tox-notification__body"]},components:[o.asSpec()],behaviours:rc([lg.config({})])}];return{uid:n.uid,dom:{tag:"div",attributes:{role:"alert"},classes:n.level.map(function(t){return["tox-notification","tox-notification--in","tox-notification--"+t]}).getOr(["tox-notification","tox-notification--in"])},behaviours:rc([hg.config({}),fg("notification-events",[er(lo(),function(t){s.getOpt(t).each(hg.focus)})])]),components:l.concat(n.progress?[u.asSpec()]:[]).concat(n.closeButton?[s.asSpec()]:[]),apis:a}},configFields:[Kn("level"),Ln("progress"),Ln("icon"),Ln("onAction"),Ln("text"),Ln("iconProvider"),Ln("translationProvider"),ue("closeButton",!0)],apis:{updateProgress:function(t,n,e){t.updateProgress(n,e)},updateText:function(t,n,e){t.updateText(n,e)}}});function bp(t,u,a){var c=u.backstage;return{open:function(t,n){var e=!t.closeButton&&t.timeout&&(0<t.timeout||t.timeout<0),o=function(){n(),Zg.hide(i)},r=mu(vp.sketch({text:t.text,level:M(["success","error","warning","warn","info"],t.type)?t.type:undefined,progress:!0===t.progressBar,icon:dt.from(t.icon),closeButton:!e,onAction:o,iconProvider:c.shared.providers.icons,translationProvider:c.shared.providers.translate})),i=mu(Zg.sketch(ft({dom:{tag:"div",classes:["tox-notifications-container"]},lazySink:u.backstage.shared.getSink,fireDismissalEventInstead:{}},c.shared.header.isPositionedAtTop()?{}:{fireRepositionEventInstead:{}})));return a.add(i),0<t.timeout&&fp.setTimeout(function(){o()},t.timeout),{close:o,moveTo:function(t,n){Zg.showAt(i,{anchor:"makeshift",x:t,y:n},gu(r))},moveRel:function(t,n){var e,o;"banner"!==n?(e=function(t){switch(t){case"bc-bc":return lp;case"tc-tc":return sp;case"tc-bc":return La;case"bc-tc":default:return ja}}(n),o={anchor:"node",root:Ui(),node:dt.some(me.fromDom(t)),layouts:{onRtl:function(){return[e]},onLtr:function(){return[e]}}},Zg.showAt(i,o,gu(r))):Zg.showAt(i,u.backstage.shared.anchors.banner(),gu(r))},text:function(t){vp.updateText(r,t)},settings:t,getEl:function(){return r.element.dom},progressBar:{value:function(t){vp.updateProgress(r,t)}}}},close:function(t){t.close()},reposition:function(t){var e;mt(t,function(t){return t.moveTo(0,0)}),0<(e=t).length&&(K(e).each(function(t){return t.moveRel(null,"banner")}),mt(e,function(t,n){0<n&&t.moveRel(e[n-1].getEl(),"bc-tc")}))},getArgs:function(t){return t.settings}}}var yp,xp,wp=function(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null!==r&&clearTimeout(r),r=setTimeout(function(){e.apply(null,t),r=null},o)}}},Sp=tinymce.util.Tools.resolve("tinymce.dom.TextSeeker"),kp=function(o,t,n,e,r){var i=Sp(o,function(t){return e=t,(n=o).isBlock(e)||M(["BR","IMG","HR","INPUT"],e.nodeName)||"false"===n.getContentEditable(e);var n,e});return dt.from(i.backwards(t,n,e,r))},Cp=function(e,n){return Op(me.fromDom(e.selection.getNode())).getOrThunk(function(){var t=me.fromHtml('<span data-mce-autocompleter="1" data-mce-bogus="1"></span>',e.getDoc());return zr(t,me.fromDom(n.extractContents())),n.insertNode(t.dom),kr(t).each(function(t){return t.dom.normalize()}),Cs(t,ks).map(function(t){var n;e.selection.setCursorLocation(t.dom,"img"===mr(n=t)?1:ws(n).fold(function(){return _r(n).length},function(t){return t.length}))}),t})},Op=function(t){return Ku(t,"[data-mce-autocompleter]")},_p=function(t){return t.toString().replace(/\u00A0/g," ").replace(/\uFEFF/g,"")},Tp=function(t){return""!==t&&-1!==" \xa0\f\n\r\t\x0B".indexOf(t)},Ep=function(t,n){return t.substring(n.length)},Dp=function(t,o,r,i){if(void 0===i&&(i=0),!(n=o).collapsed||3!==n.startContainer.nodeType)return dt.none();var n,e=t.getParent(o.startContainer,t.isBlock)||t.getRoot();return kp(t,o.startContainer,o.startOffset,function(t,n,e){return function(t,n,e){for(var o=n-1;0<=o;o--){var r=t.charAt(o);if(Tp(r))return dt.none();if(r===e)break}return dt.some(o)}(e,n,r).getOr(n)},e).bind(function(t){var n=o.cloneRange();if(n.setStart(t.container,t.offset),n.setEnd(o.endContainer,o.endOffset),n.collapsed)return dt.none();var e=_p(n);return 0!==e.lastIndexOf(r)||Ep(e,r).length<i?dt.none():dt.some({text:Ep(e,r),range:n,triggerChar:r})})},Bp=function(o,t,r,n){return void 0===n&&(n=0),Op(me.fromDom(t.startContainer)).fold(function(){return Dp(o,t,r,n)},function(t){var n=o.createRng();n.selectNode(t.dom);var e=_p(n);return dt.some({range:n,text:Ep(e,r),triggerChar:r})})},Ap=function(e,t){t.on("keypress compositionend",e.onKeypress.throttle),t.on("remove",e.onKeypress.cancel);var o=function(t,n){qo(t,mo(),{raw:n})};t.on("keydown",function(n){var t=function(){return e.getView().bind(ld.getHighlighted)};8===n.which&&e.onKeypress.throttle(n),e.isActive()&&(27===n.which&&e.cancelIfNecessary(),e.isMenuOpen()?13===n.which?(t().each(Ko),n.preventDefault()):40===n.which?(t().fold(function(){e.getView().each(ld.highlightFirst)},function(t){o(t,n)}),n.preventDefault(),n.stopImmediatePropagation()):37!==n.which&&38!==n.which&&39!==n.which||t().each(function(t){o(t,n),n.preventDefault(),n.stopImmediatePropagation()}):13!==n.which&&38!==n.which&&40!==n.which||e.cancelIfNecessary())}),t.on("NodeChange",function(t){e.isActive()&&!e.isProcessingAction()&&Op(me.fromDom(t.element)).isNone()&&e.cancelIfNecessary()})},Mp=tinymce.util.Tools.resolve("tinymce.util.Promise"),Fp=function(t,n){return{container:t,offset:n}},Ip=function(t){if(3===t.nodeType)return Fp(t,t.data.length);var n=t.childNodes;return 0<n.length?Ip(n[n.length-1]):Fp(t,n.length)},Rp=function(t,n){var e=t.childNodes;return 0<e.length&&n<e.length?Rp(e[n],0):0<e.length&&1===t.nodeType&&e.length===n?Ip(e[e.length-1]):Fp(t,n)},Vp=function(r){return function(t){var n,e,o=Rp(t.startContainer,t.startOffset);return!kp(n=r,(e=o).container,e.offset,function(t,n){return 0===n?-1:n},n.getRoot()).filter(function(t){var n=t.container.data.charAt(t.offset-1);return!Tp(n)}).isSome()}},Pp=function(n,e){var o,r,t=e(),i=n.selection.getRng();return o=n.dom,r=i,Q(t.triggerChars,function(t){return Bp(o,r,t)}).bind(function(t){return Hp(n,e,t)})},Hp=function(n,t,e,o){void 0===o&&(o={});var r=t(),i=n.selection.getRng().startContainer.nodeValue,u=H(r.lookupByChar(e.triggerChar),function(t){return e.text.length>=t.minChars&&t.matches.getOrThunk(function(){return Vp(n.dom)})(e.range,i,e.text)});if(0===u.length)return dt.none();var a=Mp.all(V(u,function(n){return n.fetch(e.text,n.maxResults,o).then(function(t){return{matchText:e.text,items:t,columns:n.columns,onAction:n.onAction,highlightOn:n.highlightOn}})}));return dt.some({lookupData:a,context:e})},zp=fn([Un("type"),Qn("text")]),Np=fn([ne("type","autocompleteitem"),ne("active",!1),ne("disabled",!1),ne("meta",{}),Un("value"),Qn("text"),Qn("icon")]),Lp=fn([Un("type"),Un("ch"),oe("minChars",1),ne("columns",1),oe("maxResults",10),Zn("matches"),Gn("fetch"),Gn("onAction"),ce("highlightOn",[],Rn)]),jp=[ue("disabled",!1),Qn("tooltip"),Qn("icon"),Qn("text"),ae("onSetup",function(){return ct})],Up=fn([Un("type"),Gn("onAction")].concat(jp)),Wp=function(t){return _n("toolbarbutton",Up,t)},Gp=[ue("active",!1)].concat(jp),Xp=fn(Gp.concat([Un("type"),Gn("onAction")])),Yp=function(t){return _n("ToggleButton",Xp,t)},qp=[ae("predicate",l),ie("scope","node",["node","editor"]),ie("position","selection",["node","selection","line"])],Kp=jp.concat([ne("type","contextformbutton"),ne("primary",!1),Gn("onAction"),le("original",lt)]),Jp=Gp.concat([ne("type","contextformbutton"),ne("primary",!1),Gn("onAction"),le("original",lt)]),$p=jp.concat([ne("type","contextformbutton")]),Qp=Gp.concat([ne("type","contextformtogglebutton")]),Zp=An("type",{contextformbutton:Kp,contextformtogglebutton:Jp}),th=fn([ne("type","contextform"),ae("initValue",function(){return""}),Qn("label"),qn("commands",Zp),Jn("launch",An("type",{contextformbutton:$p,contextformtogglebutton:Qp}))].concat(qp)),nh=fn([ne("type","contexttoolbar"),Un("items")].concat(qp)),eh=function(t){var n,e,o=t.ui.registry.getAll().popups,r=Tt(o,function(t){return _n("Autocompleter",Lp,t).fold(function(t){throw new Error(Dn(t))},function(t){return t})}),i=(n=Bt(r,function(t){return t.ch}),e={},mt(n,function(t){e[t]={}}),Ct(e)),u=Mt(r);return{dataset:r,triggerChars:i,lookupByChar:function(n){return H(u,function(t){return t.ch===n})}}};(xp=yp=yp||{})[xp.CLOSE_ON_EXECUTE=0]="CLOSE_ON_EXECUTE",xp[xp.BUBBLE_TO_SANDBOX=1]="BUBBLE_TO_SANDBOX";var oh,rh,ih=yp,uh="tox-menu-nav__js",ah="tox-collection__item",ch="tox-swatch",sh={normal:uh,color:ch},lh="tox-collection__item--enabled",fh="tox-collection__item-label",dh="tox-collection__item-caret",mh="tox-collection__item--active",gh="tox-collection__item-container",ph="tox-collection__item-container--row",hh=function(t){return Ft(sh,t).getOr(uh)},vh=function(t){return{backgroundMenu:"tox-background-menu",selectedMenu:"tox-selected-menu",selectedItem:"tox-collection__item--active",hasIcons:"tox-menu--has-icons",menu:"color"===t?"tox-swatches":"tox-menu",tieredMenu:"tox-tiered-menu"}},bh=function(t){var n=vh(t);return{backgroundMenu:n.backgroundMenu,selectedMenu:n.selectedMenu,menu:n.menu,selectedItem:n.selectedItem,item:hh(t)}},yh=[Xg.parts.items({})],xh=function(t,n,e){var o=vh(e);return{dom:{tag:"div",classes:gt([[o.tieredMenu]])},markers:bh(e)}},wh=function(e,o){return function(t){var n=R(t,o);return V(n,function(t){return{dom:e,components:t}})}},Sh=function(t,e){var o=[],r=[];return mt(t,function(t,n){e(t,n)?(0<r.length&&o.push(r),r=[],It(t.dom,"innerHtml")&&r.push(t)):r.push(t)}),0<r.length&&o.push(r),V(o,function(t){return{dom:{tag:"div",classes:["tox-collection__group"]},components:t}})},kh=function(n,e,t){return{dom:{tag:"div",classes:["tox-menu","tox-collection"].concat(1===n?["tox-collection--list"]:["tox-collection--grid"])},components:[Xg.parts.items({preprocess:function(t){return"auto"!==n&&1<n?wh({tag:"div",classes:["tox-collection__group"]},n)(t):Sh(t,function(t,n){return"separator"===e[n].type})}})]}},Ch=function(t){return F(t,function(t){return"icon"in t&&t.icon!==undefined})},Oh=function(t){return console.error(Dn(t)),console.log(t),dt.none()},_h=function(t,n,e,o,r){var i,u=(i=e,{dom:{tag:"div",classes:["tox-collection","tox-collection--horizontal"]},components:[Xg.parts.items({preprocess:function(t){return Sh(t,function(t,n){return"separator"===i[n].type})}})]});return{value:t,dom:u.dom,components:u.components,items:e}},Th=function(t,n,e,o,r){var i,u,a,c,s,l;return"color"===r?{value:t,dom:(a=(i=o,{dom:{tag:"div",classes:["tox-menu","tox-swatches-menu"]},components:[{dom:{tag:"div",classes:["tox-swatches"]},components:[Xg.parts.items({preprocess:"auto"!==i?wh({tag:"div",classes:["tox-swatches__row"]},i):lt})]}]})).dom,components:a.components,items:e}:"normal"===r&&"auto"===o?{value:t,dom:(a=kh(o,e)).dom,components:a.components,items:e}:"normal"===r&&1===o?{value:t,dom:(a=kh(1,e)).dom,components:a.components,items:e}:"normal"===r?{value:t,dom:(a=kh(o,e)).dom,components:a.components,items:e}:"listpreview"!==r||"auto"===o?{value:t,dom:(c=n,s=o,l=vh(r),{tag:"div",classes:gt([[l.menu,"tox-menu-"+s+"-column"],c?[l.hasIcons]:[]])}),components:yh,items:e}:{value:t,dom:(a=(u=o,{dom:{tag:"div",classes:["tox-menu","tox-collection","tox-collection--toolbar","tox-collection--toolbar-lg"]},components:[Xg.parts.items({preprocess:wh({tag:"div",classes:["tox-collection__group"]},u)})]})).dom,components:a.components,items:e}},Eh=[Un("type"),Un("src"),Qn("alt"),ce("classes",[],Rn)],Dh=fn(Eh),Bh=[Un("type"),Un("text"),Qn("name"),ce("classes",["tox-collection__item-label"],Rn)],Ah=fn(Bh),Mh=kn(function(){return Bn("type",{cardimage:Dh,cardtext:Ah,cardcontainer:Fh})}),Fh=fn([Un("type"),re("direction","horizontal"),re("align","left"),re("valign","middle"),qn("items",Mh)]),Ih=[ue("disabled",!1),Qn("text"),Qn("shortcut"),bn("value","value",Xt(function(){return oi("menuitem-value")}),Mn()),ne("meta",{})],Rh=fn([Un("type"),Qn("label"),qn("items",Mh),ae("onSetup",function(){return ct}),ae("onAction",ct)].concat(Ih)),Vh=fn([Un("type"),ue("active",!1),Qn("icon")].concat(Ih)),Ph=fn([Un("type"),Wn("fancytype",["inserttable","colorswatch"]),ae("onAction",ct)]),Hh=fn([Un("type"),ae("onSetup",function(){return ct}),ae("onAction",ct),Qn("icon")].concat(Ih)),zh=fn([Un("type"),Gn("getSubmenuItems"),ae("onSetup",function(){return ct}),Qn("icon")].concat(Ih)),Nh=fn([Un("type"),Qn("icon"),ue("active",!1),ae("onSetup",function(){return ct}),Gn("onAction")].concat(Ih)),Lh=function(t,o,n){var r=Os(t.element,"."+n);if(0<r.length){var e=j(r,function(t){var n=t.dom.getBoundingClientRect().top,e=r[0].dom.getBoundingClientRect().top;return Math.abs(n-e)>o}).getOr(r.length);return dt.some({numColumns:e,numRows:Math.ceil(r.length/e)})}return dt.none()},jh=function(t,n){return rc([fg(t,n)])},Uh=function(t){return jh(oi("unnamed-events"),t)},Wh=oi("tooltip.exclusive"),Gh=oi("tooltip.show"),Xh=oi("tooltip.hide"),Yh=function(t,n,e){t.getSystem().broadcastOn([Wh],{})},qh=/* */Object.freeze({__proto__:null,hideAllExclusive:Yh,setComponents:function(t,n,e,o){e.getTooltip().each(function(t){t.getSystem().isConnected()&&lg.set(t,o)})}}),Kh=/* */Object.freeze({__proto__:null,events:function(r,i){var e=function(n){i.getTooltip().each(function(t){al(t),r.onHide(n,t),i.clearTooltip()}),i.clearTimer()};return Zo(gt([[er(Gh,function(o){i.resetTimer(function(){var n,t,e;n=o,i.isShowing()||(Yh(n),t=r.lazySink(n).getOrDie(),e=n.getSystem().build({dom:r.tooltipDom,components:r.tooltipComponents,events:Zo("normal"===r.mode?[er(so(),function(t){Yo(n,Gh)}),er(ao(),function(t){Yo(n,Xh)})]:[]),behaviours:rc([lg.config({})])}),i.setTooltip(e),rl(t,e),r.onShow(n,e),Zs.position(t,r.anchor(n),e))},r.delay)}),er(Xh,function(t){i.resetTimer(function(){e(t)},r.delay)}),er(Oo(),function(t,n){n.universal||M(n.channels,Wh)&&e(t)}),lr(function(t){e(t)})],"normal"===r.mode?[er(lo(),function(t){Yo(t,Gh)}),er(ko(),function(t){Yo(t,Xh)}),er(so(),function(t){Yo(t,Gh)}),er(ao(),function(t){Yo(t,Xh)})]:[er(Go(),function(t,n){Yo(t,Gh)}),er(Xo(),function(t){Yo(t,Xh)})]]))}}),Jh=[Ln("lazySink"),Ln("tooltipDom"),ne("exclusive",!0),ne("tooltipComponents",[]),ne("delay",300),ie("mode","normal",["normal","follow-highlight"]),ne("anchor",function(t){return{anchor:"hotspot",hotspot:t,layouts:{onLtr:st([ja,La,Pa,za,Ha,Na]),onRtl:st([ja,La,Pa,za,Ha,Na])}}}),ma("onHide"),ma("onShow")],$h=uc({fields:Jh,name:"tooltipping",active:Kh,state:/* */Object.freeze({__proto__:null,init:function(){var e=fe(dt.none()),n=fe(dt.none()),o=function(){e.get().each(function(t){clearTimeout(t)})},t=st("not-implemented");return xi({getTooltip:function(){return n.get()},isShowing:function(){return n.get().isSome()},setTooltip:function(t){n.set(dt.some(t))},clearTooltip:function(){n.set(dt.none())},clearTimer:o,resetTimer:function(t,n){o(),e.set(dt.some(setTimeout(function(){t()},n)))},readState:t})}}),apis:qh}),Qh=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),Zh=tinymce.util.Tools.resolve("tinymce.util.I18n"),tv=["input","button","textarea","select"],nv=function(t,n,e){(n.disabled()?av:cv)(t,n)},ev=function(t,n){return!0===n.useNative&&M(tv,mr(t.element))},ov=function(t){Yr(t.element,"disabled","disabled")},rv=function(t){$r(t.element,"disabled")},iv=function(t){Yr(t.element,"aria-disabled","true")},uv=function(t){Yr(t.element,"aria-disabled","false")},av=function(n,t,e){t.disableClass.each(function(t){Ri(n.element,t)}),(ev(n,t)?ov:iv)(n),t.onDisabled(n)},cv=function(n,t,e){t.disableClass.each(function(t){Pi(n.element,t)}),(ev(n,t)?rv:uv)(n),t.onEnabled(n)},sv=function(t,n){return ev(t,n)?Jr(t.element,"disabled"):"true"===qr(t.element,"aria-disabled")},lv=/* */Object.freeze({__proto__:null,enable:cv,disable:av,isDisabled:sv,onLoad:nv,set:function(t,n,e,o){(o?av:cv)(t,n)}}),fv=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return Si({classes:n.disabled()?n.disableClass.toArray():[]})},events:function(e,t){return Zo([tr(_o(),function(t,n){return sv(t,e)}),Za(e,t,nv)])}}),dv=[ae("disabled",l),ne("useNative",!0),Kn("disableClass"),ma("onDisabled"),ma("onEnabled")],mv=uc({fields:dv,name:"disabling",active:fv,apis:lv}),gv=tinymce.util.Tools.resolve("tinymce.EditorManager"),pv=function(t){return t.getParam("height",Math.max(t.getElement().offsetHeight,200))},hv=function(t){return t.getParam("width",Qh.DOM.getStyle(t.getElement(),"width"))},vv=function(t){return dt.from(t.getParam("min_width")).filter(at)},bv=function(t){return dt.from(t.getParam("min_height")).filter(at)},yv=function(t){return dt.from(t.getParam("max_width")).filter(at)},xv=function(t){return dt.from(t.getParam("max_height")).filter(at)},wv=function(t){return!1!==t.getParam("menubar",!0,"boolean")},Sv=function(t){var n=t.getParam("toolbar",!0),e=!0===n,o=x(n),r=c(n)&&0<n.length;return!Cv(t)&&(r||o||e)},kv=function(n){var t=I(9,function(t){return n.getParam("toolbar"+(t+1),!1,"string")}),e=H(t,function(t){return"string"==typeof t});return 0<e.length?dt.some(e):dt.none()},Cv=function(t){return kv(t).fold(function(){return 0<t.getParam("toolbar",[],"string[]").length},O)};(rh=oh=oh||{})["default"]="wrap",rh.floating="floating",rh.sliding="sliding",rh.scrolling="scrolling";var Ov,_v,Tv=function(t){return t.getParam("toolbar_mode","","string")};(_v=Ov=Ov||{}).auto="auto",_v.top="top",_v.bottom="bottom";var Ev,Dv=function(t){return t.getParam("toolbar_location",Ov.auto,"string")},Bv=function(t){return Dv(t)===Ov.bottom},Av=function(t){if(!t.inline)return dt.none();var n=t.getParam("fixed_toolbar_container","","string");if(0<n.length)return qu(Ui(),n);var e=t.getParam("fixed_toolbar_container_target");return i(e)?dt.some(me.fromDom(e)):dt.none()},Mv=function(t){return t.inline&&Av(t).isSome()},Fv=function(t){return Av(t).getOrThunk(function(){return Mr(Ar(me.fromDom(t.getElement())))})},Iv=function(t){return t.inline&&!wv(t)&&!Sv(t)&&!Cv(t)},Rv=function(t){return(t.getParam("toolbar_sticky",!1,"boolean")||t.inline)&&!Mv(t)&&!Iv(t)},Vv="silver.readonly",Pv=fn([jn("readonly",Vn)]),Hv=function(t,n){var e=t.outerContainer.element;n&&(t.mothership.broadcastOn([Sl()],{target:e}),t.uiMothership.broadcastOn([Sl()],{target:e})),t.mothership.broadcastOn([Vv],{readonly:n}),t.uiMothership.broadcastOn([Vv],{readonly:n})},zv=function(t,n){t.on("init",function(){t.mode.isReadOnly()&&Hv(n,!0)}),t.on("SwitchMode",function(){return Hv(n,t.mode.isReadOnly())}),t.getParam("readonly",!1,"boolean")&&t.setMode("readonly")},Nv=function(){var t;return lc.config({channels:((t={})[Vv]={schema:Pv,onReceive:function(t,n){mv.set(t,n.readonly)}},t)})},Lv=function(t){return mv.config({disabled:t,disableClass:"tox-collection__item--state-disabled"})},jv=function(t){return mv.config({disabled:t})},Uv=function(t){return mv.config({disabled:t,disableClass:"tox-tbtn--disabled"})},Wv=function(t){return mv.config({disabled:t,disableClass:"tox-tbtn--disabled",useNative:!1})},Gv=function(t,n){var e=t.getApi(n);return function(t){t(e)}},Xv=function(e,o){return sr(function(t){Gv(e,t)(function(t){var n=e.onSetup(t);y(n)&&o.set(n)})})},Yv=function(n,e){return lr(function(t){return Gv(n,t)(e.get())})},qv=((Ev={})[_o()]=["disabling","alloy.base.behaviour","toggling","item-events"],Ev),Kv=function(t){return U(t,function(t){return t.toArray()})},Jv=function(t,n,e,o){var r,i,u=fe(ct);return{type:"item",dom:n.dom,components:Kv(n.optComponents),data:t.data,eventOrder:qv,hasSubmenu:t.triggersSubmenu,itemBehaviours:rc([fg("item-events",[(r=t,i=e,dr(function(t,n){Gv(r,t)(r.onAction),r.triggersSubmenu||i!==ih.CLOSE_ON_EXECUTE||(t.getSystem().isConnected()&&Yo(t,Bo()),n.stop())})),Xv(t,u),Yv(t,u)]),Lv(function(){return t.disabled||o.isDisabled()}),Nv(),lg.config({})].concat(t.itemBehaviours))}},$v=function(t){return{value:t.value,meta:ft({text:t.text.getOr("")},t.meta)}},Qv=tinymce.util.Tools.resolve("tinymce.Env"),Zv=function(t){return{dom:{tag:"div",classes:["tox-collection__item-icon"],innerHtml:t}}},tb=function(t){return{dom:{tag:"div",classes:[fh]},components:[lu(Zh.translate(t))]}},nb=function(t,n){return{dom:{tag:"div",classes:n,innerHtml:t}}},eb=function(t,n){return{dom:{tag:"div",classes:[fh]},components:[{dom:{tag:t.tag,styles:t.styles},components:[lu(Zh.translate(n))]}]}},ob=function(t){return{dom:{tag:"div",classes:["tox-collection__item-accessory"],innerHtml:(n=t,e=Qv.mac?{alt:"&#x2325;",ctrl:"&#x2303;",shift:"&#x21E7;",meta:"&#x2318;",access:"&#x2303;&#x2325;"}:{meta:"Ctrl",access:"Shift+Alt"},o=n.split("+"),r=V(o,function(t){var n=t.toLowerCase().trim();return It(e,n)?e[n]:t}),Qv.mac?r.join(""):r.join("+"))}};var n,e,o,r},rb=function(t){return{dom:{tag:"div",classes:["tox-collection__item-checkmark"],innerHtml:pp("checkmark",t)}}},ib=function(t,n){var e=n.map(function(t){return{attributes:{title:Zh.translate(t)}}}).getOr({});return ft({tag:"div",classes:[uh,ah].concat(t?["tox-collection__item-icon-rtl"]:[])},e)},ub=["list-num-default","list-num-lower-alpha","list-num-lower-greek","list-num-lower-roman","list-num-upper-alpha","list-num-upper-roman"],ab=["list-bull-circle","list-bull-default","list-bull-square"],cb=function(t,r,n,i){void 0===i&&(i=dt.none());var e,o,u,a,c,s,l,f,d,m,g,p,h=Zh.isRtl()&&t.iconContent.exists(function(t){return M(ab,t)}),v=t.iconContent.map(function(t){return Zh.isRtl()&&M(ub,t)?t+"-rtl":t}).map(function(t){return n=t,e=r.icons,o=i,dt.from(e()[n.toLowerCase()]).or(o).getOrThunk(function(){return gp(e)});var n,e,o}),b=dt.from(t.meta).fold(function(){return tb},function(t){return It(t,"style")?k(eb,t.style):tb});return"color"===t.presets?(f=t.ariaLabel,d=t.value,m=r,{dom:(g=v.getOr(""),p={tag:"div",attributes:f.map(function(t){return{title:m.translate(t)}}).getOr({}),classes:["tox-swatch"]},ft(ft({},p),"custom"===d?{tag:"button",classes:T(p.classes,["tox-swatches__picker-btn"]),innerHtml:g}:"remove"===d?{classes:T(p.classes,["tox-swatch--remove"]),innerHtml:g}:{attributes:ft(ft({},p.attributes),{"data-mce-color":d}),styles:{"background-color":d}})),optComponents:[]}):(e=t,o=v,u=b,a=h,c=n?o.or(dt.some("")).map(Zv):dt.none(),s=e.checkMark,l=e.htmlContent.fold(function(){return e.textContent.map(u)},function(t){return dt.some(nb(t,[fh]))}),{dom:ib(a,e.ariaLabel),optComponents:[c,l,e.shortcutContent.map(ob),s,e.caret]})},sb=function(t,n){return Ft(t,"tooltipWorker").map(function(e){return[$h.config({lazySink:n.getSink,tooltipDom:{tag:"div",classes:["tox-tooltip-worker-container"]},tooltipComponents:[],anchor:function(t){return{anchor:"submenu",item:t,overrides:{maxHeightFunction:Mc}}},mode:"follow-highlight",onShow:function(n,t){e(function(t){$h.setComponents(n,[fu({element:me.fromDom(t)})])})}})]}).getOr([])},lb=function(t,n){var e,o=Zh.translate(t),r=(e=o,Qh.DOM.encode(e));if(0<n.length){var i=new RegExp(n.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return r.replace(i,function(t){return'<span class="tox-autocompleter-highlight">'+t+"</span>"})}return r},fb=function(t,s){return V(t,function(t){switch(t.type){case"cardcontainer":return u=fb((i=t).items,s),a="vertical"===i.direction?"tox-collection__item-container--column":ph,c="left"===i.align?"tox-collection__item-container--align-left":"tox-collection__item-container--align-right",{dom:{tag:"div",classes:[gh,a,c,function(){switch(i.valign){case"top":return"tox-collection__item-container--valign-top";case"middle":return"tox-collection__item-container--valign-middle";case"bottom":return"tox-collection__item-container--valign-bottom"}}()]},components:u};case"cardimage":return e=t.src,o=t.classes,r=t.alt,{dom:{tag:"img",classes:o,attributes:{src:e,alt:r.getOr("")}}};case"cardtext":var n=t.name.exists(function(t){return M(s.cardText.highlightOn,t)})?dt.from(s.cardText.matchText).getOr(""):"";return nb(lb(t.text,n),t.classes)}var e,o,r,i,u,a,c})},db=Of(zg(),Ng()),mb=function(t){return{value:t}},gb=/^#?([a-f\d])([a-f\d])([a-f\d])$/i,pb=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i,hb=function(t){return gb.test(t)||pb.test(t)},vb=function(t){var n={value:t.value.replace(gb,function(t,n,e,o){return n+n+e+e+o+o})},e=pb.exec(n.value);return null===e?["FFFFFF","FF","FF","FF"]:e},bb=function(t){var n=t.toString(16);return(1===n.length?"0"+n:n).toUpperCase()},yb=function(t){var n=bb(t.red)+bb(t.green)+bb(t.blue);return mb(n)},xb=Math.min,wb=Math.max,Sb=Math.round,kb=/^rgb\((\d+),\s*(\d+),\s*(\d+)\)/,Cb=/^rgba\((\d+),\s*(\d+),\s*(\d+),\s*(\d?(?:\.\d+)?)\)/,Ob=function(t,n,e,o){return{red:t,green:n,blue:e,alpha:o}},_b=function(t){var n=parseInt(t,10);return n.toString()===t&&0<=n&&n<=255},Tb=function(t){var n,e,o,r=(t.hue||0)%360,i=t.saturation/100,u=t.value/100,i=wb(0,xb(i,1)),u=wb(0,xb(u,1));if(0===i)return n=e=o=Sb(255*u),Ob(n,e,o,1);var a=r/60,c=u*i,s=c*(1-Math.abs(a%2-1)),l=u-c;switch(Math.floor(a)){case 0:n=c,e=s,o=0;break;case 1:n=s,e=c,o=0;break;case 2:n=0,e=c,o=s;break;case 3:n=0,e=s,o=c;break;case 4:n=s,e=0,o=c;break;case 5:n=c,e=0,o=s;break;default:n=e=o=0}return n=Sb(255*(n+l)),e=Sb(255*(e+l)),o=Sb(255*(o+l)),Ob(n,e,o,1)},Eb=function(t){var n=vb(t),e=parseInt(n[1],16),o=parseInt(n[2],16),r=parseInt(n[3],16);return Ob(e,o,r,1)},Db=function(t,n,e,o){var r=parseInt(t,10),i=parseInt(n,10),u=parseInt(e,10),a=parseFloat(o);return Ob(r,i,u,a)},Bb=function(t){return"rgba("+t.red+","+t.green+","+t.blue+","+t.alpha+")"},Ab=Ob(255,0,0,1),Mb=function(t,n){return t.fire("ResizeContent",n)},Fb=tinymce.util.Tools.resolve("tinymce.util.LocalStorage"),Ib="tinymce-custom-colors";var Rb,Vb,Pb="choiceitem",Hb=[{type:Pb,text:"Light Green",value:"#BFEDD2"},{type:Pb,text:"Light Yellow",value:"#FBEEB8"},{type:Pb,text:"Light Red",value:"#F8CAC6"},{type:Pb,text:"Light Purple",value:"#ECCAFA"},{type:Pb,text:"Light Blue",value:"#C2E0F4"},{type:Pb,text:"Green",value:"#2DC26B"},{type:Pb,text:"Yellow",value:"#F1C40F"},{type:Pb,text:"Red",value:"#E03E2D"},{type:Pb,text:"Purple",value:"#B96AD9"},{type:Pb,text:"Blue",value:"#3598DB"},{type:Pb,text:"Dark Turquoise",value:"#169179"},{type:Pb,text:"Orange",value:"#E67E23"},{type:Pb,text:"Dark Red",value:"#BA372A"},{type:Pb,text:"Dark Purple",value:"#843FA1"},{type:Pb,text:"Dark Blue",value:"#236FA1"},{type:Pb,text:"Light Gray",value:"#ECF0F1"},{type:Pb,text:"Medium Gray",value:"#CED4D9"},{type:Pb,text:"Gray",value:"#95A5A6"},{type:Pb,text:"Dark Gray",value:"#7E8C8D"},{type:Pb,text:"Navy Blue",value:"#34495E"},{type:Pb,text:"Black",value:"#000000"},{type:Pb,text:"White",value:"#ffffff"}],zb=function(e){void 0===e&&(e=10);var t,n=Fb.getItem(Ib),o=x(n)?JSON.parse(n):[],r=e-(t=o).length<0?t.slice(0,e):t,i=function(t){r.splice(t,1)};return{add:function(t){var n;(-1===(n=A(r,t))?dt.none():dt.some(n)).each(i),r.unshift(t),r.length>e&&r.pop(),Fb.setItem(Ib,JSON.stringify(r))},state:function(){return r.slice(0)}}}(10),Nb=function(t){return!1!==t.getParam("custom_colors")},Lb=function(t){var n=t.getParam("color_map");return n!==undefined?function(t){var n=[],u=document.createElement("canvas");u.height=1,u.width=1;for(var a=u.getContext("2d"),c=function(t,n){var e=n/255;return("0"+Math.round(t*e+255*(1-e)).toString(16)).slice(-2).toUpperCase()},e=0;e<t.length;e+=2)n.push({text:t[e+1],value:function(t){if(/^[0-9A-Fa-f]{6}$/.test(t))return"#"+t.toUpperCase();a.clearRect(0,0,u.width,u.height),a.fillStyle="#FFFFFF",a.fillStyle=t,a.fillRect(0,0,1,1);var n=a.getImageData(0,0,1,1).data,e=n[0],o=n[1],r=n[2],i=n[3];return"#"+c(e,i)+c(o,i)+c(r,i)}(t[e]),type:"choiceitem"});return n}(n):Hb},jb=function(t){zb.add(t)},Ub=function(i){i.addCommand("mceApplyTextcolor",function(t,n){var e,o,r;o=t,r=n,(e=i).undoManager.transact(function(){e.focus(),e.formatter.apply(o,{value:r}),e.nodeChanged()})}),i.addCommand("mceRemoveTextcolor",function(t){var n,e;e=t,(n=i).undoManager.transact(function(){n.focus(),n.formatter.remove(e,{value:null},null,!0),n.nodeChanged()})})},Wb=function(t){var n,e,o=Lb(t),r=(n=o.length,Math.max(5,Math.ceil(Math.sqrt(n))));return e=r,t.getParam("color_cols",e,"number")},Gb=function(n,e,t,o){"custom"===t?$b(n)(function(t){t.each(function(t){jb(t),n.execCommand("mceApplyTextcolor",e,t),o(t)})},"#000000"):"remove"===t?(o(""),n.execCommand("mceRemoveTextcolor",e)):(o(t),n.execCommand("mceApplyTextcolor",e,t))},Xb=function(t,n){return t.concat(V(zb.state(),function(t){return{type:Pb,text:t,value:t}}).concat((o={type:e="choiceitem",text:"Remove color",icon:"color-swatch-remove-color",value:"remove"},n?[o,{type:e,text:"Custom color",icon:"color-picker",value:"custom"}]:[o])));var e,o},Yb=function(n,e){return function(t){t(Xb(n,e))}},qb=function(t,n,e){var o="forecolor"===n?"tox-icon-text-color__color":"tox-icon-highlight-bg-color__color";t.setIconFill(o,e)},Kb=function(i,e,u,t,o){i.ui.registry.addSplitButton(e,{tooltip:t,presets:"color",icon:"forecolor"===e?"text-color":"highlight-bg-color",select:function(e){var t,o,r;return dt.from((o=u,(t=i).dom.getParents(t.selection.getStart(),function(t){var n;(n=t.style["forecolor"===o?"color":"background-color"])&&(r=r||n)}),r)).bind(function(t){return function(t){if("transparent"===t)return dt.some(Ob(0,0,0,0));var n=kb.exec(t);if(null!==n)return dt.some(Db(n[1],n[2],n[3],"1"));var e=Cb.exec(t);return null!==e?dt.some(Db(e[1],e[2],e[3],e[4])):dt.none()}(t).map(function(t){var n=yb(t).value;return we(e.toLowerCase(),n)})}).getOr(!1)},columns:Wb(i),fetch:Yb(Lb(i),Nb(i)),onAction:function(t){null!==o.get()&&Gb(i,u,o.get(),ct)},onItemAction:function(t,n){Gb(i,u,n,function(t){var n;o.set(t),n={name:e,color:t},i.fire("TextColorChange",n)})},onSetup:function(n){null!==o.get()&&qb(n,e,o.get());var t=function(t){t.name===e&&qb(n,t.name,t.color)};return i.on("TextColorChange",t),function(){i.off("TextColorChange",t)}}})},Jb=function(n,t,e,o){n.ui.registry.addNestedMenuItem(t,{text:o,icon:"forecolor"===t?"text-color":"highlight-bg-color",getSubmenuItems:function(){return[{type:"fancymenuitem",fancytype:"colorswatch",onAction:function(t){Gb(n,e,t.value,ct)}}]}})},$b=function(r){return function(e,t){var o=!1,n={colorpicker:t};r.windowManager.open({title:"Color Picker",size:"normal",body:{type:"panel",items:[{type:"colorpicker",name:"colorpicker",label:"Color"}]},buttons:[{type:"cancel",name:"cancel",text:"Cancel"},{type:"submit",name:"save",text:"Save",primary:!0}],initialData:n,onAction:function(t,n){"hex-valid"===n.name&&(o=n.value)},onSubmit:function(t){var n=t.getData().colorpicker;o?(e(dt.from(n)),t.close()):r.windowManager.alert(r.translate(["Invalid hex color code: {0}",n]))},onClose:ct,onCancel:function(){e(dt.none())}})}},Qb=function(t,n,e,o,r,i,u,a){var c=Ch(n),s=Zb(n,e,o,"color"!==r?"normal":"color",i,u,a);return Th(t,c,s,o,r)},Zb=function(e,o,r,i,u,a,c){return qf(V(e,function(n){return"choiceitem"===n.type?_n("choicemenuitem",Vh,n).fold(Oh,function(t){return dt.some(function(n,t,e,o,r,i,u,a){void 0===a&&(a=!0);var c=cb({presets:e,textContent:t?n.text:dt.none(),htmlContent:dt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:t?n.shortcut:dt.none(),checkMark:t?dt.some(rb(u.icons)):dt.none(),caret:dt.none(),value:n.value},u,a);return zt(Jv({data:$v(n),disabled:n.disabled,getApi:function(n){return{setActive:function(t){Eg.set(n,t)},isActive:function(){return Eg.isOn(n)},isDisabled:function(){return mv.isDisabled(n)},setDisabled:function(t){return mv.set(n,t)}}},onAction:function(t){return o(n.value)},onSetup:function(t){return t.setActive(r),ct},triggersSubmenu:!1,itemBehaviours:[]},c,i,u),{toggling:{toggleClass:lh,toggleOnExecute:!1,selected:n.active}})}(t,1===r,i,o,a(n.value),u,c,Ch(e)))}):dt.none()}))},ty=function(t,n){var e=bh(n);return 1===t?{mode:"menu",moveOnTab:!0}:"auto"===t?{mode:"grid",selector:"."+e.item,initSize:{numColumns:1,numRows:1}}:{mode:"matrix",rowSelector:"."+("color"===n?"tox-swatches__row":"tox-collection__group")}},ny=oi("cell-over"),ey=oi("cell-execute"),oy=function(t,n,e){for(var o=[],r=0;r<n;r++){for(var i=[],u=0;u<e;u++)i.push(function(n,e,t){var o,r=function(t){return qo(t,ey,{row:n,col:e})},i=function(t,n){n.stop(),r(t)};return mu({dom:{tag:"div",attributes:((o={role:"button"})["aria-labelledby"]=t,o)},behaviours:rc([fg("insert-table-picker-cell",[er(so(),hg.focus),er(_o(),r),er(vo(),i),er(Eo(),i)]),Eg.config({toggleClass:"tox-insert-table-picker__selected",toggleOnExecute:!1}),hg.config({onFocus:function(t){return qo(t,ny,{row:n,col:e})}})])})}(r,u,t));o.push(i)}return o},ry={inserttable:function(o){var t=oi("size-label"),i=oy(t,10,10),u=mp({dom:{tag:"span",classes:["tox-insert-table-picker__label"],attributes:{id:t}},components:[lu("0x0")],behaviours:rc([lg.config({})])});return{type:"widget",data:{value:oi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[db.widget({dom:{tag:"div",classes:["tox-insert-table-picker"]},components:U(i,function(t){return V(t,gu)}).concat(u.asSpec()),behaviours:rc([fg("insert-table-picker",[ur(ny,function(t,n,e){var o=e.event.row,r=e.event.col;!function(t,n,e,o,r){for(var i=0;i<o;i++)for(var u=0;u<r;u++)Eg.set(t[i][u],i<=n&&u<=e)}(i,o,r,10,10),lg.set(u.get(t),[lu(r+1+"x"+(o+1))])}),ur(ey,function(t,n,e){o.onAction({numRows:e.event.row+1,numColumns:e.event.col+1}),Yo(t,Bo())})]),ig.config({initSize:{numRows:10,numColumns:10},mode:"flatgrid",selector:'[role="button"]'})])})]}},colorswatch:function(n,t){var e=Xb(t.colorinput.getColors(),t.colorinput.hasCustomColors()),o=t.colorinput.getColorCols(),r=Qb(oi("menu-value"),e,function(t){n.onAction({value:t})},o,"color",ih.CLOSE_ON_EXECUTE,l,t.shared.providers),i=ft(ft({},r),{markers:bh("color"),movement:ty(o,"color")});return{type:"widget",data:{value:oi("widget-id")},dom:{tag:"div",classes:["tox-fancymenuitem"]},autofocus:!0,components:[db.widget(Xg.sketch(i))]}}},iy=function(n,e,t,o,r,i,u,a){void 0===a&&(a=!0);var c=cb({presets:o,textContent:dt.none(),htmlContent:t?n.text.map(function(t){return lb(t,e)}):dt.none(),ariaLabel:n.text,iconContent:n.icon,shortcutContent:dt.none(),checkMark:dt.none(),caret:dt.none(),value:n.value},u.providers,a,n.icon);return Jv({data:$v(n),disabled:n.disabled,getApi:st({}),onAction:function(t){return r(n.value,n.meta)},onSetup:st(ct),triggersSubmenu:!1,itemBehaviours:sb(n.meta,u)},c,i,u.providers)},uy=function(t){var n=t.text.fold(function(){return{}},function(t){return{innerHtml:t}});return{type:"separator",dom:ft({tag:"div",classes:[ah,"tox-collection__group-heading"]},n),components:[]}},ay=function(t,n,e,o){void 0===o&&(o=!0);var r=cb({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:dt.none(),ariaLabel:t.text,caret:dt.none(),checkMark:dt.none(),shortcutContent:t.shortcut},e,o);return Jv({data:$v(t),getApi:function(n){return{isDisabled:function(){return mv.isDisabled(n)},setDisabled:function(t){return mv.set(n,t)}}},disabled:t.disabled,onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e)},cy=function(t,n,e,o,r){void 0===o&&(o=!0),void 0===r&&(r=!1);var i,u,a=r?(u=e.icons,{dom:{tag:"div",classes:[dh],innerHtml:pp("chevron-down",u)}}):(i=e.icons,{dom:{tag:"div",classes:[dh],innerHtml:pp("chevron-right",i)}}),c=cb({presets:"normal",iconContent:t.icon,textContent:t.text,htmlContent:dt.none(),ariaLabel:t.text,caret:dt.some(a),checkMark:dt.none(),shortcutContent:t.shortcut},e,o);return Jv({data:$v(t),getApi:function(n){return{isDisabled:function(){return mv.isDisabled(n)},setDisabled:function(t){return mv.set(n,t)}}},disabled:t.disabled,onAction:ct,onSetup:t.onSetup,triggersSubmenu:!0,itemBehaviours:[]},c,n,e)},sy=function(t,n,e,o){void 0===o&&(o=!0);var r=cb({iconContent:t.icon,textContent:t.text,htmlContent:dt.none(),ariaLabel:t.text,checkMark:dt.some(rb(e.icons)),caret:dt.none(),shortcutContent:t.shortcut,presets:"normal",meta:t.meta},e,o);return zt(Jv({data:$v(t),disabled:t.disabled,getApi:function(n){return{setActive:function(t){Eg.set(n,t)},isActive:function(){return Eg.isOn(n)},isDisabled:function(){return mv.isDisabled(n)},setDisabled:function(t){return mv.set(n,t)}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:[]},r,n,e),{toggling:{toggleClass:lh,toggleOnExecute:!1,selected:t.active}})},ly=function(n,e){return t=ry,o=n.fancytype,(Object.prototype.hasOwnProperty.call(t,o)?dt.some(t[o]):dt.none()).map(function(t){return t(n,e)});var t,o},fy=function(t,n,e,o){var r={dom:ib(!1,t.label),optComponents:[dt.some({dom:{tag:"div",classes:[gh,ph]},components:fb(t.items,o)})]};return Jv({data:$v(ft({text:dt.none()},t)),disabled:t.disabled,getApi:function(e){return{isDisabled:function(){return mv.isDisabled(e)},setDisabled:function(n){mv.set(e,n),mt(Os(e.element,"*"),function(t){e.getSystem().getByDom(t).each(function(t){t.hasConfigured(mv)&&mv.set(t,n)})})}}},onAction:t.onAction,onSetup:t.onSetup,triggersSubmenu:!1,itemBehaviours:dt.from(o.itemBehaviours).getOr([])},r,n,e.providers)};(Vb=Rb=Rb||{})[Vb.ContentFocus=0]="ContentFocus",Vb[Vb.UiFocus=1]="UiFocus";var dy,my,gy,py,hy=function(t,n,e,o,r){var i=e.shared.providers,u=function(t){return r?ft(ft({},t),{shortcut:dt.none(),icon:t.text.isSome()?dt.none():t.icon}):t};switch(t.type){case"menuitem":return _n("menuitem",Hh,t).fold(Oh,function(t){return dt.some(ay(u(t),n,i,o))});case"nestedmenuitem":return _n("nestedmenuitem",zh,t).fold(Oh,function(t){return dt.some(cy(u(t),n,i,o,r))});case"togglemenuitem":return _n("togglemenuitem",Nh,t).fold(Oh,function(t){return dt.some(sy(u(t),n,i,o))});case"separator":return _n("separatormenuitem",zp,t).fold(Oh,function(t){return dt.some(uy(t))});case"fancymenuitem":return _n("fancymenuitem",Ph,t).fold(Oh,function(t){return ly(u(t),e)});default:return console.error("Unknown item in general menu",t),dt.none()}},vy=function(t,e,o,n,r,i,u){var a=1===n,c=!a||Ch(t);return qf(V(t,function(t){switch(t.type){case"separator":return _n("Autocompleter.Separator",zp,t).fold(Oh,function(t){return dt.some(uy(t))});case"cardmenuitem":return _n("cardmenuitem",Rh,t).fold(Oh,function(n){return dt.some(fy(ft(ft({},n),{onAction:function(t){n.onAction(t),o(n.value,n.meta)}}),r,i,{itemBehaviours:sb(n.meta,i),cardText:{matchText:e,highlightOn:u}}))});case"autocompleteitem":default:return _n("Autocompleter.Item",Np,t).fold(Oh,function(t){return dt.some(iy(t,e,a,"normal",o,r,i,c))})}}))},by=function(t,n,e,o,r){var i=Ch(n),u=qf(V(n,function(t){var n=function(t){return hy(t,e,o,(n=t,r?!n.hasOwnProperty("text"):i),r);var n};return"nestedmenuitem"===t.type&&t.getSubmenuItems().length<=0?n(ft(ft({},t),{disabled:!0})):n(t)}));return(r?_h:Th)(t,i,u,1,"normal")},yy=function(t){return Qg.singleData(t.value,t)},xy=function(d,c){var e=fe(dt.none()),s=fe(!1),m=mu(Zg.sketch({dom:{tag:"div",classes:["tox-autocompleter"]},components:[],fireDismissalEventInstead:{},inlineBehaviours:rc([fg("dismissAutocompleter",[er(zo(),function(){return f()})])]),lazySink:c.getSink})),o=function(){return e.get().isSome()},l=function(){o()&&Zg.hide(m)},f=function(){var t;o()&&(t=e.get().map(function(t){return t.element}),Op(t.getOr(me.fromDom(d.selection.getNode()))).each(Ur),l(),e.set(dt.none()),s.set(!1))},r=Lt(function(){return eh(d)}),g=function(t,n,e,o){t.matchLength=n.text.length;var r,i,u,a,c,s,l,f=Q(e,function(t){return dt.from(t.columns)}).getOr(1);Zg.showAt(m,{anchor:"node",root:me.fromDom(d.getBody()),node:dt.from(t.element)},Xg.sketch((r=Th("autocompleter-value",!0,o,f,"normal"),i=f,u=Rb.ContentFocus,a="normal",c=(u===Rb.ContentFocus?Md:Ad)(),s=ty(i,a),l=bh(a),{dom:r.dom,components:r.components,items:r.items,value:r.value,markers:{selectedItem:l.selectedItem,item:l.item},movement:s,fakeFocus:u===Rb.ContentFocus,focusManager:c,menuBehaviours:Uh("auto"!==i?[]:[sr(function(o,t){Lh(o,4,l.item).each(function(t){var n=t.numColumns,e=t.numRows;ig.setGridSize(o,e,n)})})])}))),Zg.getContent(m).each(ld.highlightFirst)},p=function(t){var n;n=t,e.get().map(function(t){return Bp(d.dom,d.selection.getRng(),t.triggerChar).bind(function(t){return Hp(d,r,t,n)})}).getOrThunk(function(){return Pp(d,r)}).fold(f,function(a){var t,n;t=a.context,o()||(n=Cp(d,t.range),e.set(dt.some({triggerChar:t.triggerChar,element:n,matchLength:t.text.length})),s.set(!1)),a.lookupData.then(function(u){e.get().map(function(t){var n,e,o,r,i=a.context;t.triggerChar===i.triggerChar&&(e=i.triggerChar,r=Q(o=u,function(t){return dt.from(t.columns)}).getOr(1),0<(n=U(o,function(i){var t=i.items;return vy(t,i.matchText,function(o,r){var t=d.selection.getRng();Bp(d.dom,t,e).fold(function(){return console.error("Lost context. Cursor probably moved")},function(t){var n=t.range,e={hide:function(){f()},reload:function(t){l(),p(t)}};s.set(!0),i.onAction(e,n,o,r),s.set(!1)})},r,ih.BUBBLE_TO_SANDBOX,c,i.highlightOn)})).length?g(t,i,u,n):(10<=i.text.length-t.matchLength?f:l)())})})})},t={onKeypress:wp(function(t){27!==t.which&&p()},50),cancelIfNecessary:f,isMenuOpen:function(){return Zg.isOpen(m)},isActive:o,isProcessingAction:s.get,getView:function(){return Zg.getContent(m)}};!1===d.hasPlugin("rtc")&&Ap(t,d)},wy=O,Sy=function(t,n,e){return Eu(t,n,wy,e,!1)},ky=function(t,n,e){return Eu(t,n,wy,e,!0)},Cy=Tu,Oy=function(t,n,e){return Ku(t,n,e).isSome()},_y=function(e,o){var r=null;return{cancel:function(){null!==r&&(clearTimeout(r),r=null)},schedule:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];r=setTimeout(function(){e.apply(null,t),r=null},o)}}},Ty=function(t){var n=t.raw;return n.touches===undefined||1!==n.touches.length?dt.none():dt.some(n.touches[0])},Ey=function(e){var u=fe(dt.none()),o=fe(!1),r=_y(function(t){e.triggerEvent(Do(),t),o.set(!0)},400),i=$t([{key:no(),value:function(e){return Ty(e).each(function(t){r.cancel();var n={x:t.clientX,y:t.clientY,target:e.target};r.schedule(e),o.set(!1),u.set(dt.some(n))}),dt.none()}},{key:eo(),value:function(t){return r.cancel(),Ty(t).each(function(i){u.get().each(function(t){var n,e,o,r;n=i,e=t,o=Math.abs(n.clientX-e.x),r=Math.abs(n.clientY-e.y),(5<o||5<r)&&u.set(dt.none())})}),dt.none()}},{key:oo(),value:function(n){r.cancel();return u.get().filter(function(t){return Ge(t.target,n.target)}).map(function(t){return o.get()?(n.prevent(),!1):e.triggerEvent(Eo(),n)})}}]);return{fireIfReady:function(n,t){return Ft(i,t).bind(function(t){return t(n)})}}},Dy=function(){return je().browser.isFirefox()},By=ln([Gn("triggerEvent"),ne("stopBackspace",!0)]),Ay=function(n,t){var e,o,r,i,u=En("Getting GUI events settings",By,t),a=Ey(u),c=V(["touchstart","touchmove","touchend","touchcancel","gesturestart","mousedown","mouseup","mouseover","mousemove","mouseout","click"].concat(["selectstart","input","contextmenu","change","transitionend","drag","dragstart","dragend","dragenter","dragleave","dragover","drop","keyup"]),function(t){return Sy(n,t,function(n){a.fireIfReady(n,t).each(function(t){t&&n.kill()}),u.triggerEvent(t,n)&&n.kill()})}),s=fe(dt.none()),l=Sy(n,"paste",function(n){a.fireIfReady(n,"paste").each(function(t){t&&n.kill()}),u.triggerEvent("paste",n)&&n.kill(),s.set(dt.some(setTimeout(function(){u.triggerEvent(Co(),n)},0)))}),f=Sy(n,"keydown",function(t){var n;u.triggerEvent("keydown",t)?t.kill():!0!==u.stopBackspace||((n=t).raw.which!==fd[0]||M(["input","textarea"],mr(n.target))||Oy(n.target,'[contenteditable="true"]'))||t.prevent()}),d=(e=n,o=function(t){u.triggerEvent("focusin",t)&&t.kill()},Dy()?ky(e,"focus",o):Sy(e,"focusin",o)),m=fe(dt.none()),g=(r=n,i=function(t){u.triggerEvent("focusout",t)&&t.kill(),m.set(dt.some(setTimeout(function(){u.triggerEvent(ko(),t)},0)))},Dy()?ky(r,"blur",i):Sy(r,"focusout",i));return{unbind:function(){mt(c,function(t){t.unbind()}),f.unbind(),d.unbind(),g.unbind(),l.unbind(),s.get().each(clearTimeout),m.get().each(clearTimeout)}}},My=function(t,n){var e=Ft(t,"target").getOr(n);return fe(e)},Fy=Vt([{stopped:[]},{resume:["element"]},{complete:[]}]),Iy=function(t,o,n,e,r,i){var u,a,c,s,l=t(o,e),f=(u=n,a=r,c=fe(!1),s=fe(!1),{stop:function(){c.set(!0)},cut:function(){s.set(!0)},isStopped:c.get,isCut:s.get,event:u,setSource:a.set,getSource:a.get});return l.fold(function(){return i.logEventNoHandlers(o,e),Fy.complete()},function(n){var e=n.descHandler;return ki(e)(f),f.isStopped()?(i.logEventStopped(o,n.element,e.purpose),Fy.stopped()):f.isCut()?(i.logEventCut(o,n.element,e.purpose),Fy.complete()):kr(n.element).fold(function(){return i.logNoParent(o,n.element,e.purpose),Fy.complete()},function(t){return i.logEventResponse(o,n.element,e.purpose),Fy.resume(t)})})},Ry=function(n,e,o,t,r,i){return Iy(n,e,o,t,r,i).fold(O,function(t){return Ry(n,e,o,t,r,i)},l)},Vy=function(t,n,e){var o,r,i=(o=n,r=fe(!1),{stop:function(){r.set(!0)},cut:ct,isStopped:r.get,isCut:l,event:o,setSource:a("Cannot set source of a broadcasted event"),getSource:a("Cannot get source of a broadcasted event")});return mt(t,function(t){var n=t.descHandler;ki(n)(i)}),i.isStopped()},Py=function(t,n,e,o,r){var i=My(e,o);return Ry(t,n,e,o,i,r)},Hy=function(){var a={};return{registerId:function(i,u,t){_t(t,function(t,n){var e,o,r=a[n]!==undefined?a[n]:{};r[u]=(e=t,o=i,{cHandler:k.apply(undefined,[e.handler].concat(o)),purpose:e.purpose}),a[n]=r})},unregisterId:function(e){_t(a,function(t,n){t.hasOwnProperty(e)&&delete t[e]})},filterByType:function(t){return Ft(a,t).map(function(t){return Bt(t,function(t,n){return{id:n,descHandler:t}})}).getOr([])},find:function(t,n,e){var r=Ft(a,n);return Je(e,function(t){return e=r,si(o=t).fold(function(){return dt.none()},function(n){return e.bind(function(t){return Ft(t,n)}).map(function(t){return{element:o,descHandler:t}})});var e,o},t)}}},zy=function(){var o=Hy(),r={},i=function(o){var t=o.element;return si(t).fold(function(){return t="uid-",n=o.element,e=oi(ui+t),ci(n,e),e;var t,n,e},function(t){return t})},u=function(t){si(t.element).each(function(t){delete r[t],o.unregisterId(t)})};return{find:function(t,n,e){return o.find(t,n,e)},filter:function(t){return o.filterByType(t)},register:function(t){var n=i(t);Rt(r,n)&&function(t,n){var e=r[n];if(e!==t)throw new Error('The tagId "'+n+'" is already used by: '+Zr(e.element)+"\nCannot use it for: "+Zr(t.element)+"\nThe conflicting element is"+(ji(e.element)?" ":" not ")+"already in the DOM");u(t)}(t,n);var e=[t];o.registerId(e,n,t.events),r[n]=t},unregister:u,getById:function(t){return Ft(r,t)}}},Ny=Xf({name:"Container",factory:function(t){var n=t.dom,e=n.attributes,o=_(n,["attributes"]);return{uid:t.uid,dom:ft({tag:"div",attributes:ft({role:"presentation"},e)},o),components:t.components,behaviours:Ul(t.containerBehaviours),events:t.events,domModification:t.domModification,eventOrder:t.eventOrder}},configFields:[ne("components",[]),jl("containerBehaviours",[]),ne("events",{}),ne("domModification",{}),ne("eventOrder",{})]}),Ly=function(e){var o=function(n){return kr(e.element).fold(O,function(t){return Ge(n,t)})},r=zy(),s=function(t,n){return r.find(o,t,n)},t=Ay(e.element,{triggerEvent:function(o,r){return ra(o,r.target,function(t){return e=t,Py(s,o,n=r,n.target,e);var n,e})}}),i={debugInfo:st("real"),triggerEvent:function(n,e,o){ra(n,e,function(t){return Py(s,n,o,e,t)})},triggerFocus:function(a,c){si(a).fold(function(){dc(a)},function(t){ra(So(),a,function(t){var n,e,o,r,i,u;return n=s,e=So(),i=t,u=My(o={originator:c,kill:ct,prevent:ct,target:a},r=a),Iy(n,e,o,r,u,i),!1})})},triggerEscape:function(t,n){i.triggerEvent("keydown",t.element,n.event)},getByUid:function(t){return g(t)},getByDom:function(t){return p(t)},build:mu,addToGui:function(t){a(t)},removeFromGui:function(t){c(t)},addToWorld:function(t){n(t)},removeFromWorld:function(t){u(t)},broadcast:function(t){f(t)},broadcastOn:function(t,n){d(t,n)},broadcastEvent:function(t,n){m(t,n)},isConnected:O},n=function(t){t.connect(i),hr(t.element)||(r.register(t),mt(t.components(),n),i.triggerEvent(Mo(),t.element,{target:t.element}))},u=function(t){hr(t.element)||(mt(t.components(),u),r.unregister(t)),t.disconnect()},a=function(t){rl(e,t)},c=function(t){al(t)},l=function(e){var t=r.filter(Oo());mt(t,function(t){var n=t.descHandler;ki(n)(e)})},f=function(t){l({universal:!0,data:t})},d=function(t,n){l({universal:!1,channels:t,data:n})},m=function(t,n){var e=r.filter(t);return Vy(e,n)},g=function(t){return r.getById(t).fold(function(){return pt.error(new Error('Could not find component with uid: "'+t+'" in system.'))},pt.value)},p=function(t){var n=si(t).getOr("not found");return g(n)};return n(e),{root:e,element:e.element,destroy:function(){t.unbind(),jr(e.element)},add:a,remove:c,getByUid:g,getByDom:p,addToWorld:n,removeFromWorld:u,broadcast:f,broadcastOn:d,broadcastEvent:m}},jy=st([ne("prefix","form-field"),jl("fieldBehaviours",[Zf,Ll])]),Uy=st([xf({schema:[Ln("dom")],name:"label"}),xf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Ln("text")],name:"aria-descriptor"}),bf({factory:{sketch:function(t){var n=Kt(t,["factory"]);return t.factory.sketch(n)}},schema:[Ln("factory")],name:"field"})]),Wy=Yf({name:"FormField",configFields:jy(),partFields:Uy(),factory:function(r,t,n,e){var o=Wl(r.fieldBehaviours,[Zf.config({find:function(t){return Mf(t,r,"field")}}),Ll.config({store:{mode:"manual",getValue:function(t){return Zf.getCurrent(t).bind(Ll.getValue)},setValue:function(t,n){Zf.getCurrent(t).each(function(t){Ll.setValue(t,n)})}}})]),i=Zo([sr(function(t,n){var o=If(t,r,["label","field","aria-descriptor"]);o.field().each(function(e){var n=oi(r.prefix);o.label().each(function(t){Yr(t.element,"for",n),Yr(e.element,"id",n)}),o["aria-descriptor"]().each(function(t){var n=oi(r.prefix);Yr(t.element,"id",n),Yr(e.element,"aria-describedby",n)})})})]),u={getField:function(t){return Mf(t,r,"field")},getLabel:function(t){return Mf(t,r,"label")}};return{uid:r.uid,dom:r.dom,components:t,behaviours:o,events:i,apis:u}},apis:{getField:function(t,n){return t.getField(n)},getLabel:function(t,n){return t.getLabel(n)}}}),Gy=/* */Object.freeze({__proto__:null,exhibit:function(t,n){return Si({attributes:$t([{key:n.tabAttr,value:"true"}])})}}),Xy=[ne("tabAttr","data-alloy-tabstop")],Yy=uc({fields:Xy,name:"tabstopping",active:Gy}),qy=tinymce.util.Tools.resolve("tinymce.html.Entities"),Ky=function(t,n,e,o){var r=Jy(t,n,e,o);return Wy.sketch(r)},Jy=function(t,n,e,o){return{dom:$y(e),components:t.toArray().concat([n]),fieldBehaviours:rc(o)}},$y=function(t){return{tag:"div",classes:["tox-form__group"].concat(t)}},Qy=function(t,n){return Wy.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}})},Zy=oi("form-component-change"),tx=oi("form-close"),nx=oi("form-cancel"),ex=oi("form-action"),ox=oi("form-submit"),rx=oi("form-block"),ix=oi("form-unblock"),ux=oi("form-tabchange"),ax=oi("form-resize"),cx=function(u,a){var t,n,e,o=u.label.map(function(t){return Qy(t,a)}),r=function(o){return function(n,e){Ku(e.event.target,"[data-collection-item-value]").each(function(t){o(n,e,t,qr(t,"data-collection-item-value"))})}},i=r(function(t,n,e,o){n.stop(),a.isDisabled()||qo(t,ex,{name:u.name,value:o})}),c=[er(so(),r(function(t,n,e){dc(e)})),er(vo(),i),er(Eo(),i),er(lo(),r(function(t,n,e){qu(t.element,"."+mh).each(function(t){Pi(t,mh)}),Ri(e,mh)})),er(fo(),r(function(t){qu(t.element,"."+mh).each(function(t){Pi(t,mh)})})),dr(r(function(t,n,e,o){qo(t,ex,{name:u.name,value:o})}))],s=function(t,n){return V(Os(t.element,".tox-collection__item"),n)},l=Wy.parts.field({dom:{tag:"div",classes:["tox-collection"].concat(1!==u.columns?["tox-collection--grid"]:["tox-collection--list"])},components:[],factory:{sketch:lt},behaviours:rc([mv.config({disabled:a.isDisabled,onDisabled:function(t){s(t,function(t){Ri(t,"tox-collection__item--state-disabled"),Yr(t,"aria-disabled",!0)})},onEnabled:function(t){s(t,function(t){Pi(t,"tox-collection__item--state-disabled"),$r(t,"aria-disabled")})}}),Nv(),lg.config({}),Ll.config({store:{mode:"memory",initialValue:[]},onSetValue:function(o,t){var n,e,r,i;n=o,e=V(t,function(t){var n=Zh.translate(t.text),e=1===u.columns?'<div class="tox-collection__item-label">'+n+"</div>":"",o='<div class="tox-collection__item-icon">'+t.icon+"</div>",r={_:" "," - ":" ","-":" "},i=n.replace(/\_| \- |\-/g,function(t){return r[t]});return'<div class="tox-collection__item'+(a.isDisabled()?" tox-collection__item--state-disabled":"")+'" tabindex="-1" data-collection-item-value="'+qy.encodeAllRaw(t.value)+'" title="'+i+'" aria-label="'+i+'">'+o+e+"</div>"}),r="auto"!==u.columns&&1<u.columns?R(e,u.columns):[e],i=V(r,function(t){return'<div class="tox-collection__group">'+t.join("")+"</div>"}),Gr(n.element,i.join("")),"auto"===u.columns&&Lh(o,5,"tox-collection__item").each(function(t){var n=t.numRows,e=t.numColumns;ig.setGridSize(o,n,e)}),Yo(o,ax)}}),Yy.config({}),ig.config((n=u.columns,e="normal",1===n?{mode:"menu",moveOnTab:!1,selector:".tox-collection__item"}:"auto"===n?{mode:"flatgrid",selector:".tox-collection__item",initSize:{numColumns:1,numRows:1}}:{mode:"matrix",selectors:{row:"color"===e?".tox-swatches__row":".tox-collection__group",cell:"color"===e?"."+ch:"."+ah}})),fg("collection-events",c)]),eventOrder:((t={})[_o()]=["disabling","alloy.base.behaviour","collection-events"],t)});return Ky(o,l,["tox-form__group--collection"],[])},sx=st([Kn("data"),ne("inputAttributes",{}),ne("inputStyles",{}),ne("tag","input"),ne("inputClasses",[]),ma("onSetValue"),ne("styles",{}),ne("eventOrder",{}),jl("inputBehaviours",[Ll,hg]),ne("selectOnFocus",!0)]),lx=function(t){return rc([hg.config({onFocus:t.selectOnFocus?function(t){var n=t.element,e=eu(n);n.dom.setSelectionRange(0,e.length)}:ct})])},fx=function(t){return{tag:t.tag,attributes:ft({type:"text"},t.inputAttributes),styles:t.inputStyles,classes:t.inputClasses}},dx=Xf({name:"Input",configFields:sx(),factory:function(t,n){return{uid:t.uid,dom:fx(t),components:[],behaviours:ft(ft({},lx(e=t)),Wl(e.inputBehaviours,[Ll.config({store:ft(ft({mode:"manual"},e.data.map(function(t){return{initialValue:t}}).getOr({})),{getValue:function(t){return eu(t.element)},setValue:function(t,n){eu(t.element)!==n&&ou(t.element,n)}}),onSetValue:e.onSetValue})])),eventOrder:t.eventOrder};var e}}),mx={},gx={exports:mx};dy=undefined,my=mx,gy=gx,py=undefined,function(t){"object"==typeof my&&void 0!==gy?gy.exports=t():"function"==typeof dy&&dy.amd?dy([],t):("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).EphoxContactWrapper=t()}(function(){return function l(i,u,a){function c(n,t){if(!u[n]){if(!i[n]){var e="function"==typeof py&&py;if(!t&&e)return e(n,!0);if(s)return s(n,!0);var o=new Error("Cannot find module '"+n+"'");throw o.code="MODULE_NOT_FOUND",o}var r=u[n]={exports:{}};i[n][0].call(r.exports,function(t){return c(i[n][1][t]||t)},r,r.exports,l,i,u,a)}return u[n].exports}for(var s="function"==typeof py&&py,t=0;t<a.length;t++)c(a[t]);return c}({1:[function(t,n,e){var o,r,i=n.exports={};function u(){throw new Error("setTimeout has not been defined")}function a(){throw new Error("clearTimeout has not been defined")}function c(t){if(o===setTimeout)return setTimeout(t,0);if((o===u||!o)&&setTimeout)return o=setTimeout,setTimeout(t,0);try{return o(t,0)}catch(n){try{return o.call(null,t,0)}catch(n){return o.call(this,t,0)}}}!function(){try{o="function"==typeof setTimeout?setTimeout:u}catch(t){o=u}try{r="function"==typeof clearTimeout?clearTimeout:a}catch(t){r=a}}();var s,l=[],f=!1,d=-1;function m(){f&&s&&(f=!1,s.length?l=s.concat(l):d=-1,l.length&&g())}function g(){if(!f){var t=c(m);f=!0;for(var n=l.length;n;){for(s=l,l=[];++d<n;)s&&s[d].run();d=-1,n=l.length}s=null,f=!1,function(t){if(r===clearTimeout)return clearTimeout(t);if((r===a||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(t);try{r(t)}catch(n){try{return r.call(null,t)}catch(n){return r.call(this,t)}}}(t)}}function p(t,n){this.fun=t,this.array=n}function h(){}i.nextTick=function(t){var n=new Array(arguments.length-1);if(1<arguments.length)for(var e=1;e<arguments.length;e++)n[e-1]=arguments[e];l.push(new p(t,n)),1!==l.length||f||c(g)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=h,i.addListener=h,i.once=h,i.off=h,i.removeListener=h,i.removeAllListeners=h,i.emit=h,i.prependListener=h,i.prependOnceListener=h,i.listeners=function(t){return[]},i.binding=function(t){throw new Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(t){throw new Error("process.chdir is not supported")},i.umask=function(){return 0}},{}],2:[function(t,f,n){(function(n){function o(){}function u(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=0,this._handled=!1,this._value=undefined,this._deferreds=[],l(t,this)}function r(o,r){for(;3===o._state;)o=o._value;0!==o._state?(o._handled=!0,u._immediateFn(function(){var t,n=1===o._state?r.onFulfilled:r.onRejected;if(null!==n){try{t=n(o._value)}catch(e){return void a(r.promise,e)}i(r.promise,t)}else(1===o._state?i:a)(r.promise,o._value)})):o._deferreds.push(r)}function i(t,n){try{if(n===t)throw new TypeError("A promise cannot be resolved with itself.");if(n&&("object"==typeof n||"function"==typeof n)){var e=n.then;if(n instanceof u)return t._state=3,t._value=n,void c(t);if("function"==typeof e)return void l((o=e,r=n,function(){o.apply(r,arguments)}),t)}t._state=1,t._value=n,c(t)}catch(i){a(t,i)}var o,r}function a(t,n){t._state=2,t._value=n,c(t)}function c(t){2===t._state&&0===t._deferreds.length&&u._immediateFn(function(){t._handled||u._unhandledRejectionFn(t._value)});for(var n=0,e=t._deferreds.length;n<e;n++)r(t,t._deferreds[n]);t._deferreds=null}function s(t,n,e){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.promise=e}function l(t,n){var e=!1;try{t(function(t){e||(e=!0,i(n,t))},function(t){e||(e=!0,a(n,t))})}catch(o){if(e)return;e=!0,a(n,o)}}var t,e;t=this,e=setTimeout,u.prototype["catch"]=function(t){return this.then(null,t)},u.prototype.then=function(t,n){var e=new this.constructor(o);return r(this,new s(t,n,e)),e},u.all=function(t){var c=Array.prototype.slice.call(t);return new u(function(r,i){if(0===c.length)return r([]);var u=c.length;for(var t=0;t<c.length;t++)!function a(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}}(t,c[t])})},u.resolve=function(n){return n&&"object"==typeof n&&n.constructor===u?n:new u(function(t){t(n)})},u.reject=function(e){return new u(function(t,n){n(e)})},u.race=function(r){return new u(function(t,n){for(var e=0,o=r.length;e<o;e++)r[e].then(t,n)})},u._immediateFn="function"==typeof n?function(t){n(t)}:function(t){e(t,0)},u._unhandledRejectionFn=function(t){"undefined"!=typeof console&&console&&console.warn("Possible Unhandled Promise Rejection:",t)},u._setImmediateFn=function(t){u._immediateFn=t},u._setUnhandledRejectionFn=function(t){u._unhandledRejectionFn=t},void 0!==f&&f.exports?f.exports=u:t.Promise||(t.Promise=u)}).call(this,t("timers").setImmediate)},{timers:3}],3:[function(c,t,s){(function(t,n){var o=c("process/browser.js").nextTick,e=Function.prototype.apply,r=Array.prototype.slice,i={},u=0;function a(t,n){this._id=t,this._clearFn=n}s.setTimeout=function(){return new a(e.call(setTimeout,window,arguments),clearTimeout)},s.setInterval=function(){return new a(e.call(setInterval,window,arguments),clearInterval)},s.clearTimeout=s.clearInterval=function(t){t.close()},a.prototype.unref=a.prototype.ref=function(){},a.prototype.close=function(){this._clearFn.call(window,this._id)},s.enroll=function(t,n){clearTimeout(t._idleTimeoutId),t._idleTimeout=n},s.unenroll=function(t){clearTimeout(t._idleTimeoutId),t._idleTimeout=-1},s._unrefActive=s.active=function(t){clearTimeout(t._idleTimeoutId);var n=t._idleTimeout;0<=n&&(t._idleTimeoutId=setTimeout(function(){t._onTimeout&&t._onTimeout()},n))},s.setImmediate="function"==typeof t?t:function(t){var n=u++,e=!(arguments.length<2)&&r.call(arguments,1);return i[n]=!0,o(function(){i[n]&&(e?t.apply(null,e):t.call(null),s.clearImmediate(n))}),n},s.clearImmediate="function"==typeof n?n:function(t){delete i[t]}}).call(this,c("timers").setImmediate,c("timers").clearImmediate)},{"process/browser.js":1,timers:3}],4:[function(t,n,e){var o=t("promise-polyfill"),r="undefined"!=typeof window?window:Function("return this;")();n.exports={boltExport:r.Promise||o}},{"promise-polyfill":2}]},{},[4])(4)});var px,hx,vx=gx.exports.boltExport,bx=function(t){var e=dt.none(),n=[],o=function(t){r()?u(t):n.push(t)},r=function(){return e.isSome()},i=function(t){mt(t,u)},u=function(n){e.each(function(t){setTimeout(function(){n(t)},0)})};return t(function(t){r()||(e=dt.some(t),i(n),n=[])}),{get:o,map:function(e){return bx(function(n){o(function(t){n(e(t))})})},isReady:r}},yx={nu:bx,pure:function(n){return bx(function(t){t(n)})}},xx=function(t){setTimeout(function(){throw t},0)},wx=function(e){var t=function(t){e().then(t,xx)};return{map:function(t){return wx(function(){return e().then(t)})},bind:function(n){return wx(function(){return e().then(function(t){return n(t).toPromise()})})},anonBind:function(t){return wx(function(){return e().then(function(){return t.toPromise()})})},toLazy:function(){return yx.nu(t)},toCached:function(){var t=null;return wx(function(){return null===t&&(t=e()),t})},toPromise:e,get:t}},Sx=function(t){return wx(function(){return new vx(t)})},kx=function(t){return wx(function(){return vx.resolve(t)})},Cx=["input","textarea"],Ox=function(t){var n=mr(t);return M(Cx,n)},_x=function(t,n){var e=n.getRoot(t).getOr(t.element);Pi(e,n.invalidClass),n.notify.each(function(n){Ox(t.element)&&Yr(t.element,"aria-invalid",!1),n.getContainer(t).each(function(t){Gr(t,n.validHtml)}),n.onValid(t)})},Tx=function(n,t,e,o){var r=t.getRoot(n).getOr(n.element);Ri(r,t.invalidClass),t.notify.each(function(t){Ox(n.element)&&Yr(n.element,"aria-invalid",!0),t.getContainer(n).each(function(t){Gr(t,o)}),t.onInvalid(n,o)})},Ex=function(n,t,e){return t.validator.fold(function(){return kx(pt.value(!0))},function(t){return t.validate(n)})},Dx=function(n,e,t){return e.notify.each(function(t){t.onValidate(n)}),Ex(n,e).map(function(t){return n.getSystem().isConnected()?t.fold(function(t){return Tx(n,e,0,t),pt.error(t)},function(t){return _x(n,e),pt.value(t)}):pt.error("No longer in system")})},Bx=/* */Object.freeze({__proto__:null,markValid:_x,markInvalid:Tx,query:Ex,run:Dx,isInvalid:function(t,n){var e=n.getRoot(t).getOr(t.element);return Hi(e,n.invalidClass)}}),Ax=/* */Object.freeze({__proto__:null,events:function(n,t){return n.validator.map(function(t){return Zo([er(t.onEvent,function(t){Dx(t,n).get(lt)})].concat(t.validateOnLoad?[sr(function(t){Dx(t,n).get(ct)})]:[]))}).getOr({})}}),Mx=[Ln("invalidClass"),ne("getRoot",dt.none),te("notify",[ne("aria","alert"),ne("getContainer",dt.none),ne("validHtml",""),ma("onValid"),ma("onInvalid"),ma("onValidate")]),te("validator",[Ln("validate"),ne("onEvent","input"),ne("validateOnLoad",!0)])],Fx=uc({fields:Mx,name:"invalidating",active:Ax,apis:Bx,extra:{validation:function(e){return function(t){var n=Ll.getValue(t);return kx(e(n))}}}}),Ix=/* */Object.freeze({__proto__:null,getCoupled:function(t,n,e,o){return e.getOrCreate(t,n,o)}}),Rx=[jn("others",On(pt.value,Mn()))],Vx=uc({fields:Rx,name:"coupling",apis:Ix,state:/* */Object.freeze({__proto__:null,init:function(){var i={},t=st({});return xi({readState:t,getOrCreate:function(e,o,r){var t=Ct(o.others);if(t)return Ft(i,r).getOrThunk(function(){var t=Ft(o.others,r).getOrDie("No information found for coupled component: "+r)(e),n=e.getSystem().build(t);return i[r]=n});throw new Error("Cannot find coupled component: "+r+". Known coupled components: "+JSON.stringify(t,null,2))}})}})}),Px=st("sink"),Hx=st(xf({name:Px(),overrides:st({dom:{tag:"div"},behaviours:rc([Zs.config({useFixed:O})]),events:Zo([ar(mo()),ar(io()),ar(vo())])})}));(hx=px=px||{})[hx.HighlightFirst=0]="HighlightFirst",hx[hx.HighlightNone=1]="HighlightNone";var zx,Nx,Lx,jx,Ux,Wx,Gx=function(t,n){var e=t.getHotspot(n).getOr(n),o=t.getAnchorOverrides();return t.layouts.fold(function(){return{anchor:"hotspot",hotspot:e,overrides:o}},function(t){return{anchor:"hotspot",hotspot:e,overrides:o,layouts:t}})},Xx=function(t,n,e,o,r,i,u){var a,c,s,l,f,d,m,g,p,h,v=Gx(t,e);return(c=v,l=o,f=r,d=u,m=n,g=s=e,p=(0,(a=t).fetch)(g).map(m),h=Jx(s,a),p.map(function(t){return t.bind(function(t){return dt.from(Qg.sketch(ft(ft({},f.menu()),{uid:li(""),data:t,highlightImmediately:d===px.HighlightFirst,onOpenMenu:function(t,n){var e=h().getOrDie();Zs.position(e,c,n),wl.decloak(l)},onOpenSubmenu:function(t,n,e){var o=h().getOrDie();Zs.position(o,{anchor:"submenu",item:n},e),wl.decloak(l)},onRepositionMenu:function(t,n,e){var o=h().getOrDie();Zs.position(o,c,n),mt(e,function(t){Zs.position(o,{anchor:"submenu",item:t.triggeringItem},t.triggeredMenu)})},onEscape:function(){return hg.focus(s),wl.close(l),dt.some(!0)}})))})})).map(function(t){return t.fold(function(){wl.isOpen(o)&&wl.close(o)},function(t){wl.cloak(o),wl.open(o,t),i(o)}),o})},Yx=function(t,n,e,o,r,i,u){return wl.close(o),kx(o)},qx=function(t,n,e,o,r,i){var u=Vx.getCoupled(e,"sandbox");return(wl.isOpen(u)?Yx:Xx)(t,n,e,u,o,r,i)},Kx=function(t,n,e){var o,r,i=Zf.getCurrent(n).getOr(n),u=Ou(t.element);e?Yi(i.element,"min-width",u+"px"):(o=i.element,r=u,Cu.set(o,r))},Jx=function(n,t){return n.getSystem().getByUid(t.uid+"-"+Px()).map(function(t){return function(){return pt.value(t)}}).getOrThunk(function(){return t.lazySink.fold(function(){return function(){return pt.error(new Error("No internal sink is specified, nor could an external sink be found"))}},function(t){return function(){return t(n)}})})},$x=function(t){wl.getState(t).each(function(t){Qg.repositionMenus(t)})},Qx=function(o,r,i){var u=Ju(),t=Jx(r,o);return{dom:{tag:"div",classes:o.sandboxClasses,attributes:{id:u.id,role:"listbox"}},behaviours:Xl(o.sandboxBehaviours,[Ll.config({store:{mode:"memory",initialValue:r}}),wl.config({onOpen:function(t,n){var e=Gx(o,r);u.link(r.element),o.matchWidth&&Kx(e.hotspot,n,o.useMinWidth),o.onOpen(e,t,n),i!==undefined&&i.onOpen!==undefined&&i.onOpen(t,n)},onClose:function(t,n){u.unlink(r.element),i!==undefined&&i.onClose!==undefined&&i.onClose(t,n)},isPartOf:function(t,n,e){return Qu(n,e)||Qu(r,e)},getAttachPoint:function(){return t().getOrDie()}}),Zf.config({find:function(t){return wl.getState(t).bind(function(t){return Zf.getCurrent(t)})}}),lc.config({channels:ft(ft({},_l({isExtraPart:l})),El({doReposition:$x}))})])}},Zx=function(t){var n=Vx.getCoupled(t,"sandbox");$x(n)},tw=function(){return[ne("sandboxClasses",[]),Gl("sandboxBehaviours",[Zf,lc,wl,Ll])]},nw=st([Ln("dom"),Ln("fetch"),ma("onOpen"),ga("onExecute"),ne("getHotspot",dt.some),ne("getAnchorOverrides",st({})),qc(),jl("dropdownBehaviours",[Eg,Vx,ig,hg]),Ln("toggleClass"),ne("eventOrder",{}),Kn("lazySink"),ne("matchWidth",!1),ne("useMinWidth",!1),Kn("role")].concat(tw())),ew=st([yf({schema:[la()],name:"menu",defaults:function(t){return{onExecute:t.onExecute}}}),Hx()]),ow=Yf({name:"Dropdown",configFields:nw(),partFields:ew(),factory:function(n,t,e,o){var r,i,u=function(t){wl.getState(t).each(function(t){Qg.highlightPrimary(t)})},a={expand:function(t){Eg.isOn(t)||qx(n,function(t){return t},t,o,ct,px.HighlightNone).get(ct)},open:function(t){Eg.isOn(t)||qx(n,function(t){return t},t,o,ct,px.HighlightFirst).get(ct)},isOpen:Eg.isOn,close:function(t){Eg.isOn(t)&&qx(n,function(t){return t},t,o,ct,px.HighlightFirst).get(ct)},repositionMenus:function(t){Eg.isOn(t)&&Zx(t)}},c=function(t,n){return Ko(t),dt.some(!0)};return{uid:n.uid,dom:n.dom,components:t,behaviours:Wl(n.dropdownBehaviours,[Eg.config({toggleClass:n.toggleClass,aria:{mode:"expanded"}}),Vx.config({others:{sandbox:function(t){return Qx(n,t,{onOpen:function(){return Eg.on(t)},onClose:function(){return Eg.off(t)}})}}}),ig.config({mode:"special",onSpace:c,onEnter:c,onDown:function(t,n){var e;return ow.isOpen(t)?(e=Vx.getCoupled(t,"sandbox"),u(e)):ow.open(t),dt.some(!0)},onEscape:function(t,n){return ow.isOpen(t)?(ow.close(t),dt.some(!0)):dt.none()}}),hg.config({})]),events:Bg(dt.some(function(t){qx(n,function(t){return t},t,o,u,px.HighlightFirst).get(ct)})),eventOrder:ft(ft({},n.eventOrder),((r={})[_o()]=["disabling","toggling","alloy.base.behaviour"],r)),apis:a,domModification:{attributes:ft(ft({"aria-haspopup":"true"},n.role.fold(function(){return{}},function(t){return{role:t}})),"button"===n.dom.tag?{type:(i="type",Ft(n.dom,"attributes").bind(function(t){return Ft(t,i)}).getOr("button"))}:{})}}},apis:{open:function(t,n){return t.open(n)},expand:function(t,n){return t.expand(n)},close:function(t,n){return t.close(n)},isOpen:function(t,n){return t.isOpen(n)},repositionMenus:function(t,n){return t.repositionMenus(n)}}}),rw=uc({fields:[],name:"unselecting",active:/* */Object.freeze({__proto__:null,events:function(){return Zo([tr(yo(),O)])},exhibit:function(){return Si({styles:{"-webkit-user-select":"none","user-select":"none","-ms-user-select":"none","-moz-user-select":"-moz-none"},attributes:{unselectable:"on"}})}})}),iw=oi("color-input-change"),uw=oi("color-swatch-change"),aw=oi("color-picker-cancel"),cw=function(e,n,o){var r,i,t=Wy.parts.field({factory:dx,inputClasses:["tox-textfield"],onSetValue:function(t){return Fx.run(t).get(ct)},inputBehaviours:rc([mv.config({disabled:n.providers.isDisabled}),Nv(),Yy.config({}),Fx.config({invalidClass:"tox-textbox-field-invalid",getRoot:function(t){return kr(t.element)},notify:{onValid:function(t){var n=Ll.getValue(t);qo(t,iw,{color:n})}},validator:{validateOnLoad:!1,validate:function(t){var n=Ll.getValue(t);if(0===n.length)return kx(pt.value(!0));var e=me.fromTag("span");Yi(e,"background-color",n);var o=Qi(e,"background-color").fold(function(){return pt.error("blah")},function(t){return pt.value(n)});return kx(o)}}})]),selectOnFocus:!1}),u=e.label.map(function(t){return Qy(t,n.providers)}),a=function(t,n){qo(t,uw,{value:n})},c=mp((r={dom:{tag:"span",attributes:{"aria-label":n.providers.translate("Color swatch")}},layouts:{onRtl:function(){return[Ha,Pa,ja]},onLtr:function(){return[Pa,Ha,ja]}},components:[],fetch:Yb(o.getColors(),o.hasCustomColors()),columns:o.getColorCols(),presets:"color",onItemAction:function(t,e){c.getOpt(t).each(function(n){"custom"===e?o.colorPicker(function(t){t.fold(function(){return Yo(n,aw)},function(t){a(n,t),jb(t)})},"#ffffff"):a(n,"remove"===e?"":e)})}},i=n,ow.sketch({dom:r.dom,components:r.components,toggleClass:"mce-active",dropdownBehaviours:rc([jv(i.providers.isDisabled),Nv(),rw.config({}),Yy.config({})]),layouts:r.layouts,sandboxClasses:["tox-dialog__popups"],lazySink:i.getSink,fetch:function(n){return Sx(function(t){return r.fetch(t)}).map(function(t){return dt.from(yy(zt(Qb(oi("menu-value"),t,function(t){r.onItemAction(n,t)},r.columns,r.presets,ih.CLOSE_ON_EXECUTE,l,i.providers),{movement:ty(r.columns,r.presets)})))})},parts:{menu:xh(0,0,r.presets)}})));return Wy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:u.toArray().concat([{dom:{tag:"div",classes:["tox-color-input"]},components:[t,c.asSpec()]}]),fieldBehaviours:rc([fg("form-field-events",[er(iw,function(t,n){c.getOpt(t).each(function(t){Yi(t.element,"background-color",n.event.color)}),qo(t,Zy,{name:e.name})}),er(uw,function(n,e){Wy.getField(n).each(function(t){Ll.setValue(t,e.event.value),Zf.getCurrent(n).each(hg.focus)})}),er(aw,function(n,t){Wy.getField(n).each(function(t){Zf.getCurrent(n).each(hg.focus)})})])])})},sw=xf({schema:[Ln("dom")],name:"label"}),lw=function(n){return xf({name:n+"-edge",overrides:function(t){return t.model.manager.edgeActions[n].fold(function(){return{}},function(o){return{events:Zo([or(no(),function(t,n,e){return o(t,e)},[t]),or(io(),function(t,n,e){return o(t,e)},[t]),or(uo(),function(t,n,e){e.mouseIsDown.get()&&o(t,e)},[t])])}})}})},fw=lw("top-left"),dw=lw("top"),mw=lw("top-right"),gw=lw("right"),pw=lw("bottom-right"),hw=lw("bottom"),vw=lw("bottom-left"),bw=[sw,lw("left"),gw,dw,hw,fw,mw,vw,pw,bf({name:"thumb",defaults:st({dom:{styles:{position:"absolute"}}}),overrides:function(t){return{events:Zo([ir(no(),t,"spectrum"),ir(eo(),t,"spectrum"),ir(oo(),t,"spectrum"),ir(io(),t,"spectrum"),ir(uo(),t,"spectrum"),ir(co(),t,"spectrum")])}}}),bf({schema:[le("mouseIsDown",function(){return fe(!1)})],name:"spectrum",overrides:function(e){var o=e.model.manager,r=function(n,t){return o.getValueFromEvent(t).map(function(t){return o.setValueFrom(n,e,t)})};return{behaviours:rc([ig.config({mode:"special",onLeft:function(t){return o.onLeft(t,e)},onRight:function(t){return o.onRight(t,e)},onUp:function(t){return o.onUp(t,e)},onDown:function(t){return o.onDown(t,e)}}),hg.config({})]),events:Zo([er(no(),r),er(eo(),r),er(io(),r),er(uo(),function(t,n){e.mouseIsDown.get()&&r(t,n)})])}}})],yw=st("slider.change.value"),xw=function(t){var n=t.event.raw;return-1===n.type.indexOf("touch")?n.clientX!==undefined?dt.some(n).map(function(t){return xu(t.clientX,t.clientY)}):dt.none():n.touches!==undefined&&1===n.touches.length?dt.some(n.touches[0]).map(function(t){return xu(t.clientX,t.clientY)}):dt.none()},ww=function(t){return t.model.minX},Sw=function(t){return t.model.minY},kw=function(t){return t.model.minX-1},Cw=function(t){return t.model.minY-1},Ow=function(t){return t.model.maxX},_w=function(t){return t.model.maxY},Tw=function(t){return t.model.maxX+1},Ew=function(t){return t.model.maxY+1},Dw=function(t,n,e){return n(t)-e(t)},Bw=function(t){return Dw(t,Ow,ww)},Aw=function(t){return Dw(t,_w,Sw)},Mw=function(t){return Bw(t)/2},Fw=function(t){return Aw(t)/2},Iw=function(t){return t.stepSize},Rw=function(t){return t.snapToGrid},Vw=function(t){return t.snapStart},Pw=function(t){return t.rounded},Hw=function(t,n){return t[n+"-edge"]!==undefined},zw=function(t){return Hw(t,"left")},Nw=function(t){return Hw(t,"right")},Lw=function(t){return Hw(t,"top")},jw=function(t){return Hw(t,"bottom")},Uw=function(t){return t.model.value.get()},Ww=function(t){return{x:t}},Gw=function(t){return{y:t}},Xw=function(t,n){return{x:t,y:n}},Yw=function(t,n){qo(t,yw(),{value:n})},qw=function(t,n,e,o){return t<n?t:e<t?e:t===n?n-1:Math.max(n,t-o)},Kw=function(t,n,e,o){return e<t?t:t<n?n:t===e?e+1:Math.min(e,t+o)},Jw=function(t,n,e){return Math.max(n,Math.min(e,t))},$w=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.step,u=t.snap,a=t.snapStart,c=t.rounded,s=t.hasMinEdge,l=t.hasMaxEdge,f=t.minBound,d=t.maxBound,m=t.screenRange,g=s?n-1:n,p=l?e+1:e;if(r<f)return g;if(d<r)return p;var h,v,b,y,x,w,S,k=(x=r,w=f,S=d,Math.min(S,Math.max(x,w))-w),C=Jw(k/m*o+n,g,p);return u&&n<=C&&C<=e?(h=C,v=n,b=e,y=i,a.fold(function(){var t=h-v,n=Math.round(t/y)*y;return Jw(v+n,v-1,b+1)},function(t){var n=(h-t)%y,e=Math.round(n/y),o=Math.floor((h-t)/y),r=Math.floor((b-t)/y),i=t+Math.min(r,o+e)*y;return Math.max(t,i)})):c?Math.round(C):C},Qw=function(t){var n=t.min,e=t.max,o=t.range,r=t.value,i=t.hasMinEdge,u=t.hasMaxEdge,a=t.maxBound,c=t.maxOffset,s=t.centerMinEdge,l=t.centerMaxEdge;return r<n?i?0:s:e<r?u?a:l:(r-n)/o*c},Zw="left",tS=function(t){return t.element.dom.getBoundingClientRect()},nS=function(t,n){return t[n]},eS=function(t){var n=tS(t);return nS(n,Zw)},oS=function(t){var n=tS(t);return nS(n,"right")},rS=function(t){var n=tS(t);return nS(n,"top")},iS=function(t){var n=tS(t);return nS(n,"bottom")},uS=function(t){var n=tS(t);return nS(n,"width")},aS=function(t){var n=tS(t);return nS(n,"height")},cS=function(t,n,e){return(t+n)/2-e},sS=function(t,n){var e=tS(t),o=tS(n),r=nS(e,Zw),i=nS(e,"right"),u=nS(o,Zw);return cS(r,i,u)},lS=function(t,n){var e=tS(t),o=tS(n),r=nS(e,"top"),i=nS(e,"bottom"),u=nS(o,"top");return cS(r,i,u)},fS=function(t,n){qo(t,yw(),{value:n})},dS=function(t){return{x:t}},mS=function(t,n,e){var o={min:ww(n),max:Ow(n),range:Bw(n),value:e,step:Iw(n),snap:Rw(n),snapStart:Vw(n),rounded:Pw(n),hasMinEdge:zw(n),hasMaxEdge:Nw(n),minBound:eS(t),maxBound:oS(t),screenRange:uS(t)};return $w(o)},gS=function(i){return function(t,n){return e=t,r=(0<i?Kw:qw)(Uw(o=n).x,ww(o),Ow(o),Iw(o)),fS(e,dS(r)),dt.some(r).map(O);var e,o,r}},pS=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=uS(u=n),d=s.bind(function(t){return dt.some(sS(t,u))}).getOr(0),m=l.bind(function(t){return dt.some(sS(t,u))}).getOr(f),g={min:ww(a),max:Ow(a),range:Bw(a),value:c,hasMinEdge:zw(a),hasMaxEdge:Nw(a),minBound:eS(u),minOffset:0,maxBound:oS(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},Qw(g));return eS(n)-eS(t)+p},hS=gS(-1),vS=gS(1),bS=dt.none,yS=dt.none,xS={"top-left":dt.none(),top:dt.none(),"top-right":dt.none(),right:dt.some(function(t,n){Yw(t,Ww(Tw(n)))}),"bottom-right":dt.none(),bottom:dt.none(),"bottom-left":dt.none(),left:dt.some(function(t,n){Yw(t,Ww(kw(n)))})},wS=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=mS(t,n,e),r=dS(o);return fS(t,r),o},setToMin:function(t,n){var e=ww(n);fS(t,dS(e))},setToMax:function(t,n){var e=Ow(n);fS(t,dS(e))},findValueOfOffset:mS,getValueFromEvent:function(t){return xw(t).map(function(t){return t.left})},findPositionOfValue:pS,setPositionFromValue:function(t,n,e,o){var r=Uw(e),i=pS(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=Ou(n.element)/2;Yi(n.element,"left",i-u+"px")},onLeft:hS,onRight:vS,onUp:bS,onDown:yS,edgeActions:xS}),SS=function(t,n){qo(t,yw(),{value:n})},kS=function(t){return{y:t}},CS=function(t,n,e){var o={min:Sw(n),max:_w(n),range:Aw(n),value:e,step:Iw(n),snap:Rw(n),snapStart:Vw(n),rounded:Pw(n),hasMinEdge:Lw(n),hasMaxEdge:jw(n),minBound:rS(t),maxBound:iS(t),screenRange:aS(t)};return $w(o)},OS=function(i){return function(t,n){return e=t,r=(0<i?Kw:qw)(Uw(o=n).y,Sw(o),_w(o),Iw(o)),SS(e,kS(r)),dt.some(r).map(O);var e,o,r}},_S=function(t,n,e,o,r,i){var u,a,c,s,l,f,d,m,g,p=(a=i,c=e,s=o,l=r,f=aS(u=n),d=s.bind(function(t){return dt.some(lS(t,u))}).getOr(0),m=l.bind(function(t){return dt.some(lS(t,u))}).getOr(f),g={min:Sw(a),max:_w(a),range:Aw(a),value:c,hasMinEdge:Lw(a),hasMaxEdge:jw(a),minBound:rS(u),minOffset:0,maxBound:iS(u),maxOffset:f,centerMinEdge:d,centerMaxEdge:m},Qw(g));return rS(n)-rS(t)+p},TS=dt.none,ES=dt.none,DS=OS(-1),BS=OS(1),AS={"top-left":dt.none(),top:dt.some(function(t,n){Yw(t,Gw(Cw(n)))}),"top-right":dt.none(),right:dt.none(),"bottom-right":dt.none(),bottom:dt.some(function(t,n){Yw(t,Gw(Ew(n)))}),"bottom-left":dt.none(),left:dt.none()},MS=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=CS(t,n,e),r=kS(o);return SS(t,r),o},setToMin:function(t,n){var e=Sw(n);SS(t,kS(e))},setToMax:function(t,n){var e=_w(n);SS(t,kS(e))},findValueOfOffset:CS,getValueFromEvent:function(t){return xw(t).map(function(t){return t.top})},findPositionOfValue:_S,setPositionFromValue:function(t,n,e,o){var r=Uw(e),i=_S(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),u=vu(n.element)/2;Yi(n.element,"top",i-u+"px")},onLeft:TS,onRight:ES,onUp:DS,onDown:BS,edgeActions:AS}),FS=function(t,n){qo(t,yw(),{value:n})},IS=function(t,n){return{x:t,y:n}},RS=function(c,s){return function(t,n){return o=t,r=n,i=0<c?Kw:qw,u=(e=s)?Uw(r).x:i(Uw(r).x,ww(r),Ow(r),Iw(r)),a=e?i(Uw(r).y,Sw(r),_w(r),Iw(r)):Uw(r).y,FS(o,IS(u,a)),dt.some(u).map(O);var e,o,r,i,u,a}},VS=xw,PS=RS(-1,!1),HS=RS(1,!1),zS=RS(-1,!0),NS=RS(1,!0),LS={"top-left":dt.some(function(t,n){Yw(t,Xw(kw(n),Cw(n)))}),top:dt.some(function(t,n){Yw(t,Xw(Mw(n),Cw(n)))}),"top-right":dt.some(function(t,n){Yw(t,Xw(Tw(n),Cw(n)))}),right:dt.some(function(t,n){Yw(t,Xw(Tw(n),Fw(n)))}),"bottom-right":dt.some(function(t,n){Yw(t,Xw(Tw(n),Ew(n)))}),bottom:dt.some(function(t,n){Yw(t,Xw(Mw(n),Ew(n)))}),"bottom-left":dt.some(function(t,n){Yw(t,Xw(kw(n),Ew(n)))}),left:dt.some(function(t,n){Yw(t,Xw(kw(n),Fw(n)))})},jS=/* */Object.freeze({__proto__:null,setValueFrom:function(t,n,e){var o=mS(t,n,e.left),r=CS(t,n,e.top),i=IS(o,r);return FS(t,i),i},setToMin:function(t,n){var e=ww(n),o=Sw(n);FS(t,IS(e,o))},setToMax:function(t,n){var e=Ow(n),o=_w(n);FS(t,IS(e,o))},getValueFromEvent:VS,setPositionFromValue:function(t,n,e,o){var r=Uw(e),i=pS(t,o.getSpectrum(t),r.x,o.getLeftEdge(t),o.getRightEdge(t),e),u=_S(t,o.getSpectrum(t),r.y,o.getTopEdge(t),o.getBottomEdge(t),e),a=Ou(n.element)/2,c=vu(n.element)/2;Yi(n.element,"left",i-a+"px"),Yi(n.element,"top",u-c+"px")},onLeft:PS,onRight:HS,onUp:zS,onDown:NS,edgeActions:LS}),US=Yf({name:"Slider",configFields:[ne("stepSize",1),ne("onChange",ct),ne("onChoose",ct),ne("onInit",ct),ne("onDragStart",ct),ne("onDragEnd",ct),ne("snapToGrid",!1),ne("rounded",!0),Kn("snapStart"),jn("model",An("mode",{x:[ne("minX",0),ne("maxX",100),le("value",function(t){return fe(t.mode.minX)}),Ln("getInitialValue"),va("manager",wS)],y:[ne("minY",0),ne("maxY",100),le("value",function(t){return fe(t.mode.minY)}),Ln("getInitialValue"),va("manager",MS)],xy:[ne("minX",0),ne("maxX",100),ne("minY",0),ne("maxY",100),le("value",function(t){return fe({x:t.mode.minX,y:t.mode.minY})}),Ln("getInitialValue"),va("manager",jS)]})),jl("sliderBehaviours",[ig,Ll]),le("mouseIsDown",function(){return fe(!1)})],partFields:bw,factory:function(i,t,n,e){var o,u=function(t){return Ff(t,i,"thumb")},a=function(t){return Ff(t,i,"spectrum")},r=function(t){return Mf(t,i,"left-edge")},c=function(t){return Mf(t,i,"right-edge")},s=function(t){return Mf(t,i,"top-edge")},l=function(t){return Mf(t,i,"bottom-edge")},f=i.model,d=f.manager,m=function(t,n){d.setPositionFromValue(t,n,i,{getLeftEdge:r,getRightEdge:c,getTopEdge:s,getBottomEdge:l,getSpectrum:a})},g=function(t,n){f.value.set(n);var e=u(t);m(t,e)},p=function(e){var t=i.mouseIsDown.get();i.mouseIsDown.set(!1),t&&Mf(e,i,"thumb").each(function(t){var n=f.value.get();i.onChoose(e,t,n)})},h=function(t,n){n.stop(),i.mouseIsDown.set(!0),i.onDragStart(t,u(t))},v=function(t,n){n.stop(),i.onDragEnd(t,u(t)),p(t)};return{uid:i.uid,dom:i.dom,components:t,behaviours:Wl(i.sliderBehaviours,[ig.config({mode:"special",focusIn:function(t){return Mf(t,i,"spectrum").map(ig.focusIn).map(O)}}),Ll.config({store:{mode:"manual",getValue:function(t){return f.value.get()}}}),lc.config({channels:((o={})[Cl()]={onReceive:p},o)})]),events:Zo([er(yw(),function(t,n){!function(t,n){g(t,n);var e=u(t);i.onChange(t,e,n),dt.some(!0)}(t,n.event.value)}),sr(function(t,n){var e=f.getInitialValue();f.value.set(e);var o=u(t);m(t,o);var r=a(t);i.onInit(t,o,r,f.value.get())}),er(no(),h),er(oo(),v),er(io(),h),er(co(),v)]),apis:{resetToMin:function(t){d.setToMin(t,i)},resetToMax:function(t){d.setToMax(t,i)},setValue:g,refresh:m},domModification:{styles:{position:"relative"}}}},apis:{setValue:function(t,n,e){t.setValue(n,e)},resetToMin:function(t,n){t.resetToMin(n)},resetToMax:function(t,n){t.resetToMax(n)},refresh:function(t,n){t.refresh(n)}}}),WS=function(t,n,e){return{hue:t,saturation:n,value:e}},GS=function(t){var n,e=0,o=0,r=t.red/255,i=t.green/255,u=t.blue/255,a=Math.min(r,Math.min(i,u)),c=Math.max(r,Math.max(i,u));return a===c?WS(0,0,100*(o=a)):(e=60*((e=r===a?3:u===a?1:5)-(r===a?i-u:u===a?r-i:u-r)/(c-a)),n=(c-a)/c,o=c,WS(Math.round(e),Math.round(100*n),Math.round(100*o)))},XS=function(t){return yb(Tb(t))},YS=oi("rgb-hex-update"),qS=oi("slider-update"),KS=oi("palette-update"),JS=[jl("formBehaviours",[Ll])],$S=function(t){return"<alloy.field."+t+">"},QS=function(o,t){return{uid:o.uid,dom:o.dom,components:t,behaviours:Wl(o.formBehaviours,[Ll.config({store:{mode:"manual",getValue:function(t){var n=Rf(t,o);return Tt(n,function(t,r){return t().bind(function(t){var n,e,o=Zf.getCurrent(t);return n=o,e=new Error("Cannot find a current component to extract the value from for form part '"+r+"': "+Zr(t.element)),n.fold(function(){return pt.error(e)},pt.value)}).map(Ll.getValue)})},setValue:function(e,t){_t(t,function(n,t){Mf(e,o,t).each(function(t){Zf.getCurrent(t).each(function(t){Ll.setValue(t,n)})})})}}})]),apis:{getField:function(t,n){return Mf(t,o,n).bind(Zf.getCurrent)}}}},ZS={getField:bi(function(t,n,e){return t.getField(n,e)}),sketch:function(t){var e,n=(e=[],{field:function(t,n){return e.push(t),Tf("form",$S(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return bf({name:t,pname:$S(t)})});return jf("form",JS,i,QS,o)}},tk=oi("valid-input"),nk=oi("invalid-input"),ek=oi("validating-input"),ok="colorcustom.rgb.",rk=function(d,m,g,p){var h=function(t,n,e,o,r){var i,u,a=d(ok+"range"),c=[Wy.parts.label({dom:{tag:"label",innerHtml:e,attributes:{"aria-label":o}}}),Wy.parts.field({data:r,factory:dx,inputAttributes:ft({type:"text"},"hex"===n?{"aria-live":"polite"}:{}),inputClasses:[m("textfield")],inputBehaviours:rc([(i=n,u=t,Fx.config({invalidClass:m("invalid"),notify:{onValidate:function(t){qo(t,ek,{type:i})},onValid:function(t){qo(t,tk,{type:i,value:Ll.getValue(t)})},onInvalid:function(t){qo(t,nk,{type:i,value:Ll.getValue(t)})}},validator:{validate:function(t){var n=Ll.getValue(t),e=u(n)?pt.value(!0):pt.error(d("aria.input.invalid"));return kx(e)},validateOnLoad:!1}})),Yy.config({})]),onSetValue:function(t){Fx.isInvalid(t)&&Fx.run(t).get(ct)}})],s="hex"!==n?[Wy.parts["aria-descriptor"]({text:a})]:[];return{dom:{tag:"div",attributes:{role:"presentation"}},components:c.concat(s)}},v=function(t,n){var e=n.red,o=n.green,r=n.blue;Ll.setValue(t,{red:e,green:o,blue:r})},b=mp({dom:{tag:"div",classes:[m("rgba-preview")],styles:{"background-color":"white"},attributes:{role:"presentation"}}}),y=function(t,n){b.getOpt(t).each(function(t){Yi(t.element,"background-color","#"+n.value)})};return Xf({factory:function(){var e={red:fe(dt.some(255)),green:fe(dt.some(255)),blue:fe(dt.some(255)),hex:fe(dt.some("ffffff"))},o=function(t){return e[t].get()},i=function(t,n){e[t].set(n)},r=function(t){var n=t.red,e=t.green,o=t.blue;i("red",dt.some(n)),i("green",dt.some(e)),i("blue",dt.some(o))},n=function(t,n){var e=n.event;"hex"!==e.type?i(e.type,dt.none()):p(t)},u=function(r,t,n){var e=parseInt(n,10);i(t,dt.some(e)),o("red").bind(function(e){return o("green").bind(function(n){return o("blue").map(function(t){return Ob(e,n,t,1)})})}).each(function(t){var n,e,o=(n=r,e=yb(t),ZS.getField(n,"hex").each(function(t){hg.isFocused(t)||Ll.setValue(n,{hex:e.value})}),e);qo(r,YS,{hex:o}),y(r,o)})},a=function(t,n){var e=n.event;"hex"===e.type?function(t,n){g(t);var e=mb(n);i("hex",dt.some(n));var o=Eb(e);v(t,o),r(o),qo(t,YS,{hex:e}),y(t,e)}(t,e.value):u(t,e.type,e.value)},t=function(t){return{label:d(ok+t+".label"),description:d(ok+t+".description")}},c=t("red"),s=t("green"),l=t("blue"),f=t("hex");return zt(ZS.sketch(function(t){return{dom:{tag:"form",classes:[m("rgb-form")],attributes:{"aria-label":d("aria.color.picker")}},components:[t.field("red",Wy.sketch(h(_b,"red",c.label,c.description,255))),t.field("green",Wy.sketch(h(_b,"green",s.label,s.description,255))),t.field("blue",Wy.sketch(h(_b,"blue",l.label,l.description,255))),t.field("hex",Wy.sketch(h(hb,"hex",f.label,f.description,"ffffff"))),b.asSpec()],formBehaviours:rc([Fx.config({invalidClass:m("form-invalid")}),fg("rgb-form-events",[er(tk,a),er(nk,n),er(ek,n)])])}}),{apis:{updateHex:function(t,n){var e,o;Ll.setValue(t,{hex:n.value}),e=t,o=Eb(n),v(e,o),r(o),y(t,n)}}})},name:"RgbForm",configFields:[],apis:{updateHex:function(t,n,e){t.updateHex(n,e)}},extraApis:{}})},ik=function(t,o){var r=US.parts.spectrum({dom:{tag:"canvas",attributes:{role:"presentation"},classes:[o("sv-palette-spectrum")]}}),i=US.parts.thumb({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette-thumb")],innerHtml:"<div class="+o("sv-palette-inner-thumb")+' role="presentation"></div>'}}),a=function(t,n){var e,o,r=t.width,i=t.height,u=t.getContext("2d");null!==u&&(u.fillStyle=n,u.fillRect(0,0,r,i),(e=u.createLinearGradient(0,0,r,0)).addColorStop(0,"rgba(255,255,255,1)"),e.addColorStop(1,"rgba(255,255,255,0)"),u.fillStyle=e,u.fillRect(0,0,r,i),(o=u.createLinearGradient(0,0,0,i)).addColorStop(0,"rgba(0,0,0,0)"),o.addColorStop(1,"rgba(0,0,0,1)"),u.fillStyle=o,u.fillRect(0,0,r,i))};return Xf({factory:function(t){var n=st({x:0,y:0}),e=rc([Zf.config({find:dt.some}),hg.config({})]);return US.sketch({dom:{tag:"div",attributes:{role:"presentation"},classes:[o("sv-palette")]},model:{mode:"xy",getInitialValue:n},rounded:!1,components:[r,i],onChange:function(t,n,e){qo(t,KS,{value:e})},onInit:function(t,n,e,o){a(e.element.dom,Bb(Ab))},sliderBehaviours:e})},name:"SaturationBrightnessPalette",configFields:[],apis:{setHue:function(t,n,e){var o,r,i,u;o=e,r=n.components()[0].element.dom,i=WS(o,100,100),u=Tb(i),a(r,Bb(u))},setThumb:function(t,n,e){var o,r;o=n,r=GS(Eb(e)),US.setValue(o,{x:r.saturation,y:100-r.value})}},extraApis:{}})},uk=function(b,y){return Xf({name:"ColourPicker",configFields:[Ln("dom"),ne("onValidHex",ct),ne("onInvalidHex",ct)],factory:function(t){var n,e,o,a,u,r,i=rk(b,y,t.onValidHex,t.onInvalidHex),c=ik(0,y),s={paletteRgba:fe(Ab),paletteHue:fe(0)},l=mp((n=y,e=US.parts.spectrum({dom:{tag:"div",classes:[n("hue-slider-spectrum")],attributes:{role:"presentation"}}}),o=US.parts.thumb({dom:{tag:"div",classes:[n("hue-slider-thumb")],attributes:{role:"presentation"}}}),US.sketch({dom:{tag:"div",classes:[n("hue-slider")],attributes:{role:"presentation"}},rounded:!1,model:{mode:"y",getInitialValue:st({y:0})},components:[e,o],sliderBehaviours:rc([hg.config({})]),onChange:function(t,n,e){qo(t,qS,{value:e})}}))),f=mp(c.sketch({})),d=mp(i.sketch({})),m=function(t,n,e){f.getOpt(t).each(function(t){c.setHue(t,e)})},g=function(t,n){d.getOpt(t).each(function(t){i.updateHex(t,n)})},p=function(t,n,e){l.getOpt(t).each(function(t){US.setValue(t,{y:100-e/360*100})})},h=function(t,n){f.getOpt(t).each(function(t){c.setThumb(t,n)})},v=function(n,e,o,t){var r,i;r=o,i=Eb(e),s.paletteRgba.set(i),s.paletteHue.set(r),mt(t,function(t){t(n,e,o)})};return{uid:t.uid,dom:t.dom,components:[f.asSpec(),l.asSpec(),d.asSpec()],behaviours:rc([fg("colour-picker-events",[er(YS,(r=[m,p,h],function(t,n){var e=n.event.hex,o=GS(Eb(e));v(t,e,o.hue,r)})),er(KS,(u=[g],function(t,n){var e=n.event.value,o=s.paletteHue.get(),r=WS(o,e.x,100-e.y),i=XS(r);v(t,i,o,u)})),er(qS,(a=[m,g],function(t,n){var e=(100-n.event.value.y)/100*360,o=s.paletteRgba.get(),r=GS(o),i=WS(e,r.saturation,r.value),u=XS(i);v(t,u,e,a)}))]),Zf.config({find:function(t){return d.getOpt(t)}}),ig.config({mode:"acyclic"})])}}})},ak=function(){return Zf.config({find:dt.some})},ck=function(t){return Zf.config({find:t.getOpt})},sk=function(t){return Zf.config({find:function(n){return Tr(n.element,t).bind(function(t){return n.getSystem().getByDom(t).toOptional()})}})},lk={"colorcustom.rgb.red.label":"R","colorcustom.rgb.red.description":"Red component","colorcustom.rgb.green.label":"G","colorcustom.rgb.green.description":"Green component","colorcustom.rgb.blue.label":"B","colorcustom.rgb.blue.description":"Blue component","colorcustom.rgb.hex.label":"#","colorcustom.rgb.hex.description":"Hex color code","colorcustom.rgb.range":"Range 0 to 255","colorcustom.sb.saturation":"Saturation","colorcustom.sb.brightness":"Brightness","colorcustom.sb.picker":"Saturation and Brightness Picker","colorcustom.sb.palette":"Saturation and Brightness Palette","colorcustom.sb.instructions":"Use arrow keys to select saturation and brightness, on x and y axes","colorcustom.hue.hue":"Hue","colorcustom.hue.slider":"Hue Slider","colorcustom.hue.palette":"Hue Palette","colorcustom.hue.instructions":"Use arrow keys to select a hue","aria.color.picker":"Color Picker","aria.input.invalid":"Invalid input"},fk=function(t){return lk[t]},dk=tinymce.util.Tools.resolve("tinymce.Resource"),mk=tinymce.util.Tools.resolve("tinymce.util.Tools"),gk=fn([ne("preprocess",lt),ne("postprocess",lt)]),pk=function(t,n,e){return Ll.config(zt({store:{mode:"manual",getValue:n,setValue:e}},t.map(function(t){return{store:{initialValue:t}}}).getOr({})))},hk=function(t,n,e){return pk(t,function(t){return n(t.element)},function(t,n){return e(t.element,n)})},vk=function(r,t){var i=En("RepresentingConfigs.memento processors",gk,t);return Ll.config({store:{mode:"manual",getValue:function(t){var n=r.get(t),e=Ll.getValue(n);return i.postprocess(e)},setValue:function(t,n){var e=i.preprocess(n),o=r.get(t);Ll.setValue(o,e)}}})},bk=pk,yk=function(t){return hk(t,Wr,Gr)},xk=function(t){return Ll.config({store:{mode:"memory",initialValue:t}})},wk=function(i,u){var n=function(t,n){n.stop()},e=function(t){return function(n,e){mt(t,function(t){t(n,e)})}},o=function(t,n){var e;mv.isDisabled(t)||(e=n.event.raw,a(t,e.dataTransfer.files))},r=function(t,n){var e=n.event.raw.target;a(t,e.files)},a=function(t,n){var e,o,r;Ll.setValue(t,(e=n,o=u,r=mk.explode(o.getSetting("images_file_types","jpeg,jpg,jpe,jfi,jif,jfif,png,gif,bmp,webp","string")),H($(e),function(n){return F(r,function(t){return Se(n.name.toLowerCase(),"."+t.toLowerCase())})}))),qo(t,Zy,{name:i.name})},c=mp({dom:{tag:"input",attributes:{type:"file",accept:"image/*"},styles:{display:"none"}},behaviours:rc([fg("input-file-events",[ar(vo()),ar(Eo())])])}),t=i.label.map(function(t){return Qy(t,u)}),s=Wy.parts.field({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-dropzone-container"]},behaviours:rc([xk([]),ak(),mv.config({}),Eg.config({toggleClass:"dragenter",toggleOnExecute:!1}),fg("dropzone-events",[er("dragenter",e([n,Eg.toggle])),er("dragleave",e([n,Eg.toggle])),er("dragover",n),er("drop",e([n,o])),er(ho(),r)])]),components:[{dom:{tag:"div",classes:["tox-dropzone"],styles:{}},components:[{dom:{tag:"p",innerHtml:u.translate("Drop an image here")}},dp.sketch({dom:{tag:"button",innerHtml:u.translate("Browse for an image"),styles:{position:"relative"},classes:["tox-button","tox-button--secondary"]},components:[c.asSpec()],action:function(t){c.get(t).element.dom.click()},buttonBehaviours:rc([Yy.config({}),jv(u.isDisabled),Nv()])})]}]}}}});return Ky(t,s,["tox-form__group--stretched"],[])},Sk=oi("alloy-fake-before-tabstop"),kk=oi("alloy-fake-after-tabstop"),Ck=function(t){return{dom:{tag:"div",styles:{width:"1px",height:"1px",outline:"none"},attributes:{tabindex:"0"},classes:t},behaviours:rc([hg.config({ignore:!0}),Yy.config({})])}},Ok=function(t){return{dom:{tag:"div",classes:["tox-navobj"]},components:[Ck([Sk]),t,Ck([kk])],behaviours:rc([sk(1)])}},_k=function(t,n){qo(t,mo(),{raw:{which:9,shiftKey:n}})},Tk=function(t,n){var e=n.element;Hi(e,Sk)?_k(t,!0):Hi(e,kk)&&_k(t,!1)},Ek=function(t){return Oy(t,["."+Sk,"."+kk].join(","),l)},Dk=!(je().browser.isIE()||je().browser.isEdge()),Bk=function(t,n){var o,r,e=Dk&&t.sandboxed,i=ft(ft({},t.label.map(function(t){return{title:t}}).getOr({})),e?{sandbox:"allow-scripts allow-same-origin"}:{}),u=(o=e,r=fe(""),{getValue:function(t){return r.get()},setValue:function(t,n){var e;o?Yr(t.element,"srcdoc",n):(Yr(t.element,"src","javascript:''"),(e=t.element.dom.contentWindow.document).open(),e.write(n),e.close()),r.set(n)}}),a=t.label.map(function(t){return Qy(t,n)}),c=Wy.parts.field({factory:{sketch:function(t){return Ok({uid:t.uid,dom:{tag:"iframe",attributes:i},behaviours:rc([Yy.config({}),hg.config({}),bk(dt.none(),u.getValue,u.setValue)])})}}});return Ky(a,c,["tox-form__group--stretched"],[])},Ak=function(t,n){return Ik(document.createElement("canvas"),t,n)},Mk=function(t){var n=Ak(t.width,t.height);return Fk(n).drawImage(t,0,0),n},Fk=function(t){return t.getContext("2d")},Ik=function(t,n,e){return t.width=n,t.height=e,t},Rk=function(t){return t.naturalWidth||t.width},Vk=function(t){return t.naturalHeight||t.height},Pk=window.Promise?window.Promise:(zx=function(t){if("object"!=typeof this)throw new TypeError("Promises must be constructed via new");if("function"!=typeof t)throw new TypeError("not a function");this._state=null,this._value=null,this._deferreds=[],Wx(t,jx(zk,this),jx(Nk,this))},Nx=window,Lx=zx.immediateFn||"function"==typeof Nx.setImmediate&&Nx.setImmediate||function(t){return setTimeout(t,1)},jx=function(e,o){return function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.apply(o,t)}},Ux=Array.isArray||function(t){return"[object Array]"===Object.prototype.toString.call(t)},Wx=function(t,n,e){var o=!1;try{t(function(t){o||(o=!0,n(t))},function(t){o||(o=!0,e(t))})}catch(r){if(o)return;o=!0,e(r)}},zx.prototype["catch"]=function(t){return this.then(null,t)},zx.prototype.then=function(e,o){var r=this;return new zx(function(t,n){Hk.call(r,new jk(e,o,t,n))})},zx.all=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];var c=Array.prototype.slice.call(1===t.length&&Ux(t[0])?t[0]:t);return new zx(function(r,i){if(0===c.length)return r([]);for(var u=c.length,a=function(n,t){try{if(t&&("object"==typeof t||"function"==typeof t)){var e=t.then;if("function"==typeof e)return void e.call(t,function(t){a(n,t)},i)}c[n]=t,0==--u&&r(c)}catch(o){i(o)}},t=0;t<c.length;t++)a(t,c[t])})},zx.resolve=function(n){return n&&"object"==typeof n&&n.constructor===zx?n:new zx(function(t){t(n)})},zx.reject=function(e){return new zx(function(t,n){n(e)})},zx.race=function(r){return new zx(function(t,n){for(var e=0,o=r;e<o.length;e++)o[e].then(t,n)})},zx);function Hk(o){var r=this;null!==this._state?Lx(function(){var t,n=r._state?o.onFulfilled:o.onRejected;if(null!==n){try{t=n(r._value)}catch(e){return void o.reject(e)}o.resolve(t)}else(r._state?o.resolve:o.reject)(r._value)}):this._deferreds.push(o)}function zk(t){try{if(t===this)throw new TypeError("A promise cannot be resolved with itself.");if(t&&("object"==typeof t||"function"==typeof t)){var n=t.then;if("function"==typeof n)return void Wx(jx(n,t),jx(zk,this),jx(Nk,this))}this._state=!0,this._value=t,Lk.call(this)}catch(e){Nk.call(this,e)}}function Nk(t){this._state=!1,this._value=t,Lk.call(this)}function Lk(){for(var t=0,n=this._deferreds;t<n.length;t++){var e=n[t];Hk.call(this,e)}this._deferreds=[]}function jk(t,n,e,o){this.onFulfilled="function"==typeof t?t:null,this.onRejected="function"==typeof n?n:null,this.resolve=e,this.reject=o}var Uk,Wk,Gk,Xk,Yk=function(e){return new Pk(function(t,n){(function(t){var n=t.split(","),e=/data:([^;]+)/.exec(n[0]);if(!e)return dt.none();for(var o=e[1],r=n[1],i=atob(r),u=i.length,a=Math.ceil(u/1024),c=new Array(a),s=0;s<a;++s){for(var l=1024*s,f=Math.min(1024+l,u),d=new Array(f-l),m=l,g=0;m<f;++g,++m)d[g]=i[m].charCodeAt(0);c[s]=new Uint8Array(d)}return dt.some(new Blob(c,{type:o}))})(e).fold(function(){n("uri is not base64: "+e)},t)})},qk=function(t,o,r){return o=o||"image/png",y(HTMLCanvasElement.prototype.toBlob)?new Pk(function(n,e){t.toBlob(function(t){t?n(t):e()},o,r)}):Yk(t.toDataURL(o,r))},Kk=function(t){return a=t,new Pk(function(t,n){var e=URL.createObjectURL(a),o=new Image,r=function(){o.removeEventListener("load",i),o.removeEventListener("error",u)},i=function(){r(),t(o)},u=function(){r(),n("Unable to load data of type "+a.type+": "+e)};o.addEventListener("load",i),o.addEventListener("error",u),o.src=e,o.complete&&setTimeout(i,0)}).then(function(t){Jk(t);var n=Ak(Rk(t),Vk(t));return Fk(n).drawImage(t,0,0),n});var a},Jk=function(t){URL.revokeObjectURL(t.src)},$k=function(t,n,e){var o=n.type,r=st(o),i=st(e),u=function(o,r){return t.then(function(t){return e=r,n=(n=o)||"image/png",t.toDataURL(n,e);var n,e})};return{getType:r,toBlob:function(){return Pk.resolve(n)},toDataURL:i,toBase64:function(){return e.split(",")[1]},toAdjustedBlob:function(n,e){return t.then(function(t){return qk(t,n,e)})},toAdjustedDataURL:u,toAdjustedBase64:function(t,n){return u(t,n).then(function(t){return t.split(",")[1]})},toCanvas:function(){return t.then(Mk)}}},Qk=function(n,t){return qk(n,t).then(function(t){return $k(Pk.resolve(n),t,n.toDataURL())})},Zk=function(n){return e=n,new Pk(function(t){var n=new FileReader;n.onloadend=function(){t(n.result)},n.readAsDataURL(e)}).then(function(t){return $k(Kk(n),n,t)});var e},tC=function(t,n,e){var o="string"==typeof t?parseFloat(t):t;return e<o?o=e:o<n&&(o=n),o},nC=function(){return[1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1,0,0,0,0,0,1]},eC=[0,.01,.02,.04,.05,.06,.07,.08,.1,.11,.12,.14,.15,.16,.17,.18,.2,.21,.22,.24,.25,.27,.28,.3,.32,.34,.36,.38,.4,.42,.44,.46,.48,.5,.53,.56,.59,.62,.65,.68,.71,.74,.77,.8,.83,.86,.89,.92,.95,.98,1,1.06,1.12,1.18,1.24,1.3,1.36,1.42,1.48,1.54,1.6,1.66,1.72,1.78,1.84,1.9,1.96,2,2.12,2.25,2.37,2.5,2.62,2.75,2.87,3,3.2,3.4,3.6,3.8,4,4.3,4.7,4.9,5,5.5,6,6.5,6.8,7,7.3,7.5,7.8,8,8.4,8.7,9,9.4,9.6,9.8,10],oC=function(t,n){for(var e,o=[],r=new Array(25),i=0;i<5;i++){for(var u=0;u<5;u++)o[u]=n[u+5*i];for(u=0;u<5;u++){for(var a=e=0;a<5;a++)e+=t[u+5*a]*o[a];r[u+5*i]=e}}return r},rC=function(n,e){return n.toCanvas().then(function(t){return iC(t,n.getType(),e)})},iC=function(t,n,e){var o=Fk(t),r=function(t,n){for(var e,o,r,i,u=t.data,a=n[0],c=n[1],s=n[2],l=n[3],f=n[4],d=n[5],m=n[6],g=n[7],p=n[8],h=n[9],v=n[10],b=n[11],y=n[12],x=n[13],w=n[14],S=n[15],k=n[16],C=n[17],O=n[18],_=n[19],T=0;T<u.length;T+=4)e=u[T],o=u[T+1],r=u[T+2],i=u[T+3],u[T]=e*a+o*c+r*s+i*l+f,u[T+1]=e*d+o*m+r*g+i*p+h,u[T+2]=e*v+o*b+r*y+i*x+w,u[T+3]=e*S+o*k+r*C+i*O+_;return t}(o.getImageData(0,0,t.width,t.height),e);return o.putImageData(r,0,0),Qk(t,n)},uC=function(t,n,e){var o=Fk(t),r=function(t,n,e){for(var o=function(t,n,e){return e<t?t=e:t<n&&(t=n),t},r=Math.round(Math.sqrt(e.length)),i=Math.floor(r/2),u=t.data,a=n.data,c=t.width,s=t.height,l=0;l<s;l++)for(var f=0;f<c;f++){for(var d=0,m=0,g=0,p=0;p<r;p++)for(var h=0;h<r;h++){var v=o(f+h-i,0,c-1),b=4*(o(l+p-i,0,s-1)*c+v),y=e[p*r+h];d+=u[b]*y,m+=u[1+b]*y,g+=u[2+b]*y}var x=4*(l*c+f);a[x]=o(d,0,255),a[1+x]=o(m,0,255),a[2+x]=o(g,0,255)}return n}(o.getImageData(0,0,t.width,t.height),r=o.getImageData(0,0,t.width,t.height),e);return o.putImageData(r,0,0),Qk(t,n)},aC=function(e){return function(t,n){return rC(t,e(nC(),n))}},cC=(Uk=[-1,0,0,0,255,0,-1,0,0,255,0,0,-1,0,255,0,0,0,1,0,0,0,0,0,1],function(t){return rC(t,Uk)}),sC=aC(function(t,n){return n=tC(255*n,-255,255),oC(t,[1,0,0,0,n,0,1,0,0,n,0,0,1,0,n,0,0,0,1,0,0,0,0,0,1])}),lC=aC(function(t,n){var e;return n=tC(n,-1,1),e=(n*=100)<0?127+n/100*127:127*(e=0===(e=n%1)?eC[n]:eC[Math.floor(n)]*(1-e)+eC[Math.floor(n)+1]*e)+127,oC(t,[e/127,0,0,0,.5*(127-e),0,e/127,0,0,.5*(127-e),0,0,e/127,0,.5*(127-e),0,0,0,1,0,0,0,0,0,1])}),fC=(Wk=[0,-1,0,-1,5,-1,0,-1,0],function(t){return e=Wk,(n=t).toCanvas().then(function(t){return uC(t,n.getType(),e)});var n,e}),dC=(Gk=function(t,n){return 255*Math.pow(t/255,1-n)},function(n,e){return n.toCanvas().then(function(t){return function(t,n,e){for(var o=Fk(t),r=new Array(256),i=0;i<r.length;i++)r[i]=Gk(i,e);var u=function(t,n){for(var e=t.data,o=0;o<e.length;o+=4)e[o]=n[e[o]],e[o+1]=n[e[o+1]],e[o+2]=n[e[o+2]];return t}(o.getImageData(0,0,t.width,t.height),r);return o.putImageData(u,0,0),Qk(t,n)}(t,n.getType(),e)})}),mC=function(t,n,e){var o=Rk(t),r=Vk(t),i=n/o,u=e/r,a=!1;(i<.5||2<i)&&(i=i<.5?.5:2,a=!0),(u<.5||2<u)&&(u=u<.5?.5:2,a=!0);var c=gC(t,i,u);return a?c.then(function(t){return mC(t,n,e)}):c},gC=function(u,a,c){return new Pk(function(t){var n=Rk(u),e=Vk(u),o=Math.floor(n*a),r=Math.floor(e*c),i=Ak(o,r);Fk(i).drawImage(u,0,0,n,e,0,0,o,r),t(i)})},pC=function(t,n){void 0===n&&(n=2);var e=Math.pow(10,n),o=Math.round(t*e);return Math.ceil(o/e)},hC=function(t,n,e){var o=(e<0?360+e:e)*Math.PI/180,r=t.width,i=t.height,u=Math.sin(o),a=Math.cos(o),c=pC(Math.abs(r*a)+Math.abs(i*u)),s=pC(Math.abs(r*u)+Math.abs(i*a)),l=Ak(c,s),f=Fk(l);return f.translate(c/2,s/2),f.rotate(o),f.drawImage(t,-r/2,-i/2),Qk(l,n)},vC=function(t,n,e){var o=Ak(t.width,t.height),r=Fk(o);return"v"===e?(r.scale(1,-1),r.drawImage(t,0,-o.height)):(r.scale(-1,1),r.drawImage(t,-o.width,0)),Qk(o,n)},bC=function(t,n,e,o,r,i){var u=Ak(r,i);return Fk(u).drawImage(t,-e,-o),Qk(u,n)},yC=function(t){return cC(t)},xC=function(t){return fC(t)},wC=function(t,n){return dC(t,n)},SC=function(t,n,e,o){return rC(t,(r=nC(),u=e,a=o,i=tC(i=n,0,2),u=tC(u,0,2),a=tC(a,0,2),oC(r,[i,0,0,0,0,0,u,0,0,0,0,0,a,0,0,0,0,0,1,0,0,0,0,0,1])));var r,i,u,a},kC=function(t,n){return sC(t,n)},CC=function(t,n){return lC(t,n)},OC=function(t,n){return o=n,(e=t).toCanvas().then(function(t){return vC(t,e.getType(),o)});var e,o},_C=function(t,n,e,o,r){return u=n,a=e,c=o,s=r,(i=t).toCanvas().then(function(t){return bC(t,i.getType(),u,a,c,s)});var i,u,a,c,s},TC=function(t,n,e){return r=n,i=e,(o=t).toCanvas().then(function(t){return mC(t,r,i).then(function(t){return Qk(t,o.getType())})});var o,r,i},EC=function(t,n){return o=n,(e=t).toCanvas().then(function(t){return hC(t,e.getType(),o)});var e,o},DC=function(t,n){return ft({dom:{tag:"span",innerHtml:t,classes:["tox-icon","tox-tbtn__icon-wrap"]}},n)},BC=function(t,n){return DC(pp(t,n),{})},AC=function(t,n){return DC(pp(t,n),{behaviours:rc([lg.config({})])})},MC=function(t,n,e){return{dom:{tag:"span",innerHtml:e.translate(t),classes:[n+"__select-label"]},behaviours:rc([lg.config({})])}},FC=oi("toolbar.button.execute"),IC=((Xk={})[_o()]=["disabling","alloy.base.behaviour","toggling","toolbar-button-events"],Xk),RC=oi("update-menu-text"),VC=oi("update-menu-icon"),PC=function(n,e,o){var t=fe(ct),r=n.text.map(function(t){return mp(MC(t,e,o.providers))}),i=n.icon.map(function(t){return mp(AC(t,o.providers.icons))}),u=function(t,n){var e=Ll.getValue(t);return hg.focus(e),qo(e,"keydown",{raw:n.event.raw}),ow.close(e),dt.some(!0)},a=n.role.fold(function(){return{}},function(t){return{role:t}}),c=n.tooltip.fold(function(){return{}},function(t){var n=o.providers.translate(t);return{title:n,"aria-label":n}});return mp(ow.sketch(ft(ft(ft({},n.uid?{uid:n.uid}:{}),a),{dom:{tag:"button",classes:[e,e+"--select"].concat(V(n.classes,function(t){return e+"--"+t})),attributes:ft({},c)},components:Kv([i.map(function(t){return t.asSpec()}),r.map(function(t){return t.asSpec()}),dt.some({dom:{tag:"div",classes:[e+"__select-chevron"],innerHtml:pp("chevron-down",o.providers.icons)}})]),matchWidth:!0,useMinWidth:!0,dropdownBehaviours:rc(T(n.dropdownBehaviours,[jv(function(){return n.disabled||o.providers.isDisabled()}),Nv(),rw.config({}),lg.config({}),fg("dropdown-events",[Xv(n,t),Yv(n,t)]),fg("menubutton-update-display-text",[er(RC,function(n,e){r.bind(function(t){return t.getOpt(n)}).each(function(t){lg.set(t,[lu(o.providers.translate(e.event.text))])})}),er(VC,function(n,e){i.bind(function(t){return t.getOpt(n)}).each(function(t){lg.set(t,[AC(e.event.icon,o.providers.icons)])})})])])),eventOrder:zt(IC,{mousedown:["focusing","alloy.base.behaviour","item-type-events","normal-dropdown-events"]}),sandboxBehaviours:rc([ig.config({mode:"special",onLeft:u,onRight:u})]),lazySink:o.getSink,toggleClass:e+"--active",parts:{menu:xh(0,n.columns,n.presets)},fetch:function(t){return Sx(k(n.fetch,t))}}))).asSpec()},HC=function(t){return"separator"===t.type},zC={type:"separator"},NC=function(t,e){var n=N(t,function(t,n){return x(n)?""===n?t:"|"===n?0<t.length&&!HC(t[t.length-1])?t.concat([zC]):t:It(e,n.toLowerCase())?t.concat([e[n.toLowerCase()]]):t:t.concat([n])},[]);return 0<n.length&&HC(n[n.length-1])&&n.pop(),n},LC=function(t,n){return It(t,"getSubmenuItems")?(o=n,r=(e=t).getSubmenuItems(),i=jC(r,o),{item:e,menus:zt(i.menus,Jt(e.value,i.items)),expansions:zt(i.expansions,Jt(e.value,e.value))}):{item:t,menus:{},expansions:{}};var e,o,r,i},jC=function(t,r){var n=NC(x(t)?t.split(" "):t,r);return z(n,function(t,n){var e=function(t){if(HC(t))return t;var n=Ft(t,"value").getOrThunk(function(){return oi("generated-menu-item")});return zt({value:n},t)}(n),o=LC(e,r);return{menus:zt(t.menus,o.menus),items:[o.item].concat(t.items),expansions:zt(t.expansions,o.expansions)}},{menus:{},expansions:{},items:[]})},UC=function(t,e,o,n){var r=oi("primary-menu"),i=jC(t,o.shared.providers.menuItems());if(0===i.items.length)return dt.none();var u=by(r,i.items,e,o,n),a=Tt(i.menus,function(t,n){return by(n,t,e,o,!1)}),c=zt(a,Jt(r,u));return dt.from(Qg.tieredData(r,c,i.expansions))},WC=function(e){return{isDisabled:function(){return mv.isDisabled(e)},setDisabled:function(t){return mv.set(e,t)},setActive:function(t){var n=e.element;t?(Ri(n,"tox-tbtn--enabled"),Yr(n,"aria-pressed",!0)):(Pi(n,"tox-tbtn--enabled"),$r(n,"aria-pressed"))},isActive:function(){return Hi(e.element,"tox-tbtn--enabled")}}},GC=function(e,t,o,n){return PC({text:e.text,icon:e.icon,tooltip:e.tooltip,role:n,fetch:function(t,n){e.fetch(function(t){n(UC(t,ih.CLOSE_ON_EXECUTE,o,!1))})},onSetup:e.onSetup,getApi:WC,columns:1,presets:"normal",classes:[],dropdownBehaviours:[Yy.config({})]},t,o.shared)},XC=function(n,r,i){return function(t){t(V(n,function(t){var n,e,o=t.text.fold(function(){return{}},function(t){return{text:t}});return ft(ft({type:t.type,active:!1},o),{onAction:function(t){var n=!t.isActive();t.setActive(n),e.storage.set(n),i.shared.getSink().each(function(t){r().getOpt(t).each(function(t){dc(t.element),qo(t,ex,{name:e.name,value:e.storage.get()})})})},onSetup:(n=e=t,function(t){t.setActive(n.storage.get())})})}))}},YC=function(t,n,e,o,r,i){void 0===e&&(e=[]);var u=n.fold(function(){return{}},function(t){return{action:t}}),a=ft({buttonBehaviours:rc([jv(function(){return t.disabled||i.isDisabled()}),Nv(),Yy.config({}),fg("button press",[nr("click"),nr("mousedown")])].concat(e)),eventOrder:{click:["button press","alloy.base.behaviour"],mousedown:["button press","alloy.base.behaviour"]}},u),c=zt(a,{dom:o});return zt(c,{components:r})},qC=function(t,n,e,o){void 0===o&&(o=[]);var r={tag:"button",classes:["tox-tbtn"],attributes:t.tooltip.map(function(t){return{"aria-label":e.translate(t),title:e.translate(t)}}).getOr({})},i=t.icon.map(function(t){return BC(t,e.icons)}),u=Kv([i]);return YC(t,n,o,r,u,e)},KC=function(t,n,e,o){void 0===o&&(o=[]);var r=qC(t,dt.some(n),e,o);return dp.sketch(r)},JC=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=e.translate(t.text),u=t.icon?t.icon.map(function(t){return BC(t,e.icons)}):dt.none(),a=u.isSome()?Kv([u]):[],c=u.isSome()?{}:{innerHtml:i},s=T(t.primary||t.borderless?["tox-button"]:["tox-button","tox-button--secondary"],u.isSome()?["tox-button--icon"]:[],t.borderless?["tox-button--naked"]:[],r),l=ft(ft({tag:"button",classes:s},c),{attributes:{title:i}});return YC(t,n,o,l,a,e)},$C=function(t,n,e,o,r){void 0===o&&(o=[]),void 0===r&&(r=[]);var i=JC(t,dt.some(n),e,o,r);return dp.sketch(i)},QC=function(n,e){return function(t){"custom"===e?qo(t,ex,{name:n,value:{}}):"submit"===e?Yo(t,ox):"cancel"===e?Yo(t,nx):console.error("Unknown button type: ",e)}},ZC=function(n,t,e){if("menu"===t){var o=n,r=ft(ft({},n),{onSetup:function(t){return t.setDisabled(n.disabled),ct},fetch:XC(o.items,function(){return i},e)}),i=mp(GC(r,"tox-tbtn",e,dt.none()));return i.asSpec()}if("custom"===(c=t)||"cancel"===c||"submit"===c){var u=QC(n.name,t),a=ft(ft({},n),{borderless:!1});return $C(a,u,e.shared.providers,[])}var c;console.error("Unknown footer button type: ",t)},tO=function(t,n){var e,o,r=QC(t.name,"custom");return e=dt.none(),o=Wy.parts.field(ft({factory:dp},JC(t,dt.some(r),n,[xk(""),ak()]))),Ky(e,o,[],[])},nO=st([ne("field1Name","field1"),ne("field2Name","field2"),pa("onLockedChange"),fa(["lockClass"]),ne("locked",!1),Gl("coupledFieldBehaviours",[Zf,Ll])]),eO=function(t,n){return bf({factory:Wy,name:t,overrides:function(o){return{fieldBehaviours:rc([fg("coupled-input-behaviour",[er(po(),function(e){Mf(e,o,n).bind(Zf.getCurrent).each(function(n){Mf(e,o,"lock").each(function(t){Eg.isOn(t)&&o.onLockedChange(e,n,t)})})})])])}}})},oO=st([eO("field1","field2"),eO("field2","field1"),bf({factory:dp,schema:[Ln("dom")],name:"lock",overrides:function(t){return{buttonBehaviours:rc([Eg.config({selected:t.locked,toggleClass:t.markers.lockClass,aria:{mode:"pressed"}})])}}})]),rO=Yf({name:"FormCoupledInputs",configFields:nO(),partFields:oO(),factory:function(o,t,n,e){return{uid:o.uid,dom:o.dom,components:t,behaviours:Xl(o.coupledFieldBehaviours,[Zf.config({find:dt.some}),Ll.config({store:{mode:"manual",getValue:function(t){var n=Pf(t,o,["field1","field2"]),e={};return e[o.field1Name]=Ll.getValue(n.field1()),e[o.field2Name]=Ll.getValue(n.field2()),e},setValue:function(t,n){var e=Pf(t,o,["field1","field2"]);Rt(n,o.field1Name)&&Ll.setValue(e.field1(),n[o.field1Name]),Rt(n,o.field2Name)&&Ll.setValue(e.field2(),n[o.field2Name])}}})]),apis:{getField1:function(t){return Mf(t,o,"field1")},getField2:function(t){return Mf(t,o,"field2")},getLock:function(t){return Mf(t,o,"lock")}}}},apis:{getField1:function(t,n){return t.getField1(n)},getField2:function(t,n){return t.getField2(n)},getLock:function(t,n){return t.getLock(n)}}}),iO=function(t){var n=/^\s*(\d+(?:\.\d+)?)\s*(|cm|mm|in|px|pt|pc|em|ex|ch|rem|vw|vh|vmin|vmax|%)\s*$/.exec(t);if(null===n)return pt.error(t);var e=parseFloat(n[1]),o=n[2];return pt.value({value:e,unit:o})},uO=function(t,n){var e={"":96,px:96,pt:72,cm:2.54,pc:12,mm:25.4,"in":1},o=function(t){return Object.prototype.hasOwnProperty.call(e,t)};return t.unit===n?dt.some(t.value):o(t.unit)&&o(n)?e[t.unit]===e[n]?dt.some(t.value):dt.some(t.value/e[t.unit]*e[n]):dt.none()},aO=function(t){return dt.none()},cO=function(t,n){var e,o,r,i=iO(t).toOptional(),u=iO(n).toOptional();return o=u,r=function(t,o){return uO(t,o.unit).map(function(t){return o.value/t}).map(function(t){return n=t,e=o.unit,function(t){return uO(t,e).map(function(t){return{value:t*n,unit:e}})};var n,e}).getOr(aO)},((e=i).isSome()&&o.isSome()?dt.some(r(e.getOrDie(),o.getOrDie())):dt.none()).getOr(aO)},sO=function(o,n){var a=aO,r=oi("ratio-event"),t=rO.parts.lock({dom:{tag:"button",classes:["tox-lock","tox-button","tox-button--naked","tox-button--icon"],attributes:{title:n.translate(o.label.getOr("Constrain proportions"))}},components:[{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__lock"],innerHtml:pp("lock",n.icons)}},{dom:{tag:"span",classes:["tox-icon","tox-lock-icon__unlock"],innerHtml:pp("unlock",n.icons)}}],buttonBehaviours:rc([mv.config({disabled:function(){return o.disabled||n.isDisabled()}}),Nv(),Yy.config({})])}),e=function(t){return{dom:{tag:"div",classes:["tox-form__group"]},components:t}},i=function(e){return Wy.parts.field({factory:dx,inputClasses:["tox-textfield"],inputBehaviours:rc([mv.config({disabled:function(){return o.disabled||n.isDisabled()}}),Nv(),Yy.config({}),fg("size-input-events",[er(lo(),function(t,n){qo(t,r,{isField1:e})}),er(ho(),function(t,n){qo(t,Zy,{name:o.name})})])]),selectOnFocus:!1})},u=function(t){return{dom:{tag:"label",classes:["tox-label"],innerHtml:n.translate(t)}}},c=rO.parts.field1(e([Wy.parts.label(u("Width")),i(!0)])),s=rO.parts.field2(e([Wy.parts.label(u("Height")),i(!1)]));return rO.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:[{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:[c,s,e([u("&nbsp;"),t])]}],field1Name:"width",field2Name:"height",locked:!0,markers:{lockClass:"tox-locked"},onLockedChange:function(t,i,n){iO(Ll.getValue(t)).each(function(t){a(t).each(function(t){var n,e,o,r;Ll.setValue(i,(o={"":0,px:0,pt:1,mm:1,pc:2,ex:2,em:2,ch:2,rem:2,cm:3,"in":4,"%":4},-1!==(r=(n=t).value.toFixed((e=n.unit)in o?o[e]:1)).indexOf(".")&&(r=r.replace(/\.?0*$/,"")),r+n.unit))})})},coupledFieldBehaviours:rc([mv.config({disabled:function(){return o.disabled||n.isDisabled()},onDisabled:function(t){rO.getField1(t).bind(Wy.getField).each(mv.disable),rO.getField2(t).bind(Wy.getField).each(mv.disable),rO.getLock(t).each(mv.disable)},onEnabled:function(t){rO.getField1(t).bind(Wy.getField).each(mv.enable),rO.getField2(t).bind(Wy.getField).each(mv.enable),rO.getLock(t).each(mv.enable)}}),Nv(),fg("size-input-events2",[er(r,function(t,n){var e=n.event.isField1,o=e?rO.getField1(t):rO.getField2(t),r=e?rO.getField2(t):rO.getField1(t),i=o.map(Ll.getValue).getOr(""),u=r.map(Ll.getValue).getOr("");a=cO(i,u)})])])})},lO={undo:st(oi("undo")),redo:st(oi("redo")),zoom:st(oi("zoom")),back:st(oi("back")),apply:st(oi("apply")),swap:st(oi("swap")),transform:st(oi("transform")),tempTransform:st(oi("temp-transform")),transformApply:st(oi("transform-apply"))},fO=st("save-state"),dO=st("disable"),mO=st("enable"),gO={formActionEvent:ex,saveState:fO,disable:dO,enable:mO},pO=function(r,c){var t=function(t,n,e,o){return mp($C({name:t,text:t,disabled:e,primary:o,icon:dt.none(),borderless:!1},n,c))},n=function(t,n,e,o){return mp(KC({name:t,icon:dt.some(t),tooltip:dt.some(n),disabled:o,primary:!1,borderless:!1},e,c))},u=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(mv)&&mv.disable(n)})},a=function(t,e){t.map(function(t){var n=t.get(e);n.hasConfigured(mv)&&mv.enable(n)})},s={tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools-edit-panel"]},e=ct,i=function(t,n,e){qo(t,n,e)},l=function(t){return Yo(t,gO.disable())},f=function(t){return Yo(t,gO.enable())},d=function(t,n){l(t),i(t,lO.transform(),{transform:n}),f(t)},o=function(t){return function(){J.getOpt(t).each(function(t){lg.set(t,[q])})}},m=function(t,n){l(t),i(t,lO.transformApply(),{transform:n,swap:o(t)}),f(t)},g=function(){return t("Back",function(t){return i(t,lO.back(),{swap:o(t)})},!1,!1)},p=function(){return mp({dom:{tag:"div",classes:["tox-spacer"]},behaviours:rc([mv.config({})])})},h=function(){return t("Apply",function(t){return i(t,lO.apply(),{swap:o(t)})},!0,!0)},v=[g(),p(),t("Apply",function(t){m(t,function(t){var n=r.getRect();return _C(t,n.x,n.y,n.w,n.h)}),r.hideCrop()},!1,!0)],b=Ny.sketch({dom:s,components:v.map(function(t){return t.asSpec()}),containerBehaviours:rc([fg("image-tools-crop-buttons-events",[er(gO.disable(),function(t,n){u(v,t)}),er(gO.enable(),function(t,n){a(v,t)})])])}),y=mp(sO({name:"size",label:dt.none(),constrain:!0,disabled:!1},c)),x=[g(),p(),y,p(),t("Apply",function(a){y.getOpt(a).each(function(t){var n,e,o=Ll.getValue(t),r=parseInt(o.width,10),i=parseInt(o.height,10),u=(n=r,e=i,function(t){return TC(t,n,e)});m(a,u)})},!1,!0)],w=Ny.sketch({dom:s,components:x.map(function(t){return t.asSpec()}),containerBehaviours:rc([fg("image-tools-resize-buttons-events",[er(gO.disable(),function(t,n){u(x,t)}),er(gO.enable(),function(t,n){a(x,t)})])])}),S=function(n,e){return function(t){return n(t,e)}},k=S(OC,"h"),C=S(OC,"v"),O=S(EC,-90),_=S(EC,90),T=function(t,n){var e,o;o=n,l(e=t),i(e,lO.tempTransform(),{transform:o}),f(e)},E=[g(),p(),n("flip-horizontally","Flip horizontally",function(t){T(t,k)},!1),n("flip-vertically","Flip vertically",function(t){T(t,C)},!1),n("rotate-left","Rotate counterclockwise",function(t){T(t,O)},!1),n("rotate-right","Rotate clockwise",function(t){T(t,_)},!1),p(),h()],D=Ny.sketch({dom:s,components:E.map(function(t){return t.asSpec()}),containerBehaviours:rc([fg("image-tools-fliprotate-buttons-events",[er(gO.disable(),function(t,n){u(E,t)}),er(gO.enable(),function(t,n){a(E,t)})])])}),B=function(t,n,e,o,r){var i=US.parts.label({dom:{tag:"label",classes:["tox-label"],innerHtml:c.translate(t)}}),u=US.parts.spectrum({dom:{tag:"div",classes:["tox-slider__rail"],attributes:{role:"presentation"}}}),a=US.parts.thumb({dom:{tag:"div",classes:["tox-slider__handle"],attributes:{role:"presentation"}}});return mp(US.sketch({dom:{tag:"div",classes:["tox-slider"],attributes:{role:"presentation"}},model:{mode:"x",minX:e,maxX:r,getInitialValue:st({x:o})},components:[i,u,a],sliderBehaviours:rc([hg.config({})]),onChoose:n}))},A=function(t,n,e,o,r){return[g(),(i=n,B(t,function(t,n,e){var o=S(i,e.x/100);d(t,o)},e,o,r)),h()];var i},M=function(t,n,e,o,r){var i=A(t,n,e,o,r);return Ny.sketch({dom:s,components:i.map(function(t){return t.asSpec()}),containerBehaviours:rc([fg("image-tools-filter-panel-buttons-events",[er(gO.disable(),function(t,n){u(i,t)}),er(gO.enable(),function(t,n){a(i,t)})])])})},F=[g(),p(),h()],I=Ny.sketch({dom:s,components:F.map(function(t){return t.asSpec()})}),R=M("Brightness",kC,-100,0,100),V=M("Contrast",CC,-100,0,100),P=M("Gamma",wC,-100,0,100),H=function(t){return B(t,function(l,t,n){var e=z.getOpt(l),o=L.getOpt(l),r=N.getOpt(l);e.each(function(s){o.each(function(c){r.each(function(t){var n,e,o,r=Ll.getValue(s).x/100,i=Ll.getValue(t).x/100,u=Ll.getValue(c).x/100,a=(n=r,e=i,o=u,function(t){return SC(t,n,e,o)});d(l,a)})})})},0,100,200)},z=H("R"),N=H("G"),L=H("B"),j=[g(),z,N,L,h()],U=Ny.sketch({dom:s,components:j.map(function(t){return t.asSpec()})}),W=function(n,e,o){return function(t){i(t,lO.swap(),{transform:e,swap:function(){J.getOpt(t).each(function(t){lg.set(t,[n]),o(t)})}})}},G=dt.some(xC),X=dt.some(yC),Y=[n("crop","Crop",W(b,dt.none(),function(t){r.showCrop()}),!1),n("resize","Resize",W(w,dt.none(),function(t){y.getOpt(t).each(function(t){var n=r.getMeasurements(),e=n.width,o=n.height;Ll.setValue(t,{width:e,height:o})})}),!1),n("orientation","Orientation",W(D,dt.none(),e),!1),n("brightness","Brightness",W(R,dt.none(),e),!1),n("sharpen","Sharpen",W(I,G,e),!1),n("contrast","Contrast",W(V,dt.none(),e),!1),n("color-levels","Color levels",W(U,dt.none(),e),!1),n("gamma","Gamma",W(P,dt.none(),e),!1),n("invert","Invert",W(I,X,e),!1)],q=Ny.sketch({dom:s,components:Y.map(function(t){return t.asSpec()})}),K=Ny.sketch({dom:{tag:"div"},components:[q],containerBehaviours:rc([lg.config({})])}),J=mp(K);return{memContainer:J,getApplyButton:function(t){return J.getOpt(t).map(function(t){var n=t.components()[0];return n.components()[n.components().length-1]})}}},hO=tinymce.util.Tools.resolve("tinymce.geom.Rect"),vO=tinymce.util.Tools.resolve("tinymce.dom.DomQuery"),bO=tinymce.util.Tools.resolve("tinymce.util.Observable"),yO=tinymce.util.Tools.resolve("tinymce.util.VK"),xO=function(t){var n,e;if(t.changedTouches)for(n="screenX screenY pageX pageY clientX clientY".split(" "),e=0;e<n.length;e++)t[n[e]]=t.changedTouches[0][n[e]]};function wO(t,m){var g,p,h,v,b=m.document||document,y=b.getElementById(m.handle||t),x=function(t){if(xO(t),t.button!==p)return w(t);t.deltaX=t.screenX-h,t.deltaY=t.screenY-v,t.preventDefault(),m.drag(t)},w=function(t){xO(t),vO(b).off("mousemove touchmove",x).off("mouseup touchend",w),g.remove(),m.stop&&m.stop(t)};return vO(y).on("mousedown touchstart",function(t){var n,e,o,r,i,u,a,c,s,l,f=(n=b,e=Math.max,o=n.documentElement,r=n.body,i=e(o.scrollWidth,r.scrollWidth),u=e(o.clientWidth,r.clientWidth),a=e(o.offsetWidth,r.offsetWidth),c=e(o.scrollHeight,r.scrollHeight),s=e(o.clientHeight,r.clientHeight),{width:i<a?u:i,height:c<e(o.offsetHeight,r.offsetHeight)?s:c});xO(t),t.preventDefault(),p=t.button;var d=y;h=t.screenX,v=t.screenY,l=window.getComputedStyle?window.getComputedStyle(d,null).getPropertyValue("cursor"):d.runtimeStyle.cursor,g=vO("<div></div>").css({position:"absolute",top:0,left:0,width:f.width,height:f.height,zIndex:2147483647,opacity:1e-4,cursor:l}).appendTo(b.body),vO(b).on("mousemove touchmove",x).on("mouseup touchend",w),m.start(t)}),{destroy:function(){vO(y).off()}}}var SO=0,kO=function(s,e,l,o,r){var t,n="tox-",u="tox-crid-"+SO++,a=[{name:"move",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:0,deltaH:0,label:"Crop Mask"},{name:"nw",xMul:0,yMul:0,deltaX:1,deltaY:1,deltaW:-1,deltaH:-1,label:"Top Left Crop Handle"},{name:"ne",xMul:1,yMul:0,deltaX:0,deltaY:1,deltaW:1,deltaH:-1,label:"Top Right Crop Handle"},{name:"sw",xMul:0,yMul:1,deltaX:1,deltaY:0,deltaW:-1,deltaH:1,label:"Bottom Left Crop Handle"},{name:"se",xMul:1,yMul:1,deltaX:0,deltaY:0,deltaW:1,deltaH:1,label:"Bottom Right Crop Handle"}],i=["top","right","bottom","left"],f=function(t,n){return{x:n.x-t.x,y:n.y-t.y,w:n.w,h:n.h}},c=function(t,n,e,o){var r,i=n.x,u=n.y,a=n.w,c=n.h;i+=e*t.deltaX,u+=o*t.deltaY,(a+=e*t.deltaW)<20&&(a=20),(c+=o*t.deltaH)<20&&(c=20),r=s=hO.clamp({x:i,y:u,w:a,h:c},l,"move"===t.name),r=f(l,r),p.fire("updateRect",{rect:r}),g(r)},d=function(n){var t=function(t,n){n.h<0&&(n.h=0),n.w<0&&(n.w=0),vO("#"+u+"-"+t,o).css({left:n.x,top:n.y,width:n.w,height:n.h})};mk.each(a,function(t){vO("#"+u+"-"+t.name,o).css({left:n.w*t.xMul+n.x,top:n.h*t.yMul+n.y})}),t("top",{x:e.x,y:e.y,w:e.w,h:n.y-e.y}),t("right",{x:n.x+n.w,y:n.y,w:e.w-n.x-n.w+e.x,h:n.h}),t("bottom",{x:e.x,y:n.y+n.h,w:e.w,h:e.h-n.y-n.h+e.y}),t("left",{x:e.x,y:n.y,w:n.x-e.x,h:n.h}),t("move",n)},m=function(t){d(s=t)},g=function(t){var n,e;m((n=l,{x:(e=t).x+n.x,y:e.y+n.y,w:e.w,h:e.h}))};vO('<div id="'+u+'" class="'+n+'croprect-container" role="grid" aria-dropeffect="execute">').appendTo(o),mk.each(i,function(t){vO("#"+u,o).append('<div id="'+u+"-"+t+'"class="'+n+'croprect-block" style="display: none" data-mce-bogus="all">')}),mk.each(a,function(t){vO("#"+u,o).append('<div id="'+u+"-"+t.name+'" class="'+n+"croprect-handle "+n+"croprect-handle-"+t.name+'"style="display: none" data-mce-bogus="all" role="gridcell" tabindex="-1" aria-label="'+t.label+'" aria-grabbed="false" title="'+t.label+'">')}),t=mk.map(a,function(n){var e;return wO(u,{document:o.ownerDocument,handle:u+"-"+n.name,start:function(){e=s},drag:function(t){c(n,e,t.deltaX,t.deltaY)}})}),d(s),vO(o).on("focusin focusout",function(t){vO(t.target).attr("aria-grabbed","focus"===t.type?"true":"false")}),vO(o).on("keydown",function(n){var i;mk.each(a,function(t){if(n.target.id===u+"-"+t.name)return i=t,!1});var t=function(t,n,e,o,r){t.stopPropagation(),t.preventDefault(),c(i,e,o,r)};switch(n.keyCode){case yO.LEFT:t(n,0,s,-10,0);break;case yO.RIGHT:t(n,0,s,10,0);break;case yO.UP:t(n,0,s,0,-10);break;case yO.DOWN:t(n,0,s,0,10);break;case yO.ENTER:case yO.SPACEBAR:n.preventDefault(),r()}});var p=mk.extend({toggleVisibility:function(t){var n=mk.map(a,function(t){return"#"+u+"-"+t.name}).concat(mk.map(i,function(t){return"#"+u+"-"+t})).join(",");t?vO(n,o).show():vO(n,o).hide()},setClampRect:function(t){l=t,d(s)},setRect:m,getInnerRect:function(){return f(l,s)},setInnerRect:g,setViewPortRect:function(t){e=t,d(s)},destroy:function(){mk.each(t,function(t){t.destroy()}),t=[]}},bO);return p},CO=function(n){var l=mp({dom:{tag:"div",classes:["tox-image-tools__image-bg"],attributes:{role:"presentation"}}}),f=fe(1),d=fe(dt.none()),m=fe({x:0,y:0,w:1,h:1}),c=fe({x:0,y:0,w:1,h:1}),s=function(t,s){g.getOpt(t).each(function(t){var e=f.get(),o=Ou(t.element),r=vu(t.element),i=s.dom.naturalWidth*e,u=s.dom.naturalHeight*e,a=Math.max(0,o/2-i/2),c=Math.max(0,r/2-u/2),n={left:a.toString()+"px",top:c.toString()+"px",width:i.toString()+"px",height:u.toString()+"px",position:"absolute"};qi(s,n),l.getOpt(t).each(function(t){qi(t.element,n)}),d.get().each(function(t){var n=m.get();t.setRect({x:n.x*e+a,y:n.y*e+c,w:n.w*e,h:n.h*e}),t.setClampRect({x:a,y:c,w:i,h:u}),t.setViewPortRect({x:0,y:0,w:o,h:r})})})},e=function(t,n){var e,a=me.fromTag("img");return Yr(a,"src",n),e=a.dom,new Mp(function(t){var n=function(){e.removeEventListener("load",n),t(e)};e.complete?t(e):e.addEventListener("load",n)}).then(function(){return g.getOpt(t).map(function(t){var n=fu({element:a});lg.replaceAt(t,1,dt.some(n));var e=c.get(),o={x:0,y:0,w:a.dom.naturalWidth,h:a.dom.naturalHeight};c.set(o);var r,u,i=hO.inflate(o,-20,-20);return m.set(i),e.w===o.w&&e.h===o.h||(r=t,u=a,g.getOpt(r).each(function(t){var n=Ou(t.element),e=vu(t.element),o=u.dom.naturalWidth,r=u.dom.naturalHeight,i=Math.min(n/o,e/r);1<=i?f.set(1):f.set(i)})),s(t,a),a})})},t=Ny.sketch({dom:{tag:"div",classes:["tox-image-tools__image"]},components:[l.asSpec(),{dom:{tag:"img",attributes:{src:n}}},{dom:{tag:"div"},behaviours:rc([fg("image-panel-crop-events",[sr(function(t){g.getOpt(t).each(function(t){var n=t.element.dom,e=kO({x:10,y:10,w:100,h:100},{x:0,y:0,w:200,h:200},{x:0,y:0,w:200,h:200},n,ct);e.toggleVisibility(!1),e.on("updateRect",function(t){var n=t.rect,e=f.get(),o={x:Math.round(n.x/e),y:Math.round(n.y/e),w:Math.round(n.w/e),h:Math.round(n.h/e)};m.set(o)}),d.set(dt.some(e))})})])])}],containerBehaviours:rc([lg.config({}),fg("image-panel-events",[sr(function(t){e(t,n)})])])}),g=mp(t);return{memContainer:g,updateSrc:e,zoom:function(t,n){var e=f.get(),o=0<n?Math.min(2,e+.1):Math.max(.1,e-.1);f.set(o),g.getOpt(t).each(function(t){var n=t.components()[1].element;s(t,n)})},showCrop:function(){d.get().each(function(t){t.toggleVisibility(!0)})},hideCrop:function(){d.get().each(function(t){t.toggleVisibility(!1)})},getRect:function(){return m.get()},getMeasurements:function(){var t=c.get();return{width:t.w,height:t.h}}}},OO=function(t,n,e,o,r){return KC({name:t,icon:dt.some(n),disabled:e,tooltip:dt.some(t),primary:!1,borderless:!1},o,r)},_O=function(t,n){n?mv.enable(t):mv.disable(t)};var TO,EO,DO,BO=function(t){var e,o,n,r,i=fe(t),u=fe(dt.none()),a=(o=-1,{data:e=[],add:function(t){var n=e.splice(++o);return e.push(t),{state:t,removed:n}},undo:function(){if(n())return e[--o]},redo:function(){if(r())return e[++o]},canUndo:n=function(){return 0<o},canRedo:r=function(){return-1!==o&&o<e.length-1}});a.add(t);var c=function(t){i.set(t)},s=function(t){return{blob:t,url:URL.createObjectURL(t)}},l=function(t){URL.revokeObjectURL(t.url)},f=function(){u.get().each(l),u.set(dt.none())},d=function(t){var n=s(t);c(n);var e,o=a.add(n).removed;return e=o,mk.each(e,l),n.url};return{getBlobState:function(){return i.get()},setBlobState:c,addBlobState:d,getTempState:function(){return u.get().fold(function(){return i.get()},function(t){return t})},updateTempState:function(t){var n=s(t);return f(),u.set(dt.some(n)),n.url},addTempState:function(t){var n=s(t);return u.set(dt.some(n)),n.url},applyTempState:function(n){return u.get().fold(ct,function(t){d(t.blob),n()})},destroyTempState:f,undo:function(){var t=a.undo();return c(t),t.url},redo:function(){var t=a.redo();return c(t),t.url},getHistoryStates:function(){return{undoEnabled:a.canUndo(),redoEnabled:a.canRedo()}}}},AO=function(t,n){var e,o,r,u=BO(t.currentState),i=function(t){var n=u.getHistoryStates();p.updateButtonUndoStates(t,n.undoEnabled,n.redoEnabled),qo(t,gO.formActionEvent,{name:gO.saveState(),value:n.undoEnabled})},a=function(t){return t.toBlob()},c=function(t){qo(t,gO.formActionEvent,{name:gO.disable(),value:{}})},s=function(t){h.getApplyButton(t).each(function(t){mv.enable(t)}),qo(t,gO.formActionEvent,{name:gO.enable(),value:{}})},l=function(t,n){return c(t),g.updateSrc(t,n)},f=function(n,t,e,o,r){return c(n),Zk(t).then(e).then(a).then(o).then(function(t){return l(n,t).then(function(t){return i(n),r(),s(n),t})})["catch"](function(t){return console.log(t),s(n),t})},d=function(t,n,e){var o=u.getBlobState().blob;f(t,o,n,function(t){return u.updateTempState(t)},e)},m=function(t){var n=u.getBlobState().url;return u.destroyTempState(),i(t),n},g=CO(t.currentState.url),p=(o=mp(OO("Undo","undo",!0,function(t){qo(t,lO.undo(),{direction:1})},e=n)),r=mp(OO("Redo","redo",!0,function(t){qo(t,lO.redo(),{direction:1})},e)),{container:Ny.sketch({dom:{tag:"div",classes:["tox-image-tools__toolbar","tox-image-tools__sidebar"]},components:[o.asSpec(),r.asSpec(),OO("Zoom in","zoom-in",!1,function(t){qo(t,lO.zoom(),{direction:1})},e),OO("Zoom out","zoom-out",!1,function(t){qo(t,lO.zoom(),{direction:-1})},e)]}),updateButtonUndoStates:function(t,n,e){o.getOpt(t).each(function(t){_O(t,n)}),r.getOpt(t).each(function(t){_O(t,e)})}}),h=pO(g,n);return{dom:{tag:"div",attributes:{role:"presentation"}},components:[h.memContainer.asSpec(),g.memContainer.asSpec(),p.container],behaviours:rc([Ll.config({store:{mode:"manual",getValue:function(){return u.getBlobState()}}}),fg("image-tools-events",[er(lO.undo(),function(n,t){var e=u.undo();l(n,e).then(function(t){s(n),i(n)})}),er(lO.redo(),function(n,t){var e=u.redo();l(n,e).then(function(t){s(n),i(n)})}),er(lO.zoom(),function(t,n){var e=n.event.direction;g.zoom(t,e)}),er(lO.back(),function(t,n){var e,o;o=m(e=t),l(e,o).then(function(t){s(e)}),(0,n.event.swap)(),g.hideCrop()}),er(lO.apply(),function(t,n){u.applyTempState(function(){m(t),(0,n.event.swap)()})}),er(lO.transform(),function(t,n){return d(t,n.event.transform,ct)}),er(lO.tempTransform(),function(t,n){return e=t,o=n.event.transform,r=u.getTempState().blob,void f(e,r,o,function(t){return u.addTempState(t)},ct);var e,o,r}),er(lO.transformApply(),function(t,n){return e=t,o=n.event.transform,r=n.event.swap,i=u.getBlobState().blob,void f(e,i,o,function(t){var n=u.addBlobState(t);return m(e),n},r);var e,o,r,i}),er(lO.swap(),function(n,t){var e;e=n,p.updateButtonUndoStates(e,!1,!1);var o=t.event.transform,r=t.event.swap;o.fold(function(){r()},function(t){d(n,t,r)})})]),ak()])}},MO=function(t){return!It(t,"items")},FO="data-value",IO=function(n,e,t,o){return V(t,function(t){return MO(t)?{type:"togglemenuitem",text:t.text,value:t.value,active:t.value===o,onAction:function(){Ll.setValue(n,t.value),qo(n,Zy,{name:e}),hg.focus(n)}}:{type:"nestedmenuitem",text:t.text,getSubmenuItems:function(){return IO(n,e,t.items,o)}}})},RO=function(t,n){return Q(t,function(t){return MO(t)?Kf(t.value===n,t):RO(t.items,n)})},VO=Xf({name:"HtmlSelect",configFields:[Ln("options"),jl("selectBehaviours",[hg,Ll]),ne("selectClasses",[]),ne("selectAttributes",{}),Kn("data")],factory:function(e,t){var n=V(e.options,function(t){return{dom:{tag:"option",value:t.value,innerHtml:t.text}}}),o=e.data.map(function(t){return Jt("initialValue",t)}).getOr({});return{uid:e.uid,dom:{tag:"select",classes:e.selectClasses,attributes:e.selectAttributes},components:n,behaviours:Wl(e.selectBehaviours,[hg.config({}),Ll.config({store:ft({mode:"manual",getValue:function(t){return eu(t.element)},setValue:function(t,n){L(e.options,function(t){return t.value===n}).isSome()&&ou(t.element,n)}},o)})])}}}),PO=function(e,n){var t=e.label.map(function(t){return Qy(t,n)}),o=[mv.config({disabled:function(){return e.disabled||n.isDisabled()}}),Nv(),ig.config({mode:"execution",useEnter:!0!==e.multiline,useControlEnter:!0===e.multiline,execute:function(t){return Yo(t,ox),dt.some(!0)}}),fg("textfield-change",[er(po(),function(t,n){qo(t,Zy,{name:e.name})}),er(Co(),function(t,n){qo(t,Zy,{name:e.name})})]),Yy.config({})],r=e.validation.map(function(o){return Fx.config({getRoot:function(t){return kr(t.element)},invalidClass:"tox-invalid",validator:{validate:function(t){var n=Ll.getValue(t),e=o.validator(n);return kx(!0===e?pt.value(n):pt.error(e))},validateOnLoad:o.validateOnLoad}})}).toArray(),i=e.placeholder.fold(st({}),function(t){return{placeholder:n.translate(t)}}),u=e.inputMode.fold(st({}),function(t){return{inputmode:t}}),a=ft(ft({},i),u),c=Wy.parts.field({tag:!0===e.multiline?"textarea":"input",inputAttributes:a,inputClasses:[e.classname],inputBehaviours:rc(gt([o,r])),selectOnFocus:!1,factory:dx}),s=(e.flex?["tox-form__group--stretched"]:[]).concat(e.maximized?["tox-form-group--maximize"]:[]),l=[mv.config({disabled:function(){return e.disabled||n.isDisabled()},onDisabled:function(t){Wy.getField(t).each(mv.disable)},onEnabled:function(t){Wy.getField(t).each(mv.enable)}}),Nv()];return Ky(t,c,s,l)},HO=/* */Object.freeze({__proto__:null,events:function(t,n){var e=t.stream.streams.setup(t,n);return Zo([er(t.event,e),lr(function(){return n.cancel()})].concat(t.cancelEvent.map(function(t){return[er(t,function(){return n.cancel()})]}).getOr([])))}}),zO=function(t){var n=fe(null);return xi({readState:function(){return{timer:null!==n.get()?"set":"unset"}},setTimer:function(t){n.set(t)},cancel:function(){var t=n.get();null!==t&&t.cancel()}})},NO=/* */Object.freeze({__proto__:null,throttle:zO,init:function(t){return t.stream.streams.state(t)}}),LO=[jn("stream",An("mode",{throttle:[Ln("delay"),ne("stopEvent",!0),va("streams",{setup:function(t,n){var e=t.stream,o=wp(t.onStream,e.delay);return n.setTimer(o),function(t,n){o.throttle(t,n),e.stopEvent&&n.stop()}},state:zO})]})),ne("event","input"),Kn("cancelEvent"),pa("onStream")],jO=uc({fields:LO,name:"streaming",active:HO,state:NO}),UO=function(t,n,e){var o=Ll.getValue(e);Ll.setValue(n,o),GO(n)},WO=function(t,n){var e=t.element,o=eu(e),r=e.dom;"number"!==qr(e,"type")&&n(r,o)},GO=function(t){WO(t,function(t,n){return t.setSelectionRange(n.length,n.length)})},XO=function(t,n,o){if(t.selectsOver){var e=Ll.getValue(n),r=t.getDisplayText(e),i=Ll.getValue(o);return 0===t.getDisplayText(i).indexOf(r)?dt.some(function(){var t,e;UO(0,n,o),t=n,e=r.length,WO(t,function(t,n){return t.setSelectionRange(e,n.length)})}):dt.none()}return dt.none()},YO=st("alloy.typeahead.itemexecute"),qO=st([Kn("lazySink"),Ln("fetch"),ne("minChars",5),ne("responseTime",1e3),ma("onOpen"),ne("getHotspot",dt.some),ne("getAnchorOverrides",st({})),ne("layouts",dt.none()),ne("eventOrder",{}),se("model",{},[ne("getDisplayText",function(t){return t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.value}),ne("selectsOver",!0),ne("populateFromBrowse",!0)]),ma("onSetValue"),ga("onExecute"),ma("onItemExecute"),ne("inputClasses",[]),ne("inputAttributes",{}),ne("inputStyles",{}),ne("matchWidth",!0),ne("useMinWidth",!1),ne("dismissOnBlur",!0),fa(["openClass"]),Kn("initialData"),jl("typeaheadBehaviours",[hg,Ll,jO,ig,Eg,Vx]),le("previewing",function(){return fe(!0)})].concat(sx()).concat(tw())),KO=st([yf({schema:[la()],name:"menu",overrides:function(o){return{fakeFocus:!0,onHighlight:function(n,e){o.previewing.get()?n.getSystem().getByUid(o.uid).each(function(t){XO(o.model,t,e).fold(function(){return ld.dehighlight(n,e)},function(t){return t()})}):n.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&UO(o.model,t,e)}),o.previewing.set(!1)},onExecute:function(t,n){return t.getSystem().getByUid(o.uid).toOptional().map(function(t){return qo(t,YO(),{item:n}),!0})},onHover:function(t,n){o.previewing.set(!1),t.getSystem().getByUid(o.uid).each(function(t){o.model.populateFromBrowse&&UO(o.model,t,n)})}}}})]),JO=Yf({name:"Typeahead",configFields:qO(),partFields:KO(),factory:function(r,t,n,i){var e=function(t,n,e){r.previewing.set(!1);var o=Vx.getCoupled(t,"sandbox");wl.isOpen(o)?Zf.getCurrent(o).each(function(t){ld.getHighlighted(t).fold(function(){e(t)},function(){Qo(o,t.element,"keydown",n)})}):Xx(r,u(t),t,o,i,function(t){Zf.getCurrent(t).each(e)},px.HighlightFirst).get(ct)},o=lx(r),u=function(o){return function(t){return t.map(function(t){var n=Mt(t.menus),e=U(n,function(t){return H(t.items,function(t){return"item"===t.type})});return Ll.getState(o).update(V(e,function(t){return t.data})),t})}},a=[hg.config({}),Ll.config({onSetValue:r.onSetValue,store:ft({mode:"dataset",getDataKey:function(t){return eu(t.element)},getFallbackEntry:function(t){return{value:t,meta:{}}},setValue:function(t,n){ou(t.element,r.model.getDisplayText(n))}},r.initialData.map(function(t){return Jt("initialValue",t)}).getOr({}))}),jO.config({stream:{mode:"throttle",delay:r.responseTime,stopEvent:!1},onStream:function(t,n){var e,o=Vx.getCoupled(t,"sandbox");hg.isFocused(t)&&eu(t.element).length>=r.minChars&&(e=Zf.getCurrent(o).bind(function(t){return ld.getHighlighted(t).map(Ll.getValue)}),r.previewing.set(!0),Xx(r,u(t),t,o,i,function(t){Zf.getCurrent(o).each(function(t){e.fold(function(){r.model.selectsOver&&ld.highlightFirst(t)},function(n){ld.highlightBy(t,function(t){return Ll.getValue(t).value===n.value}),ld.getHighlighted(t).orThunk(function(){return ld.highlightFirst(t),dt.none()})})})},px.HighlightFirst).get(ct))},cancelEvent:Ao()}),ig.config({mode:"special",onDown:function(t,n){return e(t,n,ld.highlightFirst),dt.some(!0)},onEscape:function(t){var n=Vx.getCoupled(t,"sandbox");return wl.isOpen(n)?(wl.close(n),dt.some(!0)):dt.none()},onUp:function(t,n){return e(t,n,ld.highlightLast),dt.some(!0)},onEnter:function(n){var t=Vx.getCoupled(n,"sandbox"),e=wl.isOpen(t);if(e&&!r.previewing.get())return Zf.getCurrent(t).bind(function(t){return ld.getHighlighted(t)}).map(function(t){return qo(n,YO(),{item:t}),!0});var o=Ll.getValue(n);return Yo(n,Ao()),r.onExecute(t,n,o),e&&wl.close(t),dt.some(!0)}}),Eg.config({toggleClass:r.markers.openClass,aria:{mode:"expanded"}}),Vx.config({others:{sandbox:function(t){return Qx(r,t,{onOpen:function(){return Eg.on(t)},onClose:function(){return Eg.off(t)}})}}}),fg("typeaheadevents",[dr(function(t){var n=ct;qx(r,u(t),t,i,n,px.HighlightFirst).get(ct)}),er(YO(),function(t,n){var e=Vx.getCoupled(t,"sandbox");UO(r.model,t,n.event.item),Yo(t,Ao()),r.onItemExecute(t,e,n.event.item,Ll.getValue(t)),wl.close(e),GO(t)})].concat(r.dismissOnBlur?[er(ko(),function(t){var n=Vx.getCoupled(t,"sandbox");gc(n.element).isNone()&&wl.close(n)})]:[]))];return{uid:r.uid,dom:fx(zt(r,{inputAttributes:{role:"combobox","aria-autocomplete":"list","aria-haspopup":"true"}})),behaviours:ft(ft({},o),Wl(r.typeaheadBehaviours,a)),eventOrder:r.eventOrder}}}),$O=function(i){return ft(ft({},i),{toCached:function(){return $O(i.toCached())},bindFuture:function(n){return $O(i.bind(function(t){return t.fold(function(t){return kx(pt.error(t))},function(t){return n(t)})}))},bindResult:function(n){return $O(i.map(function(t){return t.bind(n)}))},mapResult:function(n){return $O(i.map(function(t){return t.map(n)}))},mapError:function(n){return $O(i.map(function(t){return t.mapError(n)}))},foldResult:function(n,e){return i.map(function(t){return t.fold(n,e)})},withTimeout:function(t,r){return $O(Sx(function(n){var e=!1,o=setTimeout(function(){e=!0,n(pt.error(r()))},t);i.get(function(t){e||(clearTimeout(o),n(t))})}))}})},QO=function(t){return $O(Sx(t))},ZO=QO,t_={type:"separator"},n_=function(t){return{type:"menuitem",value:t.url,text:t.title,meta:{attach:t.attach},onAction:ct}},e_=function(t,n){return{type:"menuitem",value:n,text:t,meta:{attach:undefined},onAction:ct}},o_=function(t,n){return o=t,e=H(n,function(t){return t.type===o}),V(e,n_);var e,o},r_=function(t,n){var e=t.toLowerCase();return H(n,function(t){var n=t.meta!==undefined&&t.meta.text!==undefined?t.meta.text:t.text;return we(n.toLowerCase(),e)||we(t.value.toLowerCase(),e)})},i_=function(u,t,a){var n=Ll.getValue(t),c=n.meta.text!==undefined?n.meta.text:n.value;return a.getLinkInformation().fold(function(){return[]},function(t){var n,e,o,r,i=r_(c,(n=a.getHistory(u),V(n,function(t){return e_(t,t)})));return"file"===u?(e=[i,r_(c,o_("header",t.targets)),r_(c,gt([(r=t,dt.from(r.anchorTop).map(function(t){return e_("<top>",t)}).toArray()),o_("anchor",t.targets),(o=t,dt.from(o.anchorBottom).map(function(t){return e_("<bottom>",t)}).toArray())]))],N(e,function(t,n){return 0===t.length||0===n.length?t.concat(n):t.concat(t_,n)},[])):i})},u_=oi("aria-invalid"),a_=function(r,o,i){var t,n,e,u,a,c=o.shared.providers,s=function(t){var n=Ll.getValue(t);i.addToHistory(n.value,r.filetype)},l=Wy.parts.field({factory:JO,dismissOnBlur:!0,inputClasses:["tox-textfield"],sandboxClasses:["tox-dialog__popups"],inputAttributes:{"aria-errormessage":u_,type:"url"},minChars:0,responseTime:0,fetch:function(t){var n=i_(r.filetype,t,i),e=UC(n,ih.BUBBLE_TO_SANDBOX,o,!1);return kx(e)},getHotspot:function(t){return h.getOpt(t)},onSetValue:function(t,n){t.hasConfigured(Fx)&&Fx.run(t).get(ct)},typeaheadBehaviours:rc(gt([i.getValidationHandler().map(function(e){return Fx.config({getRoot:function(t){return kr(t.element)},invalidClass:"tox-control-wrap--status-invalid",notify:{onInvalid:function(t,n){d.getOpt(t).each(function(t){Yr(t.element,"title",c.translate(n))})}},validator:{validate:function(t){var n=Ll.getValue(t);return ZO(function(o){e({type:r.filetype,url:n.value},function(t){var n,e;"invalid"===t.status?(n=pt.error(t.message),o(n)):(e=pt.value(t.message),o(e))})})},validateOnLoad:!1}})}).toArray(),[mv.config({disabled:function(){return r.disabled||c.isDisabled()}}),Yy.config({}),fg("urlinput-events",gt(["file"===r.filetype?[er(po(),function(t){qo(t,Zy,{name:r.name})})]:[],[er(ho(),function(t){qo(t,Zy,{name:r.name}),s(t)}),er(Co(),function(t){qo(t,Zy,{name:r.name}),s(t)})]]))]])),eventOrder:((t={})[po()]=["streaming","urlinput-events","invalidating"],t),model:{getDisplayText:function(t){return t.value},selectsOver:!1,populateFromBrowse:!1},markers:{openClass:"tox-textfield--popup-open"},lazySink:o.shared.getSink,parts:{menu:xh(0,0,"normal")},onExecute:function(t,n,e){qo(n,ox,{})},onItemExecute:function(t,n,e,o){s(t),qo(t,Zy,{name:r.name})}}),f=r.label.map(function(t){return Qy(t,c)}),d=mp((n="invalid",e=dt.some(u_),void 0===(u="warning")&&(u=n),void 0===a&&(a=n),{dom:{tag:"div",classes:["tox-icon","tox-control-wrap__status-icon-"+n],innerHtml:pp(u,c.icons),attributes:ft({title:c.translate(a),"aria-live":"polite"},e.fold(function(){return{}},function(t){return{id:t}}))}})),m=mp({dom:{tag:"div",classes:["tox-control-wrap__status-icon-wrap"]},components:[d.asSpec()]}),g=i.getUrlPicker(r.filetype),p=oi("browser.url.event"),h=mp({dom:{tag:"div",classes:["tox-control-wrap"]},components:[l,m.asSpec()],behaviours:rc([mv.config({disabled:function(){return r.disabled||c.isDisabled()}})])}),v=mp($C({name:r.name,icon:dt.some("browse"),text:r.label.getOr(""),disabled:r.disabled,primary:!1,borderless:!0},function(t){return Yo(t,p)},c,[],["tox-browse-url"]));return Wy.sketch({dom:$y([]),components:f.toArray().concat([{dom:{tag:"div",classes:["tox-form__controls-h-stack"]},components:gt([[h.asSpec()],g.map(function(){return v.asSpec()}).toArray()])}]),fieldBehaviours:rc([mv.config({disabled:function(){return r.disabled||c.isDisabled()},onDisabled:function(t){Wy.getField(t).each(mv.disable),v.getOpt(t).each(mv.disable)},onEnabled:function(t){Wy.getField(t).each(mv.enable),v.getOpt(t).each(mv.enable)}}),Nv(),fg("url-input-events",[er(p,function(o){Zf.getCurrent(o).each(function(n){var t=Ll.getValue(n),e=ft({fieldname:r.name},t);g.each(function(t){t(e).get(function(t){Ll.setValue(n,t),qo(o,Zy,{name:r.name})})})})})])])})},c_=function(r){return function(n,e,o){return Ft(e,"name").fold(function(){return r(e,o)},function(t){return n.field(t,r(e,o))})}},s_={bar:c_(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-bar","tox-form__controls-h-stack"]},components:V(e.items,o.interpreter)};var e,o}),collection:c_(function(t,n){return cx(t,n.shared.providers)}),alertbanner:c_(function(t,n){return e=t,o=n.shared.providers,Ny.sketch({dom:{tag:"div",attributes:{role:"alert"},classes:["tox-notification","tox-notification--in","tox-notification--"+e.level]},components:[{dom:{tag:"div",classes:["tox-notification__icon"]},components:[dp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--naked","tox-button--icon"],innerHtml:pp(e.icon,o.icons),attributes:{title:o.translate(e.iconTooltip)}},action:function(t){qo(t,ex,{name:"alert-banner",value:e.url})}})]},{dom:{tag:"div",classes:["tox-notification__body"],innerHtml:o.translate(e.text)}}]});var e,o}),input:c_(function(t,n){return e=t,o=n.shared.providers,PO({name:e.name,multiline:!1,label:e.label,inputMode:e.inputMode,placeholder:e.placeholder,flex:!1,disabled:e.disabled,classname:"tox-textfield",validation:dt.none(),maximized:e.maximized},o);var e,o}),textarea:c_(function(t,n){return e=t,o=n.shared.providers,PO({name:e.name,multiline:!0,label:e.label,inputMode:dt.none(),placeholder:e.placeholder,flex:!0,disabled:e.disabled,classname:"tox-textarea",validation:dt.none(),maximized:e.maximized},o);var e,o}),label:c_(function(t,n){return e=t,o=n.shared,r={dom:{tag:"label",innerHtml:o.providers.translate(e.label),classes:["tox-label"]}},i=V(e.items,o.interpreter),{dom:{tag:"div",classes:["tox-form__group"]},components:[r].concat(i),behaviours:rc([ak(),lg.config({}),yk(dt.none()),ig.config({mode:"acyclic"})])};var e,o,r,i}),iframe:(TO=function(t,n){return Bk(t,n.shared.providers)},function(t,n,e){var o=zt(n,{source:"dynamic"});return c_(TO)(t,o,e)}),button:c_(function(t,n){return tO(t,n.shared.providers)}),checkbox:c_(function(t,n){return e=t,o=n.shared.providers,r=Ll.config({store:{mode:"manual",getValue:function(t){return t.element.dom.checked},setValue:function(t,n){t.element.dom.checked=n}}}),i=function(t){return t.element.dom.click(),dt.some(!0)},u=Wy.parts.field({factory:{sketch:lt},dom:{tag:"input",classes:["tox-checkbox__input"],attributes:{type:"checkbox"}},behaviours:rc([ak(),mv.config({disabled:function(){return e.disabled||o.isDisabled()}}),Yy.config({}),hg.config({}),r,ig.config({mode:"special",onEnter:i,onSpace:i,stopSpaceKeyup:!0}),fg("checkbox-events",[er(ho(),function(t,n){qo(t,Zy,{name:e.name})})])])}),a=Wy.parts.label({dom:{tag:"span",classes:["tox-checkbox__label"],innerHtml:o.translate(e.label)},behaviours:rc([rw.config({})])}),s=mp({dom:{tag:"div",classes:["tox-checkbox__icons"]},components:[(c=function(t){return{dom:{tag:"span",classes:["tox-icon","tox-checkbox-icon__"+t],innerHtml:pp("checked"===t?"selected":"unselected",o.icons)}}})("checked"),c("unchecked")]}),Wy.sketch({dom:{tag:"label",classes:["tox-checkbox"]},components:[u,s.asSpec(),a],fieldBehaviours:rc([mv.config({disabled:function(){return e.disabled||o.isDisabled()},disableClass:"tox-checkbox--disabled",onDisabled:function(t){Wy.getField(t).each(mv.disable)},onEnabled:function(t){Wy.getField(t).each(mv.enable)}}),Nv()])});var e,o,r,i,u,a,c,s}),colorinput:c_(function(t,n){return cw(t,n.shared,n.colorinput)}),colorpicker:c_(function(t){var n=function(t){return"tox-"+t},e=uk(fk,n),r=mp(e.sketch({dom:{tag:"div",classes:["tox-color-picker-container"],attributes:{role:"presentation"}},onValidHex:function(t){qo(t,ex,{name:"hex-valid",value:!0})},onInvalidHex:function(t){qo(t,ex,{name:"hex-valid",value:!1})}}));return{dom:{tag:"div"},components:[r.asSpec()],behaviours:rc([Ll.config({store:{mode:"manual",getValue:function(t){var n=r.get(t);return Zf.getCurrent(n).bind(function(t){return Ll.getValue(t).hex}).map(function(t){return"#"+t}).getOr("")},setValue:function(t,n){var e=/^#([a-fA-F0-9]{3}(?:[a-fA-F0-9]{3})?)/.exec(n),o=r.get(t);Zf.getCurrent(o).fold(function(){console.log("Can not find form")},function(t){Ll.setValue(t,{hex:dt.from(e[1]).getOr("")}),ZS.getField(t,"hex").each(function(t){Yo(t,po())})})}}}),ak()])}}),dropzone:c_(function(t,n){return wk(t,n.shared.providers)}),grid:c_(function(t,n){return e=t,o=n.shared,{dom:{tag:"div",classes:["tox-form__grid","tox-form__grid--"+e.columns+"col"]},components:V(e.items,o.interpreter)};var e,o}),listbox:c_(function(t,n){return o=t,e=(r=n).shared.providers,i=K(o.items).filter(MO),u=o.label.map(function(t){return Qy(t,e)}),a={dom:{tag:"div",classes:["tox-listboxfield"]},components:[Wy.parts.field({dom:{},factory:{sketch:function(t){return PC({uid:t.uid,text:i.map(function(t){return t.text}),icon:dt.none(),tooltip:o.label,role:dt.none(),fetch:function(t,n){var e=IO(t,o.name,o.items,Ll.getValue(t));n(UC(e,ih.CLOSE_ON_EXECUTE,r,!1))},onSetup:st(ct),getApi:st({}),columns:1,presets:"normal",classes:[],dropdownBehaviours:[Yy.config({}),Ll.config({store:{mode:"manual",initialValue:i.map(function(t){return t.value}).getOr(""),getValue:function(t){return qr(t.element,FO)},setValue:function(n,t){RO(o.items,t).each(function(t){Yr(n.element,FO,t.value),qo(n,RC,{text:t.text})})}}})]},"tox-listbox",r.shared)}}})]},Wy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:gt([u.toArray(),[a]]),fieldBehaviours:rc([mv.config({disabled:st(o.disabled),onDisabled:function(t){Wy.getField(t).each(mv.disable)},onEnabled:function(t){Wy.getField(t).each(mv.enable)}})])});var o,r,e,i,u,a}),selectbox:c_(function(t,n){return e=t,o=n.shared.providers,r=V(e.items,function(t){return{text:o.translate(t.text),value:t.value}}),i=e.label.map(function(t){return Qy(t,o)}),u=Wy.parts.field({dom:{},selectAttributes:{size:e.size},options:r,factory:VO,selectBehaviours:rc([mv.config({disabled:function(){return e.disabled||o.isDisabled()}}),Yy.config({}),fg("selectbox-change",[er(ho(),function(t,n){qo(t,Zy,{name:e.name})})])])}),a=1<e.size?dt.none():dt.some({dom:{tag:"div",classes:["tox-selectfield__icon-js"],innerHtml:pp("chevron-down",o.icons)}}),c={dom:{tag:"div",classes:["tox-selectfield"]},components:gt([[u],a.toArray()])},Wy.sketch({dom:{tag:"div",classes:["tox-form__group"]},components:gt([i.toArray(),[c]]),fieldBehaviours:rc([mv.config({disabled:function(){return e.disabled||o.isDisabled()},onDisabled:function(t){Wy.getField(t).each(mv.disable)},onEnabled:function(t){Wy.getField(t).each(mv.enable)}}),Nv()])});var e,o,r,i,u,a,c}),sizeinput:c_(function(t,n){return sO(t,n.shared.providers)}),urlinput:c_(function(t,n){return a_(t,n,n.urlinput)}),customeditor:c_(function(e){var o=fe(dt.none()),n=mp({dom:{tag:e.tag}}),r=fe(dt.none());return{dom:{tag:"div",classes:["tox-custom-editor"]},behaviours:rc([fg("custom-editor-events",[sr(function(t){n.getOpt(t).each(function(n){var t;t=e,(Object.prototype.hasOwnProperty.call(t,"init")?e.init(n.element.dom):dk.load(e.scriptId,e.scriptUrl).then(function(t){return t(n.element.dom,e.settings)})).then(function(n){r.get().each(function(t){n.setValue(t)}),r.set(dt.none()),o.set(dt.some(n))})})})]),Ll.config({store:{mode:"manual",getValue:function(){return o.get().fold(function(){return r.get().getOr("")},function(t){return t.getValue()})},setValue:function(t,n){o.get().fold(function(){r.set(dt.some(n))},function(t){return t.setValue(n)})}}}),ak()]),components:[n.asSpec()]}}),htmlpanel:c_(function(t){return"presentation"===t.presets?Ny.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html}}):Ny.sketch({dom:{tag:"div",classes:["tox-form__group"],innerHtml:t.html,attributes:{role:"document"}},containerBehaviours:rc([Yy.config({}),hg.config({})])})}),imagetools:c_(function(t,n){return AO(t,n.shared.providers)}),table:c_(function(t,n){return e=t,o=n.shared.providers,u=function(t){return{dom:{tag:"th",innerHtml:o.translate(t)}}},a=function(t){return{dom:{tag:"td",innerHtml:o.translate(t)}}},c=function(t){return{dom:{tag:"tr"},components:V(t,a)}},{dom:{tag:"table",classes:["tox-dialog__table"]},components:[(i=e.header,{dom:{tag:"thead"},components:[{dom:{tag:"tr"},components:V(i,u)}]}),(r=e.cells,{dom:{tag:"tbody"},components:V(r,c)})],behaviours:rc([Yy.config({}),hg.config({})])};var e,o,r,i,u,a,c}),panel:c_(function(t,n){return o=n,{dom:{tag:"div",classes:(e=t).classes},components:V(e.items,o.shared.interpreter)};var e,o})},l_={field:function(t,n){return n}},f_=function(n,t,e){var o=zt(e,{shared:{interpreter:function(t){return d_(n,t,o)}}});return d_(n,t,o)},d_=function(n,e,o){return Ft(s_,e.type).fold(function(){return console.error('Unknown factory type "'+e.type+'", defaulting to container: ',e),e},function(t){return t(n,e,o)})},m_={valignCentre:[],alignCentre:[],alignLeft:[],alignRight:[],right:[],left:[],bottom:[],top:[]},g_=function(t,n,e){var o=Pc(-12,12,m_),r={maxHeightFunction:Mc()};return function(){return e()?{anchor:"node",root:Mr(t()),node:dt.from(t()),bubble:o,layouts:{onRtl:function(){return[cp]},onLtr:function(){return[ap]}},overrides:r}:{anchor:"hotspot",hotspot:n(),bubble:o,layouts:{onRtl:function(){return[Pa]},onLtr:function(){return[Ha]}},overrides:r}}},p_=function(t,n,e){return function(){return e()?{anchor:"node",root:Mr(t()),node:dt.from(t()),layouts:{onRtl:function(){return[sp]},onLtr:function(){return[sp]}}}:{anchor:"hotspot",hotspot:n(),layouts:{onRtl:function(){return[ja]},onLtr:function(){return[ja]}}}}},h_=function(t,n,e){var o,r,i,u=Mv(t),a=function(){return me.fromDom(t.getBody())},c=function(){return me.fromDom(t.getContentAreaContainer())},s=function(){return u||!e()};return{inlineDialog:g_(c,n,s),banner:p_(c,n,s),cursor:(r=t,function(){return{anchor:"selection",root:i(),getSelection:function(){var t=r.selection.getRng();return dt.some(ms.range(me.fromDom(t.startContainer),t.startOffset,me.fromDom(t.endContainer),t.endOffset))}}}),node:(o=i=a,function(t){return{anchor:"node",root:o(),node:t}})}},v_=function(t){return{colorPicker:function(t,n){$b(r)(t,n)},hasCustomColors:function(){return Nb(o)},getColors:function(){return Lb(e)},getColorCols:(n=e=o=r=t,function(){return Wb(n)})};var n,e,o,r},b_=[{title:"Headings",items:[{title:"Heading 1",format:"h1"},{title:"Heading 2",format:"h2"},{title:"Heading 3",format:"h3"},{title:"Heading 4",format:"h4"},{title:"Heading 5",format:"h5"},{title:"Heading 6",format:"h6"}]},{title:"Inline",items:[{title:"Bold",format:"bold"},{title:"Italic",format:"italic"},{title:"Underline",format:"underline"},{title:"Strikethrough",format:"strikethrough"},{title:"Superscript",format:"superscript"},{title:"Subscript",format:"subscript"},{title:"Code",format:"code"}]},{title:"Blocks",items:[{title:"Paragraph",format:"p"},{title:"Blockquote",format:"blockquote"},{title:"Div",format:"div"},{title:"Pre",format:"pre"}]},{title:"Align",items:[{title:"Left",format:"alignleft"},{title:"Center",format:"aligncenter"},{title:"Right",format:"alignright"},{title:"Justify",format:"alignjustify"}]}],y_=function(t){return N(t,function(t,n){if(It(n,"items")){var e=y_(n.items);return{customFormats:t.customFormats.concat(e.customFormats),formats:t.formats.concat([{title:n.title,items:e.formats}])}}if(It(n,"inline")||It(n,"block")||It(n,"selector")){var o="custom-"+(x(n.name)?n.name:n.title.toLowerCase());return{customFormats:t.customFormats.concat([{name:o,format:n}]),formats:t.formats.concat([{title:n.title,format:o,icon:n.icon}])}}return ft(ft({},t),{formats:t.formats.concat(n)})},{customFormats:[],formats:[]})},x_=function(i){return t=i,dt.from(t.getParam("style_formats")).filter(c).map(function(t){var n,e,o,r=(n=i,e=y_(t),o=function(t){mt(t,function(t){n.formatter.has(t.name)||n.formatter.register(t.name,t.format)})},n.formatter?o(e.customFormats):n.on("init",function(){o(e.customFormats)}),e.formats);return i.getParam("style_formats_merge",!1,"boolean")?b_.concat(r):r}).getOr(b_);var t},w_=function(t,n,e){var o={type:"formatter",isSelected:n(t.format),getStylePreview:e(t.format)};return zt(t,o)},S_=function(c,t,s,l){var f=function(t){return V(t,function(t){var n,e,o,r,i,u=Ct(t);if(Rt(t,"items")){var a=f(t.items);return zt(zt(t,{type:"submenu"}),{getStyleItems:function(){return a}})}return Rt(t,"format")?w_(t,s,l):1===u.length&&M(u,"title")?zt(t,{type:"separator"}):(e=x((n=t).name)?n.name:oi(n.title),r={type:"formatter",format:o="custom-"+e,isSelected:s(o),getStylePreview:l(o)},i=zt(n,r),c.formatter.register(e,i),i)})};return f(t)},k_=mk.trim,C_=function(e){return function(t){if(i(n=t)&&1===n.nodeType){if(t.contentEditable===e)return!0;if(t.getAttribute("data-mce-contenteditable")===e)return!0}var n;return!1}},O_=C_("true"),__=C_("false"),T_=function(t,n,e,o,r){return{type:t,title:n,url:e,level:o,attach:r}},E_=function(t){return t.innerText||t.textContent},D_=function(t){return(n=t)&&"A"===n.nodeName&&(n.id||n.name)!==undefined&&A_(t);var n},B_=function(t){return t&&/^(H[1-6])$/.test(t.nodeName)},A_=function(t){return function(t){for(;t=t.parentNode;){var n=t.contentEditable;if(n&&"inherit"!==n)return O_(t)}return!1}(t)&&!__(t)},M_=function(t){return B_(t)&&A_(t)},F_=function(t){var n,e,o=(n=t).id?n.id:oi("h");return T_("header",E_(t),"#"+o,B_(e=t)?parseInt(e.nodeName.substr(1),10):0,function(){t.id=o})},I_=function(t){var n=t.id||t.name,e=E_(t);return T_("anchor",e||"#"+n,"#"+n,0,ct)},R_=function(t){var n,e;return n="h1,h2,h3,h4,h5,h6,a:not([href])",e=t,V(Os(me.fromDom(e),n),function(t){return t.dom})},V_=function(t){return 0<k_(t.title).length},P_=function(t){var n=R_(t);return H(V(H(n,M_),F_).concat(V(H(n,D_),I_)),V_)},H_="tinymce-url-history",z_=function(t){return x(t)&&/^https?/.test(t)},N_=function(t){return w(t)&&At(t,function(t){return!(c(n=t)&&n.length<=5&&W(n,z_));var n}).isNone()},L_=function(){var t,n=Fb.getItem(H_);if(null===n)return{};try{t=JSON.parse(n)}catch(e){if(e instanceof SyntaxError)return console.log("Local storage "+H_+" was not valid JSON",e),{};throw e}return N_(t)?t:(console.log("Local storage "+H_+" was not valid format",t),{})},j_=function(t){var n=L_();return Object.prototype.hasOwnProperty.call(n,t)?n[t]:[]},U_=function(n,t){var e,o,r;z_(n)&&(e=L_(),o=Object.prototype.hasOwnProperty.call(e,t)?e[t]:[],r=H(o,function(t){return t!==n}),e[t]=[n].concat(r).slice(0,5),function(t){if(!N_(t))throw new Error("Bad format for history:\n"+JSON.stringify(t));Fb.setItem(H_,JSON.stringify(t))}(e))},W_=function(t){return!!t},G_=function(t){return Tt(mk.makeMap(t,/[, ]/),W_)},X_=function(t){return dt.from(t.getParam("file_picker_callback")).filter(y)},Y_=function(t,n){var e,o,r,i,u=(e=t,o=dt.some(e.getParam("file_picker_types")).filter(W_),r=dt.some(e.getParam("file_browser_callback_types")).filter(W_),i=o.or(r).map(G_),X_(e).fold(l,function(t){return i.fold(O,function(t){return 0<Ct(t).length&&t})}));return S(u)?u?X_(t):dt.none():u[n]?X_(t):dt.none()},q_=function(t){return dt.from(t).filter(x).getOrUndefined()},K_=function(t){return dt.from((e=(n=t).getParam("file_picker_validator_handler",undefined,"function"))===undefined?n.getParam("filepicker_validator_handler",undefined,"function"):e);var n,e},J_=function(n){return{getHistory:j_,addToHistory:U_,getLinkInformation:function(){return!1===(t=n).getParam("typeahead_urls")?dt.none():dt.some({targets:P_(t.getBody()),anchorTop:q_(t.getParam("anchor_top","#top")),anchorBottom:q_(t.getParam("anchor_bottom","#bottom"))});var t},getValidationHandler:function(){return K_(n)},getUrlPicker:function(t){return Y_(r=n,i=t).map(function(o){return function(n){return Sx(function(e){var t=ft({filetype:i,fieldname:n.fieldname},dt.from(n.meta).getOr({}));o.call(r,function(t,n){if(!x(t))throw new Error("Expected value to be string");if(n!==undefined&&!w(n))throw new Error("Expected meta to be a object");e({value:t,meta:n})},n.value,t)})}});var r,i}}},$_=function(t,n,e){var o,r,i=fe(!1),u={isPositionedAtTop:function(){return"top"===o.get()},getDockingMode:(o=fe(Bv(n)?"bottom":"top")).get,setDockingMode:o.set},a={shared:{providers:{icons:function(){return n.ui.registry.getAll().icons},menuItems:function(){return n.ui.registry.getAll().menuItems},translate:Zh.translate,isDisabled:function(){return n.mode.isReadOnly()||n.ui.isDisabled()},getSetting:n.getParam.bind(n)},interpreter:function(t){return d_(l_,t,a)},anchors:h_(n,e,u.isPositionedAtTop),header:u,getSink:function(){return pt.value(t)}},urlinput:J_(n),styleselect:function(o){var r=function(t){return function(){return o.formatter.match(t)}},i=function(n){return function(){var t=o.formatter.get(n);return t!==undefined?dt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:o.dom.parseStyle(o.formatter.getCssText(n))}):dt.none()}},u=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,u):[t.format]},a=fe([]),c=fe([]),e=fe([]),s=fe([]),l=fe(!1);o.on("PreInit",function(t){var n=x_(o),e=S_(o,n,r,i);a.set(e),c.set(U(e,u))}),o.on("addStyleModifications",function(t){var n=S_(o,t.items,r,i);e.set(n),l.set(t.replace),s.set(U(n,u))});return{getData:function(){var t=l.get()?[]:a.get(),n=e.get();return t.concat(n)},getFlattenedKeys:function(){var t=l.get()?[]:c.get(),n=s.get();return t.concat(n)}}}(n),colorinput:v_(n),dialog:{isDraggableModal:(r=n,function(){return r.getParam("draggable_modal",!1,"boolean")})},isContextMenuOpen:function(){return i.get()},setContextMenuState:function(t){return i.set(t)}};return a},Q_=st(function(t,n){var e,o,r;e=t,o=Math.floor(n),r=Cu.max(e,o,["margin-left","border-left-width","padding-left","padding-right","border-right-width","margin-right"]),Yi(e,"max-width",r+"px")}),Z_="contexttoolbar-hide",tT=st([Ln("dom"),ne("shell",!0),jl("toolbarBehaviours",[lg])]),nT=st([xf({name:"groups",overrides:function(){return{behaviours:rc([lg.config({})])}}})]),eT=Yf({name:"Toolbar",configFields:tT(),partFields:nT(),factory:function(n,t,e,o){var r=function(t){return n.shell?dt.some(t):Mf(t,n,"groups")},i=n.shell?{behaviours:[lg.config({})],components:[]}:{behaviours:[],components:t};return{uid:n.uid,dom:n.dom,components:i.components,behaviours:Wl(n.toolbarBehaviours,i.behaviours),apis:{setGroups:function(t,n){r(t).fold(function(){throw console.error("Toolbar was defined to not be a shell, but no groups container was specified in components"),new Error("Toolbar was defined to not be a shell, but no groups container was specified in components")},function(t){lg.set(t,n)})}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)}}}),oT=function(t,n,e){return{within:t,extra:n,withinWidth:e}},rT=function(t,n,o){var e,r=(e=function(t,n){var e=o(t);return dt.some({element:t,start:n,finish:n+e,width:e})},N(t,function(n,t){return e(t,n.len).fold(st(n),function(t){return{len:t.finish,list:n.list.concat([t])}})},{len:0,list:[]}).list),i=H(r,function(t){return t.finish<=n}),u=z(i,function(t,n){return t+n.width},0);return{within:i,extra:r.slice(i.length),withinWidth:u}},iT=function(t){return V(t,function(t){return t.element})},uT=function(t,n,e,o){var r,i,u,a,c,s,l,f,d,m=(0===(r=rT(n,t,e)).extra.length?dt.some(r):dt.none()).getOrThunk(function(){return rT(n,t-e(o),e)}),g=m.within,p=m.extra,h=m.withinWidth;return 1===p.length&&p[0].width<=e(o)?(l=p,f=h,d=iT(g.concat(l)),oT(d,[],f)):1<=p.length?(u=p,a=o,c=h,s=iT(g).concat([a]),oT(s,iT(u),c)):(i=h,oT(iT(g),[],i))},aT=function(t,n){var e=V(n,function(t){return gu(t)});eT.setGroups(t,e)},cT=function(t,n,e){var o=Ff(t,n,"primary"),r=Vx.getCoupled(t,"overflowGroup");Yi(o.element,"visibility","hidden");var i=n.builtGroups.get().concat([r]),u=Q(i,function(n){return gc(n.element).bind(function(t){return n.getSystem().getByDom(t).toOptional()})});e([]),aT(o,i);var a=Ou(o.element),c=uT(a,n.builtGroups.get(),function(t){return Ou(t.element)},r);0===c.extra.length?(lg.remove(o,r),e([])):(aT(o,c.within),e(c.extra)),tu(o.element,"visibility"),nu(o.element),u.each(hg.focus)},sT=st([jl("splitToolbarBehaviours",[Vx]),le("builtGroups",function(){return fe([])})]),lT=st([fa(["overflowToggledClass"]),Zn("getOverflowBounds"),Ln("lazySink"),le("overflowGroups",function(){return fe([])})].concat(sT())),fT=st([bf({factory:eT,schema:tT(),name:"primary"}),yf({schema:tT(),name:"overflow"}),yf({name:"overflow-button"}),yf({name:"overflow-group"})]),dT=st([fa(["toggledClass"]),Ln("lazySink"),Gn("fetch"),Zn("getBounds"),te("fireDismissalEventInstead",[ne("event",zo())]),qc()]),mT=st([yf({name:"button",overrides:function(t){return{dom:{attributes:{"aria-haspopup":"true"}},buttonBehaviours:rc([Eg.config({toggleClass:t.markers.toggledClass,aria:{mode:"expanded"},toggleOnExecute:!1})])}}}),yf({factory:eT,schema:tT(),name:"toolbar",overrides:function(n){return{toolbarBehaviours:rc([ig.config({mode:"cyclic",onEscape:function(t){return Mf(t,n,"button").each(hg.focus),dt.none()}})])}}})]),gT=function(t,n){var e=Vx.getCoupled(t,"toolbarSandbox");wl.isOpen(e)?wl.close(e):wl.open(e,n.toolbar())},pT=function(t,n,e,o){var r=e.getBounds.map(function(t){return t()}),i=e.lazySink(t).getOrDie();Zs.positionWithinBounds(i,{anchor:"hotspot",hotspot:t,layouts:o,overrides:{maxWidthFunction:Q_()}},n,r)},hT=function(t,n,e,o,r){eT.setGroups(n,r),pT(t,n,e,o),Eg.on(t)},vT=Yf({name:"FloatingToolbarButton",factory:function(u,t,a,n){return ft(ft({},dp.sketch(ft(ft({},n.button()),{action:function(t){gT(t,n)},buttonBehaviours:Xl({dump:n.button().buttonBehaviours},[Vx.config({others:{toolbarSandbox:function(t){return o=t,e=a,r=u,{dom:{tag:"div",attributes:{id:(i=Ju()).id}},behaviours:rc([ig.config({mode:"special",onEscape:function(t){return wl.close(t),dt.some(!0)}}),wl.config({onOpen:function(t,n){r.fetch().get(function(t){hT(o,n,r,e.layouts,t),i.link(o.element),ig.focusIn(n)})},onClose:function(){Eg.off(o),hg.focus(o),i.unlink(o.element)},isPartOf:function(t,n,e){return Qu(n,e)||Qu(o,e)},getAttachPoint:function(){return r.lazySink(o).getOrDie()}}),lc.config({channels:ft(ft({},_l(ft({isExtraPart:l},r.fireDismissalEventInstead.map(function(t){return{fireEventInstead:{event:t.event}}}).getOr({})))),El({doReposition:function(){wl.getState(Vx.getCoupled(o,"toolbarSandbox")).each(function(t){pT(o,t,r,e.layouts)})}}))})])};var o,e,r,i}}})])}))),{apis:{setGroups:function(n,e){wl.getState(Vx.getCoupled(n,"toolbarSandbox")).each(function(t){hT(n,t,u,a.layouts,e)})},reposition:function(n){wl.getState(Vx.getCoupled(n,"toolbarSandbox")).each(function(t){pT(n,t,u,a.layouts)})},toggle:function(t){gT(t,n)},getToolbar:function(t){return wl.getState(Vx.getCoupled(t,"toolbarSandbox"))},isOpen:function(t){return wl.isOpen(Vx.getCoupled(t,"toolbarSandbox"))}}})},configFields:dT(),partFields:mT(),apis:{setGroups:function(t,n,e){t.setGroups(n,e)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},getToolbar:function(t,n){return t.getToolbar(n)},isOpen:function(t,n){return t.isOpen(n)}}}),bT=st([Ln("items"),fa(["itemSelector"]),jl("tgroupBehaviours",[ig])]),yT=st([wf({name:"items",unit:"item"})]),xT=Yf({name:"ToolbarGroup",configFields:bT(),partFields:yT(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,behaviours:Wl(t.tgroupBehaviours,[ig.config({mode:"flow",selector:t.markers.itemSelector})]),domModification:{attributes:{role:"toolbar"}}}}}),wT=function(t){return V(t,function(t){return gu(t)})},ST=function(t,e,o){cT(t,o,function(n){o.overflowGroups.set(n),e.getOpt(t).each(function(t){vT.setGroups(t,wT(n))})})},kT=Yf({name:"SplitFloatingToolbar",configFields:lT(),partFields:fT(),factory:function(e,t,n,o){var r=mp(vT.sketch({fetch:function(){return Sx(function(t){t(wT(e.overflowGroups.get()))})},layouts:{onLtr:function(){return[Ha,Pa]},onRtl:function(){return[Pa,Ha]},onBottomLtr:function(){return[Na,za]},onBottomRtl:function(){return[za,Na]}},getBounds:n.getOverflowBounds,lazySink:e.lazySink,fireDismissalEventInstead:{},markers:{toggledClass:e.markers.overflowToggledClass},parts:{button:o["overflow-button"](),toolbar:o.overflow()}}));return{uid:e.uid,dom:e.dom,components:t,behaviours:Wl(e.splitToolbarBehaviours,[Vx.config({others:{overflowGroup:function(){return xT.sketch(ft(ft({},o["overflow-group"]()),{items:[r.asSpec()]}))}}})]),apis:{setGroups:function(t,n){e.builtGroups.set(V(n,t.getSystem().build)),ST(t,r,e)},refresh:function(t){return ST(t,r,e)},toggle:function(t){r.getOpt(t).each(function(t){vT.toggle(t)})},isOpen:function(t){return r.getOpt(t).map(vT.isOpen).getOr(!1)},reposition:function(t){r.getOpt(t).each(function(t){vT.reposition(t)})},getOverflow:function(t){return r.getOpt(t).bind(vT.getToolbar)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},reposition:function(t,n){t.reposition(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)},getOverflow:function(t,n){return t.getOverflow(n)}}}),CT=function(n,t){return t.getAnimationRoot.fold(function(){return n.element},function(t){return t(n)})},OT=function(t){return t.dimension.property},_T=function(t,n){return t.dimension.getDimension(n)},TT=function(t,n){var e=CT(t,n);Ni(e,[n.shrinkingClass,n.growingClass])},ET=function(t,n){Pi(t.element,n.openClass),Ri(t.element,n.closedClass),Yi(t.element,OT(n),"0px"),nu(t.element)},DT=function(t,n){Pi(t.element,n.closedClass),Ri(t.element,n.openClass),tu(t.element,OT(n))},BT=function(t,n,e,o){e.setCollapsed(),Yi(t.element,OT(n),_T(n,t.element)),nu(t.element),TT(t,n),ET(t,n),n.onStartShrink(t),n.onShrunk(t)},AT=function(t,n,e,o){var r=o.getOrThunk(function(){return _T(n,t.element)});e.setCollapsed(),Yi(t.element,OT(n),r),nu(t.element);var i=CT(t,n);Pi(i,n.growingClass),Ri(i,n.shrinkingClass),ET(t,n),n.onStartShrink(t)},MT=function(t,n,e){var o=_T(n,t.element);("0px"===o?BT:AT)(t,n,e,dt.some(o))},FT=function(t,n,e){var o=CT(t,n),r=Hi(o,n.shrinkingClass),i=_T(n,t.element);DT(t,n);var u=_T(n,t.element);(r?function(){Yi(t.element,OT(n),i),nu(t.element)}:function(){ET(t,n)})(),Pi(o,n.shrinkingClass),Ri(o,n.growingClass),DT(t,n),Yi(t.element,OT(n),u),e.setExpanded(),n.onStartGrow(t)},IT=function(t,n,e){var o=CT(t,n);return!0===Hi(o,n.growingClass)},RT=function(t,n,e){var o=CT(t,n);return!0===Hi(o,n.shrinkingClass)},VT=/* */Object.freeze({__proto__:null,refresh:function(t,n,e){var o;e.isExpanded()&&(tu(t.element,OT(n)),o=_T(n,t.element),Yi(t.element,OT(n),o))},grow:function(t,n,e){e.isExpanded()||FT(t,n,e)},shrink:function(t,n,e){e.isExpanded()&&MT(t,n,e)},immediateShrink:function(t,n,e){e.isExpanded()&&BT(t,n,e,dt.none())},hasGrown:function(t,n,e){return e.isExpanded()},hasShrunk:function(t,n,e){return e.isCollapsed()},isGrowing:IT,isShrinking:RT,isTransitioning:function(t,n,e){return IT(t,n)||RT(t,n)},toggleGrow:function(t,n,e){(e.isExpanded()?MT:FT)(t,n,e)},disableTransitions:TT}),PT=/* */Object.freeze({__proto__:null,exhibit:function(t,n,e){var o=n.expanded;return Si(o?{classes:[n.openClass],styles:{}}:{classes:[n.closedClass],styles:Jt(n.dimension.property,"0px")})},events:function(e,o){return Zo([cr(bo(),function(t,n){n.event.raw.propertyName===e.dimension.property&&(TT(t,e),o.isExpanded()&&tu(t.element,e.dimension.property),(o.isExpanded()?e.onGrown:e.onShrunk)(t))})])}}),HT=[Ln("closedClass"),Ln("openClass"),Ln("shrinkingClass"),Ln("growingClass"),Kn("getAnimationRoot"),ma("onShrunk"),ma("onStartShrink"),ma("onGrown"),ma("onStartGrow"),ne("expanded",!1),jn("dimension",An("property",{width:[va("property","width"),va("getDimension",function(t){return Ou(t)+"px"})],height:[va("property","height"),va("getDimension",function(t){return vu(t)+"px"})]}))],zT=uc({fields:HT,name:"sliding",active:PT,apis:VT,state:/* */Object.freeze({__proto__:null,init:function(t){var n=fe(t.expanded);return xi({isExpanded:function(){return!0===n.get()},isCollapsed:function(){return!1===n.get()},setCollapsed:k(n.set,!1),setExpanded:k(n.set,!0),readState:function(){return"expanded: "+n.get()}})}})}),NT=st([fa(["closedClass","openClass","shrinkingClass","growingClass","overflowToggledClass"]),ma("onOpened"),ma("onClosed")].concat(sT())),LT=st([bf({factory:eT,schema:tT(),name:"primary"}),bf({factory:eT,schema:tT(),name:"overflow",overrides:function(n){return{toolbarBehaviours:rc([zT.config({dimension:{property:"height"},closedClass:n.markers.closedClass,openClass:n.markers.openClass,shrinkingClass:n.markers.shrinkingClass,growingClass:n.markers.growingClass,onShrunk:function(t){Mf(t,n,"overflow-button").each(function(t){Eg.off(t),hg.focus(t)}),n.onClosed(t)},onGrown:function(t){ig.focusIn(t),n.onOpened(t)},onStartGrow:function(t){Mf(t,n,"overflow-button").each(Eg.on)}}),ig.config({mode:"acyclic",onEscape:function(t){return Mf(t,n,"overflow-button").each(hg.focus),dt.some(!0)}})])}}}),yf({name:"overflow-button",overrides:function(t){return{buttonBehaviours:rc([Eg.config({toggleClass:t.markers.overflowToggledClass,aria:{mode:"pressed"},toggleOnExecute:!1})])}}}),yf({name:"overflow-group"})]),jT=function(n,e){Mf(n,e,"overflow-button").bind(function(){return Mf(n,e,"overflow")}).each(function(t){UT(n,e),zT.toggleGrow(t)})},UT=function(t,n){Mf(t,n,"overflow").each(function(e){cT(t,n,function(t){var n=V(t,function(t){return gu(t)});eT.setGroups(e,n)}),Mf(t,n,"overflow-button").each(function(t){zT.hasGrown(e)&&Eg.on(t)}),zT.refresh(e)})},WT=Yf({name:"SplitSlidingToolbar",configFields:NT(),partFields:LT(),factory:function(o,t,n,e){var r="alloy.toolbar.toggle";return{uid:o.uid,dom:o.dom,components:t,behaviours:Wl(o.splitToolbarBehaviours,[Vx.config({others:{overflowGroup:function(n){return xT.sketch(ft(ft({},e["overflow-group"]()),{items:[dp.sketch(ft(ft({},e["overflow-button"]()),{action:function(t){Yo(n,r)}}))]}))}}}),fg("toolbar-toggle-events",[er(r,function(t){jT(t,o)})])]),apis:{setGroups:function(t,n){var e;e=V(n,t.getSystem().build),o.builtGroups.set(e),UT(t,o)},refresh:function(t){return UT(t,o)},toggle:function(t){return jT(t,o)},isOpen:function(t){return Mf(t,o,"overflow").map(zT.hasGrown).getOr(!1)}},domModification:{attributes:{role:"group"}}}},apis:{setGroups:function(t,n,e){t.setGroups(n,e)},refresh:function(t,n){t.refresh(n)},toggle:function(t,n){t.toggle(n)},isOpen:function(t,n){return t.isOpen(n)}}}),GT=st(oi("toolbar-height-change")),XT=function(t){var n=t.title.fold(function(){return{}},function(t){return{attributes:{title:t}}});return{dom:ft({tag:"div",classes:["tox-toolbar__group"]},n),components:[xT.parts.items({})],items:t.items,markers:{itemSelector:"*:not(.tox-split-button) > .tox-tbtn:not([disabled]), .tox-split-button:not([disabled]), .tox-toolbar-nav-js:not([disabled])"},tgroupBehaviours:rc([Yy.config({}),hg.config({})])}},YT=function(t){return xT.sketch(XT(t))},qT=function(e,t){var n=sr(function(t){var n=V(e.initGroups,YT);eT.setGroups(t,n)});return rc([Wv(e.providers.isDisabled),Nv(),ig.config({mode:t,onEscape:e.onEscape,selector:".tox-toolbar__group"}),fg("toolbar-events",[n])])},KT=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return{uid:t.uid,dom:{tag:"div",classes:["tox-toolbar-overlord"]},parts:{"overflow-group":XT({title:dt.none(),items:[]}),"overflow-button":qC({name:"more",icon:dt.some("more-drawer"),disabled:!1,tooltip:dt.some("More..."),primary:!1,borderless:!1},dt.none(),t.providers)},splitToolbarBehaviours:qT(t,n)}},JT=function(i){var t=KT(i),n=kT.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}});return kT.sketch(ft(ft({},t),{lazySink:i.getSink,getOverflowBounds:function(){var t=i.moreDrawerData.lazyHeader().element,n=zu(t),e=wr(t),o=zu(e),r=Math.max(e.dom.scrollHeight,o.height);return Pu(n.x+4,o.y,n.width-8,r)},parts:ft(ft({},t.parts),{overflow:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:i.attributes}}}),components:[n],markers:{overflowToggledClass:"tox-tbtn--enabled"}}))},$T=function(t){var n=WT.parts.primary({dom:{tag:"div",classes:["tox-toolbar__primary"]}}),e=WT.parts.overflow({dom:{tag:"div",classes:["tox-toolbar__overflow"]}}),o=KT(t);return WT.sketch(ft(ft({},o),{components:[n,e],markers:{openClass:"tox-toolbar__overflow--open",closedClass:"tox-toolbar__overflow--closed",growingClass:"tox-toolbar__overflow--growing",shrinkingClass:"tox-toolbar__overflow--shrinking",overflowToggledClass:"tox-tbtn--enabled"},onOpened:function(t){t.getSystem().broadcastOn([GT()],{type:"opened"})},onClosed:function(t){t.getSystem().broadcastOn([GT()],{type:"closed"})}}))},QT=function(t){var n=t.cyclicKeying?"cyclic":"acyclic";return eT.sketch({uid:t.uid,dom:{tag:"div",classes:["tox-toolbar"].concat(t.type===oh.scrolling?["tox-toolbar--scrolling"]:[])},components:[eT.parts.groups({})],toolbarBehaviours:qT(t,n)})},ZT=fn([Un("type"),jn("items",mn([Sn([Un("name"),qn("items",Rn)]),Rn]))].concat(jp)),tE=[Qn("text"),Qn("tooltip"),Qn("icon"),Gn("fetch"),ae("onSetup",function(){return ct})],nE=fn(T([Un("type")],tE)),eE=function(t){return _n("menubutton",nE,t)},oE=fn([Un("type"),Qn("tooltip"),Qn("icon"),Qn("text"),Zn("select"),Gn("fetch"),ae("onSetup",function(){return ct}),ie("presets","normal",["normal","color","listpreview"]),ne("columns",1),Gn("onAction"),Gn("onItemAction")]),rE=/* */Object.freeze({__proto__:null,events:function(i,u){var r=function(o,r){i.updateState.each(function(t){var n=t(o,r);u.set(n)}),i.renderComponents.each(function(t){var n=t(r,u.get()),e=V(n,o.getSystem().build);ol(o,e)})};return Zo([er(Oo(),function(t,n){var e,o=n;o.universal||(e=i.channel,M(o.channels,e)&&r(t,o.data))}),sr(function(n,t){i.initialData.each(function(t){r(n,t)})})])}}),iE=/* */Object.freeze({__proto__:null,getState:function(t,n,e){return e}}),uE=[Ln("channel"),Kn("renderComponents"),Kn("updateState"),Kn("initialData")],aE=uc({fields:uE,name:"reflecting",active:rE,apis:iE,state:/* */Object.freeze({__proto__:null,init:function(){var n=fe(dt.none());return{readState:function(){return n.get().fold(function(){return"none"},function(t){return t})},get:function(){return n.get()},set:function(t){return n.set(t)},clear:function(){return n.set(dt.none())}}}})}),cE=st([Ln("toggleClass"),Ln("fetch"),pa("onExecute"),ne("getHotspot",dt.some),ne("getAnchorOverrides",st({})),qc(),pa("onItemExecute"),Kn("lazySink"),Ln("dom"),ma("onOpen"),jl("splitDropdownBehaviours",[Vx,ig,hg]),ne("matchWidth",!1),ne("useMinWidth",!1),ne("eventOrder",{}),Kn("role")].concat(tw())),sE=bf({factory:dp,schema:[Ln("dom")],name:"arrow",defaults:function(){return{buttonBehaviours:rc([hg.revoke()])}},overrides:function(n){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(t){t.getSystem().getByUid(n.uid).each(Ko)},buttonBehaviours:rc([Eg.config({toggleOnExecute:!1,toggleClass:n.toggleClass})])}}}),lE=bf({factory:dp,schema:[Ln("dom")],name:"button",defaults:function(){return{buttonBehaviours:rc([hg.revoke()])}},overrides:function(e){return{dom:{tag:"span",attributes:{role:"presentation"}},action:function(n){n.getSystem().getByUid(e.uid).each(function(t){e.onExecute(t,n)})}}}}),fE=st([sE,lE,xf({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"span",styles:{display:"none"},attributes:{"aria-hidden":"true"},innerHtml:t.text}}}},schema:[Ln("text")],name:"aria-descriptor"}),yf({schema:[la()],name:"menu",defaults:function(o){return{onExecute:function(n,e){n.getSystem().getByUid(o.uid).each(function(t){o.onItemExecute(t,n,e)})}}}}),Hx()]),dE=Yf({name:"SplitDropdown",configFields:cE(),partFields:fE(),factory:function(o,t,n,e){var r,i=function(t){Zf.getCurrent(t).each(function(t){ld.highlightFirst(t),ig.focusIn(t)})},u=function(t){qx(o,function(t){return t},t,e,i,px.HighlightFirst).get(ct)},a=function(t){var n=Ff(t,o,"button");return Ko(n),dt.some(!0)},c=ft(ft({},Zo([sr(function(e,t){Mf(e,o,"aria-descriptor").each(function(t){var n=oi("aria");Yr(t.element,"id",n),Yr(e.element,"aria-describedby",n)})})])),Bg(dt.some(u))),s={repositionMenus:function(t){Eg.isOn(t)&&Zx(t)}};return{uid:o.uid,dom:o.dom,components:t,apis:s,eventOrder:ft(ft({},o.eventOrder),((r={})[_o()]=["disabling","toggling","alloy.base.behaviour"],r)),events:c,behaviours:Wl(o.splitDropdownBehaviours,[Vx.config({others:{sandbox:function(t){var n=Ff(t,o,"arrow");return Qx(o,t,{onOpen:function(){Eg.on(n),Eg.on(t)},onClose:function(){Eg.off(n),Eg.off(t)}})}}}),ig.config({mode:"special",onSpace:a,onEnter:a,onDown:function(t){return u(t),dt.some(!0)}}),hg.config({}),Eg.config({toggleOnExecute:!1,aria:{mode:"expanded"}})]),domModification:{attributes:{role:o.role.getOr("button"),"aria-haspopup":!0}}}},apis:{repositionMenus:function(t,n){return t.repositionMenus(n)}}}),mE=function(n){return{isDisabled:function(){return mv.isDisabled(n)},setDisabled:function(t){return mv.set(n,t)}}},gE=function(n){return{setActive:function(t){Eg.set(n,t)},isActive:function(){return Eg.isOn(n)},isDisabled:function(){return mv.isDisabled(n)},setDisabled:function(t){return mv.set(n,t)}}},pE=function(t,n){return t.map(function(t){return{"aria-label":n.translate(t),title:n.translate(t)}}).getOr({})},hE=oi("focus-button"),vE=["checklist","ordered-list"],bE=["indent","outdent","table-insert-column-after","table-insert-column-before","unordered-list"],yE=function(n,e,t,o,r,i){var u,a=function(t){return Zh.isRtl()&&M(vE,t)?t+"-rtl":t},c=Zh.isRtl()&&n.exists(function(t){return M(bE,t)});return{dom:{tag:"button",classes:["tox-tbtn"].concat(e.isSome()?["tox-tbtn--select"]:[]).concat(c?["tox-tbtn__icon-rtl"]:[]),attributes:pE(t,i)},components:Kv([n.map(function(t){return BC(a(t),i.icons)}),e.map(function(t){return MC(t,"tox-tbtn",i)})]),eventOrder:((u={})[io()]=["focusing","alloy.base.behaviour","common-button-display-events"],u),buttonBehaviours:rc([Wv(i.isDisabled),Nv(),fg("common-button-display-events",[er(io(),function(t,n){n.event.prevent(),Yo(t,hE)})])].concat(o.map(function(t){return aE.config({channel:t,initialData:{icon:n,text:e},renderComponents:function(t,n){return Kv([t.icon.map(function(t){return BC(a(t),i.icons)}),t.text.map(function(t){return MC(t,"tox-tbtn",i)})])}})}).toArray()).concat(r.getOr([])))}},xE=function(t,n,e){var o,r=fe(ct),i=yE(t.icon,t.text,t.tooltip,dt.none(),dt.none(),e);return dp.sketch({dom:i.dom,components:i.components,eventOrder:IC,buttonBehaviours:rc([fg("toolbar-button-events",[(o={onAction:t.onAction,getApi:n.getApi},dr(function(n,t){Gv(o,n)(function(t){qo(n,FC,{buttonApi:t}),o.onAction(t)})})),Xv(n,r),Yv(n,r)]),Wv(function(){return t.disabled||e.isDisabled()}),Nv()].concat(n.toolbarButtonBehaviours))})},wE=function(t,n,e){return xE(t,{toolbarButtonBehaviours:[].concat(0<e.length?[fg("toolbarButtonWith",e)]:[]),getApi:mE,onSetup:t.onSetup},n)},SE=function(t,n,e){return zt(xE(t,{toolbarButtonBehaviours:[lg.config({}),Eg.config({toggleClass:"tox-tbtn--enabled",aria:{mode:"pressed"},toggleOnExecute:!1})].concat(0<e.length?[fg("toolbarToggleButtonWith",e)]:[]),getApi:gE,onSetup:t.onSetup},n))},kE=function(n,t){var e,o,r,i,u=oi("channel-update-split-dropdown-display"),a=function(e){return{isDisabled:function(){return mv.isDisabled(e)},setDisabled:function(t){return mv.set(e,t)},setIconFill:function(t,n){qu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Yr(t,"fill",n)})},setIconStroke:function(t,n){qu(e.element,'svg path[id="'+t+'"], rect[id="'+t+'"]').each(function(t){Yr(t,"stroke",n)})},setActive:function(n){Yr(e.element,"aria-pressed",n),qu(e.element,"span").each(function(t){e.getSystem().getByDom(t).each(function(t){return Eg.set(t,n)})})},isActive:function(){return qu(e.element,"span").exists(function(t){return e.getSystem().getByDom(t).exists(Eg.isOn)})}}},c=fe(ct),s={getApi:a,onSetup:n.onSetup};return dE.sketch({dom:{tag:"div",classes:["tox-split-button"],attributes:ft({"aria-pressed":!1},pE(n.tooltip,t.providers))},onExecute:function(t){n.onAction(a(t))},onItemExecute:function(t,n,e){},splitDropdownBehaviours:rc([Uv(t.providers.isDisabled),Nv(),fg("split-dropdown-events",[er(hE,hg.focus),Xv(s,c),Yv(s,c)]),rw.config({})]),eventOrder:((e={})[Po()]=["alloy.base.behaviour","split-dropdown-events"],e),toggleClass:"tox-tbtn--enabled",lazySink:t.getSink,fetch:(o=a,r=n,i=t.providers,function(n){return Sx(function(t){return r.fetch(t)}).map(function(t){return dt.from(yy(zt(Qb(oi("menu-value"),t,function(t){r.onItemAction(o(n),t)},r.columns,r.presets,ih.CLOSE_ON_EXECUTE,r.select.getOr(l),i),{movement:ty(r.columns,r.presets),menuBehaviours:Uh("auto"!==r.columns?[]:[sr(function(o,t){Lh(o,4,hh(r.presets)).each(function(t){var n=t.numRows,e=t.numColumns;ig.setGridSize(o,n,e)})})])})))})}),parts:{menu:xh(0,n.columns,n.presets)},components:[dE.parts.button(yE(n.icon,n.text,dt.none(),dt.some(u),dt.some([Eg.config({toggleClass:"tox-tbtn--enabled",toggleOnExecute:!1})]),t.providers)),dE.parts.arrow({dom:{tag:"button",classes:["tox-tbtn","tox-split-button__chevron"],innerHtml:pp("chevron-down",t.providers.icons)},buttonBehaviours:rc([Uv(t.providers.isDisabled),Nv()])}),dE.parts["aria-descriptor"]({text:t.providers.translate("To open the popup, press Shift+Enter")})]})},CE=function(i,u){return er(FC,function(t,n){var e,o=i.get(t),r=(e=o,{hide:function(){return Yo(e,Bo())},getValue:function(){return Ll.getValue(e)}});u.onAction(r,n.event.buttonApi)})},OE=function(t,n,e){var o,r,i,u,a,c,s,l,f,d,m,g,p={backstage:{shared:{providers:e}}};return"contextformtogglebutton"===n.type?(s=t,f=p,(d=(l=n).original).primary,m=_(d,["primary"]),g=Tn(Yp(ft(ft({},m),{type:"togglebutton",onAction:ct}))),SE(g,f.backstage.shared.providers,[CE(s,l)])):(o=t,i=p,(u=(r=n).original).primary,a=_(u,["primary"]),c=Tn(Wp(ft(ft({},a),{type:"button",onAction:ct}))),wE(c,i.backstage.shared.providers,[CE(o,r)]))},_E=function(t,n){var e,o,r,i,u=t.label.fold(function(){return{}},function(t){return{"aria-label":t}}),a=mp(dx.sketch({inputClasses:["tox-toolbar-textfield","tox-toolbar-nav-js"],data:t.initValue(),inputAttributes:u,selectOnFocus:!0,inputBehaviours:rc([ig.config({mode:"special",onEnter:function(t){return c.findPrimary(t).map(function(t){return Ko(t),!0})},onLeft:function(t,n){return n.cut(),dt.none()},onRight:function(t,n){return n.cut(),dt.none()}})])})),c=(e=a,o=t.commands,r=n,i=V(o,function(t){return mp(OE(e,t,r))}),{asSpecs:function(){return V(i,function(t){return t.asSpec()})},findPrimary:function(e){return Q(o,function(t,n){return t.primary?dt.from(i[n]).bind(function(t){return t.getOpt(e)}).filter(C(mv.isDisabled)):dt.none()})}});return[{title:dt.none(),items:[a.asSpec()]},{title:dt.none(),items:c.asSpecs()}]},TE=_E,EE=function(t,n){var e,o,r,i,u,a=Fu(window),c=Hu(me.fromDom(t.getContentAreaContainer())),s=wv(t)||Sv(t)||Cv(t),l=(e=c,o=a,r=Math.max(o.x,e.x),i=e.right-r,u=o.width-(r-o.x),{x:r,width:Math.min(i,u)}),f=l.x,d=l.width;if(t.inline&&!s)return Pu(f,a.y,d,a.height);var m=function(t,n,e,o){var r=me.fromDom(t.getContainer()),i=qu(r,".tox-editor-header").getOr(r),u=Hu(i),a=u.y>=n.bottom,c=o&&!a;if(t.inline&&c)return{y:Math.max(u.bottom,e.y),bottom:e.bottom};if(t.inline&&!c)return{y:e.y,bottom:Math.min(u.y,e.bottom)};var s=Hu(r);return c?{y:Math.max(u.bottom,e.y),bottom:Math.min(s.bottom,e.bottom)}:{y:Math.max(s.y,e.y),bottom:Math.min(u.y,e.bottom)}}(t,c,a,n.header.isPositionedAtTop()),g=m.y,p=m.bottom;return Pu(f,g,d,p-g)},DE=function(n,t){var e=H(t,function(t){return t.predicate(n.dom)}),o=P(e,function(t){return"contexttoolbar"===t.type});return{contextToolbars:o.pass,contextForms:o.fail}},BE=function(t,n,e){var o=DE(t,n);if(0<o.contextForms.length)return dt.some({elem:t,toolbars:[o.contextForms[0]]});var r=DE(t,e);if(0<r.contextForms.length)return dt.some({elem:t,toolbars:[r.contextForms[0]]});if(0<o.contextToolbars.length||0<r.contextToolbars.length){var i=function(t){if(t.length<=1)return t;var n=function(n){return F(t,function(t){return t.position===n})},e=function(n){return H(t,function(t){return t.position===n})},o=n("selection"),r=n("node");if(o||r){if(r&&o){var i=e("node"),u=V(e("selection"),function(t){return ft(ft({},t),{position:"node"})});return i.concat(u)}return e(o?"selection":"node")}return e("line")}(o.contextToolbars.concat(r.contextToolbars));return dt.some({elem:t,toolbars:i})}return dt.none()},AE=function(t,n,i){return t(n)?dt.none():Ke(n,function(t){var n=DE(t,i.inNodeScope),e=n.contextToolbars,o=n.contextForms,r=0<o.length?o:function(t){if(t.length<=1)return t;var n=function(n){return L(t,function(t){return t.position===n})};return n("selection").orThunk(function(){return n("node")}).orThunk(function(){return n("line")}).map(function(t){return t.position}).fold(function(){return[]},function(n){return H(t,function(t){return t.position===n})})}(e);return 0<r.length?dt.some({elem:t,toolbars:r}):dt.none()},t)},ME=function(e,r){var t={},i=[],u=[],a={},c={},o=function(n,e){var o=Tn(_n("ContextForm",th,e));(t[n]=o).launch.map(function(t){a["form:"+n]=ft(ft({},e.launch),{type:"contextformtogglebutton"===t.type?"togglebutton":"button",onAction:function(){r(o)}})}),"editor"===o.scope?u.push(o):i.push(o),c[n]=o},s=function(n,e){_n("ContextToolbar",nh,e).each(function(t){"editor"===e.scope?u.push(t):i.push(t),c[n]=t})},n=Ct(e);return mt(n,function(t){var n=e[t];"contextform"===n.type?o(t,n):"contexttoolbar"===n.type&&s(t,n)}),{forms:t,inNodeScope:i,inEditorScope:u,lookupTable:c,formNavigators:a}},FE=oi("forward-slide"),IE=oi("backward-slide"),RE=oi("change-slide-event"),VE="tox-pop--resizing",PE=function(t,n,e){var u,a,r,c,s,o=e.dataset,i="basic"===o.type?function(){return V(o.data,function(t){return w_(t,e.isSelectedFor,e.getPreviewFor)})}:o.getData;return{items:(u=n,a=e,r=function(t,n,e,o){var r=u.shared.providers.translate(t.title);if("separator"===t.type)return dt.some({type:"separator",text:r});if("submenu"!==t.type)return dt.some(ft({type:"togglemenuitem",text:r,icon:t.icon,active:t.isSelected(o),disabled:e,onAction:a.onAction(t)},t.getStylePreview().fold(function(){return{}},function(t){return{meta:{style:t}}})));var i=U(t.getStyleItems(),function(t){return c(t,n,o)});return 0===n&&i.length<=0?dt.none():dt.some({type:"nestedmenuitem",text:r,disabled:i.length<=0,getSubmenuItems:function(){return U(t.getStyleItems(),function(t){return c(t,n,o)})}})},c=function(t,n,e){var o="formatter"===t.type&&a.isInvalid(t);return 0===n?o?[]:r(t,n,!1,e).toArray():r(t,n,o,e).toArray()},{validateItems:s=function(t){var n=a.getCurrentValue(),e=a.shouldHide?0:1;return U(t,function(t){return c(t,e,n)})},getFetch:function(r,i){return function(t,n){var e=i(),o=s(e);n(UC(o,ih.CLOSE_ON_EXECUTE,r,!1))}}}),getStyleItems:i}},HE=function(o,t,n){var e=PE(0,t,n),r=e.items,i=e.getStyleItems;return PC({text:n.icon.isSome()?dt.none():dt.some(""),icon:n.icon,tooltip:dt.from(n.tooltip),role:dt.none(),fetch:r.getFetch(t,i),onSetup:function(e){return n.setInitialValue.each(function(t){return t(e.getComponent())}),n.nodeChangeHandler.map(function(t){var n=t(e.getComponent());return o.on("NodeChange",n),function(){o.off("NodeChange",n)}}).getOr(ct)},getApi:function(t){return{getComponent:function(){return t}}},columns:1,presets:"normal",classes:n.icon.isSome()?[]:["bespoke"],dropdownBehaviours:[]},"tox-tbtn",t.shared)};(DO=EO=EO||{})[DO.SemiColon=0]="SemiColon",DO[DO.Space=1]="Space";var zE,NE,LE,jE,UE,WE,GE=function(t,n,e,o){var r,i,u=t.getParam(n,e,"string");return{type:"basic",data:(i=u,r=o===EO.SemiColon?i.replace(/;$/,"").split(";"):i.split(" "),V(r,function(t){var n=t,e=t,o=t.split("=");return 1<o.length&&(n=o[0],e=o[1]),{title:n,format:e}}))}},XE=[{title:"Left",icon:"align-left",format:"alignleft",command:"JustifyLeft"},{title:"Center",icon:"align-center",format:"aligncenter",command:"JustifyCenter"},{title:"Right",icon:"align-right",format:"alignright",command:"JustifyRight"},{title:"Justify",icon:"align-justify",format:"alignjustify",command:"JustifyFull"}],YE=function(e){var n=function(t){var n=L(XE,function(t){return e.formatter.match(t.format)}).fold(function(){return"left"},function(t){return t.title.toLowerCase()});qo(t,VC,{icon:"align-"+n})},t=dt.some(function(t){return function(){return n(t)}}),o=dt.some(n),r={type:"basic",data:XE};return{tooltip:"Align",icon:dt.some("align-left"),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:dt.none,getPreviewFor:function(t){return function(){return dt.none()}},onAction:function(n){return function(){return L(XE,function(t){return t.format===n.format}).each(function(t){return e.execCommand(t.command)})}},setInitialValue:o,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}},qE=["-apple-system","Segoe UI","Roboto","Helvetica Neue","sans-serif"],KE=function(t){var n=t.split(/\s*,\s*/);return V(n,function(t){return t.replace(/^['"]+|['"]+$/g,"")})},JE=function(t){var n;return 0===t.indexOf("-apple-system")&&(n=KE(t.toLowerCase()),W(qE,function(t){return-1<n.indexOf(t.toLowerCase())}))},$E=function(r){var i=function(){var e=function(t){return t?KE(t)[0]:""},t=r.queryCommandValue("FontName"),n=u.data,o=t?t.toLowerCase():"";return{matchOpt:L(n,function(t){var n=t.format;return n.toLowerCase()===o||e(n).toLowerCase()===e(o).toLowerCase()}).orThunk(function(){return JE(o)?dt.from({title:"System Font",format:o}):dt.none()}),font:t}},n=function(t){var n=i(),e=n.matchOpt,o=n.font,r=e.fold(function(){return o},function(t){return t.title});qo(t,RC,{text:r})},t=dt.some(function(t){return function(){return n(t)}}),e=dt.some(n),u=GE(r,"font_formats","Andale Mono=andale mono,monospace;Arial=arial,helvetica,sans-serif;Arial Black=arial black,sans-serif;Book Antiqua=book antiqua,palatino,serif;Comic Sans MS=comic sans ms,sans-serif;Courier New=courier new,courier,monospace;Georgia=georgia,palatino,serif;Helvetica=helvetica,arial,sans-serif;Impact=impact,sans-serif;Symbol=symbol;Tahoma=tahoma,arial,helvetica,sans-serif;Terminal=terminal,monaco,monospace;Times New Roman=times new roman,times,serif;Trebuchet MS=trebuchet ms,geneva,sans-serif;Verdana=verdana,geneva,sans-serif;Webdings=webdings;Wingdings=wingdings,zapf dingbats",EO.SemiColon);return{tooltip:"Fonts",icon:dt.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getCurrentValue:function(){return i().matchOpt},getPreviewFor:function(t){return function(){return dt.some({tag:"div",styles:-1===t.indexOf("dings")?{"font-family":t}:{}})}},onAction:function(t){return function(){r.undoManager.transact(function(){r.focus(),r.execCommand("FontName",!1,t.format)})}},setInitialValue:e,nodeChangeHandler:t,dataset:u,shouldHide:!1,isInvalid:l}},QE={"8pt":"1","10pt":"2","12pt":"3","14pt":"4","18pt":"5","24pt":"6","36pt":"7"},ZE={"xx-small":"7pt","x-small":"8pt",small:"10pt",medium:"12pt",large:"14pt","x-large":"18pt","xx-large":"24pt"},tD=function(t,n){return/[0-9.]+px$/.test(t)?(e=72*parseInt(t,10)/96,o=n||0,r=Math.pow(10,o),Math.round(e*r)/r+"pt"):Ft(ZE,t).getOr(t);var e,o,r},nD=function(e){var i=function(){var o=dt.none(),r=u.data,i=e.queryCommandValue("FontSize");if(i)for(var t=function(t){var n=tD(i,t),e=Ft(QE,n).getOr("");o=L(r,function(t){return t.format===i||t.format===n||t.format===e})},n=3;o.isNone()&&0<=n;n--)t(n);return{matchOpt:o,size:i}},t=st(dt.none),n=function(t){var n=i(),e=n.matchOpt,o=n.size,r=e.fold(function(){return o},function(t){return t.title});qo(t,RC,{text:r})},o=dt.some(function(t){return function(){return n(t)}}),r=dt.some(n),u=GE(e,"fontsize_formats","8pt 10pt 12pt 14pt 18pt 24pt 36pt",EO.Space);return{tooltip:"Font sizes",icon:dt.none(),isSelectedFor:function(n){return function(t){return t.exists(function(t){return t.format===n})}},getPreviewFor:t,getCurrentValue:function(){return i().matchOpt},onAction:function(t){return function(){e.undoManager.transact(function(){e.focus(),e.execCommand("FontSize",!1,t.format)})}},setInitialValue:r,nodeChangeHandler:o,dataset:u,shouldHide:!1,isInvalid:l}},eD=function(t,n){var e=n(),o=V(e,function(t){return t.format});return dt.from(t.formatter.closest(o)).bind(function(n){return L(e,function(t){return t.format===n})}).orThunk(function(){return Kf(t.formatter.match("p"),{title:"Paragraph",format:"p"})})},oD=function(t){var n=fe(dt.none()),e=function(){return n.get().each(t)};return{clear:function(){e(),n.set(dt.none())},isSet:function(){return n.get().isSome()},set:function(t){e(),n.set(dt.some(t))}}},rD=function(){return oD(function(t){return t.destroy()})},iD=function(){return oD(function(t){return t.unbind()})},uD=function(o,r){return function(n){var e=iD(),t=function(){n.setActive(o.formatter.match(r));var t=o.formatter.formatChanged(r,n.setActive);e.set(t)};return o.initialized?t():o.on("init",t),e.clear}},aD=function(n){return function(t){return function(){n.undoManager.transact(function(){n.focus(),n.execCommand("mceToggleFormat",!1,t.format)})}}},cD=function(e){var n=function(t){var n=eD(e,function(){return r.data}).fold(function(){return"Paragraph"},function(t){return t.title});qo(t,RC,{text:n})},t=dt.some(function(t){return function(){return n(t)}}),o=dt.some(n),r=GE(e,"block_formats","Paragraph=p;Heading 1=h1;Heading 2=h2;Heading 3=h3;Heading 4=h4;Heading 5=h5;Heading 6=h6;Preformatted=pre",EO.SemiColon);return{tooltip:"Blocks",icon:dt.none(),isSelectedFor:function(t){return function(){return e.formatter.match(t)}},getCurrentValue:dt.none,getPreviewFor:function(n){return function(){var t=e.formatter.get(n);return dt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:e.dom.parseStyle(e.formatter.getCssText(n))})}},onAction:aD(e),setInitialValue:o,nodeChangeHandler:t,dataset:r,shouldHide:!1,isInvalid:function(t){return!e.formatter.canApply(t.format)}}},sD=function(r,t){var n=function(t){var e=function(t){var n=t.items;return n!==undefined&&0<n.length?U(n,e):[{title:t.title,format:t.format}]},n=U(x_(r),e),o=eD(r,function(){return n}).fold(function(){return"Paragraph"},function(t){return t.title});qo(t,RC,{text:o})},e=dt.some(function(t){return function(){return n(t)}}),o=dt.some(n);return{tooltip:"Formats",icon:dt.none(),isSelectedFor:function(t){return function(){return r.formatter.match(t)}},getCurrentValue:dt.none,getPreviewFor:function(n){return function(){var t=r.formatter.get(n);return t!==undefined?dt.some({tag:0<t.length&&(t[0].inline||t[0].block)||"div",styles:r.dom.parseStyle(r.formatter.getCssText(n))}):dt.none()}},onAction:aD(r),setInitialValue:o,nodeChangeHandler:e,shouldHide:r.getParam("style_formats_autohide",!1,"boolean"),isInvalid:function(t){return!r.formatter.canApply(t.format)},dataset:t}},lD=[{name:"history",items:["undo","redo"]},{name:"styles",items:["styleselect"]},{name:"formatting",items:["bold","italic"]},{name:"alignment",items:["alignleft","aligncenter","alignright","alignjustify"]},{name:"indentation",items:["outdent","indent"]},{name:"permanent pen",items:["permanentpen"]},{name:"comments",items:["addcomment"]}],fD=function(r,i){return function(t,n,e){var o=r(t).mapError(function(t){return Dn(t)}).getOrDie();return i(o,n,e)}},dD={button:fD(Wp,function(t,n){return e=t,o=n.backstage.shared.providers,wE(e,o,[]);var e,o}),togglebutton:fD(Yp,function(t,n){return e=t,o=n.backstage.shared.providers,SE(e,o,[]);var e,o}),menubutton:fD(eE,function(t,n){return GC(t,"tox-tbtn",n.backstage,dt.none())}),splitbutton:fD(function(t){return _n("SplitButton",oE,t)},function(t,n){return kE(t,n.backstage.shared)}),grouptoolbarbutton:fD(function(t){return _n("GroupToolbarButton",ZT,t)},function(t,n,e){var o,r,i,u,a,c,s=e.ui.registry.getAll().buttons,l=((o={})[Xc]=n.backstage.shared.header.isPositionedAtTop()?ta.TopToBottom:ta.BottomToTop,o);switch(Tv(e)){case oh.floating:return r=t,i=n.backstage,u=function(t){return hD(e,{buttons:s,toolbar:t,allowToolbarGroups:!1},n,dt.none())},a=l,c=i.shared,vT.sketch({lazySink:c.getSink,fetch:function(){return Sx(function(t){t(V(u(r.items),YT))})},markers:{toggledClass:"tox-tbtn--enabled"},parts:{button:yE(r.icon,r.text,r.tooltip,dt.none(),dt.none(),c.providers),toolbar:{dom:{tag:"div",classes:["tox-toolbar__overflow"],attributes:a}}}});default:throw new Error("Toolbar groups are only supported when using floating toolbar mode")}}),styleSelectButton:function(t,n){return e=t,o=n.backstage,r=ft({type:"advanced"},o.styleselect),HE(e,o,sD(e,r));var e,o,r},fontsizeSelectButton:function(t,n){return e=t,o=n.backstage,HE(e,o,nD(e));var e,o},fontSelectButton:function(t,n){return e=t,o=n.backstage,HE(e,o,$E(e));var e,o},formatButton:function(t,n){return e=t,o=n.backstage,HE(e,o,cD(e));var e,o},alignMenuButton:function(t,n){return e=t,o=n.backstage,HE(e,o,YE(e));var e,o}},mD={styleselect:dD.styleSelectButton,fontsizeselect:dD.fontsizeSelectButton,fontselect:dD.fontSelectButton,formatselect:dD.formatButton,align:dD.alignMenuButton},gD=function(t){var n,e,o,r=t.toolbar,i=t.buttons;return!1===r?[]:r===undefined||!0===r?(e=i,o=V(lD,function(t){var n=H(t.items,function(t){return It(e,t)||It(mD,t)});return{name:t.name,items:n}}),H(o,function(t){return 0<t.items.length})):x(r)?(n=r.split("|"),V(n,function(t){return{items:t.trim().split(" ")}})):s(r,function(t){return It(t,"name")&&It(t,"items")})?r:(console.error("Toolbar type should be string, string[], boolean or ToolbarGroup[]"),[])},pD=function(r,n,i,u,a,t){return Ft(n,i.toLowerCase()).orThunk(function(){return t.bind(function(t){return Q(t,function(t){return Ft(n,t+i.toLowerCase())})})}).fold(function(){return Ft(mD,i.toLowerCase()).map(function(t){return t(r,a)}).orThunk(function(){return dt.none()})},function(t){return"grouptoolbarbutton"!==t.type||u?(e=a,o=r,Ft(dD,(n=t).type).fold(function(){return console.error("skipping button defined by",n),dt.none()},function(t){return dt.some(t(n,e,o))})):(console.warn("Ignoring the '"+i+"' toolbar button. Group toolbar buttons are only supported when using floating toolbar mode and cannot be nested."),dt.none());var n,e,o})},hD=function(e,o,r,i){var t=gD(o),n=V(t,function(t){var n=U(t.items,function(t){return 0===t.trim().length?[]:pD(e,o.buttons,t,o.allowToolbarGroups,r,i).toArray()});return{title:dt.from(e.translate(t.name)),items:n}});return H(n,function(t){return 0<t.items.length})},vD={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},bD={maxHeightFunction:Mc(),maxWidthFunction:Q_()},yD={onLtr:function(){return[La,ja,za,Pa,Na,Ha,sp,lp,ap,ip,cp,up]},onRtl:function(){return[La,ja,Na,Ha,za,Pa,sp,lp,cp,up,ap,ip]}},xD={onLtr:function(){return[ja,Pa,Ha,za,Na,La,sp,lp,ap,ip,cp,up]},onRtl:function(){return[ja,Ha,Pa,Na,za,La,sp,lp,cp,up,ap,ip]}},wD=function(c,t,e,s){var o,r,l=je().deviceType.isTouch,u=mu((o={sink:e,onEscape:function(){return c.focus(),dt.some(!0)}},r=fe([]),Zg.sketch({dom:{tag:"div",classes:["tox-pop"]},fireDismissalEventInstead:{event:"doNotDismissYet"},onShow:function(t){r.set([]),Zg.getContent(t).each(function(t){tu(t.element,"visibility")}),Pi(t.element,VE),tu(t.element,"width")},inlineBehaviours:rc([fg("context-toolbar-events",[cr(bo(),function(t,n){Pi(t.element,VE),tu(t.element,"width")}),er(RE,function(t,n){var e=t.element;tu(e,"width");var o=Ou(e);Zg.setContent(t,n.event.contents),Ri(e,VE);var r=Ou(e);Yi(e,"width",o+"px"),Zg.getContent(t).each(function(t){n.event.focus.bind(function(t){return dc(t),gc(e)}).orThunk(function(){return ig.focusIn(t),mc(Ar(e))})}),fp.setTimeout(function(){Yi(t.element,"width",r+"px")},0)}),er(FE,function(n,t){Zg.getContent(n).each(function(t){r.set(r.get().concat([{bar:t,focus:mc(Ar(n.element))}]))}),qo(n,RE,{contents:t.event.forwardContents,focus:dt.none()})}),er(IE,function(n,t){J(r.get()).each(function(t){r.set(r.get().slice(0,r.get().length-1)),qo(n,RE,{contents:gu(t.bar),focus:t.focus})})})]),ig.config({mode:"special",onEscape:function(n){return J(r.get()).fold(function(){return o.onEscape()},function(t){return Yo(n,IE),dt.some(!0)})}})]),lazySink:function(){return pt.value(o.sink)}}))),f=function(){return EE(c,s.backstage.shared)},a=function(){if(l()&&s.backstage.isContextMenuOpen())return!0;var t,n,e,o,r,i,u=(t=g.get().filter(function(t){return ji(me.fromDom(t))}).map(function(t){return t.getBoundingClientRect()}).getOrThunk(function(){return c.selection.getRng().getBoundingClientRect()}),n=c.inline?Bu().top:zu(me.fromDom(c.getBody())).y,{y:t.top+n,bottom:t.bottom+n}),a=f();return e=u.y,o=u.bottom,r=a.y,i=a.bottom,!(Math.max(e,r)<=Math.min(o,i))},d=function(){m.set(dt.none()),Zg.hide(u)},n=function(){Zg.hide(u)},i=function(){m.get().each(function(t){var n=u.element;tu(n,"display"),a()?Yi(n,"display","none"):Zs.positionWithinBounds(e,t,u,dt.some(f()))})},m=fe(dt.none()),g=fe(dt.none()),p=fe(null),h=function(t){return{dom:{tag:"div",classes:["tox-pop__dialog"]},components:[t],behaviours:rc([ig.config({mode:"acyclic"}),fg("pop-dialog-wrap-events",[sr(function(t){c.shortcuts.add("ctrl+F9","focus statusbar",function(){return ig.focusIn(t)})}),lr(function(t){c.shortcuts.remove("ctrl+F9")})])])}},v=Lt(function(){return ME(t,function(t){var n=b([t]);qo(u,FE,{forwardContents:h(n)})})}),b=function(t){var n=c.ui.registry.getAll().buttons,e=v(),o=ft(ft({},n),e.formNavigators),r=Tv(c)===oh.scrolling?oh.scrolling:oh["default"],i=gt(V(t,function(t){return"contexttoolbar"===t.type?hD(c,{buttons:o,toolbar:t.items,allowToolbarGroups:!1},s,dt.some(["form:"])):(n=t,e=s.backstage.shared.providers,TE(n,e));var n,e}));return QT({type:r,uid:oi("context-toolbar"),initGroups:i,onEscape:dt.none,cyclicKeying:!0,providers:s.backstage.shared.providers})};c.on("contexttoolbar-show",function(n){var t=v();Ft(t.lookupTable,n.toolbarKey).each(function(t){x([t],n.target===c?dt.none():dt.some(n)),Zg.getContent(u).each(ig.focusIn)})});var y=function(t,n){var e,o,r="node"===t?s.backstage.shared.anchors.node(n):s.backstage.shared.anchors.cursor();return zt(r,(e=t,o=l(),"line"===e?{bubble:Pc(12,0,vD),layouts:{onLtr:function(){return[Ua]},onRtl:function(){return[Wa]}},overrides:bD}:{bubble:Pc(0,12,vD),layouts:o?xD:yD,overrides:bD}))},x=function(t,n){var e,o,r,i;S(),l()&&s.backstage.isContextMenuOpen()||(e=b(t),o=n.map(me.fromDom),r=y(t[0].position,o),m.set(dt.some(r)),g.set(n),i=u.element,tu(i,"display"),Zg.showWithinBounds(u,r,h(e),function(){return dt.some(f())}),a()&&Yi(i,"display","none"))},w=function(){var t,n,e,o,r,i,u;c.hasFocus()&&(t=v(),n=t,e=c,r=me.fromDom(e.getBody()),i=function(t){return Ge(t,r)},u=me.fromDom(e.selection.getNode()),(i(o=u)||Ye(r,o)?BE(u,n.inNodeScope,n.inEditorScope).orThunk(function(){return AE(i,u,n)}):dt.none()).fold(d,function(t){x(t.toolbars,dt.some(t.elem.dom))}))},S=function(){var t=p.get();null!==t&&(fp.clearTimeout(t),p.set(null))},k=function(){S(),p.set(fp.setEditorTimeout(c,w,0))};c.on("init",function(){c.on(Z_,n),c.on("ScrollContent ScrollWindow longpress",i),c.on("click keyup focus SetContent ObjectResized ResizeEditor",function(){k()}),c.on("focusout",function(t){fp.setEditorTimeout(c,function(){gc(e.element).isNone()&&gc(u.element).isNone()&&d()},0)}),c.on("SwitchMode",function(){c.mode.isReadOnly()&&d()}),c.on("AfterProgressState",function(t){t.state?d():c.hasFocus()&&k()}),c.on("NodeChange",function(t){gc(u.element).fold(k,ct)})})},SD=Nf,kD=kf,CD=st([ne("shell",!1),Ln("makeItem"),ne("setupItem",ct),Gl("listBehaviours",[lg])]),OD=xf({name:"items",overrides:function(){return{behaviours:rc([lg.config({})])}}}),_D=st([OD]),TD=Yf({name:st("CustomList")(),configFields:CD(),partFields:_D(),factory:function(s,t,n,e){var o=s.shell?{behaviours:[lg.config({})],components:[]}:{behaviours:[],components:t},r=function(t){return s.shell?dt.some(t):Mf(t,s,"items")};return{uid:s.uid,dom:s.dom,components:o.components,behaviours:Wl(s.listBehaviours,o.behaviours),apis:{setItems:function(a,c){r(a).fold(function(){throw console.error("Custom List was defined to not be a shell, but no item container was specified in components"),new Error("Custom List was defined to not be a shell, but no item container was specified in components")},function(n){var t=lg.contents(n),e=c.length,o=e-t.length,r=0<o?I(o,function(){return s.makeItem()}):[],i=t.slice(e);mt(i,function(t){return lg.remove(n,t)}),mt(r,function(t){return lg.append(n,t)});var u=lg.contents(n);mt(u,function(t,n){s.setupItem(a,t,c[n],n)})})}}}},apis:{setItems:function(t,n,e){t.setItems(n,e)}}}),ED=st([]),DD=/* */Object.freeze({__proto__:null,setup:ct,isDocked:l,getBehaviours:ED}),BD=function(t){return(Qi(t,"position").is("fixed")?dt.none():Or(t)).orThunk(function(){var e=me.fromTag("span");return kr(t).bind(function(t){zr(t,e);var n=Or(e);return jr(e),n})})},AD=function(t){return BD(t).map(Su).getOrThunk(function(){return xu(0,0)})},MD=Vt([{"static":[]},{absolute:["positionCss"]},{fixed:["positionCss"]}]),FD=function(t,n){var e=t.element;Ri(e,n.transitionClass),Pi(e,n.fadeOutClass),Ri(e,n.fadeInClass),n.onShow(t)},ID=function(t,n){var e=t.element;Ri(e,n.transitionClass),Pi(e,n.fadeInClass),Ri(e,n.fadeOutClass),n.onHide(t)},RD=function(t,o,r){return W(t,function(t){switch(t){case"bottom":return e=r,o.bottom<=e.bottom;case"top":return n=r,o.y>=n.y}var n,e})},VD=function(n,t){return t.getInitialPosition().map(function(t){return Pu(t.bounds.x,t.bounds.y,Ou(n),vu(n))})},PD=function(t,n,e){e.setInitialPosition(dt.some({style:function(t){var n={},e=t.dom;if(Li(e))for(var o=0;o<e.style.length;o++){var r=e.style.item(o);n[r]=e.style[r]}return n}(t),position:Ji(t,"position")||"static",bounds:n}))},HD=function(e,o,r){return r.getInitialPosition().bind(function(t){switch(r.setInitialPosition(dt.none()),t.position){case"static":return dt.some(MD["static"]());case"absolute":var n=BD(e).map(Hu).getOrThunk(function(){return Hu(Ui())});return dt.some(MD.absolute(hc("absolute",Ft(t.style,"left").map(function(t){return o.x-n.x}),Ft(t.style,"top").map(function(t){return o.y-n.y}),Ft(t.style,"right").map(function(t){return n.right-o.right}),Ft(t.style,"bottom").map(function(t){return n.bottom-o.bottom}))));default:return dt.none()}})},zD=function(t,n,e){var o,r,i,u=t.element;return Qi(u,"position").is("fixed")?(r=n,VD(o=u,i=e).filter(function(t){return RD(i.getModes(),t,r)}).bind(function(t){return HD(o,t,i)})):function(t,n,e){var o=Hu(t);if(RD(e.getModes(),o,n))return dt.none();PD(t,o,e);var r=Nu(),i=o.x-r.x,u=n.y-r.y,a=r.bottom-n.bottom,c=o.y<=n.y;return dt.some(MD.fixed(hc("fixed",dt.some(i),c?dt.some(u):dt.none(),dt.none(),c?dt.none():dt.some(a))))}(u,n,e)},ND=function(n,t){mt(["left","right","top","bottom","position"],function(t){return tu(n.element,t)}),t.onUndocked(n)},LD=function(t,n,e){vc(t.element,e),("fixed"===e.position?n.onDocked:n.onUndocked)(t)},jD=function(i,t,u,a,c){void 0===c&&(c=!1),t.contextual.each(function(r){r.lazyContext(i).each(function(t){var n,e,o=(e=a,(n=t).y<e.bottom&&n.bottom>e.y);o!==u.isVisible()&&(u.setVisible(o),c&&!o?(zi(i.element,[r.fadeOutClass]),r.onHide(i)):(o?FD:ID)(i,r))})})},UD=function(n,e,t){var o,r,i=n.element;t.setDocked(!1),o=t,r=n.element,VD(r,o).bind(function(t){return HD(r,t,o)}).each(function(t){t.fold(function(){return ND(n,e)},function(t){return LD(n,e,t)},ct)}),t.setVisible(!0),e.contextual.each(function(t){Ni(i,[t.fadeInClass,t.fadeOutClass,t.transitionClass]),t.onShow(n)}),WD(n,e,t)},WD=function(t,n,e){var o,r,i,u,a;t.getSystem().isConnected()&&(o=t,i=e,u=(r=n).lazyViewport(o),(a=i.isDocked())&&jD(o,r,i,u),zD(o,u,i).each(function(t){i.setDocked(!a),t.fold(function(){return ND(o,r)},function(t){return LD(o,r,t)},function(t){jD(o,r,i,u,!0),LD(o,r,t)})}))},GD=function(t,n,e){e.isDocked()&&UD(t,n,e)},XD=/* */Object.freeze({__proto__:null,refresh:WD,reset:GD,isDocked:function(t,n,e){return e.isDocked()},getModes:function(t,n,e){return e.getModes()},setModes:function(t,n,e,o){return e.setModes(o)}}),YD=/* */Object.freeze({__proto__:null,events:function(o,r){return Zo([cr(bo(),function(n,e){o.contextual.each(function(t){Hi(n.element,t.transitionClass)&&(Ni(n.element,[t.transitionClass,t.fadeInClass]),(r.isVisible()?t.onShown:t.onHidden)(n)),e.stop()})}),er(Ro(),function(t,n){WD(t,o,r)}),er(Vo(),function(t,n){GD(t,o,r)})])}}),qD=[te("contextual",[Un("fadeInClass"),Un("fadeOutClass"),Un("transitionClass"),Gn("lazyContext"),ma("onShow"),ma("onShown"),ma("onHide"),ma("onHidden")]),ae("lazyViewport",Nu),ce("modes",["top","bottom"],Rn),ma("onDocked"),ma("onUndocked")],KD=uc({fields:qD,name:"docking",active:YD,apis:XD,state:/* */Object.freeze({__proto__:null,init:function(t){var n=fe(!1),e=fe(!0),o=fe(dt.none()),r=fe(t.modes);return xi({isDocked:n.get,setDocked:n.set,getInitialPosition:o.get,setInitialPosition:o.set,isVisible:e.get,setVisible:e.set,getModes:r.get,setModes:r.set,readState:function(){return"docked:  "+n.get()+", visible: "+e.get()+", modes: "+r.get().join(",")}})}})}),JD={fadeInClass:"tox-editor-dock-fadein",fadeOutClass:"tox-editor-dock-fadeout",transitionClass:"tox-editor-dock-transition"},$D="tox-tinymce--toolbar-sticky-on",QD="tox-tinymce--toolbar-sticky-off",ZD=function(r){var i=r.element;kr(i).each(function(t){var n,e,o="padding-"+KD.getModes(r)[0];KD.isDocked(r)?(n=Ou(t),Yi(i,"width",n+"px"),Yi(t,o,bu(e=i)+(parseInt(Ji(e,"margin-top"),10)||0)+(parseInt(Ji(e,"margin-bottom"),10)||0)+"px")):(tu(i,"width"),tu(t,o))})},tB=function(t,n){n?(Pi(t,JD.fadeOutClass),zi(t,[JD.transitionClass,JD.fadeInClass])):(Pi(t,JD.fadeInClass),zi(t,[JD.fadeOutClass,JD.transitionClass]))},nB=function(t,n){var e=me.fromDom(t.getContainer());n?(Ri(e,$D),Pi(e,QD)):(Ri(e,QD),Pi(e,$D))},eB=function(c,t){var n,i=fe(dt.none()),o=t.getSink,u=function(n){o().each(function(t){return n(t.element)})},e=function(t){c.inline||ZD(t),nB(c,KD.isDocked(t)),t.getSystem().broadcastOn([kl()],{}),o().each(function(t){return t.getSystem().broadcastOn([kl()],{})})},r=c.inline?[]:[lc.config({channels:((n={})[GT()]={onReceive:ZD},n)})];return T([hg.config({}),KD.config({contextual:ft({lazyContext:function(t){var n,e,o=bu(t.element),r=c.inline?c.getContentAreaContainer():c.getContainer(),i=Hu(me.fromDom(r)),u=i.height-o,a=i.y+(n=t,e="top",M(KD.getModes(n),e)?0:o);return dt.some(Pu(i.x,a,i.width,u))},onShow:function(){u(function(t){return tB(t,!0)})},onShown:function(r){u(function(t){return Ni(t,[JD.transitionClass,JD.fadeInClass])}),i.get().each(function(t){var n,e,o;n=r.element,o=yr(e=t),mc(o).filter(function(t){return!Ge(e,t)}).filter(function(t){return Ge(t,me.fromDom(o.dom.body))||Ye(n,t)}).each(function(){return dc(e)}),i.set(dt.none())})},onHide:function(t){var n,e;i.set((n=t.element,e=o,gc(n).orThunk(function(){return e().toOptional().bind(function(t){return gc(t.element)})}))),u(function(t){return tB(t,!1)})},onHidden:function(){u(function(t){return Ni(t,[JD.transitionClass])})}},JD),modes:[t.header.getDockingMode()],onDocked:e,onUndocked:e})],r)},oB=/* */Object.freeze({__proto__:null,setup:function(t,n,e){t.inline||(n.header.isPositionedAtTop()||t.on("ResizeEditor",function(){e().each(KD.reset)}),t.on("ResizeWindow ResizeEditor",function(){e().each(ZD)}),t.on("SkinLoaded",function(){e().each(function(t){KD.isDocked(t)?KD.reset(t):KD.refresh(t)})}),t.on("FullscreenStateChanged",function(){e().each(KD.reset)})),t.on("AfterScrollIntoView",function(y){e().each(function(t){KD.refresh(t);var n,e,o,r,i,u,a,c,s,l,f,d,m,g,p,h,v,b=t.element;Zd(b)&&(n=y,r=yr(e=b),i=r.dom.defaultView.innerHeight,u=Bu(r),a=me.fromDom(n.elm),c=zu(a),s=vu(a),l=c.y,f=l+s,d=Su(e),m=vu(e),g=d.top,p=g+m,h=Math.abs(g-u.top)<2,v=Math.abs(p-(u.top+i))<2,h&&l<p?Au(u.left,l-m,r):v&&g<f&&(o=l-i+s+m,Au(u.left,o,r)))})}),t.on("PostRender",function(){nB(t,!1)})},isDocked:function(t){return t().map(KD.isDocked).getOr(!1)},getBehaviours:eB}),rB=Xf({factory:function(n,o){var t={focus:ig.focusIn,setMenus:function(t,n){var e=V(n,function(n){var t={type:"menubutton",text:n.text,fetch:function(t){t(n.getItems())}},e=eE(t).mapError(function(t){return Dn(t)}).getOrDie();return GC(e,"tox-mbtn",o.backstage,dt.some("menuitem"))});lg.set(t,e)}};return{uid:n.uid,dom:n.dom,components:[],behaviours:rc([lg.config({}),fg("menubar-events",[sr(function(t){n.onSetup(t)}),er(so(),function(e,t){qu(e.element,".tox-mbtn--active").each(function(n){Ku(t.event.target,".tox-mbtn").each(function(t){Ge(n,t)||e.getSystem().getByDom(n).each(function(n){e.getSystem().getByDom(t).each(function(t){ow.expand(t),ow.close(n),hg.focus(t)})})})})}),er(Lo(),function(e,t){t.event.prevFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(n){t.event.newFocus.bind(function(t){return e.getSystem().getByDom(t).toOptional()}).each(function(t){ow.isOpen(n)&&(ow.expand(t),ow.close(n))})})})]),ig.config({mode:"flow",selector:".tox-mbtn",onEscape:function(t){return n.onEscape(t),dt.some(!0)}}),Yy.config({})]),apis:t,domModification:{attributes:{role:"menubar"}}}},name:"silver.Menubar",configFields:[Ln("dom"),Ln("uid"),Ln("onEscape"),Ln("backstage"),ne("onSetup",ct)],apis:{focus:function(t,n){t.focus(n)},setMenus:function(t,n,e){t.setMenus(n,e)}}}),iB="container",uB=[jl("slotBehaviours",[])],aB=function(t){return"<alloy.field."+t+">"},cB=function(r,t){var e,n=function(t){return Vf(r)},o=function(e,o){return function(t,n){return Mf(t,r,n).map(function(t){return e(t,n)}).getOr(o)}},i=function(t,n){return"true"!==qr(t.element,"aria-hidden")},u=o(i,!1),a=o(function(t,n){var e;i(t)&&(e=t.element,Yi(e,"display","none"),Yr(e,"aria-hidden","true"),qo(t,jo(),{name:n,visible:!1}))}),c=function(n,t){mt(t,function(t){return e(n,t)})},s=o(function(t,n){var e;i(t)||(e=t.element,tu(e,"display"),$r(e,"aria-hidden"),qo(t,jo(),{name:n,visible:!0}))}),l={getSlotNames:n,getSlot:function(t,n){return Mf(t,r,n)},isShowing:u,hideSlot:e=a,hideAllSlots:function(t){return c(t,n())},showSlot:s};return{uid:r.uid,dom:r.dom,components:t,behaviours:Ul(r.slotBehaviours),apis:l}},sB=Tt({getSlotNames:function(t,n){return t.getSlotNames(n)},getSlot:function(t,n,e){return t.getSlot(n,e)},isShowing:function(t,n,e){return t.isShowing(n,e)},hideSlot:function(t,n,e){return t.hideSlot(n,e)},hideAllSlots:function(t,n){return t.hideAllSlots(n)},showSlot:function(t,n,e){return t.showSlot(n,e)}},bi),lB=ft(ft({},sB),{sketch:function(t){var e,n=(e=[],{slot:function(t,n){return e.push(t),Tf(iB,aB(t),n)},record:function(){return e}}),o=t(n),r=n.record(),i=V(r,function(t){return bf({name:t,pname:aB(t)})});return jf(iB,uB,i,cB,o)}}),fB=fn([Qn("icon"),Qn("tooltip"),ae("onShow",ct),ae("onHide",ct),ae("onSetup",function(){return ct})]),dB=function(t){return{element:function(){return t.element.dom}}},mB=function(e,o){var r=V(Ct(o),function(t){var n=o[t],e=Tn(_n("sidebar",fB,n));return{name:t,getApi:dB,onSetup:e.onSetup,onShow:e.onShow,onHide:e.onHide}});return V(r,function(t){var n=fe(ct);return e.slot(t.name,{dom:{tag:"div",classes:["tox-sidebar__pane"]},behaviours:Uh([Xv(t,n),Yv(t,n),er(jo(),function(n,t){var e=t.event;L(r,function(t){return t.name===e.name}).each(function(t){(e.visible?t.onShow:t.onHide)(t.getApi(n))})})])})})},gB=function(t,e){Zf.getCurrent(t).each(function(t){return lg.set(t,[(n=e,lB.sketch(function(t){return{dom:{tag:"div",classes:["tox-sidebar__pane-container"]},components:mB(t,n),slotBehaviours:Uh([sr(function(t){return lB.hideAllSlots(t)})])}}))]);var n})},pB=function(t){return Zf.getCurrent(t).bind(function(t){return zT.isGrowing(t)||zT.hasGrown(t)?Zf.getCurrent(t).bind(function(n){return L(lB.getSlotNames(n),function(t){return lB.isShowing(n,t)})}):dt.none()})},hB=oi("FixSizeEvent"),vB=oi("AutoSizeEvent"),bB=function(t){var n,e,o,r=me.fromHtml(t),i=_r(r),u=(e=(n=r).dom.attributes!==undefined?n.dom.attributes:[],N(e,function(t,n){var e;return"class"===n.name?t:ft(ft({},t),((e={})[n.name]=n.value,e))},{})),a=(o=r,Array.prototype.slice.call(o.dom.classList,0)),c=0===i.length?{}:{innerHtml:Wr(r)};return ft({tag:mr(r),classes:a,attributes:u},c)},yB=function(t,n,e){var o=t.element;!0===n?(lg.set(t,[{dom:{tag:"div",attributes:{"aria-label":e.translate("Loading...")},classes:["tox-throbber__busy-spinner"]},components:[{dom:bB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}],behaviours:rc([ig.config({mode:"special",onTab:function(){return dt.some(!0)},onShiftTab:function(){return dt.some(!0)}}),hg.config({})])}]),tu(o,"display"),$r(o,"aria-hidden")):(lg.set(t,[]),Yi(o,"display","none"),Yr(o,"aria-hidden","true"))},xB=kD.optional({factory:rB,name:"menubar",schema:[Ln("backstage")]}),wB=kD.optional({factory:{sketch:function(t){return TD.sketch({uid:t.uid,dom:t.dom,listBehaviours:rc([ig.config({mode:"acyclic",selector:".tox-toolbar"})]),makeItem:function(){return QT({type:t.type,uid:oi("multiple-toolbar-item"),cyclicKeying:!1,initGroups:[],providers:t.providers,onEscape:function(){return t.onEscape(),dt.some(!0)}})},setupItem:function(t,n,e,o){eT.setGroups(n,e)},shell:!0})}},name:"multiple-toolbar",schema:[Ln("dom"),Ln("onEscape")]}),SB=kD.optional({factory:{sketch:function(t){var n;return((n=t).type===oh.sliding?$T:n.type===oh.floating?JT:QT)({type:t.type,uid:t.uid,onEscape:function(){return t.onEscape(),dt.some(!0)},cyclicKeying:!1,initGroups:[],getSink:t.getSink,providers:t.providers,moreDrawerData:{lazyToolbar:t.lazyToolbar,lazyMoreButton:t.lazyMoreButton,lazyHeader:t.lazyHeader},attributes:t.attributes})}},name:"toolbar",schema:[Ln("dom"),Ln("onEscape"),Ln("getSink")]}),kB=kD.optional({factory:{sketch:function(t){var n=t.editor,e=t.sticky?eB:ED;return{uid:t.uid,dom:t.dom,components:t.components,behaviours:rc(e(n,t.sharedBackstage))}}},name:"header",schema:[Ln("dom")]}),CB=kD.optional({name:"socket",schema:[Ln("dom")]}),OB=kD.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",classes:["tox-sidebar"],attributes:{role:"complementary"}},components:[{dom:{tag:"div",classes:["tox-sidebar__slider"]},components:[],behaviours:rc([Yy.config({}),hg.config({}),zT.config({dimension:{property:"width"},closedClass:"tox-sidebar--sliding-closed",openClass:"tox-sidebar--sliding-open",shrinkingClass:"tox-sidebar--sliding-shrinking",growingClass:"tox-sidebar--sliding-growing",onShrunk:function(t){Zf.getCurrent(t).each(lB.hideAllSlots),Yo(t,vB)},onGrown:function(t){Yo(t,vB)},onStartGrow:function(t){qo(t,hB,{width:Qi(t.element,"width").getOr("")})},onStartShrink:function(t){qo(t,hB,{width:Ou(t.element)+"px"})}}),lg.config({}),Zf.config({find:function(t){var n=lg.contents(t);return K(n)}})])}],behaviours:rc([sk(0),fg("sidebar-sliding-events",[er(hB,function(t,n){Yi(t.element,"width",n.event.width)}),er(vB,function(t,n){tu(t.element,"width")})])])}}},name:"sidebar",schema:[Ln("dom")]}),_B=kD.optional({factory:{sketch:function(t){return{uid:t.uid,dom:{tag:"div",attributes:{"aria-hidden":"true"},classes:["tox-throbber"],styles:{display:"none"}},behaviours:rc([lg.config({})]),components:[]}}},name:"throbber",schema:[Ln("dom")]}),TB=Yf({name:"OuterContainer",factory:function(e,t,n){var o={getSocket:function(t){return SD.getPart(t,e,"socket")},setSidebar:function(t,n){SD.getPart(t,e,"sidebar").each(function(t){return gB(t,n)})},toggleSidebar:function(t,o){SD.getPart(t,e,"sidebar").each(function(t){return n=t,e=o,void Zf.getCurrent(n).each(function(n){Zf.getCurrent(n).each(function(t){zT.hasGrown(n)?lB.isShowing(t,e)?zT.shrink(n):(lB.hideAllSlots(t),lB.showSlot(t,e)):(lB.hideAllSlots(t),lB.showSlot(t,e),zT.grow(n))})});var n,e})},whichSidebar:function(t){return SD.getPart(t,e,"sidebar").bind(pB).getOrNull()},getHeader:function(t){return SD.getPart(t,e,"header")},getToolbar:function(t){return SD.getPart(t,e,"toolbar")},setToolbar:function(t,n){SD.getPart(t,e,"toolbar").each(function(t){t.getApis().setGroups(t,n)})},setToolbars:function(t,n){SD.getPart(t,e,"multiple-toolbar").each(function(t){TD.setItems(t,n)})},refreshToolbar:function(t){SD.getPart(t,e,"toolbar").each(function(t){return t.getApis().refresh(t)})},toggleToolbarDrawer:function(t){SD.getPart(t,e,"toolbar").each(function(n){var t,e;t=n.getApis().toggle,e=function(t){return t(n)},t!==undefined&&null!==t?dt.some(e(t)):dt.none()})},isToolbarDrawerToggled:function(t){return SD.getPart(t,e,"toolbar").bind(function(n){return dt.from(n.getApis().isOpen).map(function(t){return t(n)})}).getOr(!1)},getThrobber:function(t){return SD.getPart(t,e,"throbber")},focusToolbar:function(t){SD.getPart(t,e,"toolbar").orThunk(function(){return SD.getPart(t,e,"multiple-toolbar")}).each(function(t){ig.focusIn(t)})},setMenubar:function(t,n){SD.getPart(t,e,"menubar").each(function(t){rB.setMenus(t,n)})},focusMenubar:function(t){SD.getPart(t,e,"menubar").each(function(t){rB.focus(t)})}};return{uid:e.uid,dom:e.dom,components:t,apis:o,behaviours:e.behaviours}},configFields:[Ln("dom"),Ln("behaviours")],partFields:[kB,xB,SB,wB,CB,OB,_B],apis:{getSocket:function(t,n){return t.getSocket(n)},setSidebar:function(t,n,e){t.setSidebar(n,e)},toggleSidebar:function(t,n,e){t.toggleSidebar(n,e)},whichSidebar:function(t,n){return t.whichSidebar(n)},getHeader:function(t,n){return t.getHeader(n)},getToolbar:function(t,n){return t.getToolbar(n)},setToolbar:function(t,n,e){var o=V(e,YT);t.setToolbar(n,o)},setToolbars:function(t,n,e){var o=V(e,function(t){return V(t,YT)});t.setToolbars(n,o)},refreshToolbar:function(t,n){return t.refreshToolbar(n)},toggleToolbarDrawer:function(t,n){t.toggleToolbarDrawer(n)},isToolbarDrawerToggled:function(t,n){return t.isToolbarDrawerToggled(n)},getThrobber:function(t,n){return t.getThrobber(n)},setMenubar:function(t,n,e){t.setMenubar(n,e)},focusMenubar:function(t,n){t.focusMenubar(n)},focusToolbar:function(t,n){t.focusToolbar(n)}}}),EB={file:{title:"File",items:"newdocument restoredraft | preview | export print | deleteallconversations"},edit:{title:"Edit",items:"undo redo | cut copy paste pastetext | selectall | searchreplace"},view:{title:"View",items:"code | visualaid visualchars visualblocks | spellchecker | preview fullscreen | showcomments"},insert:{title:"Insert",items:"image link media addcomment pageembed template codesample inserttable | charmap emoticons hr | pagebreak nonbreaking anchor toc | insertdatetime"},format:{title:"Format",items:"bold italic underline strikethrough superscript subscript codeformat | formats blockformats fontformats fontsizes align lineheight | forecolor backcolor | removeformat"},tools:{title:"Tools",items:"spellchecker spellcheckerlanguage | a11ycheck code wordcount"},table:{title:"Table",items:"inserttable | cell row column | advtablesort | tableprops deletetable"},help:{title:"Help",items:"help"}},DB=function(t){return"string"==typeof t?t.split(" "):t},BB=function(i,u){var a=ft(ft({},EB),u.menus),n=0<Ct(u.menus).length,t=u.menubar===undefined||!0===u.menubar?DB("file edit view insert format tools table help"):DB(!1===u.menubar?"":u.menubar),e=H(t,function(t){return n&&u.menus.hasOwnProperty(t)&&u.menus[t].hasOwnProperty("items")||EB.hasOwnProperty(t)}),o=V(e,function(t){var n,e,o,r=a[t];return n={title:r.title,items:DB(r.items)},e=u,o=i.getParam("removed_menuitems","").split(/[ ,]/),{text:n.title,getItems:function(){return U(n.items,function(t){var n=t.toLowerCase();return 0===n.trim().length||F(o,function(t){return t===n})?[]:"separator"===n||"|"===n?[{type:"separator"}]:e.menuItems[n]?[e.menuItems[n]]:[]})}}});return H(o,function(t){return 0<t.getItems().length&&F(t.getItems(),function(t){return"separator"!==t.type})})},AB=function(t){var n=function(){t._skinLoaded=!0,t.fire("SkinLoaded")};return function(){t.initialized?n():t.on("init",n)}},MB=function(n,e){return function(){return t={message:e},n.fire("SkinLoadError",t);var t}},FB=function(e,o,r){return new Mp(function(t,n){r.load(o,t,n),e.on("remove",function(){return r.unload(o)})})},IB=function(t,n){var e;return(e=me.fromDom(t.getElement()),Fr(e).isSome())?FB(t,n+"/skin.shadowdom.min.css",Qh.DOM.styleSheetLoader):Mp.resolve()},RB=function(t,n){var e,o,r,i,u,a=(r=(e=n).getParam("skin"),i=e.getParam("skin_url"),!1!==r&&(o=r||"oxide",i=i?e.documentBaseURI.toAbsolute(i):gv.baseURL+"/skins/ui/"+o),i);a&&n.contentCSS.push(a+(t?"/content.inline":"/content")+".min.css"),!1==(!1===n.getParam("skin"))&&x(a)?Mp.all([FB(u=n,a+"/skin.min.css",u.ui.styleSheetLoader),IB(n,a)]).then(AB(n),MB(n,"Skin could not be loaded")):AB(n)()},VB=k(RB,!1),PB=k(RB,!0),HB=function(e,t,o,r){var n,i=t.outerContainer,u=o.toolbar,a=o.buttons;s(u,x)?(n=u.map(function(t){var n={toolbar:t,buttons:a,allowToolbarGroups:o.allowToolbarGroups};return hD(e,n,{backstage:r},dt.none())}),TB.setToolbars(i,n)):TB.setToolbar(i,hD(e,o,{backstage:r},dt.none()))},zB=je(),NB=zB.os.isiOS()&&zB.os.version.major<=12,LB=function(e,t){var n=e.dom,o=e.getWin(),r=e.getDoc().documentElement,i=fe(xu(o.innerWidth,o.innerHeight)),u=fe(xu(r.offsetWidth,r.offsetHeight)),a=function(){var t=i.get();t.left===o.innerWidth&&t.top===o.innerHeight||(i.set(xu(o.innerWidth,o.innerHeight)),Mb(e))},c=function(){var t=e.getDoc().documentElement,n=u.get();n.left===t.offsetWidth&&n.top===t.offsetHeight||(u.set(xu(t.offsetWidth,t.offsetHeight)),Mb(e))},s=function(t){return n=t,e.fire("ScrollContent",n);var n};n.bind(o,"resize",a),n.bind(o,"scroll",s);var l=ky(me.fromDom(e.getBody()),"load",c),f=t.uiMothership.element;e.on("hide",function(){Yi(f,"display","none")}),e.on("show",function(){tu(f,"display")}),e.on("NodeChange",c),e.on("remove",function(){l.unbind(),n.unbind(o,"resize",a),n.unbind(o,"scroll",s),o=null})},jB=/* */Object.freeze({__proto__:null,render:function(e,n,t,o,r){var i=fe(0),u=n.outerContainer;VB(e);var a,c,s=me.fromDom(r.targetNode),l=Mr(Ar(s));a=s,c=n.mothership,ll(a,c,Pr),sl(l,n.uiMothership),e.on("PostRender",function(){HB(e,n,t,o),i.set(e.getWin().innerWidth),TB.setMenubar(u,BB(e,t)),TB.setSidebar(u,t.sidebar),LB(e,n)});var f,d,m,g,p,h=TB.getSocket(u).getOrDie("Could not find expected socket element");NB&&(qi(h.element,{overflow:"scroll","-webkit-overflow-scrolling":"touch"}),m=function(){e.fire("ScrollContent")},g=20,p=null,f={cancel:function(){null!==p&&(clearTimeout(p),p=null)},throttle:function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];null===p&&(p=setTimeout(function(){m.apply(null,t),p=null},g))}},d=Sy(h.element,"scroll",f.throttle),e.on("remove",d.unbind)),zv(e,n),e.addCommand("ToggleSidebar",function(t,n){TB.toggleSidebar(u,n),e.fire("ToggleSidebar")}),e.addQueryValueHandler("ToggleSidebar",function(){return TB.whichSidebar(u)});var v=Tv(e);v!==oh.sliding&&v!==oh.floating||e.on("ResizeWindow ResizeEditor ResizeContent",function(){var t=e.getWin().innerWidth;t!==i.get()&&(TB.refreshToolbar(n.outerContainer),i.set(t))});var b={enable:function(){Hv(n,!1)},disable:function(){Hv(n,!0)},isDisabled:function(){return mv.isDisabled(u)}};return{iframeContainer:h.element.dom,editorContainer:u.element.dom,api:b}}}),UB=function(t){return/^[0-9\.]+(|px)$/i.test(""+t)?dt.some(parseInt(""+t,10)):dt.none()},WB=function(t){return at(t)?t+"px":t},GB=function(n,t,e){var o=t.filter(function(t){return n<t}),r=e.filter(function(t){return t<n});return o.or(r).getOr(n)},XB=function(t){var n,e,o,r;return(e=pv(n=t),o=bv(n),r=xv(n),UB(e).map(function(t){return GB(t,o,r)})).getOr(pv(t))},YB=function(t){var n=hv(t),e=vv(t),o=yv(t);return UB(n).map(function(t){return GB(t,e,o)})},qB=function(a,c,t,n,s){var e=t.uiMothership,l=t.outerContainer,o=Qh.DOM,f=Mv(a),d=Rv(a),m=yv(a).or(YB(a)),r=n.shared.header,g=r.isPositionedAtTop,i=Tv(a),p=i===oh.sliding||i===oh.floating,u=fe(!1),h=function(){return u.get()&&!a.removed},v=function(t){return p?t.fold(function(){return 0},function(t){return 1<t.components().length?vu(t.components()[1].element):0}):0},b=function(){e.broadcastOn([kl()],{})},y=function(t){var n,e,o,r,i,u;void 0===t&&(t=!1),h()&&(f||(n=m.getOrThunk(function(){var t=UB(Ji(Ui(),"margin-left")).getOr(0);return Ou(Ui())-Su(c).left+t}),Yi(s.get().element,"max-width",n+"px")),p&&TB.refreshToolbar(l),f||(e=TB.getToolbar(l),o=v(e),r=Hu(c),i=g()?Math.max(r.y-vu(s.get().element)+o,0):r.bottom,qi(l.element,{position:"absolute",top:Math.round(i)+"px",left:Math.round(r.x)+"px"})),d&&(u=s.get(),t?KD.reset(u):KD.refresh(u)),b())},x=function(t){var n,e;void 0===t&&(t=!0),!f&&d&&h()&&(n=r.getDockingMode(),(e=function(t){switch(Dv(a)){case Ov.auto:var n=TB.getToolbar(l),e=v(n),o=vu(t.element)-e,r=Hu(c);if(r.y>o)return"top";var i=wr(c),u=Math.max(i.dom.scrollHeight,vu(i));return r.bottom<u-o||Nu().bottom<r.bottom-o?"bottom":"top";case Ov.bottom:return"bottom";case Ov.top:default:return"top"}}(s.get()))!==n&&(function(t){var n=s.get();KD.setModes(n,[t]),r.setDockingMode(t);var e=g()?ta.TopToBottom:ta.BottomToTop;Yr(n.element,Xc,e)}(e),t&&y(!0)))};return{isVisible:h,isPositionedAtTop:g,show:function(){u.set(!0),Yi(l.element,"display","flex"),o.addClass(a.getBody(),"mce-edit-focus"),tu(e.element,"display"),x(!1),y()},hide:function(){u.set(!1),t.outerContainer&&(Yi(l.element,"display","none"),o.removeClass(a.getBody(),"mce-edit-focus")),Yi(e.element,"display","none")},update:y,updateMode:x,repositionPopups:b}},KB=function(t,n){var e=Hu(t);return{pos:n?e.y:e.bottom,bounds:e}},JB=/* */Object.freeze({__proto__:null,render:function(n,e,o,r,t){var i=e.mothership,u=e.uiMothership,a=e.outerContainer,c=fe(null),s=me.fromDom(t.targetNode),l=qB(n,s,e,r,c),f=n.getParam("toolbar_persist",!1,"boolean");PB(n);var d=function(){var t;c.get()?l.show():(c.set(TB.getHeader(a).getOrDie()),t=Fv(n),sl(t,i),sl(t,u),HB(n,e,o,r),TB.setMenubar(a,BB(n,o)),l.show(),function(c,s,l,t){var f=fe(KB(s,l.isPositionedAtTop())),n=function(t){var n=KB(s,l.isPositionedAtTop()),e=n.pos,o=n.bounds,r=f.get(),i=r.pos,u=r.bounds,a=o.height!==u.height||o.width!==u.width;f.set({pos:e,bounds:o}),a&&Mb(c,t),l.isVisible()&&(i!==e?l.update(!0):a&&(l.updateMode(),l.repositionPopups()))};t||(c.on("activate",l.show),c.on("deactivate",l.hide)),c.on("SkinLoaded ResizeWindow",function(){return l.update(!0)}),c.on("NodeChange keydown",function(t){fp.requestAnimationFrame(function(){return n(t)})}),c.on("ScrollWindow",function(){return l.updateMode()});var e=iD();e.set(ky(me.fromDom(c.getBody()),"load",n)),c.on("remove",function(){e.clear()})}(n,s,l,f),n.nodeChanged())};n.on("show",d),n.on("hide",l.hide),f||(n.on("focus",d),n.on("blur",l.hide)),n.on("init",function(){(n.hasFocus()||f)&&d()}),zv(n,e);var m={show:function(){l.show()},hide:function(){l.hide()},enable:function(){Hv(e,!1)},disable:function(){Hv(e,!0)},isDisabled:function(){return mv.isDisabled(a)}};return{editorContainer:a.element.dom,api:m}}}),$B=function(t,n){var e,o,r,i,u,a,c,s,l,f,d,m,g,p;r=PE(0,o=n,YE(e=t)),e.ui.registry.addNestedMenuItem("align",{text:o.shared.providers.translate("Align"),getSubmenuItems:function(){return r.items.validateItems(r.getStyleItems())}}),a=PE(0,u=n,$E(i=t)),i.ui.registry.addNestedMenuItem("fontformats",{text:u.shared.providers.translate("Fonts"),getSubmenuItems:function(){return a.items.validateItems(a.getStyleItems())}}),c=t,l=ft({type:"advanced"},(s=n).styleselect),f=PE(0,s,sD(c,l)),c.ui.registry.addNestedMenuItem("formats",{text:"Formats",getSubmenuItems:function(){return f.items.validateItems(f.getStyleItems())}}),m=PE(0,n,cD(d=t)),d.ui.registry.addNestedMenuItem("blockformats",{text:"Blocks",getSubmenuItems:function(){return m.items.validateItems(m.getStyleItems())}}),p=PE(0,n,nD(g=t)),g.ui.registry.addNestedMenuItem("fontsizes",{text:"Font sizes",getSubmenuItems:function(){return p.items.validateItems(p.getStyleItems())}})},QB={unsupportedLength:["em","ex","cap","ch","ic","rem","lh","rlh","vw","vh","vi","vb","vmin","vmax","cm","mm","Q","in","pc","pt","px"],fixed:["px","pt"],relative:["%"],empty:[""]},ZB=(NE="[eE][+-]?[0-9]+",jE=["Infinity",(zE="[0-9]+")+"\\."+(LE=function(t){return"(?:"+t+")?"})(zE)+LE(NE),"\\."+zE+LE(NE),zE+LE(NE)].join("|"),new RegExp("^([+-]?(?:"+jE+"))(.*)$")),tA=function(t,r){return dt.from(ZB.exec(t)).bind(function(t){var n,e=Number(t[1]),o=t[2];return n=o,F(r,function(t){return F(QB[t],function(t){return n===t})})?dt.some({value:e,unit:o}):dt.none()})},nA=function(t){return tA(t,["fixed","relative","empty"]).map(function(t){return t.value+t.unit}).getOr(t)},eA=function(o){var r=o.getParam("lineheight_formats","1 1.1 1.2 1.3 1.4 1.5 2","string").split(" "),i=new Map,u=rD(),a=function(){var t=nA(o.queryCommandValue("LineHeight"));dt.from(i.get(t)).fold(function(){return u.clear()},function(t){u.set({destroy:function(){t.setActive(!1)}}),t.setActive(!0)})};return o.on("nodeChange",a),V(r,function(n,e){return{type:"togglemenuitem",text:n,onSetup:function(t){return i.set(nA(n),t),e+1===r.length&&a(),function(){0===e&&(o.off("nodeChange",a),u.clear())}},onAction:function(){return o.execCommand("LineHeight",!1,n)}}})},oA=function(t){var n,e;(n=t).ui.registry.addNestedMenuItem("lineheight",{type:"nestedmenuitem",text:"Line height",getSubmenuItems:function(){return eA(n)}}),(e=t).ui.registry.addMenuButton("lineheight",{tooltip:"Line height",icon:"line-height",fetch:function(t){return t(eA(e))}})},rA=function(t,n){return function(){t.execCommand("mceToggleFormat",!1,n)}},iA=function(t){var n,e;!function(e){mk.each([{name:"bold",text:"Bold",icon:"bold"},{name:"italic",text:"Italic",icon:"italic"},{name:"underline",text:"Underline",icon:"underline"},{name:"strikethrough",text:"Strikethrough",icon:"strike-through"},{name:"subscript",text:"Subscript",icon:"subscript"},{name:"superscript",text:"Superscript",icon:"superscript"}],function(t,n){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onSetup:uD(e,t.name),onAction:rA(e,t.name)})});for(var t=1;t<=6;t++){var n="h"+t;e.ui.registry.addToggleButton(n,{text:n.toUpperCase(),tooltip:"Heading "+t,onSetup:uD(e,n),onAction:rA(e,n)})}}(t),n=t,mk.each([{name:"cut",text:"Cut",action:"Cut",icon:"cut"},{name:"copy",text:"Copy",action:"Copy",icon:"copy"},{name:"paste",text:"Paste",action:"Paste",icon:"paste"},{name:"help",text:"Help",action:"mceHelp",icon:"help"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all"},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document"},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting"},{name:"remove",text:"Remove",action:"Delete",icon:"remove"}],function(t){n.ui.registry.addButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return n.execCommand(t.action)}})}),e=t,mk.each([{name:"blockquote",text:"Blockquote",action:"mceBlockQuote",icon:"quote"}],function(t){e.ui.registry.addToggleButton(t.name,{tooltip:t.text,icon:t.icon,onAction:function(){return e.execCommand(t.action)},onSetup:uD(e,t.name)})})},uA=function(t){var n;iA(t),n=t,mk.each([{name:"bold",text:"Bold",action:"Bold",icon:"bold",shortcut:"Meta+B"},{name:"italic",text:"Italic",action:"Italic",icon:"italic",shortcut:"Meta+I"},{name:"underline",text:"Underline",action:"Underline",icon:"underline",shortcut:"Meta+U"},{name:"strikethrough",text:"Strikethrough",action:"Strikethrough",icon:"strike-through",shortcut:""},{name:"subscript",text:"Subscript",action:"Subscript",icon:"subscript",shortcut:""},{name:"superscript",text:"Superscript",action:"Superscript",icon:"superscript",shortcut:""},{name:"removeformat",text:"Clear formatting",action:"RemoveFormat",icon:"remove-formatting",shortcut:""},{name:"newdocument",text:"New document",action:"mceNewDocument",icon:"new-document",shortcut:""},{name:"cut",text:"Cut",action:"Cut",icon:"cut",shortcut:"Meta+X"},{name:"copy",text:"Copy",action:"Copy",icon:"copy",shortcut:"Meta+C"},{name:"paste",text:"Paste",action:"Paste",icon:"paste",shortcut:"Meta+V"},{name:"selectall",text:"Select all",action:"SelectAll",icon:"select-all",shortcut:"Meta+A"}],function(t){n.ui.registry.addMenuItem(t.name,{text:t.text,icon:t.icon,shortcut:t.shortcut,onAction:function(){return n.execCommand(t.action)}})}),n.ui.registry.addMenuItem("codeformat",{text:"Code",icon:"sourcecode",onAction:rA(n,"code")})},aA=function(t,n,e){var o=function(){return!!n.undoManager&&n.undoManager[e]()},r=function(){t.setDisabled(n.mode.isReadOnly()||!o())};return t.setDisabled(!o()),n.on("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r),function(){return n.off("Undo Redo AddUndo TypingUndo ClearUndos SwitchMode",r)}},cA=function(t){var n,e;(n=t).ui.registry.addMenuItem("undo",{text:"Undo",icon:"undo",shortcut:"Meta+Z",onSetup:function(t){return aA(t,n,"hasUndo")},onAction:function(){return n.execCommand("undo")}}),n.ui.registry.addMenuItem("redo",{text:"Redo",icon:"redo",shortcut:"Meta+Y",onSetup:function(t){return aA(t,n,"hasRedo")},onAction:function(){return n.execCommand("redo")}}),(e=t).ui.registry.addButton("undo",{tooltip:"Undo",icon:"undo",onSetup:function(t){return aA(t,e,"hasUndo")},onAction:function(){return e.execCommand("undo")}}),e.ui.registry.addButton("redo",{tooltip:"Redo",icon:"redo",onSetup:function(t){return aA(t,e,"hasRedo")},onAction:function(){return e.execCommand("redo")}})},sA=function(t,n){var e,o,r,i;!function(n){mk.each([{name:"alignleft",text:"Align left",cmd:"JustifyLeft",icon:"align-left"},{name:"aligncenter",text:"Align center",cmd:"JustifyCenter",icon:"align-center"},{name:"alignright",text:"Align right",cmd:"JustifyRight",icon:"align-right"},{name:"alignjustify",text:"Justify",cmd:"JustifyFull",icon:"align-justify"}],function(t){n.ui.registry.addToggleButton(t.name,{tooltip:t.text,onAction:function(){return n.execCommand(t.cmd)},icon:t.icon,onSetup:uD(n,t.name)})});var t="alignnone",e="No alignment",o="JustifyNone",r="align-none";n.ui.registry.addButton(t,{tooltip:e,onAction:function(){return n.execCommand(o)},icon:r})}(t),uA(t),$B(t,n),cA(t),function(t){Ub(t);var n=fe(null),e=fe(null);Kb(t,"forecolor","forecolor","Text color",n),Kb(t,"backcolor","hilitecolor","Background color",e),Jb(t,"forecolor","forecolor","Text color"),Jb(t,"backcolor","hilitecolor","Background color")}(t),(o=e=t).ui.registry.addButton("visualaid",{tooltip:"Visual aids",text:"Visual aids",onAction:function(){return o.execCommand("mceToggleVisualAid")}}),(r=e).ui.registry.addToggleMenuItem("visualaid",{text:"Visual aids",onSetup:function(t){return function(n,t){n.setActive(t.hasVisual);var e=function(t){n.setActive(t.hasVisual)};return t.on("VisualAid",e),function(){return t.off("VisualAid",e)}}(t,r)},onAction:function(){r.execCommand("mceToggleVisualAid")}}),(i=t).ui.registry.addButton("outdent",{tooltip:"Decrease indent",icon:"outdent",onSetup:function(t){return function(t,n){t.setDisabled(!n.queryCommandState("outdent"));var e=function(){t.setDisabled(!n.queryCommandState("outdent"))};return n.on("NodeChange",e),function(){return n.off("NodeChange",e)}}(t,i)},onAction:function(){return i.execCommand("outdent")}}),i.ui.registry.addButton("indent",{tooltip:"Increase indent",icon:"indent",onAction:function(){return i.execCommand("indent")}}),oA(t)},lA=function(t,n){return{anchor:"makeshift",x:t,y:n}},fA=function(t){return"longpress"===t.type||0===t.type.indexOf("touch")},dA=function(t,n){var e,o,r,i=Qh.DOM.getPos(t);return e=n,o=i.x,r=i.y,lA(e.x+o,e.y+r)},mA=function(t,n){return"contextmenu"===n.type||"longpress"===n.type?t.inline?function(t){if(fA(t)){var n=t.touches[0];return lA(n.pageX,n.pageY)}return lA(t.pageX,t.pageY)}(n):dA(t.getContentAreaContainer(),function(t){if(fA(t)){var n=t.touches[0];return lA(n.clientX,n.clientY)}return lA(t.clientX,t.clientY)}(n)):gA(t)},gA=function(t){return{anchor:"selection",root:me.fromDom(t.selection.getNode())}},pA=function(t){return{anchor:"node",node:dt.some(me.fromDom(t.selection.getNode())),root:me.fromDom(t.getBody())}},hA=function(t,n,e,o,r,i){var u=e(),a=i?pA(t):mA(t,n);UC(u,ih.CLOSE_ON_EXECUTE,o,!1).map(function(t){n.preventDefault(),Zg.showMenuAt(r,a,{menu:{markers:bh("normal")},data:t})})},vA={onLtr:function(){return[ja,Pa,Ha,za,Na,La,sp,lp,ap,ip,cp,up]},onRtl:function(){return[ja,Ha,Pa,Na,za,La,sp,lp,cp,up,ap,ip]}},bA={valignCentre:[],alignCentre:[],alignLeft:["tox-pop--align-left"],alignRight:["tox-pop--align-right"],right:["tox-pop--right"],left:["tox-pop--left"],bottom:["tox-pop--bottom"],top:["tox-pop--top"]},yA=function(n,e,t,o,r,i,u){var a,c,s=i?pA(n):(a=n,c=e,ft({bubble:Pc(0,12,bA),layouts:vA,overrides:{maxWidthFunction:Q_(),maxHeightFunction:Mc()}},mA(a,c)));UC(t,ih.CLOSE_ON_EXECUTE,o,!0).map(function(t){e.preventDefault(),Zg.showMenuWithinBounds(r,s,{menu:{markers:bh("normal"),highlightImmediately:u},data:t,type:"horizontal"},function(){return dt.some(EE(n,o.shared))}),n.fire(Z_)})},xA=function(n,e,o,r,i,u){var t,a=je(),c=a.os.isiOS(),s=a.os.isOSX(),l=a.os.isAndroid(),f=a.deviceType.isTouch(),d=function(){var t=o();yA(n,e,t,r,i,u,!(l||c||s&&f))};!s&&!c||u?(l&&!u&&n.selection.setCursorLocation(e.target,0),d()):(t=function(){!function(t){var n=t.selection.getRng(),e=function(){fp.setEditorTimeout(t,function(){t.selection.setRng(n)},10),i()};t.once("touchend",e);var o=function(t){t.preventDefault(),t.stopImmediatePropagation()};t.on("mousedown",o,!0);var r=function(){return i()};t.once("longpresscancel",r);var i=function(){t.off("touchend",e),t.off("longpresscancel",r),t.off("mousedown",o)}}(n),d()},!function(t,n){var e=t.selection;if(e.isCollapsed()||n.touches.length<1)return!1;var o=n.touches[0],r=e.getRng();return Bs(t.getWin(),ms.domRange(r)).exists(function(t){return t.left<=o.clientX&&t.right>=o.clientX&&t.top<=o.clientY&&t.bottom>=o.clientY})}(n,e)?(n.once("selectionchange",t),n.once("touchend",function(){return n.off("selectionchange",t)})):t())},wA=function(t){return"string"==typeof t?t.split(/[ ,]/):t},SA=function(t){return t.getParam("contextmenu_never_use_native",!1,"boolean")},kA=function(t){return e="contextmenu",o="link linkchecker image imagetools table spellchecker configurepermanentpen",r=(n=t).ui.registry.getAll().contextMenus,dt.from(n.getParam(e)).map(wA).getOrThunk(function(){return H(wA(o),function(t){return It(r,t)})});var n,e,o,r},CA=function(t){return x(t)?"|"===t:"separator"===t.type},OA={type:"separator"},_A=function(n){var t,e=function(t){return{text:t.text,icon:t.icon,disabled:t.disabled,shortcut:t.shortcut}};if(x(n))return n;switch(n.type){case"separator":return OA;case"submenu":return ft(ft({type:"nestedmenuitem"},e(n)),{getSubmenuItems:function(){var t=n.getSubmenuItems();return x(t)?t:V(t,_A)}});default:return ft(ft({type:"menuitem"},e(n)),{onAction:(t=n.onAction,function(){return t()})})}},TA=function(t,n){if(0===n.length)return t;var e=J(t).filter(function(t){return!CA(t)}).fold(function(){return[]},function(t){return[OA]});return t.concat(e).concat(n).concat([OA])},EA=function(t,n){return"longpress"!==n.type&&(2!==n.button||n.target===t.getBody()&&""===n.pointerType)},DA=function(t,n){return EA(t,n)?t.selection.getStart(!0):n.target},BA=function(a,t,e){var o=je().deviceType.isTouch,r=mu(Zg.sketch({dom:{tag:"div"},lazySink:t,onEscape:function(){return a.focus()},onShow:function(){return e.setContextMenuState(!0)},onHide:function(){return e.setContextMenuState(!1)},fireDismissalEventInstead:{},inlineBehaviours:rc([fg("dismissContextMenu",[er(zo(),function(t,n){wl.close(t),a.focus()})])])})),n=function(t){return Zg.hide(r)},i=function(u){var t,n;SA(a)&&u.preventDefault(),t=a,u.ctrlKey&&!SA(t)||!1===a.getParam("contextmenu")||(n=function(t,n){var e=t.getParam("contextmenu_avoid_overlap","","string");if(EA(t,n))return!0;if(e){var o=DA(t,n);return Oy(me.fromDom(o),e)}return!1}(a,u),(o()?xA:hA)(a,u,function(){var n,r,t,e=DA(a,u),o=a.ui.registry.getAll(),i=kA(a);return n=o.contextMenus,r=e,0<(t=N(i,function(o,t){return Ft(n,t.toLowerCase()).map(function(t){var n=t.update(r);if(x(n))return TA(o,n.split(" "));if(0<n.length){var e=V(n,_A);return TA(o,e)}return o}).getOrThunk(function(){return o.concat([t])})},[])).length&&CA(t[t.length-1])&&t.pop(),t},e,r,n))};a.on("init",function(){var t="ResizeEditor ScrollContent ScrollWindow longpresscancel"+(o()?"":" ResizeWindow");a.on(t,n),a.on("longpress contextmenu",i)})},AA=Vt([{offset:["x","y"]},{absolute:["x","y"]},{fixed:["x","y"]}]),MA=function(n){return function(t){return t.translate(-n.left,-n.top)}},FA=function(n){return function(t){return t.translate(n.left,n.top)}},IA=function(e){return function(t,n){return N(e,function(t,n){return n(t)},xu(t,n))}},RA=function(t,n,e){return t.fold(IA([FA(e),MA(n)]),IA([MA(n)]),IA([]))},VA=function(t,n,e){return t.fold(IA([FA(e)]),IA([]),IA([FA(n)]))},PA=function(t,n,e){return t.fold(IA([]),IA([MA(e)]),IA([FA(n),MA(e)]))},HA=function(t,n,e){var o=t.fold(function(t,n){return{position:dt.some("absolute"),left:dt.some(t+"px"),top:dt.some(n+"px")}},function(t,n){return{position:dt.some("absolute"),left:dt.some(t-e.left+"px"),top:dt.some(n-e.top+"px")}},function(t,n){return{position:dt.some("fixed"),left:dt.some(t+"px"),top:dt.some(n+"px")}});return ft({right:dt.none(),bottom:dt.none()},o)},zA=function(t,i,u,a){var n=function(o,r){return function(t,n){var e=o(i,u,a);return r(t.getOr(e.left),n.getOr(e.top))}};return t.fold(n(PA,NA),n(VA,LA),n(RA,jA))},NA=AA.offset,LA=AA.absolute,jA=AA.fixed,UA=function(t,n){var e=qr(t,n);return o(e)?NaN:parseInt(e,10)},WA=function(t,n,e,o){return r=n,i=t.element,u=UA(i,r.leftAttr),a=UA(i,r.topAttr),(isNaN(u)||isNaN(a)?dt.none():dt.some(xu(u,a))).fold(function(){return e},function(t){return jA(t.left+o.left,t.top+o.top)});var r,i,u,a},GA=function(t,n,e,o,r,i){var u,a,c,s=WA(t,n,e,o),l=(n.mustSnap?qA:KA)(t,n,s,r,i),f=RA(s,r,i);return u=n,a=f,c=t.element,Yr(c,u.leftAttr,a.left+"px"),Yr(c,u.topAttr,a.top+"px"),l.fold(function(){return{coord:jA(f.left,f.top),extra:dt.none()}},function(t){return{coord:t.output,extra:t.extra}})},XA=function(t,n){var e,o;e=n,o=t.element,$r(o,e.leftAttr),$r(o,e.topAttr)},YA=function(t,l,f,d){return Q(t,function(t){var n,e,o,r,i,u,a,c,s=t.sensor;return(n=l,e=s,o=t.range.left,r=t.range.top,a=VA(n,i=f,u=d),c=VA(e,i,u),Math.abs(a.left-c.left)<=o&&Math.abs(a.top-c.top)<=r)?dt.some({output:zA(t.output,l,f,d),extra:t.extra}):dt.none()})},qA=function(t,n,d,m,g){var e=n.getSnapPoints(t);return YA(e,d,m,g).orThunk(function(){return N(e,function(n,e){var t,o,r,i,u,a,c,s,l=e.sensor,f=(t=d,o=l,e.range.left,e.range.top,u=VA(t,r=m,i=g),a=VA(o,r,i),c=Math.abs(u.left-a.left),s=Math.abs(u.top-a.top),xu(c,s));return n.deltas.fold(function(){return{deltas:dt.some(f),snap:dt.some(e)}},function(t){return(f.left+f.top)/2<=(t.left+t.top)/2?{deltas:dt.some(f),snap:dt.some(e)}:n})},{deltas:dt.none(),snap:dt.none()}).snap.map(function(t){return{output:zA(t.output,d,m,g),extra:t.extra}})})},KA=function(t,n,e,o,r){var i=n.getSnapPoints(t);return YA(i,e,o,r)},JA=/* */Object.freeze({__proto__:null,snapTo:function(t,n,e,o){var r,i,u,a,c,s,l,f,d=n.getTarget(t.element);n.repositionTarget&&(r=yr(t.element),i=Bu(r),u=AD(d),l=i,f=u,a={coord:zA((s=o).output,s.output,l,f),extra:s.extra},c=HA(a.coord,0,u),Ki(d,c))}}),$A="data-initial-z-index",QA=function(t,n){var e;t.getSystem().addToGui(n),kr((e=n).element).filter(pr).each(function(n){Qi(n,"z-index").each(function(t){Yr(n,$A,t)}),Yi(n,"z-index",Ji(e.element,"z-index"))})},ZA=function(t){kr(t.element).filter(pr).each(function(n){Kr(n,$A).fold(function(){return tu(n,"z-index")},function(t){return Yi(n,"z-index",t)}),$r(n,$A)}),t.getSystem().removeFromGui(t)},tM=function(t,n,e){return t.getSystem().build(Ny.sketch({dom:{styles:{left:"0px",top:"0px",width:"100%",height:"100%",position:"fixed","z-index":"1000000000000000"},classes:[n]},events:e}))},nM=te("snaps",[Ln("getSnapPoints"),ma("onSensor"),Ln("leftAttr"),Ln("topAttr"),ne("lazyViewport",Nu),ne("mustSnap",!1)]),eM=[ne("useFixed",l),Ln("blockerClass"),ne("getTarget",lt),ne("onDrag",ct),ne("repositionTarget",!0),ne("onDrop",ct),ae("getBounds",Nu),nM],oM=function(n){return t=Qi(n,"left"),e=Qi(n,"top"),o=Qi(n,"position"),r=function(t,n,e){return("fixed"===e?jA:NA)(parseInt(t,10),parseInt(n,10))},(t.isSome()&&e.isSome()&&o.isSome()?dt.some(r(t.getOrDie(),e.getOrDie(),o.getOrDie())):dt.none()).getOrThunk(function(){var t=Su(n);return LA(t.left,t.top)});var t,e,o,r},rM=function(e,t,i,u,a,c,n){var o,r,s,l,f,d,m,g,p,h=t.fold(function(){var t,e,o,n=(t=i,e=c.left,o=c.top,t.fold(function(t,n){return NA(t+e,n+o)},function(t,n){return LA(t+e,n+o)},function(t,n){return jA(t+e,n+o)})),r=RA(n,u,a);return jA(r.left,r.top)},function(n){var t=GA(e,n,i,c,u,a);return t.extra.each(function(t){n.onSensor(e,t)}),t.coord});return o=h,r=u,s=a,f=(l=n).bounds,d=VA(o,r,s),m=Oc(d.left,f.x,f.x+f.width-l.width),g=Oc(d.top,f.y,f.y+f.height-l.height),p=LA(m,g),o.fold(function(){var t=PA(p,r,s);return NA(t.left,t.top)},function(){return p},function(){var t=RA(p,r,s);return jA(t.left,t.top)})},iM=function(t,n){return{bounds:t.getBounds(),height:bu(n.element),width:_u(n.element)}},uM=function(d,m,t,n,e){var o=t.update(n,e),g=t.getStartData().getOrThunk(function(){return iM(m,d)});o.each(function(t){var n,e,o,r,i,u,a,c,s,l,f;n=d,o=g,r=t,f=(e=m).getTarget(n.element),e.repositionTarget&&(i=yr(n.element),u=Bu(i),a=AD(f),c=oM(f),s=rM(n,e.snaps,c,u,a,r,o),l=HA(s,0,a),Ki(f,l)),e.onDrag(n,f,r)})},aM=function(n,t,e,o){t.each(ZA),e.snaps.each(function(t){XA(n,t)});var r=e.getTarget(n.element);o.reset(),e.onDrop(n,r)},cM=function(t){return function(n,e){var o=function(t){e.setStartData(iM(n,t))};return Zo(T([er(Ro(),function(t){e.getStartData().each(function(){return o(t)})})],t(n,e,o)))}},sM=/* */Object.freeze({__proto__:null,getData:function(t){return dt.from(xu(t.x,t.y))},getDelta:function(t,n){return xu(n.left-t.left,n.top-t.top)}}),lM=function(a,c,s){return[er(io(),function(n,t){var e,o,r,i,u;0===t.event.raw.button&&(t.stop(),r={drop:e=function(){return aM(n,dt.some(i),a,c)},delayDrop:(o=_y(e,200)).schedule,forceDrop:e,move:function(t){o.cancel(),uM(n,a,c,sM,t)}},i=tM(n,a.blockerClass,(u=r,Zo([er(io(),u.forceDrop),er(co(),u.drop),er(uo(),function(t,n){u.move(n.event)}),er(ao(),u.delayDrop)]))),s(n),QA(n,i))})]},fM=T(eM,[va("dragger",{handlers:cM(lM)})]),dM=/* */Object.freeze({__proto__:null,getData:function(t){var n,e=t.raw.touches;return 1===e.length?(n=e[0],dt.some(xu(n.clientX,n.clientY))):dt.none()},getDelta:function(t,n){return xu(n.left-t.left,n.top-t.top)}}),mM=function(u,a,c){var s=fe(dt.none());return[er(no(),function(n,t){t.stop();var e,o=function(){aM(n,s.get(),u,a),s.set(dt.none())},r={drop:o,delayDrop:ct,forceDrop:o,move:function(t){uM(n,u,a,dM,t)}},i=tM(n,u.blockerClass,(e=r,Zo([er(no(),e.forceDrop),er(oo(),e.drop),er(ro(),e.drop),er(eo(),function(t,n){e.move(n.event)})])));s.set(dt.some(i));c(n),QA(n,i)}),er(eo(),function(t,n){n.stop(),uM(t,u,a,dM,n.event)}),er(oo(),function(t,n){n.stop(),aM(t,s.get(),u,a),s.set(dt.none())}),er(ro(),function(t){aM(t,s.get(),u,a),s.set(dt.none())})]},gM=T(eM,[va("dragger",{handlers:cM(mM)})]),pM=T(eM,[va("dragger",{handlers:cM(function(t,n,e){return T(lM(t,n,e),mM(t,n,e))})})]),hM=cc({branchKey:"mode",branches:/* */Object.freeze({__proto__:null,mouse:fM,touch:gM,mouseOrTouch:pM}),name:"dragging",active:{events:function(t,n){return t.dragger.handlers(t,n)}},extra:{snap:function(t){return{sensor:t.sensor,range:t.range,output:t.output,extra:dt.from(t.extra)}}},state:/* */Object.freeze({__proto__:null,init:function(){var i=dt.none(),n=dt.none(),t=st({});return xi({readState:t,reset:function(){i=dt.none(),n=dt.none()},update:function(r,t){return r.getData(t).bind(function(t){return n=r,e=t,o=i.map(function(t){return n.getDelta(t,e)}),i=dt.some(e),o;var n,e,o})},getStartData:function(){return n},setStartData:function(t){n=dt.some(t)}})}}),apis:JA}),vM=function(t,r,i,u,n,e){return t.fold(function(){return hM.snap({sensor:LA(i-20,u-20),range:xu(n,e),output:LA(dt.some(i),dt.some(u)),extra:{td:r}})},function(t){var n=i-20,e=u-20,o=t.element.dom.getBoundingClientRect();return hM.snap({sensor:LA(n,e),range:xu(40,40),output:LA(dt.some(i-o.width/2),dt.some(u-o.height/2)),extra:{td:r}})})},bM=function(t,i,u){return{getSnapPoints:t,leftAttr:"data-drag-left",topAttr:"data-drag-top",onSensor:function(t,n){var e,o,r=n.td;e=i.get(),o=r,e.exists(function(t){return Ge(t,o)})||(i.set(dt.some(r)),u(r))},mustSnap:!0}},yM=function(t){return mp(dp.sketch({dom:{tag:"div",classes:["tox-selector"]},buttonBehaviours:rc([hM.config({mode:"mouseOrTouch",blockerClass:"blocker",snaps:t}),rw.config({})]),eventOrder:{mousedown:["dragging","alloy.base.behaviour"],touchstart:["dragging","alloy.base.behaviour"]}}))},xM=function(c,e){var o=fe([]),r=fe([]),t=fe(!1),i=fe(dt.none()),u=fe(dt.none()),n=function(t){var n=zu(t);return vM(f.getOpt(e),t,n.x,n.y,n.width,n.height)},a=function(t){var n=zu(t);return vM(d.getOpt(e),t,n.right,n.bottom,n.width,n.height)},s=bM(function(){return V(o.get(),n)},i,function(n){u.get().each(function(t){c.fire("TableSelectorChange",{start:n,finish:t})})}),l=bM(function(){return V(r.get(),a)},u,function(n){i.get().each(function(t){c.fire("TableSelectorChange",{start:t,finish:n})})}),f=yM(s),d=yM(l),m=mu(f.asSpec()),g=mu(d.asSpec()),p=function(t,n,e,o){var r=e(n);hM.snapTo(t,r);!function(t,n,e,o){var r=n.dom.getBoundingClientRect();tu(t.element,"display");var i=Sr(me.fromDom(c.getBody())).dom.innerHeight,u=e(r),a=o(r,i);(u||a)&&Yi(t.element,"display","none")}(t,n,function(t){return t[o]<0},function(t,n){return t[o]>n})},h=function(t){return p(m,t,n,"top")},v=function(t){return p(g,t,a,"bottom")};je().deviceType.isTouch()&&(c.on("TableSelectionChange",function(n){t.get()||(rl(e,m),rl(e,g),t.set(!0)),i.set(dt.some(n.start)),u.set(dt.some(n.finish)),n.otherCells.each(function(t){o.set(t.upOrLeftCells),r.set(t.downOrRightCells),h(n.start),v(n.finish)})}),c.on("ResizeEditor ResizeWindow ScrollContent",function(){i.get().each(h),u.get().each(v)}),c.on("TableSelectionClear",function(){t.get()&&(al(m),al(g),t.set(!1)),i.set(dt.none()),u.set(dt.none())}))},wM=function(i,u,a){u.delimiter||(u.delimiter="\xbb");return{dom:{tag:"div",classes:["tox-statusbar__path"],attributes:{role:"navigation"}},behaviours:rc([ig.config({mode:"flow",selector:"div[role=button]"}),mv.config({disabled:a.isDisabled}),Nv(),Yy.config({}),lg.config({}),fg("elementPathEvents",[sr(function(r,t){i.shortcuts.add("alt+F11","focus statusbar elementpath",function(){return ig.focusIn(r)}),i.on("NodeChange",function(t){var n,o,e=function(t){for(var n=[],e=t.length;0<e--;){var o=t[e];if(1===o.nodeType&&!function(t){if(1===t.nodeType){if("BR"===t.nodeName||t.getAttribute("data-mce-bogus"))return!0;if("bookmark"===t.getAttribute("data-mce-type"))return!0}return!1}(o)){var r=i.fire("ResolveName",{name:o.nodeName.toLowerCase(),target:o});if(r.isDefaultPrevented()||n.push({name:r.name,element:o}),r.isPropagationStopped())break}}return n}(t.parents);0<e.length?lg.set(r,(n=V(e||[],function(n,t){return dp.sketch({dom:{tag:"div",classes:["tox-statusbar__path-item"],attributes:{role:"button","data-index":t,"tab-index":-1,"aria-level":t+1},innerHtml:n.name},action:function(t){i.focus(),i.selection.select(n.element),i.nodeChanged()},buttonBehaviours:rc([jv(a.isDisabled),Nv()])})}),o={dom:{tag:"div",classes:["tox-statusbar__path-divider"],attributes:{"aria-hidden":!0},innerHtml:" "+u.delimiter+" "}},N(n.slice(1),function(t,n){var e=t;return e.push(o),e.push(n),e},[n[0]]))):lg.set(r,[])})})])]),components:[]}};(WE=UE=UE||{})[WE.None=0]="None",WE[WE.Both=1]="Both",WE[WE.Vertical=2]="Vertical";var SM,kM,CM,OM=function(t,n,e){var o,r,i,u,a,c,s=me.fromDom(t.getContainer()),l=(o=t,r=n,i=e,u=vu(s),a=Ou(s),(c={}).height=GB(u+r.top,bv(o),xv(o)),i===UE.Both&&(c.width=GB(a+r.left,vv(o),yv(o))),c);_t(l,function(t,n){return Yi(s,n,WB(t)),0}),t.fire("ResizeEditor")},_M=function(t,n,e,o){var r=xu(20*e,20*o);return OM(t,r,n),dt.some(!0)},TM=function(o,t){var n,e,r,i=(e=!(n=o).hasPlugin("autoresize"),!1===(r=n.getParam("resize",e))?UE.None:"both"===r?UE.Both:UE.Vertical);return i===UE.None?dt.none():dt.some({dom:{tag:"div",classes:["tox-statusbar__resize-handle"],attributes:{title:t.translate("Resize")},innerHtml:pp("resize-handle",t.icons)},behaviours:rc([hM.config({mode:"mouse",repositionTarget:!1,onDrag:function(t,n,e){return OM(o,e,i)},blockerClass:"tox-blocker"}),ig.config({mode:"special",onLeft:function(){return _M(o,i,-1,0)},onRight:function(){return _M(o,i,1,0)},onUp:function(){return _M(o,i,0,-1)},onDown:function(){return _M(o,i,0,1)}}),Yy.config({}),hg.config({})])})},EM=function(u,a){var t,n;return{dom:{tag:"div",classes:["tox-statusbar"]},components:(t=function(){var t,o,n,r,e,i=[];return u.getParam("elementpath",!0,"boolean")&&i.push(wM(u,{},a)),u.hasPlugin("wordcount")&&i.push((t=u,o=a,r=function(t,n,e){return lg.set(t,[lu(o.translate(["{0} "+e,n[e]]))])},dp.sketch({dom:{tag:"button",classes:["tox-statusbar__wordcount"]},components:[],buttonBehaviours:rc([jv(o.isDisabled),Nv(),Yy.config({}),lg.config({}),Ll.config({store:{mode:"memory",initialValue:{mode:"words",count:{words:0,characters:0}}}}),fg("wordcount-events",[dr(function(t){var n=Ll.getValue(t),e="words"===n.mode?"characters":"words";Ll.setValue(t,{mode:e,count:n.count}),r(t,n.count,e)}),sr(function(e){t.on("wordCountUpdate",function(t){var n=Ll.getValue(e).mode;Ll.setValue(e,{mode:n,count:t.wordCount}),r(e,t.wordCount,n)})})])]),eventOrder:((n={})[_o()]=["disabling","alloy.base.behaviour","wordcount-events"],n)}))),u.getParam("branding",!0,"boolean")&&i.push({dom:{tag:"span",classes:["tox-statusbar__branding"],innerHtml:'<a href="https://www.tiny.cloud/?utm_campaign=editor_referral&amp;utm_medium=poweredby&amp;utm_source=tinymce&amp;utm_content=v5" rel="noopener" target="_blank" tabindex="-1" aria-label="'+(e=Zh.translate(["Powered by {0}","Tiny"]))+'">'+e+"</a>"}}),0<i.length?[{dom:{tag:"div",classes:["tox-statusbar__text-container"]},components:i}]:[]}(),n=TM(u,a),t.concat(n.toArray()))}},DM=function(x){var t,n,e,o,r=x.inline,w=r?JB:jB,S=Rv(x)?oB:DD,i=dt.none(),u=je(),a=u.browser.isIE()?["tox-platform-ie"]:[],c=u.deviceType.isTouch()?["tox-platform-touch"]:[],s=Bv(x),l=Fv(x),f=Zh.isRtl()?{attributes:{dir:"rtl"}}:{},d={attributes:((t={})[Xc]=s?ta.BottomToTop:ta.TopToBottom,t)},k=function(){return i.bind(TB.getHeader)},m=function(){Yi(it.element,"width",document.body.clientWidth+"px")},C=mu((n=Ge(Ui(),l)&&"grid"===Ji(l,"display"),e={dom:ft({tag:"div",classes:["tox","tox-silver-sink","tox-tinymce-aux"].concat(a).concat(c)},f),behaviours:rc([Zs.config({useFixed:function(){return S.isDocked(k)}})])},o={dom:{styles:{width:document.body.clientWidth+"px"}},events:Zo([er(Vo(),m)])},zt(e,n?o:{}))),O=function(){return pt.value(C)},g=mp({dom:{tag:"div",classes:["tox-anchorbar"]}}),_=function(){return i.bind(function(t){return TB.getThrobber(t)}).getOrDie("Could not find throbber element")},T=$_(C,x,function(){return i.bind(function(t){return g.getOpt(t)}).getOrDie("Could not find a anchor bar element")}),p=TB.parts.menubar({dom:{tag:"div",classes:["tox-menubar"]},backstage:T,onEscape:function(){x.focus()}}),E=Tv(x),h=TB.parts.toolbar(ft({dom:{tag:"div",classes:["tox-toolbar"]},getSink:O,providers:T.shared.providers,onEscape:function(){x.focus()},type:E,lazyToolbar:function(){return i.bind(function(t){return TB.getToolbar(t)}).getOrDie("Could not find more toolbar element")},lazyHeader:function(){return k().getOrDie("Could not find header element")}},d)),v=TB.parts["multiple-toolbar"]({dom:{tag:"div",classes:["tox-toolbar-overlord"]},providers:T.shared.providers,onEscape:function(){x.focus()},type:E}),b=TB.parts.socket({dom:{tag:"div",classes:["tox-edit-area"]}}),y=TB.parts.sidebar({dom:{tag:"div",classes:["tox-sidebar"]}}),D=TB.parts.throbber({dom:{tag:"div",classes:["tox-throbber"]},backstage:T}),B=x.getParam("statusbar",!0,"boolean")&&!r?dt.some(EM(x,T.shared.providers)):dt.none(),A={dom:{tag:"div",classes:["tox-sidebar-wrap"]},components:[b,y]},M=Cv(x),F=Sv(x),I=wv(x),R=TB.parts.header({dom:ft({tag:"div",classes:["tox-editor-header"]},d),components:gt([I?[p]:[],M?[v]:F?[h]:[],Mv(x)?[]:[g.asSpec()]]),sticky:Rv(x),editor:x,sharedBackstage:T.shared}),V=gt([s?[]:[R],r?[]:[A],s?[R]:[]]),P=gt([[{dom:{tag:"div",classes:["tox-editor-container"]},components:V}],r?[]:B.toArray(),[D]]),H=Iv(x),z=ft(ft({role:"application"},Zh.isRtl()?{dir:"rtl"}:{}),H?{"aria-hidden":"true"}:{}),N=mu(TB.sketch({dom:{tag:"div",classes:["tox","tox-tinymce"].concat(r?["tox-tinymce-inline"]:[]).concat(s?["tox-tinymce--toolbar-bottom"]:[]).concat(c).concat(a),styles:ft({visibility:"hidden"},H?{opacity:"0",border:"0"}:{}),attributes:z},components:P,behaviours:rc([Nv(),mv.config({disableClass:"tox-tinymce--disabled"}),ig.config({mode:"cyclic",selector:".tox-menubar, .tox-toolbar, .tox-toolbar__primary, .tox-toolbar__overflow--open, .tox-sidebar__overflow--open, .tox-statusbar__path, .tox-statusbar__wordcount, .tox-statusbar__branding a, .tox-statusbar__resize-handle"})])})),i=dt.some(N);x.shortcuts.add("alt+F9","focus menubar",function(){TB.focusMenubar(N)}),x.shortcuts.add("alt+F10","focus toolbar",function(){TB.focusToolbar(N)}),x.addCommand("ToggleToolbarDrawer",function(){TB.toggleToolbarDrawer(N)}),x.addQueryStateHandler("ToggleToolbarDrawer",function(){return TB.isToolbarDrawerToggled(N)});var L,j,U,W,G,X,Y,q,K,J,$,Q,Z,tt,nt,et,ot,rt=Ly(N),it=Ly(C);L=x,j=rt,U=it,W=function(n,e){mt([j,U],function(t){t.broadcastEvent(n,e)})},G=function(n,e){mt([j,U],function(t){t.broadcastOn([n],e)})},X=function(t){return G(Sl(),{target:t.target})},Y=Sy(me.fromDom(document),"touchstart",X),q=Sy(me.fromDom(document),"touchmove",function(t){return W(Fo(),t)}),K=Sy(me.fromDom(document),"touchend",function(t){return W(Io(),t)}),J=Sy(me.fromDom(document),"mousedown",X),$=Sy(me.fromDom(document),"mouseup",function(t){0===t.raw.button&&G(Cl(),{target:t.target})}),Q=function(t){return G(Sl(),{target:me.fromDom(t.target)})},Z=function(t){0===t.button&&G(Cl(),{target:me.fromDom(t.target)})},tt=function(t){return W(Ro(),Cy(t))},nt=function(t){G(kl(),{}),W(Vo(),Cy(t))},et=function(){return G(kl(),{})},ot=function(t){t.state&&G(Sl(),{target:me.fromDom(L.getContainer())})},L.on("PostRender",function(){L.on("click",Q),L.on("tap",Q),L.on("mouseup",Z),L.on("ScrollWindow",tt),L.on("ResizeWindow",nt),L.on("ResizeEditor",et),L.on("AfterProgressState",ot)}),L.on("remove",function(){L.off("click",Q),L.off("tap",Q),L.off("mouseup",Z),L.off("ScrollWindow",tt),L.off("ResizeWindow",nt),L.off("ResizeEditor",et),L.off("AfterProgressState",ot),J.unbind(),Y.unbind(),q.unbind(),K.unbind(),$.unbind()}),L.on("detach",function(){fl(j),fl(U),j.destroy(),U.destroy()});var ut=function(){var t,n=WB(XB(x)),e=WB(YB(t=x).getOr(hv(t)));return x.inline||(Zi("div","width",e)&&Yi(N.element,"width",e),Zi("div","height",n)?Yi(N.element,"height",n):Yi(N.element,"height","200px")),n};return{mothership:rt,uiMothership:it,backstage:T,renderUI:function(){var o,r,e,n,i,u,a,c;S.setup(x,T.shared,k),sA(x,T),BA(x,O,T),r=(o=x).ui.registry.getAll().sidebars,mt(Ct(r),function(n){var t=r[n],e=function(){return dt.from(o.queryCommandValue("ToggleSidebar")).is(n)};o.ui.registry.addToggleButton(n,{icon:t.icon,tooltip:t.tooltip,onAction:function(t){o.execCommand("ToggleSidebar",!1,n),t.setActive(e())},onSetup:function(t){var n=function(){return t.setActive(e())};return o.on("ToggleSidebar",n),function(){o.off("ToggleSidebar",n)}}})}),e=x,n=_,i=T.shared,u=fe(!1),a=fe(dt.none()),c=function(t){t!==u.get()&&(yB(n(),t,i.providers),u.set(t),e.fire("AfterProgressState",{state:t}))},e.on("ProgressState",function(t){var n;a.get().each(fp.clearTimeout),at(t.time)?(n=fp.setEditorTimeout(e,function(){return c(t.state)},t.time),a.set(dt.some(n))):(c(t.state),a.set(dt.none()))}),Tt(x.getParam("toolbar_groups",{},"object"),function(t,n){x.ui.registry.addGroupToolbarButton(n,t)});var t,s=x.ui.registry.getAll(),l=s.buttons,f=s.menuItems,d=s.contextToolbars,m=s.sidebars,g=kv(x),p={menuItems:f,menus:(t=x.getParam("menu"))?Tt(t,function(t){return ft(ft({},t),{items:t.items})}):{},menubar:x.getParam("menubar"),toolbar:g.getOrThunk(function(){return x.getParam("toolbar",!0)}),allowToolbarGroups:E===oh.floating,buttons:l,sidebar:m};wD(x,d,C,{backstage:T}),xM(x,C);var h=x.getElement(),v=ut(),b={mothership:rt,uiMothership:it,outerContainer:N},y={targetNode:h,height:v};return w.render(x,b,p,T,y)},getUi:function(){return{channels:{broadcastAll:it.broadcast,broadcastOn:it.broadcastOn,register:ct}}}}},BM=st([Ln("lazySink"),Kn("dragBlockClass"),ae("getBounds",Nu),ne("useTabstopAt",O),ne("eventOrder",{}),jl("modalBehaviours",[ig]),ga("onExecute"),ha("onEscape")]),AM={sketch:lt},MM=st([xf({name:"draghandle",overrides:function(t,n){return{behaviours:rc([hM.config({mode:"mouse",getTarget:function(t){return Yu(t,'[role="dialog"]').getOr(t)},blockerClass:t.dragBlockClass.getOrDie(new Error("The drag blocker class was not specified for a dialog with a drag handle: \n"+JSON.stringify(n,null,2)).message),getBounds:t.getDragBounds})])}}}),bf({schema:[Ln("dom")],name:"title"}),bf({factory:AM,schema:[Ln("dom")],name:"close"}),bf({factory:AM,schema:[Ln("dom")],name:"body"}),xf({factory:AM,schema:[Ln("dom")],name:"footer"}),yf({factory:{sketch:function(t,n){return ft(ft({},t),{dom:n.dom,components:n.components})}},schema:[ne("dom",{tag:"div",styles:{position:"fixed",left:"0px",top:"0px",right:"0px",bottom:"0px"}}),ne("components",[])],name:"blocker"})]),FM=/* */Object.freeze({__proto__:null,block:function(t,n,e,o){Yr(t.element,"aria-busy",!0);var r=n.getRoot(t).getOr(t),i=rc([ig.config({mode:"special",onTab:function(){return dt.some(!0)},onShiftTab:function(){return dt.some(!0)}}),hg.config({})]),u=o(r,i),a=r.getSystem().build(u);lg.append(r,gu(a)),a.hasConfigured(ig)&&ig.focusIn(a),e.isBlocked()||n.onBlock(t),e.blockWith(function(){return lg.remove(r,a)})},unblock:function(t,n,e){$r(t.element,"aria-busy"),e.isBlocked()&&n.onUnblock(t),e.clear()}}),IM=[ae("getRoot",dt.none),ma("onBlock"),ma("onUnblock")],RM=uc({fields:IM,name:"blocking",apis:FM,state:/* */Object.freeze({__proto__:null,init:function(){var n=rD();return xi({readState:n.isSet,blockWith:function(t){n.set({destroy:t})},clear:n.clear,isBlocked:n.isSet})}})}),VM=Yf({name:"ModalDialog",configFields:BM(),partFields:MM(),factory:function(a,t,n,r){var e,i=fe(dt.none()),o=oi("modal-events"),u=ft(ft({},a.eventOrder),((e={})[Po()]=[o].concat(a.eventOrder["alloy.system.attached"]||[]),e));return{uid:a.uid,dom:a.dom,components:t,apis:{show:function(t){i.set(dt.some(t));var n=a.lazySink(t).getOrDie(),e=r.blocker(),o=n.getSystem().build(ft(ft({},e),{components:e.components.concat([gu(t)]),behaviours:rc([hg.config({}),fg("dialog-blocker-events",[cr(lo(),function(){ig.focusIn(t)})])])}));rl(n,o),ig.focusIn(t)},hide:function(n){i.set(dt.none()),kr(n.element).each(function(t){n.getSystem().getByDom(t).each(function(t){al(t)})})},getBody:function(t){return Ff(t,a,"body")},getFooter:function(t){return Ff(t,a,"footer")},setIdle:function(t){RM.unblock(t)},setBusy:function(t,n){RM.block(t,n)}},eventOrder:u,domModification:{attributes:{role:"dialog","aria-modal":"true"}},behaviours:Wl(a.modalBehaviours,[lg.config({}),ig.config({mode:"cyclic",onEnter:a.onExecute,onEscape:a.onEscape,useTabstopAt:a.useTabstopAt}),RM.config({getRoot:i.get}),fg(o,[sr(function(t){var n,e,o,r,i,u;n=t.element,e=Ff(t,a,"title").element,o=Kr(n,"id").fold(function(){var t=oi("dialog-label");return Yr(e,"id",t),t},lt),Yr(n,"aria-labelledby",o),r=t.element,i=Ff(t,a,"body").element,u=dt.from(qr(r,"id")).fold(function(){var t=oi("dialog-describe");return Yr(i,"id",t),t},lt),Yr(r,"aria-describedby",u)})])])}},apis:{show:function(t,n){t.show(n)},hide:function(t,n){t.hide(n)},getBody:function(t,n){return t.getBody(n)},getFooter:function(t,n){return t.getFooter(n)},setBusy:function(t,n,e){t.setBusy(n,e)},setIdle:function(t,n){t.setIdle(n)}}}),PM=fn([Un("type"),Un("name")].concat(Ih)),HM=Vn,zM=[bn("name","name",Xt(function(){return oi("button-name")}),Rn),Qn("icon"),ie("align","end",["start","end"]),ue("primary",!1),ue("disabled",!1)],NM=T(zM,[Un("text")]),LM=T([Wn("type",["submit","cancel","custom"])],NM),jM=T([Wn("type",["menu"]),Qn("text"),Qn("tooltip"),Qn("icon"),qn("items",PM)],zM),UM=An("type",{submit:LM,cancel:LM,custom:LM,menu:jM}),WM=[Un("type"),Un("text"),Wn("level",["info","warn","error","success"]),Un("icon"),ne("url","")],GM=fn(WM),XM=[Un("type"),Un("text"),ue("disabled",!1),ue("primary",!1),bn("name","name",Xt(function(){return oi("button-name")}),Rn),Qn("icon"),ue("borderless",!1)],YM=fn(XM),qM=[Un("type"),Un("name"),Un("label"),ue("disabled",!1)],KM=fn(qM),JM=Vn,$M=[Un("type"),Un("name")],QM=$M.concat([Qn("label")]),ZM=QM.concat([ne("columns","auto")]),tF=fn(ZM),nF=Sn([Un("value"),Un("text"),Un("icon")]),eF=fn(QM),oF=Rn,rF=fn(QM),iF=Rn,uF=$M.concat([re("tag","textarea"),Un("scriptId"),Un("scriptUrl"),(SM=undefined,ee("settings",SM,zn))]),aF=$M.concat([re("tag","textarea"),Gn("init")]),cF=Cn(function(t){return _n("customeditor.old",ln(aF),t).orThunk(function(){return _n("customeditor.new",ln(uF),t)})}),sF=Rn,lF=fn(QM),fF=dn(wn),dF=function(t){return[Un("type"),jn("columns",In),t]},mF=[Un("type"),Un("html"),ie("presets","presentation",["presentation","document"])],gF=fn(mF),pF=QM.concat([ue("sandboxed",!0)]),hF=fn(pF),vF=Rn,bF=QM.concat([jn("currentState",fn([Ln("blob"),Un("url")]))]),yF=fn(bF),xF=QM.concat([Qn("inputMode"),Qn("placeholder"),ue("maximized",!1),ue("disabled",!1)]),wF=fn(xF),SF=Rn,kF=[Un("text"),Un("value")],CF=[Un("text"),qn("items",(kM=function(){return OF},CM=Lt(function(){return kM()}),{extract:function(t,n,e){return CM().extract(t,n,e)},toString:function(){return CM().toString()}}))],OF=mn([fn(kF),fn(CF)]),_F=QM.concat([qn("items",OF),ue("disabled",!1)]),TF=fn(_F),EF=Rn,DF=QM.concat([Yn("items",[Un("text"),Un("value")]),oe("size",1),ue("disabled",!1)]),BF=fn(DF),AF=Rn,MF=QM.concat([ue("constrain",!0),ue("disabled",!1)]),FF=fn(MF),IF=fn([Un("width"),Un("height")]),RF=[Un("type"),qn("header",Rn),qn("cells",dn(Rn))],VF=fn(RF),PF=QM.concat([Qn("placeholder"),ue("maximized",!1),ue("disabled",!1)]),HF=fn(PF),zF=Rn,NF=QM.concat([ie("filetype","file",["image","media","file"]),ne("disabled",!1)]),LF=fn(NF),jF=fn([Un("value"),ne("meta",{})]),UF=function(n){return bn("items","items",Wt(),dn(Cn(function(t){return _n("Checking item of "+n,WF,t).fold(function(t){return pt.error(Dn(t))},function(t){return pt.value(t)})})))},WF=kn(function(){return Bn("type",{alertbanner:GM,bar:fn((n=UF("bar"),[Un("type"),n])),button:YM,checkbox:KM,colorinput:eF,colorpicker:rF,dropzone:lF,grid:fn(dF(UF("grid"))),iframe:hF,input:wF,listbox:TF,selectbox:BF,sizeinput:FF,textarea:HF,urlinput:LF,customeditor:cF,htmlpanel:gF,imagetools:yF,collection:tF,label:fn((t=UF("label"),[Un("type"),Un("label"),t])),table:VF,panel:XF});var t,n}),GF=[Un("type"),ne("classes",[]),qn("items",WF)],XF=fn(GF),YF=[bn("name","name",Xt(function(){return oi("tab-name")}),Rn),Un("title"),qn("items",WF)],qF=[Un("type"),Yn("tabs",YF)],KF=fn(qF),JF=NM,$F=UM,QF=fn([Un("title"),jn("body",Bn("type",{panel:XF,tabpanel:KF})),re("size","normal"),qn("buttons",$F),ne("initialData",{}),ae("onAction",ct),ae("onChange",ct),ae("onSubmit",ct),ae("onClose",ct),ae("onCancel",ct),ne("onTabChange",ct)]),ZF=fn(T([Wn("type",["cancel","custom"])],JF)),tI=fn([Un("title"),Un("url"),$n("height"),$n("width"),Jn("buttons",dn(ZF)),ae("onAction",ct),ae("onCancel",ct),ae("onClose",ct),ae("onMessage",ct)]),nI=function(t){return w(t)?[t].concat(U(Mt(t),nI)):c(t)?U(t,nI):[]},eI=function(t){return x(t.type)&&x(t.name)},oI={checkbox:JM,colorinput:oF,colorpicker:iF,dropzone:fF,input:SF,iframe:vF,sizeinput:IF,selectbox:AF,listbox:EF,size:IF,textarea:zF,urlinput:jF,customeditor:sF,collection:nF,togglemenuitem:HM},rI=function(t){var n=H(nI(t),eI),e=U(n,function(n){return t=n,dt.from(oI[t.type]).fold(function(){return[]},function(t){return[jn(n.name,t)]});var t});return fn(e)},iI=function(t){return{internalDialog:Tn(_n("dialog",QF,t)),dataValidator:rI(t),initialData:t.initialData}},uI={open:function(t,n){var e=iI(n);return t(e.internalDialog,e.initialData,e.dataValidator)},openUrl:function(t,n){return t(Tn(_n("dialog",tI,n)))},redial:iI},aI=function(t){var e=[],o={};return _t(t,function(t,n){t.fold(function(){e.push(n)},function(t){o[n]=t})}),0<e.length?pt.error(e):pt.value(o)},cI=Xf({name:"TabButton",configFields:[ne("uid",undefined),Ln("value"),bn("dom","dom",Yt(function(){return{attributes:{role:"tab",id:oi("aria"),"aria-selected":"false"}}}),Mn()),Kn("action"),ne("domModification",{}),jl("tabButtonBehaviours",[hg,ig,Ll]),Ln("view")],factory:function(t,n){return{uid:t.uid,dom:t.dom,components:t.components,events:Bg(t.action),behaviours:Wl(t.tabButtonBehaviours,[hg.config({}),ig.config({mode:"execution",useSpace:!0,useEnter:!0}),Ll.config({store:{mode:"memory",initialValue:t.value}})]),domModification:t.domModification}}}),sI=st([Ln("tabs"),Ln("dom"),ne("clickToDismiss",!1),jl("tabbarBehaviours",[ld,ig]),fa(["tabClass","selectedClass"])]),lI=wf({factory:cI,name:"tabs",unit:"tab",overrides:function(o){var r=function(t,n){ld.dehighlight(t,n),qo(t,Wo(),{tabbar:t,button:n})},i=function(t,n){ld.highlight(t,n),qo(t,Uo(),{tabbar:t,button:n})};return{action:function(t){var n=t.getSystem().getByUid(o.uid).getOrDie(),e=ld.isHighlighted(n,t);(e&&o.clickToDismiss?r:e?ct:i)(n,t)},domModification:{classes:[o.markers.tabClass]}}}}),fI=st([lI]),dI=Yf({name:"Tabbar",configFields:sI(),partFields:fI(),factory:function(t,n,e,o){return{uid:t.uid,dom:t.dom,components:n,"debug.sketcher":"Tabbar",domModification:{attributes:{role:"tablist"}},behaviours:Wl(t.tabbarBehaviours,[ld.config({highlightClass:t.markers.selectedClass,itemClass:t.markers.tabClass,onHighlight:function(t,n){Yr(n.element,"aria-selected","true")},onDehighlight:function(t,n){Yr(n.element,"aria-selected","false")}}),ig.config({mode:"flow",getInitial:function(t){return ld.getHighlighted(t).map(function(t){return t.element})},selector:"."+t.markers.tabClass,executeOnMove:!0})])}}}),mI=Xf({name:"Tabview",configFields:[jl("tabviewBehaviours",[lg])],factory:function(t,n){return{uid:t.uid,dom:t.dom,behaviours:Wl(t.tabviewBehaviours,[lg.config({})]),domModification:{attributes:{role:"tabpanel"}}}}}),gI=st([ne("selectFirst",!0),ma("onChangeTab"),ma("onDismissTab"),ne("tabs",[]),jl("tabSectionBehaviours",[])]),pI=bf({factory:dI,schema:[Ln("dom"),Xn("markers",[Ln("tabClass"),Ln("selectedClass")])],name:"tabbar",defaults:function(t){return{tabs:t.tabs}}}),hI=bf({factory:mI,name:"tabview"}),vI=st([pI,hI]),bI=Yf({name:"TabSection",configFields:gI(),partFields:vI(),factory:function(i,t,n,e){var o=function(t,n){Mf(t,i,"tabbar").each(function(t){n(t).each(Ko)})};return{uid:i.uid,dom:i.dom,components:t,behaviours:Ul(i.tabSectionBehaviours),events:Zo(gt([i.selectFirst?[sr(function(t,n){o(t,ld.getFirst)})]:[],[er(Uo(),function(t,n){var o,r,e=n.event.button;o=e,r=Ll.getValue(o),Mf(o,i,"tabview").each(function(e){L(i.tabs,function(t){return t.value===r}).each(function(t){var n=t.view();Kr(o.element,"id").each(function(t){Yr(e.element,"aria-labelledby",t)}),lg.set(e,n),i.onChangeTab(e,o,n)})})}),er(Wo(),function(t,n){var e=n.event.button;i.onDismissTab(t,e)})]])),apis:{getViewItems:function(t){return Mf(t,i,"tabview").map(function(t){return lg.contents(t)}).getOr([])},showTab:function(t,e){o(t,function(n){var t=ld.getCandidates(n);return L(t,function(t){return Ll.getValue(t)===e}).filter(function(t){return!ld.isHighlighted(n,t)})})}}}},apis:{getViewItems:function(t,n){return t.getViewItems(n)},showTab:function(t,n,e){t.showTab(n,e)}}}),yI=function(t,n){Yi(t,"height",n+"px"),je().browser.isIE()?tu(t,"flex-basis"):Yi(t,"flex-basis",n+"px")},xI=function(t,m,n){Yu(t,'[role="dialog"]').each(function(d){qu(d,'[role="tablist"]').each(function(f){n.get().map(function(t){return Yi(m,"height","0"),Yi(m,"flex-basis","0"),Math.min(t,(e=m,o=f,r=wr(n=d).dom,i=Yu(n,".tox-dialog-wrap").getOr(n),u="fixed"===Ji(i,"position")?Math.max(r.clientHeight,window.innerHeight):Math.max(r.offsetHeight,r.scrollHeight),a=vu(e),c=e.dom.offsetLeft>=o.dom.offsetLeft+Ou(o)?Math.max(vu(o),a):a,s=parseInt(Ji(n,"margin-top"),10)||0,l=parseInt(Ji(n,"margin-bottom"),10)||0,u-(vu(n)+s+l-c)));var n,e,o,r,i,u,a,c,s,l}).each(function(t){yI(m,t)})})})},wI=function(t){return qu(t,'[role="tabpanel"]')},SI=function(a){var c;return{smartTabHeight:(c=fe(dt.none()),{extraEvents:[sr(function(t){var e=t.element;wI(e).each(function(u){var n;Yi(u,"visibility","hidden"),t.getSystem().getByDom(u).toOptional().each(function(t){var o,r,i,n=(r=u,i=t,V(o=a,function(t,n){lg.set(i,o[n].view());var e=r.dom.getBoundingClientRect();return lg.set(i,[]),e.height})),e=K(Y(n,function(t,n){return n<t?-1:t<n?1:0}));c.set(e)}),xI(e,u,c),tu(u,"visibility"),n=t,K(a).each(function(t){return bI.showTab(n,t.value)}),fp.requestAnimationFrame(function(){xI(e,u,c)})})}),er(Vo(),function(t){var n=t.element;wI(n).each(function(t){xI(n,t,c)})}),er(ax,function(t,n){var r=t.element;wI(r).each(function(n){var t=mc(Ar(n));Yi(n,"visibility","hidden");var e=Qi(n,"height").map(function(t){return parseInt(t,10)});tu(n,"height"),tu(n,"flex-basis");var o=n.dom.getBoundingClientRect().height;e.forall(function(t){return t<o})?(c.set(dt.from(o)),xI(r,n,c)):e.each(function(t){yI(n,t)}),tu(n,"visibility"),t.each(dc)})})],selectFirst:!1}),naiveTabHeight:{extraEvents:[],selectFirst:!0}}},kI="send-data-to-section",CI="send-data-to-view",OI=oi("update-dialog"),_I=oi("update-title"),TI=oi("update-body"),EI=oi("update-footer"),DI=oi("body-send-message"),BI=function(t,n,d,e){return{dom:{tag:"div",classes:["tox-dialog__content-js"],attributes:ft(ft({},n.map(function(t){return{id:t}}).getOr({})),e?{"aria-live":"polite"}:{})},components:[],behaviours:rc([sk(0),aE.config({channel:TI,updateState:function(t,n){return dt.some({isTabPanel:function(){return"tabpanel"===n.body.type}})},renderComponents:function(t){switch(t.body.type){case"tabpanel":return[(r=t.body,i=d,u=fe({}),a=function(t){var n=Ll.getValue(t),e=aI(n).getOr({}),o=u.get(),r=zt(o,e);u.set(r)},c=function(t){var n=u.get();Ll.setValue(t,n)},s=fe(null),l=V(r.tabs,function(t){return{value:t.name,dom:{tag:"div",classes:["tox-dialog__body-nav-item"],innerHtml:i.shared.providers.translate(t.title)},view:function(){return[ZS.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"]},components:V(t.items,function(t){return f_(n,t,i)}),formBehaviours:rc([ig.config({mode:"acyclic",useTabstopAt:C(Ek)}),fg("TabView.form.events",[sr(c),lr(a)]),lc.config({channels:$t([{key:kI,value:{onReceive:a}},{key:CI,value:{onReceive:c}}])})])}})]}}}),f=SI(l).smartTabHeight,bI.sketch({dom:{tag:"div",classes:["tox-dialog__body"]},onChangeTab:function(t,n,e){var o=Ll.getValue(n);qo(t,ux,{name:o,oldName:s.get()}),s.set(o)},tabs:l,components:[bI.parts.tabbar({dom:{tag:"div",classes:["tox-dialog__body-nav"]},components:[dI.parts.tabs({})],markers:{tabClass:"tox-tab",selectedClass:"tox-dialog__body-nav-item--active"},tabbarBehaviours:rc([Yy.config({})])}),bI.parts.tabview({dom:{tag:"div",classes:["tox-dialog__body-content"]}})],selectFirst:f.selectFirst,tabSectionBehaviours:rc([fg("tabpanel",f.extraEvents),ig.config({mode:"acyclic"}),Zf.config({find:function(t){return K(bI.getViewItems(t))}}),Ll.config({store:{mode:"manual",getValue:function(t){return t.getSystem().broadcastOn([kI],{}),u.get()},setValue:function(t,n){u.set(n),t.getSystem().broadcastOn([CI],{})}}})])}))];default:return[(e=t.body,o=d,{dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[(n=mp(ZS.sketch(function(n){return{dom:{tag:"div",classes:["tox-form"].concat(e.classes)},components:V(e.items,function(t){return f_(n,t,o)})}}))).asSpec()]}],behaviours:rc([ig.config({mode:"acyclic",useTabstopAt:C(Ek)}),ck(n),vk(n,{postprocess:function(t){return aI(t).fold(function(t){return console.error(t),{}},function(t){return t})}})])})]}var e,o,n,r,i,u,a,c,s,l,f},initialData:t})])}},AI=Qv.deviceType.isTouch(),MI=function(t,n){return{dom:{tag:"div",styles:{display:"none"},classes:["tox-dialog__header"]},components:[t,n]}},FI=function(t,n){return VM.parts.close(dp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":n.translate("Close")}},action:t,buttonBehaviours:rc([Yy.config({})])}))},II=function(){return VM.parts.title({dom:{tag:"div",classes:["tox-dialog__title"],innerHtml:"",styles:{display:"none"}}})},RI=function(t,n){return VM.parts.body({dom:{tag:"div",classes:["tox-dialog__body"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-content"]},components:[{dom:bB("<p>"+n.translate(t)+"</p>")}]}]})},VI=function(t){return VM.parts.footer({dom:{tag:"div",classes:["tox-dialog__footer"]},components:t})},PI=function(t,n){return[Ny.sketch({dom:{tag:"div",classes:["tox-dialog__footer-start"]},components:t}),Ny.sketch({dom:{tag:"div",classes:["tox-dialog__footer-end"]},components:n})]},HI=function(n){var t,e="tox-dialog",o=e+"-wrap",r=o+"__backdrop",i=e+"__disable-scroll";return VM.sketch({lazySink:n.lazySink,onEscape:function(t){return n.onEscape(t),dt.some(!0)},useTabstopAt:function(t){return!Ek(t)},dom:{tag:"div",classes:[e].concat(n.extraClasses),styles:ft({position:"relative"},n.extraStyles)},components:T([n.header,n.body],n.footer.toArray()),parts:{blocker:{dom:bB('<div class="'+o+'"></div>'),components:[{dom:{tag:"div",classes:AI?[r,r+"--opaque"]:[r]}}]}},dragBlockClass:o,modalBehaviours:rc(T([hg.config({}),fg("dialog-events",n.dialogEvents.concat([cr(lo(),function(t,n){ig.focusIn(t)})])),fg("scroll-lock",[sr(function(){Ri(Ui(),i)}),lr(function(){Pi(Ui(),i)})])],n.extraBehaviours)),eventOrder:ft(((t={})[_o()]=["dialog-events"],t[Po()]=["scroll-lock","dialog-events","alloy.base.behaviour"],t[Ho()]=["alloy.base.behaviour","dialog-events","scroll-lock"],t),n.eventOrder)})},zI=function(t){return dp.sketch({dom:{tag:"button",classes:["tox-button","tox-button--icon","tox-button--naked"],attributes:{type:"button","aria-label":t.translate("Close"),title:t.translate("Close")}},components:[{dom:{tag:"div",classes:["tox-icon"],innerHtml:pp("close",t.icons)}}],action:function(t){Yo(t,nx)}})},NI=function(t,n,e){var o=function(t){return[lu(e.translate(t.title))]};return{dom:{tag:"div",classes:["tox-dialog__title"],attributes:ft({},n.map(function(t){return{id:t}}).getOr({}))},components:o(t),behaviours:rc([aE.config({channel:_I,renderComponents:o})])}},LI=function(){return{dom:bB('<div class="tox-dialog__draghandle"></div>')}},jI=function(t,n){return e={title:n.shared.providers.translate(t),draggable:n.dialog.isDraggableModal()},o=n.shared.providers,r=VM.parts.title(NI(e,dt.none(),o)),i=VM.parts.draghandle(LI()),u=VM.parts.close(zI(o)),a=[r].concat(e.draggable?[i]:[]).concat([u]),Ny.sketch({dom:bB('<div class="tox-dialog__header"></div>'),components:a});var e,o,r,i,u,a},UI=function(t,n,e){return{dom:{tag:"div",classes:["tox-dialog__busy-spinner"],attributes:{"aria-label":e.translate(t)},styles:{left:"0px",right:"0px",bottom:"0px",top:"0px",position:"absolute"}},behaviours:n,components:[{dom:bB('<div class="tox-spinner"><div></div><div></div><div></div></div>')}]}},WI=function(t,o,n){return{onClose:function(){return n.closeWindow()},onBlock:function(e){VM.setBusy(t(),function(t,n){return UI(e.message,n,o)})},onUnblock:function(){VM.setIdle(t())}}},GI=function(t,n,e,o){var r;return mu(HI(ft(ft({},t),{lazySink:o.shared.getSink,extraBehaviours:T([aE.config({channel:OI,updateState:function(t,n){return dt.some(n)},initialData:n}),xk({})],t.extraBehaviours),onEscape:function(t){Yo(t,nx)},dialogEvents:e,eventOrder:((r={})[Oo()]=[aE.name(),lc.name()],r[Po()]=["scroll-lock",aE.name(),"messages","dialog-events","alloy.base.behaviour"],r[Ho()]=["alloy.base.behaviour","dialog-events","messages",aE.name(),"scroll-lock"],r)})))},XI=function(t){return V(t,function(t){return"menu"===t.type?(e=V((n=t).items,function(t){var n=fe(!1);return ft(ft({},t),{storage:n})}),ft(ft({},n),{items:e})):t;var n,e})},YI=function(t){return N(t,function(t,n){return"menu"!==n.type?t:N(n.items,function(t,n){return t[n.name]=n.storage,t},t)},{})},qI=function(t,e){return[ur(lo(),Tk),t(tx,function(t,n){e.onClose(),n.onClose()}),t(nx,function(t,n,e,o){n.onCancel(t),Yo(o,tx)}),er(ix,function(t,n){return e.onUnblock()}),er(rx,function(t,n){return e.onBlock(n.event)})]},KI=function(i,t){var n=function(t,r){return er(t,function(e,o){u(e,function(t,n){r(i(),t,o.event,e)})})},u=function(n,e){aE.getState(n).get().each(function(t){e(t,n)})};return T(qI(n,t),[n(ex,function(t,n,e){n.onAction(t,{name:e.name})})])},JI=function(i,t,c){var n=function(t,r){return er(t,function(e,o){u(e,function(t,n){r(i(),t,o.event,e)})})},u=function(n,e){aE.getState(n).get().each(function(t){e(t.internalDialog,n)})};return T(qI(n,t),[n(ox,function(t,n){return n.onSubmit(t)}),n(Zy,function(t,n,e){n.onChange(t,{name:e.name})}),n(ex,function(t,n,e,o){var r=function(){return ig.focusIn(o)},i=function(t){return Jr(t,"disabled")||Kr(t,"aria-disabled").exists(function(t){return"true"===t})},u=Ar(o.element),a=mc(u);n.onAction(t,{name:e.name,value:e.value}),mc(u).fold(r,function(n){i(n)||a.exists(function(t){return Ye(n,t)&&i(t)})?r():c().toOptional().filter(function(t){return!Ye(t.element,n)}).each(r)})}),n(ux,function(t,n,e){n.onTabChange(t,{newTabName:e.name,oldTabName:e.oldName})}),lr(function(t){var n=i();Ll.setValue(t,n.getData())})])},$I=function(t,n){var e=n.map(function(t){return t.footerButtons}).getOr([]),o=P(e,function(t){return"start"===t.align}),r=function(t,n){return Ny.sketch({dom:{tag:"div",classes:["tox-dialog__footer-"+t]},components:V(n,function(t){return t.memento.asSpec()})})};return[r("start",o.pass),r("end",o.fail)]},QI=function(t,i){return{dom:bB('<div class="tox-dialog__footer"></div>'),components:[],behaviours:rc([aE.config({channel:EI,initialData:t,updateState:function(t,n){var r=V(n.buttons,function(t){var n,e,o=mp((e=i,ZC(n=t,n.type,e)));return{name:t.name,align:t.align,memento:o}});return dt.some({lookupByName:function(t,n){return e=t,o=n,L(r,function(t){return t.name===o}).bind(function(t){return t.memento.getOpt(e)});var e,o},footerButtons:r})},renderComponents:$I})])}},ZI=QI,tR=function(t,n){return VM.parts.footer(QI(t,n))},nR=function(n,e){if(n.getRoot().getSystem().isConnected()){var o=Zf.getCurrent(n.getFormWrapper()).getOr(n.getFormWrapper());return ZS.getField(o,e).fold(function(){var t=n.getFooter();return aE.getState(t).get().bind(function(t){return t.lookupByName(o,e)})},function(t){return dt.some(t)})}return dt.none()},eR=function(c,o,s){var t=function(t){var n=c.getRoot();n.getSystem().isConnected()&&t(n)},l={getData:function(){var t=c.getRoot(),n=t.getSystem().isConnected()?c.getFormWrapper():t,e=Ll.getValue(n),o=Tt(s,function(t){return t.get()});return ft(ft({},e),o)},setData:function(a){t(function(t){var n,e,o=l.getData(),r=ft(ft({},o),a),i=(n=r,e=c.getRoot(),aE.getState(e).get().map(function(t){return Tn(_n("data",t.dataValidator,n))}).getOr(n)),u=c.getFormWrapper();Ll.setValue(u,i),_t(s,function(t,n){It(r,n)&&t.set(r[n])})})},disable:function(t){nR(c,t).each(mv.disable)},enable:function(t){nR(c,t).each(mv.enable)},focus:function(t){nR(c,t).each(hg.focus)},block:function(n){if(!x(n))throw new Error("The dialogInstanceAPI.block function should be passed a blocking message of type string as an argument");t(function(t){qo(t,rx,{message:n})})},unblock:function(){t(function(t){Yo(t,ix)})},showTab:function(e){t(function(t){var n=c.getBody();aE.getState(n).get().exists(function(t){return t.isTabPanel()})&&Zf.getCurrent(n).each(function(t){bI.showTab(t,e)})})},redial:function(e){t(function(t){var n=o(e);t.getSystem().broadcastOn([OI],n),t.getSystem().broadcastOn([_I],n.internalDialog),t.getSystem().broadcastOn([TI],n.internalDialog),t.getSystem().broadcastOn([EI],n.internalDialog),l.setData(n.initialData)})},close:function(){t(function(t){Yo(t,tx)})}};return l},oR=function(t,n,e){var o,r,i,u=jI(t.internalDialog.title,e),a=(o={body:t.internalDialog.body},r=e,i=BI(o,dt.none(),r,!1),VM.parts.body(i)),c=XI(t.internalDialog.buttons),s=YI(c),l=tR({buttons:c},e),f=JI(function(){return p},WI(function(){return g},e.shared.providers,n),e.shared.getSink),d=function(t){switch(t){case"large":return["tox-dialog--width-lg"];case"medium":return["tox-dialog--width-md"];default:return[]}}(t.internalDialog.size),m={header:u,body:a,footer:dt.some(l),extraClasses:d,extraBehaviours:[],extraStyles:{}},g=GI(m,t,f,e),p=eR({getRoot:function(){return g},getBody:function(){return VM.getBody(g)},getFooter:function(){return VM.getFooter(g)},getFormWrapper:function(){var t=VM.getBody(g);return Zf.getCurrent(t).getOr(t)}},n.redial,s);return{dialog:g,instanceApi:p}},rR=function(t,n,o,e){var r,i,u,a,c,s,l,f,d,m=oi("dialog-label"),g=oi("dialog-content"),p=mp((u={title:t.internalDialog.title,draggable:!0},a=m,c=o.shared.providers,Ny.sketch({dom:bB('<div class="tox-dialog__header"></div>'),components:[NI(u,dt.some(a),c),LI(),zI(c)],containerBehaviours:rc([hM.config({mode:"mouse",blockerClass:"blocker",getTarget:function(t){return Ku(t,'[role="dialog"]').getOrDie()},snaps:{getSnapPoints:function(){return[]},leftAttr:"data-drag-left",topAttr:"data-drag-top"}})])}))),h=mp((s={body:t.internalDialog.body},l=g,f=o,d=e,BI(s,dt.some(l),f,d))),v=XI(t.internalDialog.buttons),b=YI(v),y=mp(ZI({buttons:v},o)),x=JI(function(){return S},{onBlock:function(e){RM.block(w,function(t,n){return UI(e.message,n,o.shared.providers)})},onUnblock:function(){RM.unblock(w)},onClose:function(){return n.closeWindow()}},o.shared.getSink),w=mu({dom:{tag:"div",classes:["tox-dialog","tox-dialog-inline"],attributes:((r={role:"dialog"})["aria-labelledby"]=m,r["aria-describedby"]=""+g,r)},eventOrder:((i={})[Oo()]=[aE.name(),lc.name()],i[_o()]=["execute-on-form"],i[Po()]=["reflecting","execute-on-form"],i),behaviours:rc([ig.config({mode:"cyclic",onEscape:function(t){return Yo(t,tx),dt.some(!0)},useTabstopAt:function(t){return!Ek(t)&&("button"!==mr(t)||"disabled"!==qr(t,"disabled"))}}),aE.config({channel:OI,updateState:function(t,n){return dt.some(n)},initialData:t}),hg.config({}),fg("execute-on-form",x.concat([cr(lo(),function(t,n){ig.focusIn(t)})])),RM.config({getRoot:function(){return dt.some(w)}}),lg.config({}),xk({})]),components:[p.asSpec(),h.asSpec(),y.asSpec()]}),S=eR({getRoot:function(){return w},getFooter:function(){return y.get(w)},getBody:function(){return h.get(w)},getFormWrapper:function(){var t=h.get(w);return Zf.getCurrent(t).getOr(t)}},n.redial,b);return{dialog:w,instanceApi:S}},iR=tinymce.util.Tools.resolve("tinymce.util.URI"),uR=["insertContent","setContent","execCommand","close","block","unblock"],aR=function(t){return w(t)&&-1!==uR.indexOf(t.mceAction)},cR=function(o,t,r,n){var e,i,u,a,c=jI(o.title,n),s=(i={dom:{tag:"div",classes:["tox-dialog__content-js"]},components:[{dom:{tag:"div",classes:["tox-dialog__body-iframe"]},components:[Ok({dom:{tag:"iframe",attributes:{src:o.url}},behaviours:rc([Yy.config({}),hg.config({})])})]}],behaviours:rc([ig.config({mode:"acyclic",useTabstopAt:C(Ek)})])},VM.parts.body(i)),l=o.buttons.bind(function(t){return 0===t.length?dt.none():dt.some(tR({buttons:t},n))}),f=KI(function(){return y},WI(function(){return b},n.shared.providers,t)),d=ft(ft({},o.height.fold(function(){return{}},function(t){return{height:t+"px","max-height":t+"px"}})),o.width.fold(function(){return{}},function(t){return{width:t+"px","max-width":t+"px"}})),m=o.width.isNone()&&o.height.isNone()?["tox-dialog--width-lg"]:[],g=new iR(o.url,{base_uri:new iR(window.location.href)}),p=g.protocol+"://"+g.host+(g.port?":"+g.port:""),h=fe(dt.none()),v=[fg("messages",[sr(function(){var t=Sy(me.fromDom(window),"message",function(t){var n,e;g.isSameOrigin(new iR(t.raw.origin))&&(n=t.raw.data,aR(n)?function(t,n,e){switch(e.mceAction){case"insertContent":t.insertContent(e.content);break;case"setContent":t.setContent(e.content);break;case"execCommand":var o=!!S(e.ui)&&e.ui;t.execCommand(e.cmd,o,e.value);break;case"close":n.close();break;case"block":n.block(e.message);break;case"unblock":n.unblock()}}(r,y,n):!aR(e=n)&&w(e)&&It(e,"mceAction")&&o.onMessage(y,n))});h.set(dt.some(t))}),lr(function(){h.get().each(function(t){return t.unbind()})})]),lc.config({channels:((e={})[DI]={onReceive:function(t,n){qu(t.element,"iframe").each(function(t){t.dom.contentWindow.postMessage(n,p)})}},e)})],b=GI({header:c,body:s,footer:l,extraClasses:m,extraBehaviours:v,extraStyles:d},o,f,n),y=(a=function(t){u.getSystem().isConnected()&&t(u)},{block:function(n){if(!x(n))throw new Error("The urlDialogInstanceAPI.block function should be passed a blocking message of type string as an argument");a(function(t){qo(t,rx,{message:n})})},unblock:function(){a(function(t){Yo(t,ix)})},close:function(){a(function(t){Yo(t,tx)})},sendMessage:function(n){a(function(t){t.getSystem().broadcastOn([DI],n)})}});return{dialog:u=b,instanceApi:y}},sR=function(t){var c,s,l,f,p=t.backstage,h=t.editor,v=Rv(h),e=(s=(c=t).backstage.shared,{open:function(t,n){var e=function(){VM.hide(u),n()},o=mp(ZC({name:"close-alert",text:"OK",primary:!0,align:"end",disabled:!1,icon:dt.none()},"cancel",c.backstage)),r=II(),i=FI(e,s.providers),u=mu(HI({lazySink:function(){return s.getSink()},header:MI(r,i),body:RI(t,s.providers),footer:dt.some(VI(PI([],[o.asSpec()]))),onEscape:e,extraClasses:["tox-alert-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[er(nx,e)],eventOrder:{}}));VM.show(u);var a=o.get(u);hg.focus(a)}}),o=(f=(l=t).backstage.shared,{open:function(t,n){var e=function(t){VM.hide(a),n(t)},o=mp(ZC({name:"yes",text:"Yes",primary:!0,align:"end",disabled:!1,icon:dt.none()},"submit",l.backstage)),r=ZC({name:"no",text:"No",primary:!1,align:"end",disabled:!1,icon:dt.none()},"cancel",l.backstage),i=II(),u=FI(function(){return e(!1)},f.providers),a=mu(HI({lazySink:function(){return f.getSink()},header:MI(i,u),body:RI(t,f.providers),footer:dt.some(VI(PI([],[r,o.asSpec()]))),onEscape:function(){return e(!1)},extraClasses:["tox-confirm-dialog"],extraBehaviours:[],extraStyles:{},dialogEvents:[er(nx,function(){return e(!1)}),er(ox,function(){return e(!0)})],eventOrder:{}}));VM.show(a);var c=o.get(a);hg.focus(c)}}),r=function(t,e){return uI.openUrl(function(t){var n=cR(t,{closeWindow:function(){VM.hide(n.dialog),e(n.instanceApi)}},h,p);return VM.show(n.dialog),n.instanceApi},t)},i=function(t,i){return uI.open(function(t,n,e){var o=n,r=oR({dataValidator:e,initialData:o,internalDialog:t},{redial:uI.redial,closeWindow:function(){VM.hide(r.dialog),i(r.instanceApi)}},p);return VM.show(r.dialog),r.instanceApi.setData(o),r.instanceApi},t)},u=function(t,d,m,g){return uI.open(function(t,n,e){var o,r,i,u=Tn(_n("data",e,n)),a=(o=fe(dt.none()),{clear:function(){return o.set(dt.none())},set:function(t){return o.set(dt.some(t))},isSet:function(){return o.get().isSome()},on:function(t){return o.get().each(t)}}),c=p.shared.header.isPositionedAtTop(),s=function(){return a.on(function(t){Zg.reposition(t),KD.refresh(t)})},l=rR({dataValidator:e,initialData:u,internalDialog:t},{redial:uI.redial,closeWindow:function(){a.on(Zg.hide),h.off("ResizeEditor",s),a.clear(),m(l.instanceApi)}},p,g),f=mu(Zg.sketch(ft(ft({lazySink:p.shared.getSink,dom:{tag:"div",classes:[]},fireDismissalEventInstead:{}},c?{}:{fireRepositionEventInstead:{}}),{inlineBehaviours:rc(T([fg("window-manager-inline-events",[er(zo(),function(t,n){Yo(l.dialog,nx)})])],(r=h,i=c,v&&i?[]:[KD.config({contextual:{lazyContext:function(){return dt.some(Hu(me.fromDom(r.getContentAreaContainer())))},fadeInClass:"tox-dialog-dock-fadein",fadeOutClass:"tox-dialog-dock-fadeout",transitionClass:"tox-dialog-dock-transition"},modes:["top"]})]))),isExtraPart:function(t,n){return Oy(e=n,".tox-alert-dialog")||Oy(e,".tox-confirm-dialog");var e}})));return a.set(f),Zg.showWithin(f,d,gu(l.dialog),dt.some(Ui())),v&&c||(KD.refresh(f),h.on("ResizeEditor",s)),l.instanceApi.setData(u),ig.focusIn(l.dialog),l.instanceApi},t)};return{open:function(t,n,e){return n!==undefined&&"toolbar"===n.inline?u(t,p.shared.anchors.inlineDialog(),e,n.ariaAttrs):n!==undefined&&"cursor"===n.inline?u(t,p.shared.anchors.cursor(),e,n.ariaAttrs):i(t,e)},openUrl:function(t,n){return r(t,n)},alert:function(t,n){e.open(t,function(){n()})},close:function(t){t.close()},confirm:function(t,n){o.open(t,function(t){n(t)})}}};r.add("silver",function(t){var n=DM(t),e=n.uiMothership,o=n.backstage,r=n.renderUI,i=n.getUi;xy(t,o.shared);var u=sR({editor:t,backstage:o});return{renderUI:r,getWindowManagerImpl:st(u),getNotificationManagerImpl:function(){return bp(0,{backstage:o},e)},ui:i()}})}();