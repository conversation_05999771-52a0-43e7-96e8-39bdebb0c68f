<?php

namespace App\Console\Commands;

use App\Models\Location;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class MigrateLocationTables extends Command
{
    protected $signature = 'migrate:location-tables {--start-date= : Start date for migration (format: Y-m-d)}
                                                   {--end-date= : End date for migration (format: Y-m-d)}
                                                   {--delete-old : Delete old tables after migration}
                                                   {--only-delete : Only delete old tables without migration}
                                                   {--force : Force deletion without confirmation}';
    
    protected $description = 'Migrate location data from daily tables to monthly tables';
    
    public function handle()
    {
        $startDate = $this->option('start-date') ? Carbon::parse($this->option('start-date')) : Carbon::now()->subMonths(24);
        $endDate = $this->option('end-date') ? Carbon::parse($this->option('end-date')) : Carbon::now();
        $deleteOld = $this->option('delete-old');
        $onlyDelete = $this->option('only-delete');
        
        $this->info("Starting operation from {$startDate->format('Y-m-d')} to {$endDate->format('Y-m-d')}");
        
        if ($onlyDelete) {
            $this->deleteOldTables($startDate, $endDate);
            $this->info('Deletion completed successfully!');
            return;
        }
        
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $this->migrateDay($currentDate);
            $currentDate->addDay();
        }
        
        if ($deleteOld) {
            $this->deleteOldTables($startDate, $endDate);
        }
        
        $this->info('Migration completed successfully!');
    }
    
    protected function migrateDay(Carbon $date)
    {
        $dailyTableSuffix = $date->format('Ymd');
        $monthlyTableSuffix = $date->format('Ym');
        
        $dailyTable = "locations_{$dailyTableSuffix}";
        $monthlyTable = "locations_{$monthlyTableSuffix}";
        
        if (!Schema::hasTable($dailyTable)) {
            $this->warn("Table {$dailyTable} does not exist, skipping...");
            return;
        }
        
        $this->info("Migrating data from {$dailyTable} to {$monthlyTable}");
        
        // Create monthly table if it doesn't exist
        if (!Schema::hasTable($monthlyTable)) {
            $this->createMonthlyTable($monthlyTable);
        }
        
        // Get count of records to migrate
        $count = DB::table($dailyTable)->count();
        $this->info("Found {$count} records to migrate");
        
        // Migrate in batches to avoid memory issues
        $batchSize = 1000;
        $batches = ceil($count / $batchSize);
        
        for ($i = 0; $i < $batches; $i++) {
            $records = DB::table($dailyTable)
                ->select('*')
                ->skip($i * $batchSize)
                ->take($batchSize)
                ->get();
            
            $this->insertRecords($monthlyTable, $records);
            $this->info("Migrated batch " . ($i + 1) . " of {$batches}");
        }
    }
    
    protected function createMonthlyTable($tableName)
    {
        $this->info("Creating table {$tableName}");
        
        // Get the structure from an existing daily table or create based on model
        Schema::create($tableName, function ($table) {
            // Add columns based on your Location model structure
            $table->id();
            $table->string('belong_type');
            $table->unsignedBigInteger('belong_id');
            $table->decimal('lng', 10, 7);
            $table->decimal('lat', 10, 7);
            $table->unsignedBigInteger('time');
            $table->timestamps();
            
            // Add any indexes you need
            $table->index(['belong_type', 'belong_id']);
        });
    }
    
    protected function insertRecords($tableName, $records)
    {
        $data = [];
        
        foreach ($records as $record) {
            $recordArray = (array) $record;
            // Don't copy the ID to avoid conflicts
            if (isset($recordArray['id'])) {
                unset($recordArray['id']);
            }
            $data[] = $recordArray;
        }
        
        if (!empty($data)) {
            DB::table($tableName)->insert($data);
        }
    }
    
    protected function deleteOldTables(Carbon $startDate, Carbon $endDate)
    {
        $this->info('Preparing to delete old daily tables...');
        
        $tablesToDelete = [];
        $currentDate = clone $startDate;
        
        while ($currentDate <= $endDate) {
            $dailyTableSuffix = $currentDate->format('Ymd');
            $dailyTable = "locations_{$dailyTableSuffix}";
            
            if (Schema::hasTable($dailyTable)) {
                $tablesToDelete[] = $dailyTable;
            }
            
            $currentDate->addDay();
        }
        
        if (empty($tablesToDelete)) {
            $this->info('No tables found to delete.');
            return;
        }
        
        $this->info('The following tables will be deleted:');
        foreach ($tablesToDelete as $table) {
            $this->line(" - {$table}");
        }
        
        if (!$this->option('force') && !$this->confirm('Do you want to proceed with deletion?')) {
            $this->info('Deletion cancelled.');
            return;
        }
        
        foreach ($tablesToDelete as $table) {
            $this->info("Dropping table {$table}");
            Schema::drop($table);
        }
        
        $this->info('All specified tables have been deleted.');
    }
} 