<?php

namespace App\Admin\Actions\Form;

use App\Admin\Actions\Imports\WhiteListImport;
use Dcat\Admin\Widgets\Form;
use Maatwebsite\Excel\Facades\Excel;

class WhiteListForm extends Form
{
    public function handle(array $input)
    {
        try {
            set_time_limit(0);
            //上传文件位置，这里默认是在storage中，如有修改请对应替换
            $file = storage_path( 'app/public/'. $input['file']);
            Excel::import(new WhiteListImport(), $file);
            return $this->response()->success('数据导入成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage());
        }
    }

    public function form()
    {
        $this->file('file', '上传数据（Excel）')
            ->rules('required', ['required' => '文件不能为空'])->autoUpload()
            ->disk('public');

    }
}
