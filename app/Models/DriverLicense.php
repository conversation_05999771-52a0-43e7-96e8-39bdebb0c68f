<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DriverLicense extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const ExpTimeTypeMap = [
        1 => '永久',
        2 => '非永久',
    ];

    const ExpTimeForever = 1;
    const ExpTimeNotForever = 2;

    protected $fillable = [
        'user_id',
        'cover',
        'obverse',
        'card_no',
        'name',
        'vehicle_type',
        'exp_time_type',
        'start_time',
        'end_time',
        'create_time',
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
