<?php

namespace App\Http\Controllers;

use App\Models\Article;
use App\Models\MtOrder;
use App\Models\Notify;
use App\Models\NotifyLog;
use Carbon\Carbon;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        return view('home');
    }

    public function newIndex()
    {
        return view('home_new');
    }

    public function article($id)
    {
        $article = Article::query()->findOrFail($id);
        return view('article', compact('article'));
    }

    public function notify($id, Request $request)
    {
        $notify = Notify::query()->findOrFail($id);
        if ($request->user_id > 0) { //标记已读
            NotifyLog::query()->firstOrCreate(["notify_id" => $id, "user_id" => $request->user_id]);
        }
        return view('notify', compact('notify'));
    }


    public function share()
    {
        return redirect()->to("https://m.dingdongjuhe.com/share/?code=" . request()->get('code'));
//        if (app()->environment() == 'production') {
//            $downloadUrl = 'https://storage.dingdongjuhe.com/versions/production/yqs_prod_1.1.1_2023032902.apk';
//        } else {
//            if (get_device_type() == 'ios') {
//                $downloadUrl = '';
//            } else {
//                $downloadUrl = 'http://storage.dingdongjuhe.com/versions/test/%E9%9B%A8%E9%AA%91%E5%A3%AB_%E6%B5%8B%E8%AF%95_v1.0.1.apk';
//            }
//        }
//        return view('m.download', ['download_url' => $downloadUrl]);
    }

    public function orderSubmit()
    {
        $data = request()->only(['order_sn', 'rider_name', 'create_time', 'distance', 'finish_count', 'run_mode']);

        if (!isset($data['order_sn'])) {
            return '';
        }

        $order = MtOrder::query()->where('order_sn', $data['order_sn'])->first();
        if ($order) {
            if ($data['run_mode'] == 1) {
                return response()->json(['can_continue' => true]);
            } else {
                if (time() - Carbon::parse($order->created_at)->timestamp < 5 * 60) {
                    return response()->json(['can_continue' => true]);
                }
                return response()->json(['can_continue' => false]);
            }
        } else {
            $order = new MtOrder();
            $order->order_sn = $data['order_sn'];
            $order->rider_name = $data['rider_name'];
            $order->create_time = $data['create_time'];
            $order->distance = $data['distance'];
            $order->save();
            return response()->json(['can_continue' => true]);
        }
    }

    public function openCallback()
    {
        return 'success';
    }
}
