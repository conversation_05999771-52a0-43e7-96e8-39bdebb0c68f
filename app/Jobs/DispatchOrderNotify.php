<?php

namespace App\Jobs;

use App\Models\Location;
use App\Models\Rider;
use App\Models\UserRid;
use App\Services\MapService;
use App\Services\PushService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class DispatchOrderNotify implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($order)
    {
        //
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        // 查询订单关联站点中的所有在线骑手
        $siteId = $this->order->site_id;

        $riders = Rider::query()->where('site_id', $siteId)->where('status', 1)->get();

        $pushService = new PushService();

        foreach ($riders as $rider) {
            $name = $rider->name;
            $prev = app()->environment() == 'production' ? 1 : 2;
            $prev.= '_' . $rider->user->recommend_code;

            $userRid = UserRid::query()->where('user_id', $rider->user_id)->orderBy('updated_at', 'desc')->first();
            if ($userRid) {
                $rid = $userRid->rid;
                Log::info("new order created push rider {$name} message {$rid}");
                $pushService->sendOrderMessage($this->order, $rid);
            }
        }
    }
}
