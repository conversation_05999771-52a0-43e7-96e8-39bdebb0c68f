<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderDetailResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'spu_id' => $this->spu_id,
            'spu_name' => $this->spu_name,
            'spu_cover' => $this->spu_cover ? img_url($this->spu_cover) : "",
            'price' => fentoyuan($this->price),
            'discount_price' => fentoyuan($this->discount_price),
            'quantity' => $this->quantity,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
