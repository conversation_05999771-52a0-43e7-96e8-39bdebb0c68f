<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class HealthCertRequest extends FormRequest
{
    public function rules()
    {

        $rules = match (request()->method) {
            'POST' => [
                'health_cert_no' => 'sometimes|required|string',
                'health_cert_expire_date' => 'required',
                'health_cert_cover' => 'required',
                'health_cert_obverse' => 'required',
            ],
            default => [],
        };


        return $rules;
    }
}
