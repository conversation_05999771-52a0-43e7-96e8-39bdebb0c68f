<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\HealthCertRequest;
use App\Models\Rider;
use App\Models\RiderProfile;
use Illuminate\Http\Request;

class HealthCertController extends Controller
{
    public function store(HealthCertRequest $request)
    {
        $input = $request->only(['health_cert_no', 'health_cert_publisher', 'health_cert_expire_date',
            'health_cert_cover', 'health_cert_obverse']);

        $user = $request->user();
        $rider = Rider::query()->where('user_id', $user->id)->first();
        if (!$rider->health_status || $rider->health_status == 3) {
            if ($user->riderProfile) {
                $user->riderProfile->update($input);
            } else {
                $input['user_id'] = $user->id;
                RiderProfile::create($input);
            }
            if ($rider->health_status != 2) {
                $rider->health_status = 1;
                $rider->save();
            }
        } else {
            if ($rider->health_status == 1) {
                $this->errorResponse(200, '您提交的健康证审核中');
            }
            if ($rider->health_status == 2) {
                $this->errorResponse(200, '您的健康证已成功审核');
            }
        }

        return $this->success('提交成功', $user->riderProfile);
    }

    public function my(Request $request)
    {
        $user = $request->user();

        $result = [
            'health_cert_no' => $user->riderProfile->health_cert_no,
            'health_cert_publisher' => $user->riderProfile->health_cert_publisher,
            'health_cert_expire_date' => $user->riderProfile->health_cert_expire_date,
            'health_cert_cover' => $user->riderProfile->health_cert_cover,
            'health_cert_obverse' => $user->riderProfile->health_cert_obverse,
            'health_cert_article' => route('article', 8),
        ];

        return $this->success('success', $result);

    }
}
