<?php


namespace App\Services;


use App\Models\HealthyReport;
use App\Models\Site;
use App\Models\SystemConfig;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class CommonService
{
    /**
     * 判断点是否在站点覆盖范围内，不指定站点系统按照点附近的站点来判断
     * @param $point
     * @param int $siteId
     * @return bool
     */
    public function pointInSite($point, $siteId = 0)
    {
        $flag = false;
        if ($siteId > 0) {
            $site = Site::query()->find($siteId);
            foreach ($site->regions as $region) {
                $flag = is_point_in_polygon($point, $region->points);
                if ($flag) return true;
            }
        } else {
            $siteList = Site::GetSiteFromRedis($point['lng'], $point['lat'], 5);
            foreach ($siteList as $siteItem) {
                $site = Site::query()->find($siteItem[0]);
                foreach ($site->regions as $region) {
                    $flag = is_point_in_polygon($point, $region->points);
                    if ($flag) return true;
                }
            }

        }

        return $flag;
    }


    /**
     * 判断点是否再站点覆盖范围内
     * @param $point
     */
    public function getPointSite($point)
    {
        $distance = app()->environment() == 'production' ? 100 : 200;
        $siteList = Site::GetSiteFromRedis($point['lng'], $point['lat'], $distance);
        Log::info(json_encode($siteList));
        foreach ($siteList as $siteItem) {
            $site = Site::query()->find($siteItem[0]);
            foreach ($site->regions as $region) {
                $flag = is_point_in_polygon($point, $region->points);
                Log::info(json_encode($flag));
                if ($flag) return $site;
            }
        }
        return null;
    }

    /**
     * @param $userId
     * @return boolean
     */
    public function getHealthyStatus($userId)
    {
        $cacheKey = 'healtyp_report_' . $userId . '_' . Carbon::now()->format('Ymd');
        if ($result = Cache::get($cacheKey)) {
            return $result;
        }

        $needUploadHealth = SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_OPEN_HEALTHY_CHECK);

        if ($needUploadHealth) {
            // 查询用户今日是否上报
            $report = HealthyReport::query()->where('user_id', $userId)
                ->whereBetween('check_time', [Carbon::now()->startOfDay(), Carbon::now()])->first();
            if ($report) {
                $needUploadHealth = false;
                Cache::put($cacheKey, $needUploadHealth, Carbon::now()->addDay()->startOfDay());
            }
        } else {
            return true;
        }

        return $needUploadHealth;
    }
}
