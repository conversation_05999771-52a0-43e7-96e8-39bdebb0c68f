<?php


namespace App\Services\Amap;


use Exception;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;

class GaodeService
{
    const GeoUrl = "https://restapi.amap.com/v3/geocode/geo";
    const RegeoUrl = "https://restapi.amap.com/v3/geocode/regeo";
    const IpUrl = "https://restapi.amap.com/v3/ip";
    const WeatherUrl = "https://restapi.amap.com/v3/weather/weatherInfo";
    const DistrictUrl = "https://restapi.amap.com/v3/config/district";
    const DistanceUrl = "https://restapi.amap.com/v3/distance";
    const ElectrobikeUrl = "https://restapi.amap.com/v5/direction/electrobike";
    const SearchPoi = "https://restapi.amap.com/v5/place/text?parameters";
    const SearchPoiAround = "https://restapi.amap.com/v5/place/around?parameters";
    const SearchText = "https://restapi.amap.com/v5/place/text";
    const InputTips = "https://restapi.amap.com/v3/assistant/inputtips";

    const Bicycling = "https://restapi.amap.com/v4/direction/bicycling";

    protected $appKey;

    public function __construct()
    {
        $this->appKey = config('services.map_key.gaode');
    }

    /**
     * 发起http请求
     * @param string $requestUrl 请求url
     * @param array $params 请求参数
     * @return mixed
     * @throws Exception
     */
    private function do(string $requestUrl, array $params = []): mixed
    {
        try {
            $params["key"] = $this->appKey;
            $res = Curl::to($requestUrl . "?" . http_build_query($params))->asJsonResponse(true)->get();
        } catch (Exception $e) {
            throw new Exception($requestUrl . "：" . $e->getMessage());
        }
        if ($res["status"] != 1) {
            throw new Exception($requestUrl . "：" . $res["info"]);
        }
        return $res;
    }

    private function doV4(string $requestUrl, array $params = []): mixed
    {
        try {
            $params["key"] = $this->appKey;
            $res = Curl::to($requestUrl . "?" . http_build_query($params))->asJsonResponse(true)->get();
        } catch (Exception $e) {
            throw new Exception($requestUrl . "：" . $e->getMessage());
        }
        if ($res["errcode"] != 0) {
            throw new Exception($requestUrl . "：" . $res["errmsg"]);
        }
        return $res;
    }


    /**
     * 地理编码
     * @param string $address 例：杭州市文一西路海创科技中心
     * @return mixed
     * @throws Exception
     */
    public function geoQuery(string $address, string $city = ""): mixed
    {
        $params['address'] = $address;
        if ($city) {
            $params['city'] = $city;
        }
        return $this->do(self::GeoUrl, $params);
    }

    /**
     * 逆地理编码
     * @param string $lng 经度 116.2335
     * @param string $lat 纬度 32.3443
     * @return mixed
     * @throws Exception
     */
    public function reGeoQuery(string $lng, string $lat): mixed
    {
        try {
            // 处理经纬度精度
            $arr = explode(".", $lng);
            if (count($arr) > 1) {
                if (strlen($arr[1]) > 6) $lng = $arr[0] . "." . substr($arr[1], 0, 6);
            }
            $arr = explode(".", $lat);
            if (count($arr) > 1) {
                if (strlen($arr[1]) > 6) $lat = $arr[0] . "." . substr($arr[1], 0, 6);
            }

            // 构建请求参数
            $params = [
                "location" => $lng . ',' . $lat,
                "extensions" => "all", // 返回完整数据
                "radius" => 1000, // 搜索半径
                "roadlevel" => 1, // 仅输出主干道路数据
            ];

                $response = $this->do(self::RegeoUrl, $params);


            // 记录请求日志
            Log::info('高德地图逆地理编码请求', [
                'params' => $params,
                'response' => $response
            ]);

            return $response;
        } catch (Exception $e) {
            Log::error('高德地图逆地理编码异常', [
                'lng' => $lng,
                'lat' => $lat,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * IP定位
     * @param string $ip
     * @return mixed
     * @throws Exception
     */
    public function ipQuery(string $ip): mixed
    {
        $res = $this->do(self::IpUrl, ["ip" => $ip]);
        $districtQueryRes = $this->districtQuery($res["adcode"] ?: "");
        $res["location"] = $districtQueryRes["districts"][0]["center"];
        return $res;
    }

    /**
     * 天气查询
     * @param string $adcode
     * @return mixed
     * @throws Exception
     */
    public function weatherQuery(string $adcode): mixed
    {
        return $this->do(self::WeatherUrl, ["city" => $adcode]);
    }

    /**
     * 行政区域查询
     * @param string $keyword
     * @param int $type 0：不返回下级行政区；1：返回下一级行政区；2：返回下两级行政区；3：返回下三级行政区；
     * @return mixed
     * @throws Exception
     */
    public function districtQuery(string $keyword, int $type = 0): mixed
    {
        return $this->do(self::DistrictUrl, ["keywords" => $keyword, "subdistrict" => $type]);
    }

    /**
     * 距离
     * @param string $from 起点经纬度 支持100个坐标对，坐标对见用"|"分隔；经度和纬度用","分隔
     * @param string $to 终点经纬度  例：117.500244, 40.417801
     * @return mixed
     * @throws Exception
     */
    public function distanceQuery(string $from, string $to): mixed
    {
        $result =  $this->do(self::DistanceUrl, ["origins" => $from, "destination" => $to, "type" => 0]);
        return $result['results'] ?? [];
    }

    /**
     * 电动车路线
     * @param string $fromLng 起点经度
     * @param string $fromLat 起点纬度
     * @param string $toLng 终点经度
     * @param string $toLat 终点纬度
     * @return mixed
     * @throws Exception
     */
    public function electrobike(string $fromLng, string $fromLat, string $toLng, string $toLat): mixed
    {
        $arr = explode(".", $fromLng);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $fromLng = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $fromLat);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $fromLat = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $toLng);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $toLng = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $toLat);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $toLat = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        try {
            $res = $this->do(self::Bicycling, ["origin" => $fromLng . ',' . $fromLat, "destination" => $toLng . ',' . $toLat, "alternative_route" => 3]);
            // 适配百度地图返回格式
            return $res;
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
        return [];
    }

    // 自行车路线 https://lbs.amap.com/api/webservice/guide/api/direction#t7
    public function bicycling(string $fromLng, string $fromLat, string $toLng, string $toLat): mixed
    {
        $arr = explode(".", $fromLng);
        if (count($arr) > 1) {
                if (strlen($arr[1]) > 6) $fromLng = $arr[0] . "." . substr($arr[1], 0, 6);
            }
            $arr = explode(".", $fromLat);
            if (count($arr) > 1) {
                if (strlen($arr[1]) > 6) $fromLat = $arr[0] . "." . substr($arr[1], 0, 6);
            }
            $arr = explode(".", $toLng);
            if (count($arr) > 1) {
                if (strlen($arr[1]) > 6) $toLng = $arr[0] . "." . substr($arr[1], 0, 6);
            }
            $arr = explode(".", $toLat);
            if (count($arr) > 1) {
                if (strlen($arr[1]) > 6) $toLat = $arr[0] . "." . substr($arr[1], 0, 6);
            }
            try {
                $res = $this->doV4(self::Bicycling, ["origin" => $fromLng . ',' . $fromLat, "destination" => $toLng . ',' . $toLat]);
                // 适配百度地图返回格式
                return $res;
            } catch (Exception $e) {
            Log::error($e->getMessage());
        }
        return [];
    }

    public function queryPoi($lat, $lng, $page, $pageSize, $category) {
        switch ($category) {
            case 1:
                $types = '050000|060000';
                break;
            default:
                $types = '120000|150000|050000|190000|060000|100000|110000|170000';
                break;
        }
        $params = [
            'types' => $types,
            'location' => "$lng,$lat",
            'radius' => 5000,
            'page_size' => $pageSize,
            'page' => $page
        ];

        return $this->do(self::SearchPoiAround, $params);
    }

    /**
     * 关键词搜索POI
     * @param array $params 搜索参数
     * @return mixed
     * @throws Exception
     */
    public function searchText(array $params): mixed
    {
        try {
            $response = $this->do(self::SearchText, $params);
            return $response;
        } catch (Exception $e) {
            Log::error('高德地图关键词搜索失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 输入提示API
     * @param array $params 请求参数
     * @return mixed
     * @throws Exception
     */
    public function inputTips(array $params): mixed
    {
        try {
            // 构建请求参数
            $requestParams = array_merge($params, [
                'datatype' => $params['datatype'] ?? 'poi',
                'citylimit' => $params['citylimit'] ?? 'true',
                'output' => 'JSON'
            ]);

            $response = $this->do(self::InputTips, $requestParams);
            return $response;
        } catch (Exception $e) {
            Log::error('高德地图输入提示API调用失败', [
                'params' => $params,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
}
