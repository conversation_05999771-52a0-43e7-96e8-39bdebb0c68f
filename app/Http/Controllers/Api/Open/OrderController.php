<?php

namespace App\Http\Controllers\Api\Open;

use App\Exceptions\BusinessException;
use App\Http\Resources\O2oErrandOrderResource;
use App\Models\O2oErrandOrder;
use App\Services\O2oErrandOrderService;
use Illuminate\Http\Request;

class OrderController extends OpenController
{
    public function store(Request $request, O2oErrandOrderService $service)
    {
        try {
            $order = $service->createOrderFromOpen($request->user()->id, $request->out_order_no, $request->all(), $this->appKey);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage() . $e->getFile() . $e->getLine());
        }
        return $this->success("配送订单下单成功", [
            'out_order_no' => $order->out_order_no,
            'order_no' => $order->order_no,
            'order_status' => $order->order_status,
        ]);
    }

    public function cancel(Request $request)
    {
        if (!$request->out_order_no && !$request->order_no) {
            throw new BusinessException(500, '订单号不能为空');
        }

        $order = O2oErrandOrder::query()->where('out_order_no', $request->out_order_no)->orWhere('order_no', $request->order_no)->first();
        if (!$order) {
            throw new BusinessException(500, '订单不存在');
        }

        $order->order_status = O2oErrandOrder::STATUS_CANCEL;
        $order->save();

        return $this->success("配送订单取消成功", [
            'out_order_no' => $order->out_order_no,
            'order_no' => $order->order_no,
            'order_status' => $order->order_status,
        ]);

    }
}
