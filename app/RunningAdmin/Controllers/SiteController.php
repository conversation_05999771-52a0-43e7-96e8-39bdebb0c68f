<?php

namespace App\RunningAdmin\Controllers;

use App\Models\AdminSiteRel;
use App\RunningAdmin\Repositories\Site;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class SiteController extends AdminController
{
    protected $title = '站点管理';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Site(), function (Grid $grid) {
            if (!Admin::user()->isAdministrator()) {
                $grid->disableCreateButton();
                $grid->disableDeleteButton();
                $siteIds = AdminSiteRel::query()->where('user_id', Admin::user()->id)->pluck('site_id');
                $grid->model()->whereIn('id', $siteIds);
            }
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('address');
            $grid->column('name');
            $grid->column('contact_name');
            $grid->column('phone');
            $grid->column("farthest");
            $grid->column('latitude');
            $grid->column('longitude');

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $url = route('site.regions', ['site_id' => $this->id]);
                $url2 = route('admin.order.map',['site_id' => $this->id]);
                $url3 = route('admin.rider.map',['site_id' => $this->id]);
                $actions->append('<a href="'.$url.'" target="_blank">配送范围</a>');
                $actions->append('  <a href="'.$url2.'" target="_blank">人工派单</a>');
                $actions->append('  <a href="'.$url3.'" target="_blank">骑手位置</a>');
            });

            $grid->quickSearch('name', 'address', 'contact_name')->placeholder('搜索...');;
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Site(), function (Show $show) {
            $show->field('id');
            $show->field('city_id');
            $show->field('name');
            $show->field('address');
            $show->field('contact_name');
            $show->field('phone');
            $show->field("farthest");
            $show->field('latitude');
            $show->field('longitude');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Site(), function (Form $form) {
            $form->display('id');
            $form->text('name')->required()->width(3);
            $form->text('contact_name')->width(3);
            $form->text('phone')->width(3);

//            $form->select('province', '省份')->options('/options/provinces')->load('city', '/options/cities')->required();
//            $form->select('city', '市')->load('district', '/options/district')->required();
//            $form->select('district', '区')->required();
            $form->text('address')->required();
            $form->number("farthest")->default(20000)->required();
            $form->html(view('coordinate'), '位置选择');

            $form->hidden('map_points', '坐标点'); // 隐藏域，用于接收坐标点（这里如果想数据回填可以，->value('49.121221,132.2321312')）
            $form->hidden('map_area', '当前区域'); // 隐藏域，用于接收详细点位地址
            $form->hidden('latitude');
            $form->hidden('longitude');

            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function ($form) {
            if ($form->map_points) {
                $points = $form->map_points;
                $pointArr = explode(',', $points);
                $form->latitude = $pointArr[0];
                $form->longitude = $pointArr[1];
            }
            $form->deleteInput('map_points');
            $form->deleteInput('map_area');
        });
    }


    public function customMap()
    {
        return view('map');
    }
}
