<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\CancelOrderRequest;
use App\Http\Requests\Api\OrderCreateRequest;
use App\Http\Requests\Api\RefundOrderRequest;
use App\Http\Resources\CartResource;
use App\Http\Resources\OrderResource;
use App\Http\Resources\ShopResource;
use App\Models\Cart;
use App\Models\Order;
use App\Models\Shop;
use App\Models\UserAddress;
use App\Services\OrderService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class OrderController extends Controller
{
    //订单列表
    public function index(Request $request)
    {
        $query = Order::query()->with(["shop", "details"])->where("user_id", $request->user()->id);

        if ($request->status) {
            $statusArr = explode(",", $request->status);

            if (false) { // 方式1
                $ids = [];
                foreach ($statusArr as $status) {
                    $statusQuery = Order::query()->where("user_id", $request->user()->id);
                    switch ($status) {
                        case Order::STATUS_WAITING_PAY:
                            $statusQuery->where("closed", false)->whereNull("paid_at");
                            break;
                        case Order::STATUS_PAID:
                            $statusQuery->validOrders()->where("paid_at", ">", Carbon::now()->addSeconds(-1 * config('app.order_paid_ttl')));
                            break;
                        case Order::STATUS_TAKING:
                            $statusQuery->validOrders()->where("paid_at", "<=", Carbon::now()->addSeconds(-1 * config('app.order_paid_ttl')))->where("sending_start_time", ">", Carbon::now());
                            break;
                        case Order::STATUS_DELIVERY:
                            $statusQuery->validOrders()->where("sending_start_time", "<=", Carbon::now())->where("sending_end_time", ">", Carbon::now());
                            break;
                        case Order::STATUS_FINISHED:
                            $statusQuery->validOrders()->where("sending_end_time", "<=", Carbon::now());
                            break;
                        case Order::STATUS_CANCEL:
                            $statusQuery->whereRaw("(closed = 1 or refund_type < 0)");
                            break;
                        default:
                            if ($status >= 10) {
                                $statusQuery->where("closed", false)->whereNotNull("paid_at")->where("refund_type", $status);
                            } else {
                                $statusQuery->where("id", 0);
                            }
                    }
                    $data = $statusQuery->pluck("id");
                    if ($data->count() > 0) {
                        $ids = array_merge($ids, $data->all());
                    }
                }
                if (empty($ids)) $ids = [0];
                $query->whereIn("id", $ids);
            } else { // 方式2
                $query->where(function ($query) use ($statusArr) {
                    foreach ($statusArr as $status) {
                        switch ($status) {
                            case Order::STATUS_WAITING_PAY:
                                $query->orWhere(function ($query) {
                                    $query->where("closed", 0)->whereNull("paid_at");
                                });
                                break;
                            case Order::STATUS_PAID:
                                $query->orWhere(function ($query) {
                                    $query->validOrders()->where("paid_at", ">", Carbon::now()->addSeconds(-1 * config('app.order_paid_ttl')));
                                });
                                break;
                            case Order::STATUS_TAKING:
                                $query->orWhere(function ($query) {
                                    $query->validOrders()->where("paid_at", "<=", Carbon::now()->addSeconds(-1 * config('app.order_paid_ttl')))->where("sending_start_time", ">", Carbon::now());
                                });
                                break;
                            case Order::STATUS_DELIVERY:
                                $query->orWhere(function ($query) {
                                    $query->validOrders()->where("sending_start_time", "<=", Carbon::now())->where("sending_end_time", ">", Carbon::now());
                                });
                                break;
                            case Order::STATUS_FINISHED:
                                $query->orWhere(function ($query) {
                                    $query->validOrders()->where("sending_end_time", "<=", Carbon::now());
                                });
                                break;
                            case Order::STATUS_CANCEL:
                                $query->orWhereRaw("(closed = 1 or refund_type < 0)");
                                break;
                            default:
                                if ($status >= 10) {
                                    $query->orWhere(function ($query) use ($status) {
                                        $query->where("closed", 0)->whereNotNull("paid_at")->where("refund_type", $status);
                                    });
                                }
                        }
                    }
                });
            }
        }

        $orders = $query->orderBy("created_at", "desc")
            ->orderBy("id", "desc")
            ->paginate($request->input('page_size', $this->pageSize));
        $result = [
            'total' => $orders->total(),
            'has_next' => $orders->hasMorePages(),
            'data' => [],
        ];
        if ($orders) {
            foreach ($orders as $v) {
                $result['data'][] = new OrderResource($v);
            }
        }
        return $this->success('success', $result);
    }

    //订单详情
    public function show($id, Request $request)
    {
        $order = Order::query()->with(["shop", "details", "refunds"])->find($id);
        if (!$order) $this->errorResponse(500, "该订单不存在");
        if ($order->user_id != $request->user()->id) $this->errorResponse(500, "该订单不存在");
        return $this->success("success", new OrderResource($order));
    }

    //下单确认页
    public function pre(Request $request)
    {
        $shopId = intval($request->shop_id ?? 0);
        if ($shopId <= 0) $this->errorResponse(500, "店铺必传");
        $shop = Shop::query()->find($shopId);
        if (!$shop) $this->errorResponse(500, "该店铺不存在");
        $cartItems = Cart::query()->with(["spu"])->where("user_id", $request->user()->id)->where("shop_id", $request->shop_id)->get();
        if ($cartItems->count() <= 0) {
            $this->errorResponse(500, "你的购物车是空的");
        }

        $res = [
            "shop" => new ShopResource($shop),
            'order_amount' => 0,
            'reduce_amount' => 0,
            "pay_amount" => 0,
            'address' => null,
            "cart_items" => null,
        ];

        {
            $address = UserAddress::query()->where("user_id", $request->user()->id)->where("is_white_list", true)->orderBy("is_default", "desc")->first();
            if ($address) $res["address"] = $address;
        }

        foreach ($cartItems as $item) {
            if ($item->quantity <= 0) continue;
            if (!$item->spu) $this->errorResponse(500, "该商品不存在");
            $res["order_amount"] += $item->spu->price * $item->quantity;
            $res["pay_amount"] += $item->spu->discount_price * $item->quantity;
            $res["cart_items"][] = new CartResource($item);
        }
        $res["reduce_amount"] = $res["order_amount"] - $res["pay_amount"];
        return $this->success("SUCCESS", $res);
    }

    //预下单
    public function store(OrderCreateRequest $request)
    {
        try {
            $order = app(OrderService::class)->preOrder($request->user(), [
                "shop_id" => $request->shop_id,
                "address_id" => $request->address_id,
                "sending_time" => $request->sending_time,
                "is_tel_protect" => $request->is_tel_protect,
                "remark" => $request->remark ?? ""
            ]);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("预下单成功", new OrderResource($order));
    }

    //取消订单
    public function cancel(CancelOrderRequest $request)
    {
        try {
            $order = app(OrderService::class)->cancelOrder($request->order_id, $request->user()->id);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("取消订单成功", new OrderResource($order));
    }

    //预计配送前30分钟前取消订单 & 申请售后
    public function refund(RefundOrderRequest $request)
    {
        try {
            $order = app(OrderService::class)->refundOrder([
                'order_id' => $request->order_id,
                'refund_reason' => $request->refund_reason,
                'refund_remark' => $request->refund_remark ?? "",
                'refund_pics' => $request->refund_pics ?? [],
                'refund_items' => $request->refund_items ?? [],
            ], $request->user()->id);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("取消订单/申请售后成功", new OrderResource($order));
    }
}
