<?php

namespace App\Services;

use App\Models\O2oErrandOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Ixudra\Curl\Facades\Curl;
use App\Models\MerchantToken;
use App\Models\Merchant;
use Illuminate\Support\Facades\Log;

class MaiYaTianService
{
    protected string $appKey;
    protected string $appSecret;
    protected string $tag;
    protected string $host;

    const ACCESS_TOKEN = "myt_access_token";
    const REFRESH_TOKEN = "myt_refresh_token";
    const TOKEN_EXPIRE = "myt_expire_time";

    public function __construct(){
        $this->appKey = "oxBIKMS7";
        $this->appSecret = "895edbef513c6fc3e07cbe0e117f74d1";
        $this->tag = "YQS";
        if (app()->environment() == 'production') {
            $this->host = "https://open-api.maiyatian.com/v1/delivery";
        }else{
            $this->host = "https://open-api-test.maiyatian.com/v1/delivery";
        }
    }

    public function accessToken(int $type, string $code, string $mobile, string $storeId, string $city, string $cityCode){
        Log::channel('maiyatian')->info('获取麦芽田access_token', [
            'type' => $type,
            'code' => $code,
            'mobile' => $mobile,
            'store_id' => $storeId,
            'city' => $city,
            'city_code' => $cityCode
        ]);

        $result = $this->post("access_token", ["grant_type" => "$type", "code" => $code, "mobile" => $mobile, "store_id" => $storeId, "city" => $city, "city_code" => $cityCode]);
        // {"shop_id":"1347535","token":"00dcd2d37c0cbe0c1b4a33c1180ab918","refresh_token":"07e4eb51ebc233e30f1e3a7ecaa6ad28","expire_time":0,"refresh_expire_time":0}
        // 过期时间 = 0 表示永久有效

        // 获取商家对应的用户ID
        $merchant = Merchant::find($type);
        $userId = $merchant ? $merchant->user_id : null;

        // 查询是否存在记录
        $tokenRecord = MerchantToken::where('merchant_id', $type)
            ->where('platform', O2oErrandOrder::APP_KEY_MYT)
            ->when($userId, function($query) use ($userId) {
                return $query->where('user_id', $userId);
            })
            ->first();

        // 准备额外数据
        $extraData = [
            'mobile' => $mobile,
            'store_id' => $storeId,
            'city' => $city,
            'city_code' => $cityCode
        ];

        if ($result["expire_time"] > 0){
            $expireTime = Carbon::now()->addSeconds($result["expire_time"]);
            Cache::forever(self::TOKEN_EXPIRE, $expireTime->format(Carbon::DEFAULT_TO_STRING_FORMAT));
            Cache::put(self::ACCESS_TOKEN, $result["token"], $expireTime);
            Cache::put(self::REFRESH_TOKEN, $result["refresh_token"], $expireTime);

            // 同时保存到数据库中
            if ($tokenRecord) {
                // 更新已存在的记录
                $tokenRecord->access_token = $result["token"];
                $tokenRecord->refresh_token = $result["refresh_token"];
                $tokenRecord->expire_time = $expireTime;
                $tokenRecord->refresh_expire_time = isset($result["refresh_expire_time"]) && $result["refresh_expire_time"] > 0 ?
                    Carbon::now()->addSeconds($result["refresh_expire_time"]) : null;
                $tokenRecord->shop_id = $result["shop_id"] ?? '';
                $tokenRecord->extra_data = $extraData;
                $tokenRecord->save();
            } else {
                // 创建新记录
                $newToken = new MerchantToken();
                $newToken->merchant_id = $type;
                $newToken->user_id = $userId;
                $newToken->platform = O2oErrandOrder::APP_KEY_MYT;
                $newToken->access_token = $result["token"];
                $newToken->refresh_token = $result["refresh_token"];
                $newToken->expire_time = $expireTime;
                $newToken->refresh_expire_time = isset($result["refresh_expire_time"]) && $result["refresh_expire_time"] > 0 ?
                    Carbon::now()->addSeconds($result["refresh_expire_time"]) : null;
                $newToken->shop_id = $result["shop_id"] ?? '';
                $newToken->extra_data = $extraData;
                $newToken->save();
            }
        } else {
            Cache::forever(self::ACCESS_TOKEN, $result["token"]);
            Cache::forever(self::REFRESH_TOKEN, $result["refresh_token"]);

            // 同时保存到数据库中，过期时间为null表示永久有效
            if ($tokenRecord) {
                // 更新已存在的记录
                $tokenRecord->access_token = $result["token"];
                $tokenRecord->refresh_token = $result["refresh_token"];
                $tokenRecord->expire_time = null;
                $tokenRecord->refresh_expire_time = null;
                $tokenRecord->shop_id = $result["shop_id"] ?? '';
                $tokenRecord->extra_data = $extraData;
                $tokenRecord->save();
            } else {
                // 创建新记录
                $newToken = new MerchantToken();
                $newToken->merchant_id = $type;
                $newToken->user_id = $userId;
                $newToken->platform = O2oErrandOrder::APP_KEY_MYT;
                $newToken->access_token = $result["token"];
                $newToken->refresh_token = $result["refresh_token"];
                $newToken->expire_time = null;
                $newToken->refresh_expire_time = null;
                $newToken->shop_id = $result["shop_id"] ?? '';
                $newToken->extra_data = $extraData;
                $newToken->save();
            }
        }

        // 清除相关缓存，确保下次查询能获取最新数据
        if (isset($result["shop_id"]) && !empty($result["shop_id"])) {
            $this->clearMerchantInfoCache($result["shop_id"]);
        }

        Log::channel('maiyatian')->info('麦芽田access_token获取成功', [
            'merchant_id' => $type,
            'user_id' => $userId,
            'shop_id' => $result["shop_id"] ?? '',
            'expire_time' => $result["expire_time"] ?? 0
        ]);

        return $result["token"];
    }

    private function getToken(string $command){
        try {
            //return "00dcd2d37c0cbe0c1b4a33c1180ab918";
            if (in_array($command, ["access_token", "refresh_token"])) return "";
            $expireTime = Cache::get(self::TOKEN_EXPIRE, "");
            if ($expireTime) { // 有过期时间
                $tt = strtotime($expireTime);
                // 过期（提前10秒过期）
                if($tt <= time() + 10) {
                    Log::channel('maiyatian')->warning('麦芽田token已过期');
                    throw new \Exception("获取token失败");
                }
                // 即将过期（5分钟内）
                if($tt - time() <= 300) {
                    Log::channel('maiyatian')->info('麦芽田token即将过期，准备刷新');

                    $accessToken = Cache::get(self::ACCESS_TOKEN);
                    $refreshToken = Cache::get(self::REFRESH_TOKEN);
                    if (empty($accessToken) || empty($refreshToken)){
                        // 尝试从数据库获取token
                        $tokenRecord = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_MYT)
                            ->whereNotNull('access_token')
                            ->whereNotNull('refresh_token')
                            ->first();

                        if ($tokenRecord) {
                            $accessToken = $tokenRecord->access_token;
                            $refreshToken = $tokenRecord->refresh_token;
                            Log::channel('maiyatian')->info('从数据库获取token成功');
                        } else {
                            Log::channel('maiyatian')->error('获取token失败：缓存和数据库中均无有效token');
                            throw new \Exception("获取token失败");
                        }
                    }

                    $result = $this->post("refresh_token", ["refresh_token" => $refreshToken, "token" => $accessToken]);
                    // {"shop_id":"1347535","token":"0b9d767f702ef4d4408dd4bd360589fd","refresh_token":"4f23f6e8e40d5e64bf9d74ccc2d17902","expire_time":1745136139,"refresh_expire_time":1746432139}
                    // 过期时间 = 0 表示永久有效
                    if ($result["expire_time"] > 0){
                        $expireTime = Carbon::now()->addSeconds($result["expire_time"]);
                        Cache::forever(self::TOKEN_EXPIRE, $expireTime->format(Carbon::DEFAULT_TO_STRING_FORMAT));
                        Cache::put(self::ACCESS_TOKEN, $result["token"], $expireTime);
                        Cache::put(self::REFRESH_TOKEN, $result["refresh_token"], $expireTime);

                        // 更新数据库中的token
                        if ($tokenRecord) {
                            $tokenRecord->access_token = $result["token"];
                            $tokenRecord->refresh_token = $result["refresh_token"];
                            $tokenRecord->expire_time = $expireTime;
                            $tokenRecord->refresh_expire_time = isset($result["refresh_expire_time"]) && $result["refresh_expire_time"] > 0 ?
                                Carbon::now()->addSeconds($result["refresh_expire_time"]) : null;
                            $tokenRecord->save();
                        } else {
                            // 如果数据库中没有找到记录，但缓存中有，则尝试创建新记录
                            // 尝试找到一个有效的商家ID
                            $merchantId = null;
                            $merchant = Merchant::where('status', 1)->first(); // 获取一个有效商家
                            if ($merchant) {
                                $merchantId = $merchant->id;
                            }

                            // 使用新实例方式创建记录
                            $newToken = new MerchantToken();
                            $newToken->merchant_id = $merchantId ?: 1;
                            $newToken->platform = O2oErrandOrder::APP_KEY_MYT;
                            $newToken->access_token = $result["token"];
                            $newToken->refresh_token = $result["refresh_token"];
                            $newToken->expire_time = $expireTime;
                            $newToken->refresh_expire_time = isset($result["refresh_expire_time"]) && $result["refresh_expire_time"] > 0 ?
                                Carbon::now()->addSeconds($result["refresh_expire_time"]) : null;
                            $newToken->shop_id = $result["shop_id"] ?? '';
                            $newToken->save();
                        }
                    } else {
                        Cache::forever(self::ACCESS_TOKEN, $result["token"]);
                        Cache::forever(self::REFRESH_TOKEN, $result["refresh_token"]);

                        // 更新数据库中的token
                        if ($tokenRecord) {
                            $tokenRecord->access_token = $result["token"];
                            $tokenRecord->refresh_token = $result["refresh_token"];
                            $tokenRecord->expire_time = null;
                            $tokenRecord->refresh_expire_time = null;
                            $tokenRecord->save();
                        } else {
                            // 如果数据库中没有找到记录，但缓存中有，则尝试创建新记录
                            // 尝试找到一个有效的商家ID
                            $merchantId = null;
                            $merchant = Merchant::where('status', 1)->first(); // 获取一个有效商家
                            if ($merchant) {
                                $merchantId = $merchant->id;
                            }

                            // 使用新实例方式创建记录
                            $newToken = new MerchantToken();
                            $newToken->merchant_id = $merchantId ?: 1;
                            $newToken->platform = O2oErrandOrder::APP_KEY_MYT;
                            $newToken->access_token = $result["token"];
                            $newToken->refresh_token = $result["refresh_token"];
                            $newToken->expire_time = null;
                            $newToken->refresh_expire_time = null;
                            $newToken->shop_id = $result["shop_id"] ?? '';
                            $newToken->save();
                        }
                    }
                    Log::channel('maiyatian')->info('麦芽田token刷新成功');
                    return $result["token"];
                }
            }

            $accessToken = Cache::get(self::ACCESS_TOKEN);
            if ($accessToken){
                return $accessToken;
            }

            // 从数据库获取token
            $tokenRecord = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_MYT)
                ->whereNotNull('access_token')
                ->first();
            if ($tokenRecord && $tokenRecord->access_token) {
                // 如果有过期时间，检查是否有效
                if ($tokenRecord->expire_time && $tokenRecord->expire_time->isPast()) {
                    throw new \Exception("获取token失败：token已过期");
                }
                // 将token同步到缓存
                if ($tokenRecord->expire_time) {
                    Cache::put(self::ACCESS_TOKEN, $tokenRecord->access_token, $tokenRecord->expire_time);
                    Cache::put(self::REFRESH_TOKEN, $tokenRecord->refresh_token, $tokenRecord->expire_time);
                    Cache::forever(self::TOKEN_EXPIRE, $tokenRecord->expire_time->format(Carbon::DEFAULT_TO_STRING_FORMAT));
                } else {
                    Cache::forever(self::ACCESS_TOKEN, $tokenRecord->access_token);
                    Cache::forever(self::REFRESH_TOKEN, $tokenRecord->refresh_token);
                }
                return $tokenRecord->access_token;
            }

            // 没有则报错
            throw new \Exception("获取token失败");
        } catch (\Exception $e) {
            Log::channel('maiyatian')->error('获取麦芽田token异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'command' => $command
            ]);
            throw $e;
        }
    }

    private function getSignature(array $data){
        ksort($data);
        $str = '';
        foreach ($data as $k => $v) {
            if ($str) $str .= ",";
            $str .= $k . '=' . $v;
        }
        $hash = hash_hmac('sha256', $str, $this->appSecret, true);
        return strtr(base64_encode($hash), '+/', '-_');
    }

    private function getData(string $command, array $data){
        $postData = [
            "app_key" => $this->appKey,
            "timestamp" => intval(round(microtime(true) * 1000)),
            "token" => $this->getToken($command) ?: ($data["token"] ?? ""),
            "data" => $data ? json_encode($data, JSON_UNESCAPED_UNICODE) : "",
            "command" => $command,
        ];
        $postData['signature'] = $this->getSignature($postData);
        $postData['request_id'] = create_uuid();
        return $postData;
    }

    private function post(string $command, array $data = [], array $headers = []){
        try {
            $headers['Content-Type'] = 'application/json;charset=utf-8';

            Log::channel('maiyatian')->debug('麦芽田API请求', [
                'command' => $command,
                'data' => $data
            ]);

            $result = Curl::to($this->host . '/'. $command)
                ->asJson(true)
                ->withHeaders($headers)
                ->withData($this->getData($command, $data))
                ->post();

            Log::channel('maiyatian')->debug('麦芽田API响应', [
                'command' => $command,
                'result' => $result
            ]);

            if(($result["code"]?? 0) == 200){
                return json_decode($result["data"], true);
            }

            Log::channel('maiyatian')->error('麦芽田API错误', [
                'command' => $command,
                'code' => $result["code"] ?? 0,
                'message' => $result["msg"] ?? '未知错误'
            ]);

            throw new \Exception($result["msg"]?? "未知错误", $result["code"]?? 0);
        } catch (\Exception $e) {
            Log::channel('maiyatian')->error('麦芽田API异常', [
                'command' => $command,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw $e;
        }
    }

    public function deliveryChange($userId, $orderNo, $thirdOrderNo, $status, $otherData = [])
    {
        // 通过userId获取门店ID
        $shopId = "";
        $merchantToken = MerchantToken::where('user_id', $userId)
            ->where('platform', O2oErrandOrder::APP_KEY_MYT)
            ->whereNotNull('shop_id')
            ->first();

        if ($merchantToken && !empty($merchantToken->shop_id)) {
            $shopId = $merchantToken->shop_id;
        }

        // 必填信息没有需要初始化
        foreach (["rider_name", "rider_phone", "longitude", "latitude"] as $key){
            if (isset($otherData[$key])){
                $otherData[$key] .= "";
            }else{
                $otherData[$key] = "";
            }
        }

        Log::channel('maiyatian')->info('配送状态变更', [
            'user_id' => $userId,
            'shop_id' => $shopId,
            'order_no' => $orderNo,
            'source_order_no' => $thirdOrderNo,
            'status' => $status
        ]);

        return $this->post("delivery_change", array_merge([
            "order_no" => $orderNo,
            "source_order_no" => $thirdOrderNo,
            "shop_id" => $shopId,
            "status" => $status,
            "at_time" => time()
        ], $otherData));
    }

    public function locationChange($userId, $orderNo, $thirdOrderNo, $status, $longitude, $latitude) {
        // 通过userId获取门店ID
        $shopId = "";
        $merchantToken = MerchantToken::where('user_id', $userId)
            ->where('platform', O2oErrandOrder::APP_KEY_MYT)
            ->whereNotNull('shop_id')
            ->first();

        if ($merchantToken && !empty($merchantToken->shop_id)) {
            $shopId = $merchantToken->shop_id;
        }

        Log::channel('maiyatian')->info('位置变更', [
            'user_id' => $userId,
            'shop_id' => $shopId,
            'order_no' => $orderNo,
            'source_order_no' => $thirdOrderNo,
            'status' => $status,
            'longitude' => $longitude,
            'latitude' => $latitude
        ]);

        return $this->post("location_change", [
            "order_no" => $orderNo,
            "source_order_no" => $thirdOrderNo,
            "shop_id" => $shopId,
            "locations" => [
                [
                    "longitude" => "$longitude",
                    "latitude" => "$latitude",
                    "status" => $status,
                    "update_time" => time()
                ]
            ]
        ]);
    }

    /**
     * 通过麦芽田的shop_id获取对应的商家信息和用户ID
     *
     * @param string $shopId 麦芽田平台的商铺ID
     * @return array|null 包含merchant_id和user_id的数组，未找到时返回null
     */
    public function getMerchantInfoByShopId(string $shopId): ?array
    {
        // 使用缓存键前缀，方便管理
        $cacheKey = "myt_shop_merchant_{$shopId}";

        // 先尝试从缓存获取
        // if (Cache::has($cacheKey)) {
        //     Log::channel('maiyatian')->info('从缓存获取商家信息', ['shop_id' => $shopId]);
        //     return Cache::get($cacheKey);
        // }

        // 缓存没有，从数据库查询
        $token = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_MYT)
            ->where('shop_id', $shopId)
            ->first();

        if (!$token) {
            Log::channel('maiyatian')->warning('未找到shop_id对应的商家记录', ['shop_id' => $shopId]);
            return null;
        }

        $merchantId = $token->merchant_id;
        $userId = $token->user_id;

        // 如果token记录中没有user_id，尝试从商家表获取
        if (!$userId && $merchantId) {
            $merchant = Merchant::find($merchantId);
            $userId = $merchant ? $merchant->user_id : null;
        }

        $result = [
            'merchant_id' => $merchantId,
            'user_id' => $userId,
            'token' => $token
        ];

        // 缓存结果，设置1小时过期时间
        Cache::put($cacheKey, $result, 3600);

        Log::channel('maiyatian')->info('获取商家信息成功', [
            'shop_id' => $shopId,
            'merchant_id' => $merchantId,
            'user_id' => $userId
        ]);

        return $result;
    }

    /**
     * 清除指定shop_id的商家信息缓存
     *
     * @param string $shopId 麦芽田平台的商铺ID
     * @return void
     */
    public function clearMerchantInfoCache(string $shopId): void
    {
        $cacheKey = "myt_shop_merchant_{$shopId}";
        Cache::forget($cacheKey);
        Log::channel('maiyatian')->info('清除商家信息缓存', ['shop_id' => $shopId]);
    }

    /**
     * 解绑指定shop_id的token
     *
     * @param string $shopId 麦芽田平台的商铺ID
     * @return bool 是否成功解绑
     */
    public function unbindToken(string $shopId): bool
    {
        if (empty($shopId)) {
            Log::channel('maiyatian')->warning('解绑token时shop_id为空');
            return false;
        }

        try {
            // 查找并删除token记录
            $token = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_MYT)
                ->where('shop_id', $shopId)
                ->first();

            if ($token) {
                // 记录详细信息用于审计
                Log::channel('maiyatian')->info('解绑token', [
                    'shop_id' => $shopId,
                    'merchant_id' => $token->merchant_id,
                    'user_id' => $token->user_id,
                    'created_at' => $token->created_at
                ]);

                // 删除记录
                $token->delete();

                // 清除相关缓存
                $this->clearMerchantInfoCache($shopId);

                // 同时清除全局token缓存
                Cache::forget(self::ACCESS_TOKEN);
                Cache::forget(self::REFRESH_TOKEN);
                Cache::forget(self::TOKEN_EXPIRE);

                return true;
            } else {
                Log::channel('maiyatian')->warning('未找到需要解绑的token记录', ['shop_id' => $shopId]);
                return false;
            }
        } catch (\Exception $e) {
            Log::channel('maiyatian')->error('解绑token异常', [
                'shop_id' => $shopId,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return false;
        }
    }
}
