<?php

namespace App\Admin\Actions\Imports;


use App\Models\UserWhiteList;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithBatchInserts;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use Maatwebsite\Excel\Concerns\WithStartRow;
use Maatwebsite\Excel\Concerns\WithUpserts;
use PhpOffice\PhpSpreadsheet\Shared\Date;

class WhiteListImport implements ToModel, WithStartRow, WithMultipleSheets, WithBatchInserts, WithUpserts
{
    /**
     * @param array $row
     * @return null
     */
    public function model(array $row)
    {
        $userWhite = UserWhiteList::query()->where('tel', $row[1])->first();
        if (!$userWhite) {
            \App\Models\UserWhiteList::create([
                'name' => $row[0],
                'tel' => $row[1],
                'province' => $row[2],
                'city' => $row[3],
                'county' => $row[4],
                'address_detail' => $row[5],
                'sign_date' => Date::excelToDateTimeObject($row[6]),
            ]);
        }
        return null;
    }


    /**
     * 从第几行开始处理数据 就是不处理标题
     * @return int
     */
    public function startRow(): int
    {
        return 2;
    }

    public function sheets(): array
    {
        return [
            0 => new WhiteListImport(),
        ];
    }

    public function batchSize(): int
    {
        return 1000;
    }


    public function chunkSize(): int
    {
        return 1000;
    }

    public function uniqueBy()
    {
        return 'tel';
    }
}
