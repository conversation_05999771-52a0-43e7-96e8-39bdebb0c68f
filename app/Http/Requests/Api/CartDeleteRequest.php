<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CartDeleteRequest extends FormRequest
{
    public function rules()
    {
        return [
            'type' => ['required', 'integer', Rule::in([1, 2])],
            'ids' => 'required_if:type,1|array',
            'shop_id' => 'required_if:type,2|integer|min:1',
        ];
    }

    public function attributes()
    {
        return [
            'type' => "类型",
            'ids' => "商品",
            'shop_id' => "店铺",
        ];
    }
}
