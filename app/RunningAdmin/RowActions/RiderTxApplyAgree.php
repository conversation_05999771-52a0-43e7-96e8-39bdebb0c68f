<?php


namespace App\RunningAdmin\RowActions;

use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderApply;
use App\Models\RiderProfile;
use App\Models\RiderTxApply;
use App\Services\PayService;
use Carbon\Carbon;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RiderTxApplyAgree extends RowAction
{
    /**
     * 标题
     *
     * @return string
     */
    public function title(): string
    {
        return '通过';
    }

    /**
     * 设置确认弹窗信息，如果返回空值，则不会弹出弹窗
     *
     * 允许返回字符串或数组类型
     *
     * @return array|string|void
     */
    public function confirm()
    {
        return [];
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return \Dcat\Admin\Actions\Response
     */
    public function handle(Request $request)
    {
        // 获取当前行ID
        $id = $this->getKey();
        DB::beginTransaction();
        try {
            $apply = RiderTxApply::query()->with(['user'])->find($id);
            $apply->status = 1;
            $apply->check_time = Carbon::now();
            // 打款
            $payService = app(PayService::class);
            $payService->riderTransfer($apply);
            $apply->trans_status = 1;
            $apply->transfer_time = Carbon::now();
            $apply->save();
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error($e->getMessage())->refresh();
        }

        return $this->response()->success("审核成功")->refresh();

    }


    /**
     * 设置要POST到接口的数据
     *
     * @return array
     */
    public function parameters()
    {
        return [
        ];
    }

}
