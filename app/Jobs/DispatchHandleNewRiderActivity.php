<?php

namespace App\Jobs;

use App\Models\Activity;
use App\Models\ActivityRec;
use App\Models\ActivityRule;
use App\Models\O2oErrandOrder;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DispatchHandleNewRiderActivity implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $child;

    protected $o2oOrder;

    /**
     * DispatchHandleNewUserActivity constructor.
     * @param User $child
     * @param O2oErrandOrder $o2oOrder
     */
    public function __construct($child, $o2oOrder)
    {
        $this->child = $child;
        $this->o2oOrder = $o2oOrder;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->child->parent_id == 0) return;
        if ($this->o2oOrder) {
            if (!$this->valid($this->o2oOrder)) return;
            // 骑手订单激活逻辑
            $rec = ActivityRec::query()->where('user_id', $this->child->parent_id)
                ->where('child_id', $this->child->id)
                ->whereIn('activity_id', function ($query) {
                    return $query->selectRaw('id')->where('type', 2)->from(with((new Activity())->getTable()));
                })
                ->with('activity')->first();
            if ($rec) {
                $orderCount = O2oErrandOrder::query()
                    ->where('rider_id', $this->child->rider->id)
                    ->whereNotNull('finish_time')
                    ->whereNull('refund_at')
                    ->whereBetween('paid_at', [$rec->activity->start_time, $rec->activity->end_time])
                    ->count();
                if ($this->o2oOrder->refund_no) {
                    // 判断用户当前是否没有其它有效订单，则回滚奖励
                    if ($orderCount < $rec->order_limit) {
                        $rec->status = 0;
                        if ($rec->settle_time) {
                            $rec->settle_status = 3;
                        }
                        $rec->save();
                    }
                } else {
                    // 正向逻辑
                    if ($orderCount >= $rec->order_limit && $rec->status == 0) {
                        $rule = $this->getCurrentRule($this->child->parent_id, $rec->activity_id);
                        $rec->pre_reward_amount = $rule ? yuantofen($rule->reward) : 0;
                        $rec->reward_amount = $rule ? yuantofen($rule->reward) : 0;
                        $rec->status = 1;
                        $rec->save();
                    }
                }
            }

            // 骑手活跃奖处理
            $activity = Activity::query()
                ->where('start_time', '<=', $this->o2oOrder->create_time)
                ->where('end_time', '>=', $this->o2oOrder->create_time)
                ->where('status', 1)
                ->where('type', 4)
                ->first();
            if ($activity) {
                // 有用户活跃活动，查询奖励和活动
                $rule = $this->getCurrentRiderOrderRule($activity);
                $rec = ActivityRec::query()->where('user_id', $this->child->id)
                    ->where('activity_id', $activity->id)
                    ->where('child_id', $this->child->id)
                    ->first();
                if (!$rec) {
                    $rec = new ActivityRec();
                    $rec->activity_id = $activity->id;
                    $rec->user_id = $this->child->id;
                    $rec->child_id = $this->child->id;
                    $rec->order_limit = $rule ? $rule->order_count : 0;
                    $rec->pre_reward_amount = $rule ? yuantofen($rule->reward) : 0;
                    $rec->status = 0;
                }
                // 判断订单是否完成活动要求
                $count = O2oErrandOrder::query()
                    ->where('rider_id', $this->child->rider->id)
                    ->whereBetween('create_time', [$activity->start_time, $activity->end_time])
                    ->where('order_status', O2oErrandOrder::STATUS_FINISH)
                    ->whereNull('refund_at')
                    ->count();
                if ($count >= $rule->order_count) {
                    $rec->reward_amount = yuantofen($rule->reward);
                    $rec->status = 1;
                }
                $rec->save();
            }

        } else {
            // 骑手邀请逻辑
            $activity = Activity::query()->where('type', 2)
                ->where('start_time', '<=', $this->child->created_at)
                ->where('end_time', '>=', $this->child->created_at)
                ->where('status', 1)
                ->orderByDesc('id')->first();
            if ($activity) {
                $rec = ActivityRec::query()->where('user_id', $this->child->parent_id)
                    ->where('activity_id', $activity->id)
                    ->where('child_id', $this->child->id)
                    ->first();
                $rule = $this->getCurrentRule($this->child->parent_id, $activity->id);
                if (!$rec) {
                    $rec = new ActivityRec();
                    $rec->activity_id = $activity->id;
                    $rec->user_id = $this->child->parent_id;
                    $rec->child_id = $this->child->id;
                    $rec->order_limit = $rule ? $rule->order_count : 0;
                    $rec->pre_reward_amount = $rule ? yuantofen($rule->reward) : 0;
                    $rec->reward_amount = 0;
                    $rec->status = 0;
                    $rec->save();
                }

            }
        }
    }

    private function caculateAmount()
    {
        return 0;
    }

    private function valid($o2oOrder)
    {
        if (!$o2oOrder->paid_at) {
            return false;
        }
        // 骑手订单
        if ($o2oOrder->rider_id != $this->child->rider->id) {
            return false;
        }

        if (!$o2oOrder->finish_time) {
            return false;
        }
        return true;
    }

    private function getCurrentRule($parentId, $activityId)
    {
        // 邀请用户数
        $activityCount = ActivityRec::query()->where('user_id', $parentId)
            ->where('activity_id', $activityId)
            ->where('status', 1)->count();
        return ActivityRule::query()
            ->where('activity_id', $activityId)
            ->where('start', '<=', $activityCount + 1)
            ->where('end', '>', $activityCount)
            ->orderBy('order_count')->first();

    }

    private function getCurrentRiderOrderRule($activity)
    {
        return ActivityRule::query()
            ->where('activity_id', $activity->id)
            ->orderBy('order_count')->first();
    }
}
