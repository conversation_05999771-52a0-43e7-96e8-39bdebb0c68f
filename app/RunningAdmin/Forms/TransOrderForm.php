<?php

namespace App\RunningAdmin\Forms;

use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\RiderApply;
use App\Services\RiderOrderService;
use App\Services\TaskService;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class TransOrderForm extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $order = O2oErrandOrder::query()->where('id', $this->payload['id'])->with('rider')->first();

        $taskService = new TaskService();
        try {
            $taskService->transTask($order, $input['reason'], $input['rider_id']);
            return $this->response()->success('转单成功')->refresh();
        } catch (\Exception $e) {
            return $this->response()->error($e->getMessage())->refresh();
        }

    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $order = O2oErrandOrder::query()->where('id', $this->payload['id'])->with('rider')->first();

        $rider = $order->rider;

        $this->display('rider_info', '现任骑手')->value($rider->name . PHP_EOL . $rider->phone);

        $service = app(RiderOrderService::class);

        $riders = $service->getRidersByOrder($order, 5);

        $options = [];

        foreach ($riders as $rider) {
            if ($rider['id'] != $order->rider_id) {
                $options[$rider['id']] = sprintf("%s 距离%skm 当前订单%s单", $rider['name'], round($rider['distance'], 2), $rider['order_count']);
            }
        }

        $this->select('rider_id', '改派骑手')->options($options)->required();

        $this->textarea('reason', '原因');
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'rider_info' => '',
            'rider_id' => '',
        ];
    }
}
