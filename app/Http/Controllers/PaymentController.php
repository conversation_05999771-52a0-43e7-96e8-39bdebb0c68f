<?php

namespace App\Http\Controllers;

use App\Models\Common;
use App\Models\Merchant;
use App\Models\MerchantRechargeOrder;
use App\Models\MerchantAccountLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yansongda\Pay\Pay;

class PaymentController extends Controller
{
    /**
     * 支付宝支付回调
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function alipayNotify(Request $request)
    {
        // 配置支付宝
        Pay::config(config('pay'));
        
        try {
            // 验证回调参数是否正确
            $data = Pay::alipay()->callback();
            
            // 找到对应的订单
            $orderNo = $data->out_trade_no;
            
            // 判断订单前缀
            $orderPrefix = substr($orderNo, 0, 2);
            
            if ($orderPrefix == MerchantRechargeOrder::RECHARGE_ORDER_PREFIX) {
                // 商户充值订单
                return $this->handleMerchantRechargeOrder($orderNo, $data->trade_no, Common::PAY_METHOD_ALIPAY);
            }
            
            // 其他类型订单处理...
            
            // 返回成功
            return Pay::alipay()->success();
        } catch (\Exception $e) {
            Log::error('支付宝回调处理异常：' . $e->getMessage());
            return response('fail', 500);
        }
    }
    
    /**
     * 微信支付回调
     *
     * @param Request $request
     * @return \Symfony\Component\HttpFoundation\Response
     */
    public function wechatNotify(Request $request)
    {
        // 配置微信支付
        Pay::config(config('pay'));
        
        try {
            // 验证回调参数是否正确
            $data = Pay::wechat()->callback();
            
            // 找到对应的订单
            $orderNo = $data['resource']['ciphertext']['out_trade_no'];
            $transactionId = $data['resource']['ciphertext']['transaction_id'];
            
            // 判断订单前缀
            $orderPrefix = substr($orderNo, 0, 2);
            
            if ($orderPrefix == MerchantRechargeOrder::RECHARGE_ORDER_PREFIX) {
                // 商户充值订单
                return $this->handleMerchantRechargeOrder($orderNo, $transactionId, Common::PAY_METHOD_WECHAT);
            }
            
            // 其他类型订单处理...
            
            // 返回成功
            return Pay::wechat()->success();
        } catch (\Exception $e) {
            Log::error('微信支付回调处理异常：' . $e->getMessage());
            return response('fail', 500);
        }
    }
    
    /**
     * 处理商户充值订单
     *
     * @param string $orderNo 订单号
     * @param string $paymentNo 支付交易号
     * @param int $payMethod 支付方式
     * @return \Symfony\Component\HttpFoundation\Response
     */
    protected function handleMerchantRechargeOrder($orderNo, $paymentNo, $payMethod)
    {
        // 查找订单
        $order = MerchantRechargeOrder::where('order_no', $orderNo)
                                     ->where('closed', false)
                                     ->whereNull('paid_at')
                                     ->first();
        
        if (!$order) {
            Log::warning('支付回调订单不存在或已处理：' . $orderNo);
            return Pay::alipay()->success(); // 返回成功，避免重复通知
        }
        
        DB::transaction(function () use ($order, $paymentNo, $payMethod) {
            // 更新订单状态
            $order->pay_method = $payMethod;
            $order->payment_no = $paymentNo;
            $order->paid_at = now();
            $order->save();
            
            // 查找商户
            $merchant = Merchant::find($order->merchant_id);
            
            // 记录账户流水
            MerchantAccountLog::create([
                'merchant_id' => $merchant->id,
                'amount' => $order->actual_amount,
                'before_balance' => $merchant->balance,
                'after_balance' => $merchant->balance + $order->actual_amount,
                'type' => MerchantAccountLog::TYPE_RECHARGE,
                'order_no' => $order->order_no,
                'remark' => '账户充值',
            ]);
            
            // 增加商家余额
            $merchant->balance += $order->actual_amount;
            $merchant->save();
        });
        
        // 根据支付方式返回成功
        if ($payMethod == Common::PAY_METHOD_ALIPAY) {
            return Pay::alipay()->success();
        } else {
            return Pay::wechat()->success();
        }
    }
    
    /**
     * 支付宝同步回调
     *
     * @param Request $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function alipayReturn(Request $request)
    {
        try {
            // 验证回调参数是否正确
            Pay::config(config('pay'));
            $data = Pay::alipay()->find($request->out_trade_no);
            
            if ($data->trade_status == 'TRADE_SUCCESS' || $data->trade_status == 'TRADE_FINISHED') {
                // 支付成功，跳转到充值记录页面
                return redirect()->route('merchant.recharge.records')
                                 ->with('success', '充值成功！');
            }
            
            return redirect()->route('merchant.recharge.records')
                             ->with('error', '支付未完成，请稍后查看订单状态');
        } catch (\Exception $e) {
            return redirect()->route('merchant.recharge.records')
                             ->with('error', '支付处理异常：' . $e->getMessage());
        }
    }
} 