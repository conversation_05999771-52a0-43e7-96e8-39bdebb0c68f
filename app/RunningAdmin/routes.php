<?php

use App\Admin\Controllers\RegionController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix' => config('admin.route.prefix'),
    'namespace' => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');

    $router->resource('categories', 'CategoryController');
    $router->resource('/cooperates', 'CooperateController');
    $router->resource('/riders', 'RiderController');
    $router->resource('/coupons', 'CouponController');
    $router->resource('/pricings', 'PricingController');
    $router->resource('/advertisements', 'AdvertisementController');
    $router->resource('/notifies', 'NotifyController');

    $router->resource('/o2o_errand_orders', 'O2oErrandOrderController');

    $router->resource('/activities', 'ActivityController');
    $router->resource('/sites', 'SiteController');
    $router->resource('/configs', 'SystemConfigController');

    $router->resource('/rider_applies', 'RiderApplyController');
    $router->resource('/rider_tx_applies', 'RiderTxApplyController');

    $router->resource('/users', 'UserController');

    $router->resource('/level_settings', 'RiderLevelSettingController');
    $router->resource('/rights', 'RiderRightController');

    $router->get('custom/map', 'SiteController@customMap'); // 自定义地图视图

    $router->resource('site-admin', 'AdminSiteRelController');

    $router->resource('phone_lists', 'PhoneListController');


    $router->group(['prefix' => "options"], function (Router $router) {
        $router->get('provinces', [RegionController::class, 'provinces']);
        $router->get('cities', [RegionController::class, 'cities']);
        $router->get('district', [RegionController::class, 'districts']);
    });
});
