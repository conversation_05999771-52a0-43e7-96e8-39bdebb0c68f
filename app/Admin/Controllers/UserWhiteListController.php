<?php

namespace App\Admin\Controllers;

use App\Admin\Actions\Modal\ErrorOrderModal;
use App\Admin\Actions\Modal\WhiteListModal;
use App\Admin\Repositories\UserWhiteList;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserWhiteListController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserWhiteList(), function (Grid $grid) {
            $grid->tools(function (Grid\Tools $tools) {
                $tools->append(new WhiteListModal());
                $tools->disableBatchActions();
            });
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('user_id');
            $grid->column('tel');
            $grid->column('province');
            $grid->column('city');
            $grid->column('county');
            $grid->column('address_detail');
            $grid->column('active')->using([1 => '激活', 0 => '未激活'])->label([1 => 'success', 0 => 'default']);
            $grid->column('sign_date');

            $grid->export();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->panel();
                $filter->expand();
                $filter->like('tel')->width(3);
                $filter->like('name')->width(3);
                $filter->equal('active')->radio([1 => '激活', 0 => '未激活'])->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserWhiteList(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('user_id');
            $show->field('tel');
            $show->field('province');
            $show->field('city');
            $show->field('county');
            $show->field('address_detail');
            $show->field('active');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserWhiteList(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('user_id');
            $form->text('tel');
            $form->text('province');
            $form->text('city');
            $form->text('county');
            $form->text('address_detail');
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
