<?php

namespace App\Models;

use Carbon\Carbon;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class SystemConfig extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const PlatformSQ = 1;
    const PlatformPT = 2;

    const PlatformMap = [
        self::PlatformSQ => "社区食堂",
        self::PlatformPT => "雨骑士",
    ];

    const PARAM_TECHNICAL_SUPPORT = "technical_support";
    const PARAM_SENDING_TIME = "sending_time";
    const PARAM_CANCEL_ORDER_REASON = "cancel_order_reason";
    const PARAM_REFUND_ORDER_REASON = "refund_order_reason";
    const PARAM_OPEN_HEALTHY_CHECK = 'healthy_check';
    const PARAM_ORDER_QA = 'order_qa';
    const PARAM_ORDER_REDUNDANT_TIME = 'order_redundant_time';
    const PARAM_RIDER_AUTO_APPLY = 'rider_auto_apply';
    const PARAM_REJECT_REASON = 'reject_trans_oder_reason';
    const PARAM_DISTANCE_SCHEME = "distance_scheme";
    const PARAM_RIDER_REJECT_ORDER_REASON = 'rider_reject_order_reason';
    const PARAM_NEW_USER_COUPON = 'new_user_coupon';
    const PARAM_PHONE_PROTECT_EXPIRE_TIME = "phone_protect_expire_time";

    const ParamRemarkMapForSq = [
        self::PARAM_TECHNICAL_SUPPORT => "技术支持",
        self::PARAM_SENDING_TIME => "配送时间",
        self::PARAM_CANCEL_ORDER_REASON => "取消订单原因",
        self::PARAM_REFUND_ORDER_REASON => "申请售后原因",
    ];

    const ParamRemarkMapForPt = [
        self::PARAM_TECHNICAL_SUPPORT => "技术支持",
        self::PARAM_CANCEL_ORDER_REASON => "取消订单原因",
        self::PARAM_OPEN_HEALTHY_CHECK => "开启健康申报",
        self::PARAM_ORDER_QA => "订单常见问题",
        self::PARAM_ORDER_REDUNDANT_TIME => "订单冗余时间/分钟",
        self::PARAM_RIDER_AUTO_APPLY => '骑手申请自动通过',
        self::PARAM_REJECT_REASON => '转单拒绝原因',
        self::PARAM_DISTANCE_SCHEME => "配送距离方案",
        self::PARAM_RIDER_REJECT_ORDER_REASON => '骑手拒绝接单原因',
        self::PARAM_NEW_USER_COUPON => '新人优惠券',
        self::PARAM_PHONE_PROTECT_EXPIRE_TIME => '号码隐私保护过期时间/分钟',
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public static function cacheKey($platform, $name): string
    {
        return sprintf("%s/%s-%s", 'system_config', $platform, $name);
    }

    public static function getCacheConfigValue($platform, $name)
    {
        $cacheKey = self::cacheKey($platform, $name);
        if (Cache::has($cacheKey)) {
            return Cache::get($cacheKey);
        } else {
            $value = null;
            $config = self::query()->where('name', $name)->where('platform', $platform)->first();
            if ($config) {
                if ($config->is_json) {
                    $value = json_decode($config->value, true);
                } else {
                    $value = $config->value;
                }
            }
            if ($value) {
                Cache::put($cacheKey, $value, Carbon::now()->addMinutes(15));
            }
            return $value;
        }
//        return Cache::remember($cacheKey, 15, function () use ($platform, $name) {});
    }

    protected static function boot()
    {
        parent::boot();
        static::updated(function ($model) {
            Cache::forget(self::cacheKey($model->platform, $model->name));
        });
    }

}
