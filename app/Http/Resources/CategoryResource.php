<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'pid' => $this->pid,
            'title' => $this->title,
            'sub_title' => $this->sub_title,
            'view_order' => $this->view_order,
            'pic' => $this->pic ? img_url($this->pic) : "",
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
