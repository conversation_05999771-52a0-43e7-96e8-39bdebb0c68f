<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\CategoryResource;
use App\Models\Category;
use Illuminate\Http\Request;

class CategoryController extends Controller
{
    public function index(Request $request)
    {
        $query = Category::query();
        if ($request->pid) {
            $query->where("pid", $request->pid);
        } else {
            $query->where("pid", 0);
        }

        $categories = $query->orderBy("view_order")->orderBy("id")->get();
        return $this->success("success", CategoryResource::collection($categories));
    }
}
