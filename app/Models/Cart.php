<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cart extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id', 'shop_id', 'spu_id', 'quantity'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, "user_id");
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class, "shop_id");
    }

    public function spu()
    {
        return $this->belongsTo(ShopSpu::class, "spu_id");
    }
}
