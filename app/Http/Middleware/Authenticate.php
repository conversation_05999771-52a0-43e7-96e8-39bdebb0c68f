<?php

namespace App\Http\Middleware;

use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
        if (! $request->expectsJson()) {
            // 根据当前使用的守卫决定重定向到哪个登录页面
            if ($request->is('merchant*')) {
                return route('merchant.login');
            } elseif ($request->is('admin*')) {
                // Dcat Admin 的登录页面通常是 admin/auth/login
                return '/admin/auth/login';
            }
            
            // 如果没有默认的login路由，可以创建一个或重定向到首页
            return route('home');
        }
    }
}
