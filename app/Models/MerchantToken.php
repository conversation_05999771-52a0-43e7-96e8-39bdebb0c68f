<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantToken extends Model
{
    use HasFactory;
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'user_id',
        'platform',
        'access_token',
        'refresh_token',
        'expire_time',
        'refresh_expire_time',
        'shop_id',
        'extra_data',
    ];
    
    /**
     * 应被转换为日期的属性
     *
     * @var array
     */
    protected $dates = [
        'expire_time',
        'refresh_expire_time',
        'created_at',
        'updated_at',
    ];
    
    /**
     * 应该被转换成原生类型的属性
     *
     * @var array
     */
    protected $casts = [
        'extra_data' => 'json',
    ];

    public function merchant()
    {
        return $this->belongsTo(Merchant::class, 'merchant_id', 'id');
    }
}
