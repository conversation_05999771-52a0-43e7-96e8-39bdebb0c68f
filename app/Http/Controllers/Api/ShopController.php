<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\ShopResource;
use App\Models\Common;
use App\Models\Community;
use App\Models\Shop;
use App\Services\LocationService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ShopController extends Controller
{
    protected int $distance = 5000;

    public function __construct()
    {
        if (app()->environment() != 'production') {
            $this->distance = 5000;
        }
    }

    public function index(Request $request, LocationService $locationService)
    {
        $lng = $request->lng;
        $lat = $request->lat;
        if (!$lng || !$lat) { //没有传经纬度
            $location = $locationService->getLocationFromIp($request->getClientIp());
            $lng = $location[0];
            $lat = $location[1];
        }

        $redisLocations = Shop::GetShopFromRedis($lng, $lat, $this->distance);

        $shopIdList = [];
        $shopsDistance = [];
        foreach ($redisLocations as $location) {
            $shopIdList[] = $location[0];
            $shopsDistance[$location[0]] = $location[1];
        }

        $query = Shop::query()->whereIn('id', $shopIdList);

        if ($request->community_id) $query->where("community_id", $request->community_id);

        $query->orderByRaw(DB::raw("FIND_IN_SET(id, '" . implode(',', $shopIdList) . "'" . ')')); //按照指定顺序排序

        $shops = $query->get();

        $result = [];
        foreach ($shops as $shop) {
            $result[] = $this->formatShop($shop, $shopsDistance[$shop->id]);
        }

        return $this->success('success', $result);
    }

    public function show($id)
    {
        return $this->success("success", $this->formatShop(Shop::query()->findOrFail($id), '0'));
    }

    private function formatShop($shop, $distance)
    {
        return [
            'id' => $shop->id,
            "community_id" => $shop->community_id,
            'name' => $shop->name,
            'promotion_info' => $shop->promotion_info,
            'province' => $shop->regions[0] ?? $shop->province,
            'city' => $shop->regions[1] ?? $shop->city,
            'district' => $shop->regions[2] ?? $shop->district,
            'address_detail' => $shop->address_detail,
            'logo' => img_url($shop->logo),
            'cover' => img_url($shop->cover),
            'tel' => $shop->tel,
            'created_at' => $shop->created_at,
            'updated_at' => $shop->updated_at,
            'distance' => formatDistance($distance),
        ];
    }

}
