<?php

namespace App\RunningAdmin\Controllers;

use App\Models\PhoneList;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class PhoneListController extends AdminController
{
    protected $title = '手机白名单';
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new PhoneList(), function (Grid $grid) {
            $grid->enableDialogCreate();
            $grid->disableBatchActions();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->column('id')->sortable();
            $grid->column('phone', '手机号');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('phone', '手机号')->width(4);

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new PhoneList(), function (Show $show) {
            $show->field('id');
            $show->field('phone', '手机号');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new PhoneList(), function (Form $form) {
            $form->display('id');
            $form->text('phone', '手机号');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
