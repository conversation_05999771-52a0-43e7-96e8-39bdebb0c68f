<?php

namespace App\Jobs;

use App\Models\O2oErrandOrder;
use App\Services\MaiYaTianService;
use App\Services\O2oErrandOrderService;
use App\Services\WrcService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

// 代表这个类需要被放到队列中执行，而不是触发时立即执行
class RefundO2oErrandOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $orderNo;

    public function __construct($orderNo, $delay)
    {
        $this->orderNo = $orderNo;
        // 设置延迟的时间，delay() 方法的参数代表多少秒之后执行
        $this->delay($delay);
    }

    // 定义这个任务类具体的执行逻辑
    // 当队列处理器从队列中取出任务时，会调用 handle() 方法
    public function handle()
    {
        $order = O2oErrandOrder::query()->where('order_no', $this->orderNo)->first();
        if ($order->order_status != O2oErrandOrder::STATUS_PAID) {
            return;
        }
        if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT) {
            return;
        }
        try {
            app(O2oErrandOrderService::class)->refundOrder($order->order_no, "30分钟未接单自动取消");
            try {
                switch ($order->app_key) {
                    case O2oErrandOrder::APP_KEY_MYT:
                        (new MaiYaTianService())->deliveryChange($order->user_id, $order->order_no, $order->out_order_no, "CANCEL", ["cancel_type" => 1002, "cancel_reason" => "没有骑手接单"]);
                        break;
                    case O2oErrandOrder::APP_KEY_WRC:
                        (new WrcService())->deliveryChange($order->user_id, $order->order_no, $order->out_order_no, "CANCEL", ["cancel_type" => 1002, "cancel_reason" => "没有骑手接单"]);
                        break;
                }
            }catch (\Exception $ee) {
                Log::error($order->app_key."-配送状态同步：" . $ee->getMessage());
            }
        } catch (\Exception $e) {
            Log::error("帮我*订单-" . $order->order_no . "自动退款失败：" . $e->getMessage());
        }
    }
}
