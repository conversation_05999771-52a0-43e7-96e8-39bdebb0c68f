<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\CartChangeRequest;
use App\Http\Requests\Api\CartDeleteRequest;
use App\Http\Requests\Api\CartListRequest;
use App\Http\Resources\CartResource;
use App\Models\Cart;
use App\Models\Common;
use App\Models\ShopSpu;

class CartController extends Controller
{
    public function index(CartListRequest $request)
    {
        // shop_id(必填)
        $carts = Cart::query()->with(["spu"])
            ->where("user_id", $request->user()->id)
            ->where("shop_id", $request->shop_id)
            ->orderBy("id", "desc")
            ->get();
        return $this->success("success", CartResource::collection($carts));
    }

    public function change(CartChangeRequest $request)
    {
        //spu_id, quantity
        $spu = ShopSpu::query()->find($request->spu_id);
        if (!$spu)  $this->errorResponse(500, "该商品不存在");
        if ($spu->status != Common::STATUS_UP)  $this->errorResponse(500, "该商品已下架");
        if ($request->quantity > 0) {
            if ($spu->stock < $request->quantity)  $this->errorResponse(500, "该商品库存不足");
        }
        $cartInfo = Cart::query()->where("user_id", $request->user()->id)->where("spu_id", $request->spu_id)->first();
        $res = null;
        if ($cartInfo) {
            if ($request->quantity > 0) {
                if ($request->quantity != $cartInfo->quantity) {
                    $cartInfo->update(["quantity" => $request->quantity]);
                }
                $res = $cartInfo;
            }else{
                $cartInfo->delete();
            }
        } else {
            if ($request->quantity <= 0) {
                return $this->errorResponse(500, "商品数量至少为1");
            }
            $cartInfo = Cart::query()->create([
                'user_id' => $request->user()->id,
                'shop_id' => $spu->shop_id,
                'spu_id' => $spu->id,
                'quantity' => $request->quantity
            ]);
            $res = $cartInfo;
        }
        return $this->success("添加成功", $res ? new CartResource($res) : null);
    }

    public function delete(CartDeleteRequest $request)
    {
        //type 类型：1-指定删除（要传指定ID）；2-清空
        switch ($request->type) {
            case 1:
                Cart::query()->where("user_id", $request->user()->id)->whereIn("id", $request->ids)->delete();
                break;
            case 2:
                Cart::query()->where("user_id", $request->user()->id)->where("shop_id", $request->shop_id)->delete();
                break;
            default:
                return $this->errorResponse(500, "请选择删除类型");
        }
        return $this->success("删除成功", null);
    }
}
