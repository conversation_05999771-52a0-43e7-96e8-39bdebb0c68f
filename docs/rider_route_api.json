{"openapi": "3.0.1", "info": {"title": "骑手多路径规划API", "description": "骑手APP多路径规划功能相关接口文档", "version": "1.0.0"}, "servers": [{"url": "http://api.example.com/api/v1", "description": "API服务器"}], "paths": {"/rider/route/multi_task": {"post": {"tags": ["路径规划"], "summary": "获取骑手多任务的最优路径规划", "description": "系统自动根据时间窗口和位置关联性来确定需要进行路径优化的任务，计算最优配送路径", "operationId": "multiTaskRoute", "security": [{"bearerAuth": []}], "requestBody": {"description": "骑手位置和筛选条件", "content": {"application/json": {"schema": {"type": "object", "required": ["lat", "lng"], "properties": {"lat": {"type": "number", "description": "骑手当前纬度", "example": 30.282412}, "lng": {"type": "number", "description": "骑手当前经度", "example": 120.128412}, "time_window": {"type": "integer", "description": "时间窗口大小(分钟)，可选参数", "minimum": 15, "maximum": 120, "default": 60, "example": 60}, "max_distance": {"type": "integer", "description": "最大距离(米)，可选参数", "minimum": 1000, "maximum": 20000, "default": 5000, "example": 5000}}}}}, "required": true}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "object", "properties": {"route_id": {"type": "integer", "description": "路径规划ID", "example": 123}, "total_distance": {"type": "integer", "description": "总距离(米)", "example": 7500}, "estimated_time": {"type": "integer", "description": "预计时间(秒)", "example": 1800}, "waypoints": {"type": "array", "description": "路径点数据", "items": {"type": "object"}}, "savings": {"type": "object", "description": "节省信息", "properties": {"time_savings": {"type": "integer", "description": "节省时间(秒)", "example": 900}, "distance_savings": {"type": "integer", "description": "节省距离(米)", "example": 2300}, "percentage_time": {"type": "integer", "description": "节省时间百分比", "example": 25}, "percentage_distance": {"type": "integer", "description": "节省距离百分比", "example": 20}}}, "tasks": {"type": "array", "description": "任务信息", "items": {"type": "object"}}, "task_count": {"type": "integer", "description": "优化后包含的任务数量", "example": 3}, "total_task_count": {"type": "integer", "description": "骑手所有活跃任务数量", "example": 5}}}}}}}}, "400": {"description": "任务数量不足，无需路径优化", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "当前任务数量不足，无需路径优化"}, "data": {"type": "object", "properties": {"has_optimization": {"type": "boolean", "example": false}, "task_count": {"type": "integer", "example": 1}}}}}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "您不是骑手，无法使用此功能"}, "data": {"type": "null"}}}}}}, "422": {"description": "参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 422}, "message": {"type": "string", "example": "参数错误"}, "data": {"type": "object"}}}}}}}}}, "/rider/route/detail/{id}": {"get": {"tags": ["路径规划"], "summary": "获取路径规划详情", "description": "根据路径规划ID获取详细信息", "operationId": "routeDetail", "security": [{"bearerAuth": []}], "parameters": [{"name": "id", "in": "path", "description": "路径规划ID", "required": true, "schema": {"type": "integer", "format": "int64"}}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "object", "properties": {"id": {"type": "integer", "description": "路径规划ID", "example": 123}, "rider_id": {"type": "integer", "description": "骑手ID", "example": 456}, "total_distance": {"type": "integer", "description": "总距离(米)", "example": 7500}, "estimated_time": {"type": "integer", "description": "预计时间(秒)", "example": 1800}, "time_savings": {"type": "integer", "description": "节省时间(秒)", "example": 900}, "distance_savings": {"type": "integer", "description": "节省距离(米)", "example": 2300}, "percentage_time": {"type": "integer", "description": "节省时间百分比", "example": 25}, "percentage_distance": {"type": "integer", "description": "节省距离百分比", "example": 20}, "status": {"type": "string", "description": "状态：pending待确认,accepted已接受,rejected已拒绝", "example": "pending"}, "route": {"type": "array", "description": "路径数据", "items": {"type": "object"}}, "waypoints": {"type": "array", "description": "路径点数据", "items": {"type": "object"}}, "tasks": {"type": "array", "description": "任务信息", "items": {"type": "object"}}}}}}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "您不是骑手，无法使用此功能"}, "data": {"type": "null"}}}}}}, "404": {"description": "未找到资源", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "未找到路径规划信息"}, "data": {"type": "null"}}}}}}}}}, "/rider/route/save": {"post": {"tags": ["路径规划"], "summary": "保存用户选择的路径", "description": "骑手确认接受或拒绝优化路径", "operationId": "saveRoute", "security": [{"bearerAuth": []}], "requestBody": {"description": "路径确认信息", "content": {"application/json": {"schema": {"type": "object", "required": ["route_id", "is_accepted"], "properties": {"route_id": {"type": "integer", "description": "路径规划ID", "example": 123}, "is_accepted": {"type": "boolean", "description": "是否接受优化路径", "example": true}}}}}, "required": true}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "保存成功"}, "data": {"type": "null"}}}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "您不是骑手，无法使用此功能"}, "data": {"type": "null"}}}}}}, "404": {"description": "未找到资源", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "未找到路径规划信息或无权操作"}, "data": {"type": "null"}}}}}}, "422": {"description": "参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 422}, "message": {"type": "string", "example": "参数错误"}, "data": {"type": "object"}}}}}}}}}, "/rider/route/navigate": {"post": {"tags": ["路径规划"], "summary": "开始导航", "description": "获取导航信息并开始导航", "operationId": "startNavigation", "security": [{"bearerAuth": []}], "requestBody": {"description": "导航请求信息", "content": {"application/json": {"schema": {"type": "object", "required": ["route_id", "lat", "lng"], "properties": {"route_id": {"type": "integer", "description": "路径规划ID", "example": 123}, "lat": {"type": "number", "description": "骑手当前纬度", "example": 30.282412}, "lng": {"type": "number", "description": "骑手当前经度", "example": 120.128412}}}}}, "required": true}, "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "object", "properties": {"current_location": {"type": "object", "description": "当前位置", "properties": {"lat": {"type": "number", "example": 30.282412}, "lng": {"type": "number", "example": 120.128412}}}, "nearest_point": {"type": "object", "description": "最近的导航点"}, "distance_to_nearest": {"type": "integer", "description": "到最近点的距离(米)", "example": 350}, "duration_to_nearest": {"type": "integer", "description": "到最近点的时间(秒)", "example": 180}, "remaining_route": {"type": "array", "description": "剩余路线", "items": {"type": "object"}}, "remaining_distance": {"type": "integer", "description": "剩余距离(米)", "example": 5000}, "remaining_time": {"type": "integer", "description": "剩余时间(秒)", "example": 1200}, "task_info": {"type": "object", "description": "任务信息", "properties": {"id": {"type": "integer", "example": 789}, "order_no": {"type": "string", "example": "ORD2023120112345"}, "type": {"type": "string", "example": "pickup"}, "status": {"type": "string", "example": "accepted"}, "address": {"type": "string", "example": "浙江省杭州市临安区滨湖天地"}, "name": {"type": "string", "example": "张三"}, "phone": {"type": "string", "example": "13812345678"}}}, "amap_navigation": {"type": "object", "description": "高德地图导航信息", "properties": {"origin": {"type": "string", "example": "120.128412,30.282412"}, "destination": {"type": "string", "example": "120.135678,30.287654"}, "waypoints": {"type": "string", "example": "120.145678,30.297654;120.155678,30.307654"}}}}}}}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "您不是骑手，无法使用此功能"}, "data": {"type": "null"}}}}}}, "404": {"description": "未找到资源", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 404}, "message": {"type": "string", "example": "未找到路径规划信息"}, "data": {"type": "null"}}}}}}, "422": {"description": "参数错误", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 422}, "message": {"type": "string", "example": "参数错误"}, "data": {"type": "object"}}}}}}}}}, "/rider/route/savings": {"get": {"tags": ["路径规划"], "summary": "查询路径优化节省信息", "description": "查询当前任务的路径优化可能节省的时间和距离", "operationId": "routeSavings", "security": [{"bearerAuth": []}], "responses": {"200": {"description": "成功响应", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 0}, "message": {"type": "string", "example": "成功"}, "data": {"type": "object", "properties": {"has_optimization": {"type": "boolean", "description": "是否有优化空间", "example": true}, "task_count": {"type": "integer", "description": "任务数量", "example": 3}, "time_savings": {"type": "integer", "description": "可节省时间(秒)", "example": 900}, "distance_savings": {"type": "integer", "description": "可节省距离(米)", "example": 2300}}}}}}}}, "400": {"description": "任务数量不足，无需路径优化", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 400}, "message": {"type": "string", "example": "当前任务数量不足，无需路径优化"}, "data": {"type": "object", "properties": {"has_optimization": {"type": "boolean", "example": false}, "task_count": {"type": "integer", "example": 1}}}}}}}}, "403": {"description": "权限不足", "content": {"application/json": {"schema": {"type": "object", "properties": {"code": {"type": "integer", "example": 403}, "message": {"type": "string", "example": "您不是骑手，无法使用此功能"}, "data": {"type": "null"}}}}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT"}}}}