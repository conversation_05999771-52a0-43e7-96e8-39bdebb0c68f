<?php

namespace App\Http\Requests\Api;

class UserRequest extends FormRequest
{
    public function rules()
    {
        switch (request()->method) {
            case 'POST':
                return [
                    'verification_key' => 'required|string',
                    'verification_code' => 'required|string',
                    'password' => 'sometimes|required|alpha_dash|min:6|confirmed',
                    'invite_code' => 'sometimes|required'
                ];
            case 'PATCH':
                return [
                    'avatar_image_id' => 'sometimes|required',
                ];

        }

    }

    public function attributes()
    {
        return [
            'verification_key' => '短信验证码 key',
            'verification_code' => '短信验证码',
        ];
    }
}
