<?php


namespace App\Services;


use App\Exceptions\BusinessException;
use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class RiderOrderService
{
    private $commonService;

    public function __construct()
    {
        $this->commonService = app(CommonService::class);
    }

    public function pickupOrder($order, $rider, $dispatchStatus = 0)
    {
        $data = [
            'order_status' => $order->type == O2oErrandOrder::TYPE_HELP ? O2oErrandOrder::STATUS_DELIVERY : O2oErrandOrder::STATUS_PICKUP,
            'rider_id' => $rider->id,
            'receipt_time' => Carbon::now(),
        ];

        if ($rider->role == Rider::RolePartTime) {
            $data['reward_amount'] = $order->reward_amount_part;
            $detail = $order['detail'];
            $detail['reward_detail'] = $detail['part'];
            $data['detail'] = $detail;
        } else {
            $data['reward_amount'] = $order->reward_amount_full;
            $detail = $order['detail'];
            $detail['reward_detail'] = $detail['full'];
            $data['detail'] = $detail;
        }

        if ($dispatchStatus) {
            $data['dispatch_status'] = $dispatchStatus;
        }
        $count = O2oErrandOrder::query()->where('order_no', $order->order_no)
            ->where('order_status', O2oErrandOrder::STATUS_PAID)->update($data);

        if ($count == 0) {
            throw new BusinessException('订单被抢啦，看看其它订单吧～');
        }
    }

    /**
     * 骑手校验
     * @param $order
     * @param $user
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws BusinessException
     */
    public function checkRider($order, $user)
    {
        $rider = Rider::query()->where('user_id', $user->id)->first();

        if (!$this->commonService->getHealthyStatus($user->id)) {
            throw new BusinessException('今日还没有做健康申报 无法接单');
        }

        if ($rider->id_card_certified == 0) {
            throw new BusinessException('骑手未实名认证，无法接单');
        }

        if ($rider->face_certified == 0) {
            throw new BusinessException('骑手未人脸认证，无法接单');
        }

        if ($rider->status == 0) {
            throw new BusinessException('骑手已离线，无法接单');
        }

//        $count = O2oErrandOrder::query()->where('rider_id', $rider->id)
//            ->whereBetween('receipt_time', [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()])
//            ->whereIn('order_status', [O2oErrandOrder::STATUS_FINISH,
//                O2oErrandOrder::STATUS_PICKUP,
//                O2oErrandOrder::STATUS_DELIVERY,
//                O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT])
//            ->whereNull('refund_at')->count();
//        if ($count >= $rider->order_limit) {
//            throw new BusinessException('已到接单上限制，明天再继续吧～');
//        }

        return $rider;

    }

    /**
     * @param $orderNo
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws BusinessException
     */
    public function checkOrder($orderNo)
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) {
            throw new BusinessException('订单不存在');
        }
        if ($order->order_status != O2oErrandOrder::STATUS_PAID) {
            throw new BusinessException('订单状态发生改变，无法接单');
        }

        return $order;
    }

    public function getRidersByOrder($order, $distance)
    {
        if ($order->type == O2oErrandOrder::TYPE_BUY) {
            $lng = $order->deliver_lng;
            $lat = $order->deliver_lat;
        } else {
            $lng = $order->pickup_lng;
            $lat = $order->pickup_lat;
        }

        $redisLocations = Location::getRiderFromRedis($lng, $lat, $distance);
        $riderIdList = [];
        $riderDistance = [];
        foreach ($redisLocations as $location) {
            $riderIdList[] = $location[0];
            $riderDistance[$location[0]] = [
                'distance' => $location[1],
                'lgn' => $location[2][0],
                'lat' => $location[2][1],
            ];
        }
        $riders = Rider::query()->where('status', Rider::STATUS_UP)
            ->whereIn('id', $riderIdList)
            ->orderByRaw(DB::raw("FIND_IN_SET(id, '" . implode(',', $riderIdList) . "'" . ')'))
            ->get();
        foreach ($riders as $rider) {
            $orders = O2oErrandOrder::query()->where('rider_id', $rider->id)->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT)
                ->where('order_status', [O2oErrandOrder::STATUS_PICKUP, O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, O2oErrandOrder::STATUS_DELIVERY])->get();
            $riderList[] = [
                'id' => $rider->id,
                'name' => $rider->name,
                'phone' => $rider->phone,
                'avatar' => $rider->avatar_url,
                'order_count' => $rider->getOrderCount(),
                'distance' => $riderDistance[$rider->id]['distance'],
                'lgn' => $riderDistance[$rider->id]['lgn'],
                'lat' => $riderDistance[$rider->id]['lat'],
                'direction' => $this->getRiderDirection($riderDistance[$rider->id]['lgn'], $riderDistance[$rider->id]['lat'], $orders),
            ];
        }

        return $riderList;
    }

    private function getRiderDirection($lng, $lat, $orders)
    {
        $direction = [];
        foreach ($orders as $order) {
            if ($order->order_status == O2oErrandOrder::STATUS_PICKUP) {
                if ($order->pickup_lng) {
                    $direction[] = getAngle($lng, $lat, $order->pickup_lng, $order->pickup_lat, 2);
                } else {
                    $direction[] = getAngle($lng, $lat, $order->deliver_lng, $order->deliver_lat, 2);
                }
            } else {
                $direction[] = getAngle($lng, $lat, $order->deliver_lng, $order->deliver_lat, 2);
            }
        }
        if ($direction) {
            return $direction[0];
        }
        return '';
    }
}
