<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class DriverLiscenseRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'cover' => 'required',
            'obverse' => 'required',
            'card_no' => 'required',
            'name' => 'required',
            'vehicle_type' => 'required',
            'exp_time_type' => 'required',
            'start_time' => 'required',
            'end_time' => 'required',
            'create_time' => 'required',
        ];
    }
}
