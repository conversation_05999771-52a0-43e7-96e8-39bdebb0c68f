<?php

namespace App\Http\Controllers\Merchant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use App\Models\MerchantAccountLog;
use App\Models\Merchant;

class AccountController extends Controller
{
    /**
     * 显示账户流水列表
     */
    public function logs(Request $request)
    {
        $merchant = Auth::guard('merchant')->user();
        
        $query = MerchantAccountLog::where('merchant_id', $merchant->id)
            ->orderBy('created_at', 'desc');
            
        // 按类型筛选
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }
        
        // 按时间范围筛选
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }
        
        $logs = $query->paginate(15);
        
        return view('merchant.account.logs', [
            'logs' => $logs,
            'currentType' => $request->type,
            'startDate' => $request->start_date,
            'endDate' => $request->end_date,
        ]);
    }
    
    /**
     * 记录账户变动
     * 
     * @param int $merchantId 商家ID
     * @param int $amount 变动金额(分)
     * @param string $type 变动类型
     * @param string|null $orderNo 关联订单号
     * @param string|null $remark 备注说明
     */
    public static function recordAccountChange($merchantId, $amount, $type, $orderNo = null, $remark = null)
    {
        $merchant = Merchant::findOrFail($merchantId);
        
        $beforeBalance = $merchant->balance;
        $afterBalance = $beforeBalance + $amount;
        
        // 更新商家余额
        $merchant->balance = $afterBalance;
        $merchant->save();
        
        // 记录账户变动
        MerchantAccountLog::create([
            'merchant_id' => $merchantId,
            'amount' => $amount,
            'before_balance' => $beforeBalance,
            'after_balance' => $afterBalance,
            'type' => $type,
            'order_no' => $orderNo,
            'remark' => $remark,
        ]);
    }
}
