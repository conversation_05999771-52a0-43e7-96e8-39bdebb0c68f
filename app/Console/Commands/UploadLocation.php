<?php

namespace App\Console\Commands;

use App\Models\Community;
use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Shop;
use App\Models\Site;
use Illuminate\Console\Command;

class UploadLocation extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'upload:location';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // 社区
        Community::PushLocation();
        // 店铺
        Shop::PushLocation();
        // 订单
        O2oErrandOrder::PushLocation();
        // 位置
        Site::PushLocation();

        return Command::SUCCESS;
    }
}
