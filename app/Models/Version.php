<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Version extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $casts = ['current' => 'boolean'];

    const StatusMap = [
        0 => '未发布',
        1 => '审核中',
        2 => '已发布'
    ];

    const platformMap = [
        SystemConfig::PlatformSQ => '社区食堂',
        SystemConfig::PlatformPT => '雨骑士'
    ];

    const SystemMap = [
        1 => 'android',
        2 => 'ios',
    ];

    const AppTypeMap = [
        1 => 'app',
        2 => '微信小程序'
    ];

}
