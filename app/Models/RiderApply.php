<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RiderApply extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const StatusTextMap = [
        0 => '待审核',
        1 => '审核通过',
        2 => '拒绝'
    ];

    public function user() {
        return $this->belongsTo(User::class, 'user_id');
    }
}
