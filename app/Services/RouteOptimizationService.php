<?php

namespace App\Services;

use App\Models\O2oErrandOrder;
use App\Models\Rider;


class RouteOptimizationService
{
    /**
     * @var MapService
     */
    protected $mapService;

    /**
     * @var TaskService
     */
    protected $taskService;

    /**
     * 路径规划服务构造函数
     */
    public function __construct(MapService $mapService, TaskService $taskService)
    {
        $this->mapService = $mapService;
        $this->taskService = $taskService;
    }

    /**
     * 获取多个点之间的距离矩阵
     *
     * @param array $points 点的集合，每个点是包含经纬度的数组 ['lat' => 纬度, 'lng' => 经度]
     * @return array 距离矩阵
     */
    public function getDistanceMatrix(array $points): array
    {
        $n = count($points);
        $distanceMatrix = array_fill(0, $n, array_fill(0, $n, 0));

        // 批量处理，每次最多处理100对点(根据高德API限制调整)
        $batchSize = 100;
        $batches = [];

        for ($i = 0; $i < $n; $i++) {
            for ($j = $i + 1; $j < $n; $j++) {
                $batches[] = [
                    'origin_index' => $i,
                    'dest_index' => $j,
                    'origin' => $points[$i],
                    'destination' => $points[$j]
                ];

                // 达到批量上限或最后一对，发送请求
                if (count($batches) >= $batchSize || ($i == $n-2 && $j == $n-1)) {
                    $origins = array_column($batches, 'origin');
                    $destinations = array_column($batches, 'destination');

                    $response = $this->mapService->batchDistanceMatrix($origins, $destinations);

                    // 处理响应结果
                    if (isset($response['results']) && is_array($response['results'])) {
                        foreach ($response['results'] as $k => $result) {
                            // 确保索引存在于batches数组中
                            if (!isset($batches[$k])) {
                                continue;
                            }

                            $i = $batches[$k]['origin_index'];
                            $j = $batches[$k]['dest_index'];

                            $distance = $result['distance'] ?? 0;
                            $distanceMatrix[$i][$j] = $distance;
                            $distanceMatrix[$j][$i] = $distance; // 距离矩阵是对称的
                        }
                    }

                    // 清空批次
                    $batches = [];
                }
            }
        }

        return $distanceMatrix;
    }

    /**
     * 最近邻算法实现路径规划
     *
     * @param array $points 包含所有点的数组，每个点包含 ['lat' => 纬度, 'lng' => 经度, 'type' => 类型(pickup/delivery), 'order_id' => 订单ID]
     * @param int $startPointIndex 起始点索引
     * @return array 最优路径的点索引顺序
     */
    public function nearestNeighborAlgorithm(array $points, int $startPointIndex = 0): array
    {
        $n = count($points);
        $distanceMatrix = $this->getDistanceMatrix($points);

        $visited = array_fill(0, $n, false);
        $path = [$startPointIndex];
        $visited[$startPointIndex] = true;

        for ($i = 1; $i < $n; $i++) {
            $lastPoint = $path[$i - 1];
            $minDistance = PHP_INT_MAX;
            $nextPoint = -1;

            for ($j = 0; $j < $n; $j++) {
                if (!$visited[$j] && $distanceMatrix[$lastPoint][$j] < $minDistance) {
                    $minDistance = $distanceMatrix[$lastPoint][$j];
                    $nextPoint = $j;
                }
            }

            if ($nextPoint != -1) {
                $path[] = $nextPoint;
                $visited[$nextPoint] = true;
            }
        }

        return $path;
    }

    /**
     * 获取订单路径规划
     *
     * @param array $riderLocation 骑手当前位置 ['lat' => 纬度, 'lng' => 经度]
     * @param array $orders 订单数组，每个订单包含 ['id' => 订单ID, 'pickup' => 取货点, 'delivery' => 送货点]
     *                    取货点和送货点都是包含经纬度的数组 ['lat' => 纬度, 'lng' => 经度]
     * @return array 优化后的路径
     */
    public function getOptimizedRoute(array $riderLocation, array $orders): array
    {
        // 收集所有点
        $allPoints = [];
        $pointDetails = [];

        // 添加骑手当前位置
        $allPoints[] = $riderLocation;
        $pointDetails[] = [
            'type' => 'rider',
            'order_id' => null,
            'lat' => $riderLocation['lat'],
            'lng' => $riderLocation['lng']
        ];

        // 添加所有订单的取货点和送货点
        foreach ($orders as $order) {
            $allPoints[] = $order['pickup'];
            $pointDetails[] = [
                'type' => 'pickup',
                'order_id' => $order['id'],
                'lat' => $order['pickup']['lat'],
                'lng' => $order['pickup']['lng']
            ];

            $allPoints[] = $order['delivery'];
            $pointDetails[] = [
                'type' => 'delivery',
                'order_id' => $order['id'],
                'lat' => $order['delivery']['lat'],
                'lng' => $order['delivery']['lng']
            ];
        }

        // 使用最近邻算法获取路径规划
        $pathIndices = $this->nearestNeighborAlgorithm($allPoints);

        // 构建最终路径
        $optimizedRoute = [];
        foreach ($pathIndices as $index) {
            $optimizedRoute[] = $pointDetails[$index];
        }

        return $optimizedRoute;
    }

    /**
     * 获取优化路径后的详细信息
     *
     * @param array $riderLocation 骑手当前位置
     * @param array $orders 订单数组
     * @return array 路径详细信息，包括总距离、预计时间等
     */
    public function getRouteDetails(array $riderLocation, array $orders): array
    {
        $optimizedRoute = $this->getOptimizedRoute($riderLocation, $orders);
        $totalDistance = 0;
        $estimatedTime = 0;
        $waypoints = [];

        for ($i = 0; $i < count($optimizedRoute) - 1; $i++) {
            $from = $optimizedRoute[$i];
            $to = $optimizedRoute[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                $totalDistance += $distance;

                // 检查是否存在duration字段
                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;
                $estimatedTime += $duration;

                // 构建导航点
                $waypoints[] = [
                    'from' => $from,
                    'to' => $to,
                    'distance' => $distance,
                    'duration' => $duration
                ];
            }
        }

        return [
            'route' => $optimizedRoute,
            'total_distance' => $totalDistance,
            'estimated_time' => $estimatedTime,
            'waypoints' => $waypoints
        ];
    }

    /**
     * 考虑取送货顺序约束的路径规划
     * 取货必须在送货之前
     *
     * @param array $riderLocation 骑手当前位置
     * @param array $orders 订单数组
     * @return array 优化后的路径
     */
    public function getConstrainedRoute(array $riderLocation, array $orders): array
    {
        // 创建所有点的数组
        $points = [];
        $points[] = [
            'type' => 'rider',
            'order_id' => null,
            'lat' => $riderLocation['lat'],
            'lng' => $riderLocation['lng']
        ];

        // 添加所有订单的取货点和送货点
        foreach ($orders as $order) {
            $points[] = [
                'type' => 'pickup',
                'order_id' => $order['id'],
                'lat' => $order['pickup']['lat'],
                'lng' => $order['pickup']['lng']
            ];

            $points[] = [
                'type' => 'delivery',
                'order_id' => $order['id'],
                'lat' => $order['delivery']['lat'],
                'lng' => $order['delivery']['lng']
            ];
        }

        // 获取点的经纬度数组用于计算距离矩阵
        $locations = array_map(function($point) {
            return ['lat' => $point['lat'], 'lng' => $point['lng']];
        }, $points);

        $distanceMatrix = $this->getDistanceMatrix($locations);

        // 使用贪心算法考虑约束条件
        $path = $this->constrainedGreedyAlgorithm($points, $distanceMatrix);

        return $path;
    }

    /**
     * 考虑约束条件的贪心算法
     *
     * @param array $points 所有点
     * @param array $distanceMatrix 距离矩阵
     * @return array 路径
     */
    private function constrainedGreedyAlgorithm(array $points, array $distanceMatrix): array
    {
        $n = count($points);
        $visited = array_fill(0, $n, false);
        $pickedUp = [];  // 已取货的订单ID

        // 从骑手位置开始
        $currentIdx = 0;
        $visited[0] = true;
        $path = [$points[0]];

        while (count(array_filter($visited, function($v) { return !$v; })) > 0) {
            $minDist = PHP_INT_MAX;
            $nextIdx = -1;

            for ($i = 0; $i < $n; $i++) {
                if (!$visited[$i]) {
                    $point = $points[$i];

                    // 约束条件: 送货点必须在相应的取货点之后
                    if ($point['type'] == 'delivery' && !in_array($point['order_id'], $pickedUp)) {
                        continue;
                    }

                    $dist = $distanceMatrix[$currentIdx][$i];
                    if ($dist < $minDist) {
                        $minDist = $dist;
                        $nextIdx = $i;
                    }
                }
            }

            // 如果找不到下一个可访问的点，结束循环
            if ($nextIdx == -1) {
                break;
            }

            // 访问该点
            $visited[$nextIdx] = true;
            $currentIdx = $nextIdx;
            $path[] = $points[$nextIdx];

            // 如果是取货点，记录已取货
            if ($points[$nextIdx]['type'] == 'pickup') {
                $pickedUp[] = $points[$nextIdx]['order_id'];
            }
        }

        return $path;
    }

    /**
     * 为骑手的当前订单获取最优路径规划
     *
     * @param int $riderId 骑手ID
     * @param array $riderLocation 骑手当前位置 ['lat' => 纬度, 'lng' => 经度]
     * @return array 路径规划详情
     */
    public function getRiderOrdersRoute(int $riderId, array $riderLocation): array
    {
        // 获取骑手当前进行中的订单
        $orders = O2oErrandOrder::query()
            ->where('rider_id', $riderId)
            ->whereIn('order_status', [
                O2oErrandOrder::STATUS_PICKUP,
                O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT,
                O2oErrandOrder::STATUS_DELIVERY
            ])
            ->where('refund_status', O2oErrandOrder::REFUND_STATUS_INIT)
            ->get();

        if ($orders->isEmpty()) {
            return [
                'route' => [
                    [
                        'type' => 'rider',
                        'order_id' => null,
                        'lat' => $riderLocation['lat'],
                        'lng' => $riderLocation['lng']
                    ]
                ],
                'total_distance' => 0,
                'estimated_time' => 0,
                'waypoints' => [],
                'orders' => []
            ];
        }

        // 转换订单为路径规划所需的格式
        $routeOrders = [];
        foreach ($orders as $order) {
            $routeOrder = [
                'id' => $order->order_no,
                'order_status' => $order->order_status,
                'type' => $order->type,
            ];

            // 根据订单状态确定需要包含哪些点
            if ($order->order_status == O2oErrandOrder::STATUS_PICKUP) {
                // 待取货状态，需要包含取货点和送货点
                $routeOrder['pickup'] = [
                    'lat' => $order->pickup_lat,
                    'lng' => $order->pickup_lng,
                    'name' => $order->pickup_name,
                    'address' => $order->pickup_address,
                    'phone' => $order->pickup_phone
                ];
                $routeOrder['delivery'] = [
                    'lat' => $order->deliver_lat,
                    'lng' => $order->deliver_lng,
                    'name' => $order->deliver_name,
                    'address' => $order->deliver_address,
                    'phone' => $order->deliver_phone
                ];
            } elseif ($order->order_status == O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                // 已到达取货点状态，仍需包含取货点和送货点
                $routeOrder['pickup'] = [
                    'lat' => $order->pickup_lat,
                    'lng' => $order->pickup_lng,
                    'name' => $order->pickup_name,
                    'address' => $order->pickup_address,
                    'phone' => $order->pickup_phone
                ];
                $routeOrder['delivery'] = [
                    'lat' => $order->deliver_lat,
                    'lng' => $order->deliver_lng,
                    'name' => $order->deliver_name,
                    'address' => $order->deliver_address,
                    'phone' => $order->deliver_phone
                ];
            } elseif ($order->order_status == O2oErrandOrder::STATUS_DELIVERY) {
                // 派送中状态，只需包含送货点
                $routeOrder['delivery'] = [
                    'lat' => $order->deliver_lat,
                    'lng' => $order->deliver_lng,
                    'name' => $order->deliver_name,
                    'address' => $order->deliver_address,
                    'phone' => $order->deliver_phone
                ];
            }

            $routeOrders[] = $routeOrder;
        }

        // 获取约束路径规划（取货必须在送货之前）
        $normalizedOrders = $this->normalizeOrdersForRouting($routeOrders);
        $route = $this->getConstrainedRoute($riderLocation, $normalizedOrders);

        // 计算总距离和时间
        $totalDistance = 0;
        $estimatedTime = 0;
        $waypoints = [];

        for ($i = 0; $i < count($route) - 1; $i++) {
            $from = $route[$i];
            $to = $route[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                // 检查是否存在duration字段
                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;

                $totalDistance += $distance;
                $estimatedTime += $duration;

                // 构建导航点
                $waypoints[] = [
                    'from' => $from,
                    'to' => $to,
                    'distance' => $distance,
                    'duration' => $duration
                ];
            }
        }

        return [
            'route' => $route,
            'total_distance' => $totalDistance,
            'estimated_time' => $estimatedTime,
            'waypoints' => $waypoints,
            'orders' => $orders
        ];
    }

    /**
     * 将骑手订单标准化为路径规划所需的格式
     *
     * @param array $orders 骑手订单数组
     * @return array 标准化后的订单数组
     */
    private function normalizeOrdersForRouting(array $orders): array
    {
        $normalizedOrders = [];

        foreach ($orders as $order) {
            $normalized = [
                'id' => $order['id']
            ];

            // 根据订单状态添加取货点和送货点
            if (isset($order['pickup'])) {
                $normalized['pickup'] = [
                    'lat' => $order['pickup']['lat'],
                    'lng' => $order['pickup']['lng']
                ];
            }

            if (isset($order['delivery'])) {
                $normalized['delivery'] = [
                    'lat' => $order['delivery']['lat'],
                    'lng' => $order['delivery']['lng']
                ];
            }

            $normalizedOrders[] = $normalized;
        }

        return $normalizedOrders;
    }

    /**
     * 获取骑手多任务的最优路径规划 V2版本
     * 考虑取送货约束和路径组合优化
     *
     * @param array $allActiveTasks 所有活跃任务
     * @param array $riderLocation 骑手当前位置 ['lat' => 纬度, 'lng' => 经度]
     * @return array 返回优化后的任务序列和节省时间信息，包含以下字段：
     *               - tasks: 优化排序后的任务列表
     *               - time_savings: 优化路径相比默认路径节省的时间(秒)
     *               - percentage_saved: 节省时间的百分比
     *               - route_info: 路径详细信息
     *                  - optimized_time: 优化后的总行程时间(秒)
     *                  - default_time: 默认路径的总行程时间(秒)
     *                  - route_points: 优化路径的所有点详情
     */
    public function getMultiTaskRouteV2($allActiveTasks, $riderLocation): array
    {
        if (empty($allActiveTasks)) {
            return [
                'tasks' => [],
                'time_savings' => 0,
                'percentage_saved' => 0,
                'rider' => [
                    'lat' => $riderLocation['lat'],
                    'lng' => $riderLocation['lng'],
                    'address_info' => "",
                ],
                'route_info' => [
                    'optimized_time' => 0,
                    'default_time' => 0,
                    'route_points' => []
                ]
            ];
        }

        // 获取时间窗口（分钟），默认为120分钟
        $timeWindow = $riderLocation['time_window'] ?? 120;
        
        // 将时间窗口转换为秒
        $timeWindowSeconds = $timeWindow * 60;
        
        // 当前时间戳（使用系统当前时间）
        $currentTime = time();
        
        // 分离预定单和普通单
        $normalTasks = [];
        $scheduledTasks = [];
        
        foreach ($allActiveTasks as $task) {
            // 检查是否有预计送达时间
            if (isset($task['expected_delivery_time']) && !empty($task['expected_delivery_time'])) {
                $expectedTime = strtotime($task['expected_delivery_time']);
                
                // 如果预计送达时间与当前时间的差值超过时间窗口，则视为预定单
                if (($expectedTime - $currentTime) > $timeWindowSeconds) {
                    $scheduledTasks[] = $task;
                    continue;
                }
            }
            
            // 否则视为普通单
            $normalTasks[] = $task;
        }
        
        // 获取骑手位置的地址信息
        $riderAddressInfo = '';
        $geoResponse = $this->mapService->geocoder($riderLocation["lng"], $riderLocation["lat"],false);
        if (isset($geoResponse['status']) && $geoResponse['status'] === '1' && isset($geoResponse['result']['address'])) {
            $riderAddressInfo = $geoResponse['result']['address'];
        }
        
        // 如果所有任务都是预定单，则按预计送达时间排序并直接返回
        if (empty($normalTasks) && !empty($scheduledTasks)) {
            usort($scheduledTasks, function($a, $b) {
                $timeA = isset($a['expected_delivery_time']) ? strtotime($a['expected_delivery_time']) : PHP_INT_MAX;
                $timeB = isset($b['expected_delivery_time']) ? strtotime($b['expected_delivery_time']) : PHP_INT_MAX;
                return $timeA <=> $timeB;
            });
            
            return [
                'tasks' => $scheduledTasks,
                'time_savings' => 0,
                'percentage_saved' => 0,
                'rider' => [
                    'lat' => $riderLocation['lat'],
                    'lng' => $riderLocation['lng'],
                    'address_info' => $riderAddressInfo
                ],
                'route_info' => [
                    'optimized_time' => 0,
                    'default_time' => 0,
                    'route_points' => [
                        [
                            'id' => 'rider',
                            'type' => 'rider',
                            'lat' => $riderLocation['lat'],
                            'lng' => $riderLocation['lng']
                        ]
                    ],
                   
                ]
            ];
        }

        // 复制一份原始任务列表用于默认路径计算（只包含普通单）
        $defaultOrderedTasks = array_map(function($task) {
            return $task;
        }, $normalTasks);

        // 始终确保按ID排序，保持确定性
        usort($normalTasks, function($a, $b) {
            return $a['id'] <=> $b['id'];
        });

        // 使用固定的时间戳计算紧急度，避免time()函数带来的不确定性
        $fixedTimestamp = strtotime('2023-01-01 00:00:00'); // 使用固定时间戳

        // 1. 准备任务点数据
        $points = [];
        // 添加骑手当前位置作为起点
        $points[] = [
            'id' => 'rider',
            'type' => 'rider',
            'lat' => $riderLocation['lat'],
            'lng' => $riderLocation['lng']
        ];

        // 构建所有任务点（取货和送货点）
        $taskPoints = [];
        $pickupPoints = [];
        $deliveryPoints = [];
        $constraints = []; // 存储约束关系：取货点必须在相应的送货点之前

        foreach ($normalTasks as $task) {
            $taskId = $task['id'];

            if ($task['type'] == 'pickup' || ($task['type'] == 'delivery' && $task['status'] == 'accepted')) {
                // 未取货的任务，需要先取货再送货
                $pickupId = 'pickup_' . $taskId;
                $deliveryId = 'delivery_' . $taskId;

                // 添加取货点
                $points[] = [
                    'id' => $pickupId,
                    'task_id' => $taskId,
                    'type' => 'pickup',
                    'lat' => $task['order']['pickup_lat'],
                    'lng' => $task['order']['pickup_lng'],
                    'expected_time' => $task['expected_delivery_time'] ?? null
                ];
                $pickupPoints[] = $pickupId;

                // 添加送货点
                $points[] = [
                    'id' => $deliveryId,
                    'task_id' => $taskId,
                    'type' => 'delivery',
                    'lat' => $task['order']['deliver_lat'],
                    'lng' => $task['order']['deliver_lng'],
                    'expected_time' => $task['expected_delivery_time'] ?? null
                ];
                $deliveryPoints[] = $deliveryId;

                // 添加约束：取货必须在送货之前
                $constraints[$deliveryId] = $pickupId;

                // 记录任务点
                $taskPoints[$taskId] = [
                    'pickup' => $pickupId,
                    'delivery' => $deliveryId
                ];
            } else if ($task['type'] == 'delivery' && ($task['status'] == 'arrived' || $task['status'] == 'picked_up')) {
                // 已取货的任务，只需要送货
                $deliveryId = 'delivery_' . $taskId;

                // 添加送货点
                $points[] = [
                    'id' => $deliveryId,
                    'task_id' => $taskId,
                    'type' => 'delivery',
                    'lat' => $task['order']['deliver_lat'],
                    'lng' => $task['order']['deliver_lng'],
                    'expected_time' => $task['expected_delivery_time'] ?? null
                ];
                $deliveryPoints[] = $deliveryId;

                // 记录任务点
                $taskPoints[$taskId] = [
                    'delivery' => $deliveryId
                ];
            }
        }

        // 如果没有任何普通单有效点，但有预定单，则直接返回按预计送达时间排序的预定单
        if (count($points) <= 1 && !empty($scheduledTasks)) {
            usort($scheduledTasks, function($a, $b) {
                $timeA = isset($a['expected_delivery_time']) ? strtotime($a['expected_delivery_time']) : PHP_INT_MAX;
                $timeB = isset($b['expected_delivery_time']) ? strtotime($b['expected_delivery_time']) : PHP_INT_MAX;
                return $timeA <=> $timeB;
            });
            
            return [
                'tasks' => $scheduledTasks,
                'time_savings' => 0,
                'percentage_saved' => 0,
                'rider' => [
                        'lat' => $riderLocation['lat'],
                        'lng' => $riderLocation['lng'],
                        'address_info' => $riderAddressInfo
                ],
                'route_info' => [
                    'optimized_time' => 0,
                    'default_time' => 0,
                    'route_points' => $points,
                ]
            ];
        }
        // 如果没有任何有效点，返回空结果
        if (count($points) <= 1 && empty($scheduledTasks)) {
            return [
                'tasks' => $normalTasks,
                'time_savings' => 0,
                'percentage_saved' => 0,
                'rider' => [
                        'lat' => $riderLocation['lat'],
                        'lng' => $riderLocation['lng'],
                        'address_info' => $riderAddressInfo
                ],
                'route_info' => [
                    'optimized_time' => 0,
                    'default_time' => 0,
                    'route_points' => $points,
                    
                ]
            ];
        }

        // 2. 计算点之间的时间/距离矩阵
        $n = count($points);
        $timeMatrix = array_fill(0, $n, array_fill(0, $n, PHP_INT_MAX));
        $distanceMatrix = array_fill(0, $n, array_fill(0, $n, PHP_INT_MAX)); // 新增距离矩阵
        $stepsMatrix = array_fill(0, $n, array_fill(0, $n, [])); // 新增steps矩阵

        // 计算所有点之间的时间和距离成本
        for ($i = 0; $i < $n; $i++) {
            // 设置自身到自身的时间和距离为0
            $timeMatrix[$i][$i] = 0;
            $distanceMatrix[$i][$i] = 0;
            $stepsMatrix[$i][$i] = []; // 自身到自身没有steps

            for ($j = 0; $j < $n; $j++) {
                if ($i != $j && $timeMatrix[$i][$j] == PHP_INT_MAX) { // 避免重复计算
                    // $fromCoord = $points[$i]['lng']. ',' .$points[$i]['lat'];
                    // $toCoord = $points[$j]['lng']. ',' .$points[$j]['lat'];
                    $response = $this->mapService->bicycling(
                        $points[$i]['lng'],
                        $points[$i]['lat'],
                        $points[$j]['lng'],
                        $points[$j]['lat'],
                    );
                    // 处理gaoDeDistanceMatrix响应
                    if (isset($response['errcode']) && $response['errcode'] === 0) {
                        $duration = 0;
                        $distance = 0;
                        $steps = []; // 存储路径步骤

                        // 循环所有路径累加duration和distance
                        if (isset($response['data']['paths']) && is_array($response['data']['paths'])) {
                            foreach ($response['data']['paths'] as $path) {
                                // 累加距离
                                if (isset($path['distance'])) {
                                    $distance += (int)$path['distance'];
                                }
                                
                                // 如果路径中直接有duration字段
                                if (isset($path['duration'])) {
                                    $duration += (int)$path['duration'];
                                }
                                // 如果路径中有cost数据
                                else if (isset($path['cost']['duration'])) {
                                    $duration += (int)$path['cost']['duration'];
                                }
                                // 如果需要从steps中累加每一步的duration
                                else if (isset($path['steps']) && is_array($path['steps'])) {
                                    foreach ($path['steps'] as $step) {
                                        if (isset($step['cost']['duration'])) {
                                            $duration += (int)$step['cost']['duration'];
                                        }
                                    }
                                }
                                
                                // 收集steps信息
                                if (isset($path['steps']) && is_array($path['steps'])) {
                                    $steps = array_merge($steps, $path['steps']);
                                }
                            }
                        }

                        // 确保时间和距离成本有一个合理的默认值，避免无法计算路径
                        if ($duration <= 0 || $distance <= 0) {
                            // 如果API没有返回有效的时间或距离，使用基于距离的估算
                            // 假设平均速度为30km/h，转换为米/秒约为8.33
                            $lat1 = (float)$points[$i]['lat'];
                            $lng1 = (float)$points[$i]['lng'];
                            $lat2 = (float)$points[$j]['lat'];
                            $lng2 = (float)$points[$j]['lng'];

                            // 使用haversine公式计算距离（米）
                            $earthRadius = 6371000; // 地球半径，单位米
                            $dLat = deg2rad($lat2 - $lat1);
                            $dLon = deg2rad($lng2 - $lng1);
                            $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                            $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                            $calculatedDistance = $earthRadius * $c;

                            // 如果距离无效，则使用计算值
                            if ($distance <= 0) {
                                $distance = $calculatedDistance;
                            }
                            
                            // 如果时间无效，则使用距离估算
                            if ($duration <= 0) {
                                // 估算时间（秒）= 距离/速度
                                $duration = $distance / 8.33;
                            }
                        }

                        // 检查API返回的距离是否合理
                        $lat1 = (float)$points[$i]['lat'];
                        $lng1 = (float)$points[$i]['lng'];
                        $lat2 = (float)$points[$j]['lat'];
                        $lng2 = (float)$points[$j]['lng'];
                        
                        // 使用haversine公式计算直线距离作为参考
                        $earthRadius = 6371000; // 地球半径，单位米
                        $dLat = deg2rad($lat2 - $lat1);
                        $dLon = deg2rad($lng2 - $lng1);
                        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                        $directDistance = $earthRadius * $c;
                        
                        // 实际路径距离通常是直线距离的1.2-1.5倍，但不应超过3倍
                        $maxReasonableDistance = $directDistance * 3;
                        $minExpectedDistance = $directDistance * 1.1;
                        
                        // 如果API返回的距离明显不合理，调整为合理值
                        if ($distance > $maxReasonableDistance || $distance < $minExpectedDistance) {
                            // 使用直线距离的1.4倍作为估算（考虑实际道路弯曲因素）
                            $distance = $directDistance * 1.4;
                            // 同时重新估算时间
                            $duration = $distance / 8.33;
                        }

                        $timeMatrix[$i][$j] = $duration;
                        $timeMatrix[$j][$i] = $duration; // 同时设置反向路径
                        
                        $distanceMatrix[$i][$j] = $distance;
                        $distanceMatrix[$j][$i] = $distance; // 同时设置反向路径
                        
                        $stepsMatrix[$i][$j] = $steps; // 保存steps信息
                        $stepsMatrix[$j][$i] = $steps; // 同时设置反向路径的steps
                    } else {
                        // 如果API调用失败，设置一个合理的默认值
                        // 使用基于直线距离的估算
                        $lat1 = (float)$points[$i]['lat'];
                        $lng1 = (float)$points[$i]['lng'];
                        $lat2 = (float)$points[$j]['lat'];
                        $lng2 = (float)$points[$j]['lng'];

                        // 使用haversine公式计算距离（米）
                        $earthRadius = 6371000; // 地球半径，单位米
                        $dLat = deg2rad($lat2 - $lat1);
                        $dLon = deg2rad($lng2 - $lng1);
                        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                        $directDistance = $earthRadius * $c;

                        // 实际道路距离通常比直线距离长1.2-1.5倍
                        $distance = $directDistance * 1.4;
                        
                        // 确保距离值合理
                        $maxReasonableDistance = 50000; // 50公里
                        if ($distance > $maxReasonableDistance) {
                            $distance = $maxReasonableDistance;
                        }

                        // 估算时间（秒）= 距离/速度，假设平均速度为30km/h
                        $duration = $distance / 8.33;

                        $timeMatrix[$i][$j] = $duration;
                        $timeMatrix[$j][$i] = $duration;
                        
                        $distanceMatrix[$i][$j] = $distance;
                        $distanceMatrix[$j][$i] = $distance;
                        
                        $steps = []; // API调用失败，steps为空数组
                        
                        $stepsMatrix[$i][$j] = $steps; // 保存steps信息
                        $stepsMatrix[$j][$i] = $steps; // 同时设置反向路径的steps
                    }
                }
            }
        }


        // 3. 使用优化算法找出最佳路径
        // 获取所有点的ID与索引映射关系
        $pointIndexMap = [];
        foreach ($points as $index => $point) {
            $pointIndexMap[$point['id']] = $index;
        }

        // 递归+剪枝方法搜索最优路径
        // 为了避免算法复杂度过高，当任务数超过阈值时，使用贪心方法
        $MAX_TASKS_FOR_EXACT = 5; // 精确算法的最大任务数阈值

        // 根据任务数量选择算法
        if (count($normalTasks) <= $MAX_TASKS_FOR_EXACT) {
            // 直接使用精确算法
            $routeResult = $this->findOptimalRouteDeterministic($points, $timeMatrix, $pointIndexMap, $constraints);
            $bestRoute = $routeResult['route'];
            $bestTime = $routeResult['total_time'];
            $optimizedTasks = $this->reorderTasksByRouteDeterministic($normalTasks, $bestRoute, $points);
        } else {
            // 任务数量过多时使用贪心方法
            $greedyResult = $this->greedyOptimizationDeterministic($normalTasks, $riderLocation, $points, $timeMatrix, $pointIndexMap, $constraints, $fixedTimestamp);
            $optimizedTasks = $greedyResult['tasks'];
            $bestRoute = $greedyResult['route'];
            $bestTime = $greedyResult['total_time'];
        }

        // 如果仍然没有找到有效路径，使用默认顺序
        if (empty($bestRoute) || $bestTime == PHP_INT_MAX) {
            // 创建默认顺序的路径
            $bestRoute = ['rider'];
            foreach ($normalTasks as $task) {
                $taskId = $task['id'];
                if (isset($taskPoints[$taskId]['pickup'])) {
                    $bestRoute[] = $taskPoints[$taskId]['pickup'];
                }
                if (isset($taskPoints[$taskId]['delivery'])) {
                    $bestRoute[] = $taskPoints[$taskId]['delivery'];
                }
            }

            // 计算默认路径的总时间
            $bestTime = $this->calculatePathTime($bestRoute, $pointIndexMap, $timeMatrix, false);
            if ($bestTime == PHP_INT_MAX) {
                $bestTime = 0; // 如果仍然无法计算，设置为0
            }

            // 使用原始任务顺序
            $optimizedTasks = $normalTasks;
        }

        // 4. 计算默认顺序下的总时间（严格按任务ID从小到大排序）
        // 先按ID从小到大排序任务
        usort($defaultOrderedTasks, function($a, $b) {
            return $a['id'] <=> $b['id'];
        });

        // 直接构建按ID顺序的默认路径
        $defaultRoute = ['rider']; // 从骑手位置开始
        foreach ($defaultOrderedTasks as $task) {
            $taskId = $task['id'];
            // 先添加取货点，再添加送货点
            if (isset($taskPoints[$taskId]['pickup'])) {
                $defaultRoute[] = $taskPoints[$taskId]['pickup'];
            }
            if (isset($taskPoints[$taskId]['delivery'])) {
                $defaultRoute[] = $taskPoints[$taskId]['delivery'];
            }
        }
        
        // 计算默认路径的总时间
        $defaultTime = $this->calculateUnifiedPathTime($defaultRoute, $points, $pointIndexMap, $timeMatrix, $distanceMatrix);
        if ($defaultTime == PHP_INT_MAX) {
            $defaultTime = 0; // 如果无法计算，设置为0
        }
        
        // 验证默认路径中的点是否都存在于points数组中
        $validDefaultRoute = true;
        $pointIds = array_column($points, 'id');
        foreach ($defaultRoute as $pointId) {
            if (!in_array($pointId, $pointIds)) {
                $validDefaultRoute = false;
                break;
            }
        }
        
        // 如果默认路径无效，创建一个包含骑手点的默认路径
        if (!$validDefaultRoute) {
            // 重新创建一个有效的默认路径
            $defaultRoute = ['rider'];
            foreach ($defaultOrderedTasks as $task) {
                $taskId = $task['id'];
                if (isset($taskPoints[$taskId]['pickup'])) {
                    $defaultRoute[] = $taskPoints[$taskId]['pickup'];
                }
                if (isset($taskPoints[$taskId]['delivery'])) {
                    $defaultRoute[] = $taskPoints[$taskId]['delivery'];
                }
            }
        }
        
        // 使用统一的方法重新计算两个路径的时间，确保使用相同的计算逻辑
        $recalculatedBestTime = $this->calculateUnifiedPathTime($bestRoute, $points, $pointIndexMap, $timeMatrix, $distanceMatrix);
        $recalculatedDefaultTime = $this->calculateUnifiedPathTime($defaultRoute, $points, $pointIndexMap, $timeMatrix, $distanceMatrix);
        
        // 使用重新计算的时间
        $bestTime = $recalculatedBestTime;
        $defaultTime = $recalculatedDefaultTime;
        
        // 如果优化路径的时间仍然大于默认路径，则使用默认路径作为最优路径
        if ($bestTime > $defaultTime) {
            $bestTime = $defaultTime;
            $bestRoute = $defaultRoute;
            $optimizedTasks = $defaultOrderedTasks;
        }

        // 5. 计算节省的时间和百分比
        $timeSavings = max(0, $defaultTime - $bestTime); // 确保节省时间不为负
        $percentageSaved = ($defaultTime > 0) ? round(($timeSavings / $defaultTime) * 100, 2) : 0;

        // 构建路径点详情
        $routePoints = [];
        
        // 获取点的索引和ID映射，用于从timeMatrix获取数据
        $pointIndexMap = [];
        $pointsById = [];
        foreach ($points as $index => $point) {
            $pointIndexMap[$point['id']] = $index;
            $pointsById[$point['id']] = $point;
        }
        
        // 确保bestRoute中的所有点ID都存在于points数组中
        $validBestRoute = [];
        foreach ($bestRoute as $pointId) {
            if (isset($pointsById[$pointId])) {
                $validBestRoute[] = $pointId;
            }
        }
        $bestRoute = $validBestRoute;
        
        // 如果过滤后路径为空，确保至少包含骑手点
        if (empty($bestRoute)) {
            $bestRoute = ['rider'];
            // 使用默认任务排序重建路径
            foreach ($optimizedTasks as $task) {
                $taskId = $task['id'];
                if (isset($taskPoints[$taskId]['pickup'])) {
                    $bestRoute[] = $taskPoints[$taskId]['pickup'];
                }
                if (isset($taskPoints[$taskId]['delivery'])) {
                    $bestRoute[] = $taskPoints[$taskId]['delivery'];
                }
            }
        }
        
        $routeLength = count($bestRoute);
        foreach ($bestRoute as $index => $pointId) {
            foreach ($points as $point) {
                if ($point['id'] == $pointId) {
                    $point['is_scheduled'] = false;
                    
                    // 修改 distance 字段逻辑，表示到达当前点的距离，而不是当前点到下一个点的距离
                    if ($index == 0) {
                        // 第一个点的到达距离为0
                        $point['distance'] = 0;
                        $point['steps'] = []; 
                    } else {
                        // 获取前一个点和当前点的索引
                        $prevId = $bestRoute[$index - 1];
                        $prevIndex = $pointIndexMap[$prevId];
                        $currentIndex = $pointIndexMap[$pointId];
                        
                        // 直接从距离矩阵获取距离信息，不再调用API
                        if (isset($distanceMatrix[$prevIndex][$currentIndex]) && $distanceMatrix[$prevIndex][$currentIndex] != PHP_INT_MAX) {
                            // 确保距离值合理，不超过实际可能的最大距离（50公里）
                            $distFromMatrix = (int)$distanceMatrix[$prevIndex][$currentIndex];
                            
                            // 计算直线距离作为参考
                            $lat1 = (float)$points[$prevIndex]['lat'];
                            $lng1 = (float)$points[$prevIndex]['lng'];
                            $lat2 = (float)$points[$currentIndex]['lat'];
                            $lng2 = (float)$points[$currentIndex]['lng'];
                            
                            // 使用haversine公式计算直线距离（米）
                            $earthRadius = 6371000; // 地球半径，单位米
                            $dLat = deg2rad($lat2 - $lat1);
                            $dLon = deg2rad($lng2 - $lng1);
                            $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                            $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                            $directDistance = $earthRadius * $c;
                            
                            // 如果矩阵距离超过直线距离的3倍，可能是不合理值，使用直线距离的1.4倍作为估算
                            // 实际道路距离通常是直线距离的1.2-1.5倍
                            if ($distFromMatrix > $directDistance * 3) {
                                $point['distance'] = (int)($directDistance * 1.4);
                            } else {
                                $point['distance'] = $distFromMatrix;
                            }
                        } else {
                            // 如果距离矩阵中没有有效值，使用时间和平均速度估算距离
                            $time = $timeMatrix[$prevIndex][$currentIndex];
                            
                            // 计算直线距离作为参考
                            $lat1 = (float)$points[$prevIndex]['lat'];
                            $lng1 = (float)$points[$prevIndex]['lng'];
                            $lat2 = (float)$points[$currentIndex]['lat'];
                            $lng2 = (float)$points[$currentIndex]['lng'];
                            
                            // 使用haversine公式计算直线距离（米）
                            $earthRadius = 6371000; // 地球半径，单位米
                            $dLat = deg2rad($lat2 - $lat1);
                            $dLon = deg2rad($lng2 - $lng1);
                            $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                            $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                            $directDistance = $earthRadius * $c;
                            
                            // 估算的距离是直线距离的1.4倍（考虑实际道路弯曲因素）或者基于时间的估算，取较小值
                            $timeBasedDistance = (int)($time * 8.33); // 8.33米/秒 = 30公里/小时
                            $estimatedDistance = (int)($directDistance * 1.4);
                            
                            $point['distance'] = min($timeBasedDistance, $estimatedDistance);
                        }
                        
                        // 添加steps信息
                        $point['steps'] = $stepsMatrix[$prevIndex][$currentIndex] ?? [];
                    }
                    $routePoints[] = $point;
                    break;
                }
            }
        }

        // 如果有预定单，按照预计送达时间从小到大排序，然后追加到优化后的普通单后面
        if (!empty($scheduledTasks)) {
            // 对预定单按照预计送达时间排序
            usort($scheduledTasks, function($a, $b) {
                $timeA = isset($a['expected_delivery_time']) ? strtotime($a['expected_delivery_time']) : PHP_INT_MAX;
                $timeB = isset($b['expected_delivery_time']) ? strtotime($b['expected_delivery_time']) : PHP_INT_MAX;
                return $timeA <=> $timeB;
            });
            
            // 将预定单追加到优化后的普通单后面
            $optimizedTasks = array_merge($optimizedTasks, $scheduledTasks);
            
            // 在route_points中也添加预定单的点
            $scheduledPoints = [];
            
            foreach ($scheduledTasks as $taskIndex => $task) {
                $taskId = $task['id'];
                
                if ($task['type'] == 'pickup' || ($task['type'] == 'delivery' && $task['status'] == 'accepted')) {
                    // 未取货的预定单，需要添加取货点和送货点
                    $pickupPoint = [
                        'id' => 'pickup_' . $taskId,
                        'task_id' => $taskId,
                        'type' => 'pickup',
                        'lat' => $task['order']['pickup_lat'],
                        'lng' => $task['order']['pickup_lng'],
                        'expected_time' => $task['expected_delivery_time'] ?? null,
                        'is_scheduled' => true
                    ];
                    
                    $deliveryPoint = [
                        'id' => 'delivery_' . $taskId,
                        'task_id' => $taskId,
                        'type' => 'delivery',
                        'lat' => $task['order']['deliver_lat'],
                        'lng' => $task['order']['deliver_lng'],
                        'expected_time' => $task['expected_delivery_time'] ?? null,
                        'is_scheduled' => true
                    ];
                    
                    $scheduledPoints[] = $pickupPoint;
                    $scheduledPoints[] = $deliveryPoint;
                } else if ($task['type'] == 'delivery' && ($task['status'] == 'arrived' || $task['status'] == 'picked_up')) {
                    // 已取货的预定单，只需要添加送货点
                    $deliveryPoint = [
                        'id' => 'delivery_' . $taskId,
                        'task_id' => $taskId,
                        'type' => 'delivery',
                        'lat' => $task['order']['deliver_lat'],
                        'lng' => $task['order']['deliver_lng'],
                        'expected_time' => $task['expected_delivery_time'] ?? null,
                        'is_scheduled' => true
                    ];
                    
                    $scheduledPoints[] = $deliveryPoint;
                }
            }
            
            // 计算预定单点的距离
            $scheduledLength = count($scheduledPoints);
            for ($i = 0; $i < $scheduledLength; $i++) {
                if ($i == 0) {
                    // 第一个预定单点到达距离
                    if (!empty($routePoints)) {
                        // 如果有普通单，计算从最后一个普通单点到第一个预定单点的距离
                        $lastNormalPoint = end($routePoints); // 使用end()获取最后一个普通单点
                        $firstScheduledPoint = $scheduledPoints[0];
                        
                        // 使用haversine公式计算距离
                        $lat1 = (float)$lastNormalPoint['lat'];
                        $lng1 = (float)$lastNormalPoint['lng'];
                        $lat2 = (float)$firstScheduledPoint['lat'];
                        $lng2 = (float)$firstScheduledPoint['lng'];
                        
                        // 计算距离（米）
                        $earthRadius = 6371000; // 地球半径，单位米
                        $dLat = deg2rad($lat2 - $lat1);
                        $dLon = deg2rad($lng2 - $lng1);
                        $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                        $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                        $directDistance = $earthRadius * $c;
                        
                        // 实际路径距离通常是直线距离的1.2-1.5倍
                        $distance = (int)($directDistance * 1.4);
                        
                        // 确保距离值合理，不要过大
                        $maxReasonableDistance = 50000; // 50公里
                        if ($distance > $maxReasonableDistance) {
                            // 如果距离太大，限制为最大合理距离
                            $distance = $maxReasonableDistance;
                        }
                        
                        // 设置第一个预定单点的距离
                        $scheduledPoints[0]['distance'] = $distance;
                    } else {
                        // 如果没有普通单，第一个预定单点的距离为0
                        $scheduledPoints[0]['distance'] = 0;
                    }
                    $scheduledPoints[0]['steps'] = []; // 第一个预定单点的steps
                } else {
                    // 其他预定单点，计算与前一个点的距离
                    $currentPoint = $scheduledPoints[$i];
                    $prevPoint = $scheduledPoints[$i - 1];
                    
                    // 首先尝试使用haversine公式计算距离
                    $lat1 = (float)$prevPoint['lat'];
                    $lng1 = (float)$prevPoint['lng'];
                    $lat2 = (float)$currentPoint['lat'];
                    $lng2 = (float)$currentPoint['lng'];
                    
                    // 使用haversine公式计算直线距离（米）
                    $earthRadius = 6371000; // 地球半径，单位米
                    $dLat = deg2rad($lat2 - $lat1);
                    $dLon = deg2rad($lng2 - $lng1);
                    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                    $directDistance = $earthRadius * $c;
                    
                    // 实际路径距离通常是直线距离的1.2-1.5倍
                    $distance = (int)($directDistance * 1.4);
                    
                    // 确保距离值合理，不要过大
                    $maxReasonableDistance = 50000; // 50公里
                    if ($distance > $maxReasonableDistance) {
                        // 如果距离太大，限制为最大合理距离
                        $distance = $maxReasonableDistance;
                    }
                    
                    $scheduledPoints[$i]['distance'] = $distance;
                    $scheduledPoints[$i]['steps'] = []; // steps为空数组
                }
            }
            
            // 合并普通单和预定单的点
            $routePoints = array_merge($routePoints, $scheduledPoints);
        }

        // 返回优化后的任务序列和节省信息
        return [
            'tasks' => $optimizedTasks,               // 按优化路径排序后的任务列表，后跟预定单
            'time_savings' => intval($timeSavings),           // 节省的时间(秒)
            'percentage_saved' => $percentageSaved,   // 节省时间的百分比
            'rider' => [
                    'lat' => $riderLocation['lat'],
                    'lng' => $riderLocation['lng'],
                    'address_info' => $riderAddressInfo
            ],
            'route_info' => [
                'optimized_time' => intval($bestTime),        // 优化后的总行程时间(秒)
                'default_time' => intval($defaultTime),       // 默认路径的总行程时间(秒)
                'route_points' => $routePoints,       // 优化路径的所有点详情
                
            ]
        ];
    }

    /**
     * 确定性贪心算法优化路径（用于任务数较多的情况）
     */
    private function greedyOptimizationDeterministic(array $allActiveTasks, array $riderLocation, array $points,
                                      array $timeMatrix, array $pointIndexMap, array $constraints, int $fixedTimestamp): array
    {
        // 开始于骑手当前位置
        $currentPointIndex = $pointIndexMap['rider'];
        $visited = array_fill(0, count($points), false);
        $visited[$currentPointIndex] = true;

        $route = [$points[$currentPointIndex]['id']];
        $pickedUpTasks = [];
        $totalTime = 0;

        // 计算每个任务的紧急程度 - 使用系统当前时间而非固定时间戳，以便准确评估超时风险
        $taskUrgency = [];
        $taskTimeLeft = []; // 记录每个任务剩余的时间（截止时间 - 当前时间）
        $taskEstimatedCompletionTime = []; // 估计每个任务的完成时间
        $now = time(); // 使用当前系统时间更准确

        // 先计算每个取送货点到其目的地的估计时间
        $pointEstimatedTimes = [];
        foreach ($points as $i => $point) {
            if ($point['type'] === 'pickup' || $point['type'] === 'delivery') {
                $taskId = $point['task_id'] ?? null;
                if ($taskId) {
                    // 计算从当前点到该任务点的时间
                    $timeFromRider = $timeMatrix[$currentPointIndex][$i];
                    
                    // 找出该任务的送货点
                    $deliveryPointIndex = null;
                    foreach ($points as $j => $possibleDelivery) {
                        if ($possibleDelivery['type'] === 'delivery' && 
                            isset($possibleDelivery['task_id']) && 
                            $possibleDelivery['task_id'] == $taskId) {
                            $deliveryPointIndex = $j;
                            break;
                        }
                    }
                    
                    // 如果找到送货点且当前点是取货点，则计算取货→送货的时间
                    if ($deliveryPointIndex !== null && $point['type'] === 'pickup') {
                        $timeFromPickupToDelivery = $timeMatrix[$i][$deliveryPointIndex];
                        $pointEstimatedTimes[$i] = $timeFromRider + $timeFromPickupToDelivery;
                    } else {
                        // 直接使用从骑手位置到该点的时间
                        $pointEstimatedTimes[$i] = $timeFromRider;
                    }
                }
            }
        }

        foreach ($allActiveTasks as $task) {
            $taskId = $task['id'];
            
            if (!empty($task['expected_delivery_time'])) {
                $deadline = strtotime($task['expected_delivery_time']);
                $timeLeft = max(0, $deadline - $now); // 剩余时间，越小越紧急
                $taskTimeLeft[$taskId] = $timeLeft;
                
                // 找出该任务的送货点索引
                $deliveryPointIndex = null;
                foreach ($points as $i => $point) {
                    if ($point['type'] === 'delivery' && 
                        isset($point['task_id']) && 
                        $point['task_id'] == $taskId) {
                        $deliveryPointIndex = $i;
                        break;
                    }
                }
                
                // 如果找到送货点，估算完成时间
                if ($deliveryPointIndex !== null) {
                    $estimatedTimeToComplete = $pointEstimatedTimes[$deliveryPointIndex] ?? 0;
                    $taskEstimatedCompletionTime[$taskId] = $estimatedTimeToComplete;
                    
                    // 检查是否预计超时
                    $willBeOverdue = ($now + $estimatedTimeToComplete) > $deadline;
                    
                    // 紧急度基于剩余时间，但如果预计超时，紧急度大幅提高
                    if ($willBeOverdue) {
                        // 超时任务，紧急度取反并放大，使其优先级非常高
                        $taskUrgency[$taskId] = -10000 + $timeLeft; // 越超时越紧急
                    } else {
                        // 非超时任务，正常计算紧急度
                        $taskUrgency[$taskId] = $timeLeft;
                    }
                } else {
                    // 找不到送货点，使用默认紧急度
                    $taskUrgency[$taskId] = $timeLeft;
                }
            } else {
                // 没有截止时间的任务，设置较低的紧急度
                $taskUrgency[$taskId] = PHP_INT_MAX;
                $taskTimeLeft[$taskId] = PHP_INT_MAX;
            }
        }

        // 贪心选择下一个点
        while (count($visited) > array_sum($visited)) {
            $candidates = [];

            // 考虑所有未访问的点
            for ($i = 0; $i < count($points); $i++) {
                if ($visited[$i]) {
                    continue;
                }

                $pointId = $points[$i]['id'];
                $pointType = $points[$i]['type'];
                $taskId = $points[$i]['task_id'] ?? null;

                // 检查约束：送货点必须在相应的取货点之后
                if (isset($constraints[$pointId])) {
                    $requiredPointId = $constraints[$pointId];
                    $requiredIndex = $pointIndexMap[$requiredPointId];

                    if (!$visited[$requiredIndex]) {
                        // 对应的取货点尚未访问，不能访问该送货点
                        continue;
                    }
                }

                // 计算访问该点的成本
                $timeCost = $timeMatrix[$currentPointIndex][$i];

                // 更新预计到达该点时的时间
                $estimatedArrivalTime = $now + $totalTime + $timeCost;
                
                // 计算紧急度分数
                $urgencyScore = PHP_INT_MAX;
                $isOverdue = false;
                $overduePenalty = 0;
                
                if ($taskId && isset($taskUrgency[$taskId])) {
                    $urgencyScore = $taskUrgency[$taskId];
                    
                    // 检查该任务是否超时或即将超时
                    if (isset($task['expected_delivery_time'])) {
                        $deadline = strtotime($task['expected_delivery_time']);
                        
                        // 如果是送货点，直接比较预计到达时间和截止时间
                        if ($pointType === 'delivery') {
                            $isOverdue = $estimatedArrivalTime > $deadline;
                            $overduePenalty = $isOverdue ? ($estimatedArrivalTime - $deadline) : 0;
                        } 
                        // 如果是取货点，需要加上从取货点到送货点的时间
                        else if ($pointType === 'pickup') {
                            // 找出对应的送货点
                            $deliveryIndex = null;
                            foreach ($points as $j => $point) {
                                if ($point['type'] === 'delivery' && 
                                    isset($point['task_id']) && 
                                    $point['task_id'] == $taskId) {
                                    $deliveryIndex = $j;
                                    break;
                                }
                            }
                            
                            if ($deliveryIndex !== null) {
                                $pickupToDeliveryTime = $timeMatrix[$i][$deliveryIndex];
                                $totalTimeToDelivery = $estimatedArrivalTime + $pickupToDeliveryTime;
                                $isOverdue = $totalTimeToDelivery > $deadline;
                                $overduePenalty = $isOverdue ? ($totalTimeToDelivery - $deadline) : 0;
                            }
                        }
                    }
                }

                // 优化：检查如果访问该点，下一步的最小成本是多少（看一步之后）
                $minNextCost = PHP_INT_MAX;
                for ($j = 0; $j < count($points); $j++) {
                    if ($j != $i && !$visited[$j] && $j != $currentPointIndex) {
                        // 检查约束：如果下一个点是送货点，它的取货点必须已访问
                        $nextPointId = $points[$j]['id'];
                        $canVisitNext = true;

                        if (isset($constraints[$nextPointId])) {
                            $nextRequiredPointId = $constraints[$nextPointId];
                            $nextRequiredIndex = $pointIndexMap[$nextRequiredPointId];

                            // 如果必要点既不是当前考虑的点，也没有被访问过，那么不能访问这个点
                            if ($nextRequiredIndex != $i && !$visited[$nextRequiredIndex]) {
                                $canVisitNext = false;
                            }
                        }

                        if ($canVisitNext && $timeMatrix[$i][$j] < $minNextCost) {
                            $minNextCost = $timeMatrix[$i][$j];
                        }
                    }
                }

                // 如果没有下一步可走，使用一个大值
                if ($minNextCost == PHP_INT_MAX) {
                    $minNextCost = 0;
                }

                // 获取任务紧急程度
                $urgency = $taskId ? ($urgencyScore ?? PHP_INT_MAX) : PHP_INT_MAX;

                // 如果是取货点，增加其优先级
                $isPickup = (strpos($pointId, 'pickup_') === 0);

                // 计算综合得分：考虑当前成本、下一步成本、紧急程度和超时惩罚
                // 分数越低越优先
                // 优化：增加距离权重，降低超时惩罚权重
                $score = $timeCost * 1.5 + ($minNextCost * 0.8);

                // 超时或即将超时的任务有优先级，但权重降低
                if ($isOverdue) {
                    // 超时任务，适度提高优先级，但不要过分优先
                    // 降低超时任务的权重
                    $score = $score * 0.8 - min($overduePenalty * 0.002, 100); // 限制超时惩罚的影响
                }
                // 紧急任务有额外优先级
                else if ($urgency < 3600) { // 如果剩余时间不到1小时
                    $score *= 0.9; // 轻微提高优先级
                    
                    // 剩余时间越少，优先级越高，但权重降低
                    $score -= (3600 - $urgency) / 3600 * 30; // 降低紧急程度的影响
                }

                // 取货点比送货点有更高优先级（先取后送）
                if ($isPickup) {
                    $score *= 0.95; // 轻微降低分数，适度提高优先级
                }

                // 获取任务ID作为排序依据（如果不是任务点，使用最大值）
                $currentTaskId = $taskId ? $taskId : PHP_INT_MAX;

                // 创建候选点对象，收集所有可访问的点
                $candidates[] = [
                    'index' => $i,
                    'urgency' => $urgency,
                    'timeCost' => $timeCost,
                    'score' => $score,
                    'isPickup' => $isPickup,
                    'taskId' => $currentTaskId,
                    'isOverdue' => $isOverdue,
                    'overduePenalty' => $overduePenalty,
                    'distance' => $distanceMatrix[$currentPointIndex][$i] ?? PHP_INT_MAX
                ];
            }

            // 如果没有候选点，结束循环
            if (empty($candidates)) {
                break;
            }

            // 对候选点进行确定性排序 - 修改排序策略，更强调距离和路径优化
            usort($candidates, function($a, $b) {
                // 首先，按综合得分排序（得分越低越优先，这是主要排序依据）
                if (abs($a['score'] - $b['score']) > 0.1) {
                    return $a['score'] <=> $b['score'];
                }
                
                // 距离相差超过20%时，优先选择距离较近的点
                $distanceRatio = $a['distance'] > 0 && $b['distance'] > 0 
                    ? max($a['distance'], $b['distance']) / min($a['distance'], $b['distance']) 
                    : 1;
                if ($distanceRatio > 1.2) {
                    return $a['distance'] <=> $b['distance'];
                }
                
                // 如果得分和距离相近，检查超时情况
                if ($a['isOverdue'] != $b['isOverdue']) {
                    return $b['isOverdue'] <=> $a['isOverdue']; // true排在前面
                }
                
                // 如果都是超时任务，按超时惩罚（严重程度）排序
                if ($a['isOverdue'] && $b['isOverdue'] && abs($a['overduePenalty'] - $b['overduePenalty']) > 10) {
                    return $b['overduePenalty'] <=> $a['overduePenalty']; // 惩罚越大越优先，但权重已降低
                }
                
                // 如果上述条件都不满足，优先取货点
                if ($a['isPickup'] != $b['isPickup']) {
                    return $b['isPickup'] <=> $a['isPickup']; // true排在前面
                }

                // 最后按任务ID排序（ID小的优先级高）
                return $a['taskId'] <=> $b['taskId'];
            });

            // 选择排序后的第一个点
            $nextPointIndex = $candidates[0]['index'];

            // 访问该点
            $visited[$nextPointIndex] = true;
            $currentPointIndex = $nextPointIndex;
            $route[] = $points[$nextPointIndex]['id'];
            $totalTime += $candidates[0]['timeCost'];

            // 如果是取货点，记录已取货的任务
            if ($points[$nextPointIndex]['type'] == 'pickup') {
                $pickedUpTasks[] = $points[$nextPointIndex]['task_id'];
            }
        }

        // 根据最终路径重排任务
        $optimizedTasks = $this->reorderTasksByRouteDeterministic($allActiveTasks, $route, $points);

        return [
            'tasks' => $optimizedTasks,
            'route' => $route,
            'total_time' => $totalTime
        ];
    }

    /**
     * 确定性地根据路径顺序重排任务
     */
    private function reorderTasksByRouteDeterministic(array $allActiveTasks, array $route, array $points): array
    {
        // 创建点ID到点信息的映射
        $pointsMap = [];
        foreach ($points as $point) {
            $pointsMap[$point['id']] = $point;
        }

        // 记录每个任务在路径中首次出现的位置
        $taskFirstAppearance = [];

        foreach ($route as $index => $pointId) {
            if ($pointId == 'rider') {
                continue;
            }

            $point = $pointsMap[$pointId];
            $taskId = $point['task_id'] ?? null;

            if ($taskId && !isset($taskFirstAppearance[$taskId])) {
                $taskFirstAppearance[$taskId] = $index;
            }
        }

        // 根据任务首次出现的位置排序
        $taskOrder = [];
        foreach ($allActiveTasks as $task) {
            $taskOrder[$task['id']] = $taskFirstAppearance[$task['id']] ?? PHP_INT_MAX;
        }

        // 稳定排序任务
        $result = $allActiveTasks;
        usort($result, function($a, $b) use ($taskOrder) {
            if ($taskOrder[$a['id']] == $taskOrder[$b['id']]) {
                // 如果位置相同，按ID排序确保稳定性
                return $a['id'] <=> $b['id'];
            }
            return $taskOrder[$a['id']] - $taskOrder[$b['id']];
        });

        return $result;
    }

    /**
     * 确定性的最优路径查找算法
     */
    private function findOptimalRouteDeterministic(array $points, array $timeMatrix, array $pointIndexMap, array $constraints): array
    {
        $startIndex = $pointIndexMap['rider']; // 起点是骑手位置
        $visited = array_fill(0, count($points), false);
        $visited[$startIndex] = true;

        $currentPath = [$startIndex];
        $bestPath = [$startIndex]; // 至少包含起点，避免空数组
        $bestTime = PHP_INT_MAX;

        // 检查是否有无效的时间值
        $hasInvalidTime = false;
        for ($i = 0; $i < count($points); $i++) {
            for ($j = 0; $j < count($points); $j++) {
                if ($i != $j && $timeMatrix[$i][$j] == PHP_INT_MAX) {
                    $hasInvalidTime = true;
                    break 2;
                }
            }
        }

        // 如果存在无效的时间值，则直接返回默认路径
        if ($hasInvalidTime) {
            // 构建一个简单的默认路径，从起点开始
            $defaultRoute = [$points[$startIndex]['id']];

            // 按索引顺序遍历所有点（确保确定性）
            for ($i = 0; $i < count($points); $i++) {
                if ($i != $startIndex) {
                    $defaultRoute[] = $points[$i]['id'];
                }
            }

            // 使用calculatePathTime计算路径时间，确保使用优化路径计算模式
            $pathTime = $this->calculatePathTime($defaultRoute, $pointIndexMap, $timeMatrix, false);
            if ($pathTime == PHP_INT_MAX) {
                // 如果无法计算，使用简单估计
                $pathTime = count($defaultRoute) * 300; // 每点约5分钟
            }

            return [
                'route' => $defaultRoute,
                'total_time' => $pathTime
            ];
        }

        // 递归查找最优路径 - 使用确定性的点访问顺序
        $this->searchPathDeterministic($points, $timeMatrix, $pointIndexMap, $constraints, $visited, $currentPath, 0, $bestPath, $bestTime);

        // 如果没有找到有效路径，bestPath只会包含起点，bestTime仍然是PHP_INT_MAX
        if ($bestTime == PHP_INT_MAX) {
            // 构建一个简单的贪心路径
            $visited = array_fill(0, count($points), false);
            $visited[$startIndex] = true;

            $simplePath = [$startIndex];
            $currentIndex = $startIndex;
            $totalTime = 0;

            // 贪心选择：总是选择距离当前点最近的未访问点，但确保确定性
            while (count($simplePath) < count($points)) {
                $candidates = [];

                for ($i = 0; $i < count($points); $i++) {
                    if (!$visited[$i]) {
                        // 检查约束条件
                        $canVisit = true;
                        $nextPointId = $points[$i]['id'];

                        if (isset($constraints[$nextPointId])) {
                            $requiredPointId = $constraints[$nextPointId];
                            $requiredIndex = $pointIndexMap[$requiredPointId];

                            if (!$visited[$requiredIndex]) {
                                $canVisit = false;
                            }
                        }

                        if ($canVisit) {
                            $candidates[] = [
                                'index' => $i,
                                'time' => $timeMatrix[$currentIndex][$i]
                            ];
                        }
                    }
                }

                // 如果没有候选点，结束循环
                if (empty($candidates)) {
                    break;
                }

                // 确定性地选择最近的点
                usort($candidates, function($a, $b) {
                    if ($a['time'] == $b['time']) {
                        return $a['index'] <=> $b['index']; // 当时间相同时按索引排序确保确定性
                    }
                    return $a['time'] <=> $b['time'];
                });

                $nextIndex = $candidates[0]['index'];
                $visited[$nextIndex] = true;
                $simplePath[] = $nextIndex;
                $totalTime += $candidates[0]['time'];
                $currentIndex = $nextIndex;
            }

            // 转换索引路径为点ID路径
            $simpleRouteIds = [];
            foreach ($simplePath as $index) {
                $simpleRouteIds[] = $points[$index]['id'];
            }

            // 使用calculatePathTime重新计算路径时间，确保使用优化路径计算模式
            $recalculatedTime = $this->calculatePathTime($simpleRouteIds, $pointIndexMap, $timeMatrix, false);
            if ($recalculatedTime == PHP_INT_MAX) {
                $recalculatedTime = $totalTime;
            }

            return [
                'route' => $simpleRouteIds,
                'total_time' => $recalculatedTime
            ];
        }

        // 转换索引路径为点ID路径
        $bestRouteIds = [];
        foreach ($bestPath as $index) {
            $bestRouteIds[] = $points[$index]['id'];
        }

        // 使用calculatePathTime重新计算最优路径的总时间，强制使用优化路径计算模式
        $finalTime = $this->calculatePathTime($bestRouteIds, $pointIndexMap, $timeMatrix, false);
        if ($finalTime == PHP_INT_MAX) {
            $finalTime = $bestTime; // 如果无法计算，使用之前的bestTime
        }

        return [
            'route' => $bestRouteIds,
            'total_time' => $finalTime
        ];
    }

    /**
     * 确定性递归搜索最优路径
     */
    private function searchPathDeterministic(array $points, array $timeMatrix, array $pointIndexMap, array $constraints,
                               array $visited, array $currentPath, float $currentTime,
                               array &$bestPath, float &$bestTime): void
    {
        // 如果所有点都已访问，检查是否是最优路径
        if (count($currentPath) == count($points)) {
            if ($currentTime < $bestTime) {
                $bestPath = $currentPath;
                $bestTime = $currentTime;
            }
            return;
        }

        // 优化1: 如果当前路径已经超过了最佳时间，直接剪枝
        if ($currentTime >= $bestTime) {
            return;
        }

        // 当前位置索引
        $lastIndex = $currentPath[count($currentPath) - 1];

        // 收集所有可能的下一个点
        $candidates = [];

        // 优化2: 首先标记所有必须优先访问的取货点
        $mandatoryPickups = [];
        $pickupToDelivery = []; // 取货点到送货点的映射

        foreach ($constraints as $deliveryId => $pickupId) {
            $deliveryIndex = $pointIndexMap[$deliveryId];
            $pickupIndex = $pointIndexMap[$pickupId];

            if (!$visited[$deliveryIndex] && !$visited[$pickupIndex]) {
                $mandatoryPickups[$pickupIndex] = true;
                $pickupToDelivery[$pickupIndex] = $deliveryIndex;
            }
        }

        // 获取当前系统时间，用于评估超时风险
        $now = time();
        
        // 检查任务是否会超时的辅助函数
        $checkOverdue = function($pointIndex, $arrivalTime) use ($points, $now) {
            $point = $points[$pointIndex];
            if (isset($point['expected_time']) && !empty($point['expected_time'])) {
                $deadline = strtotime($point['expected_time']);
                return ($now + $arrivalTime) > $deadline;
            }
            return false;
        };
        
        // 计算超时严重程度的辅助函数
        $calculateOverdueSeverity = function($pointIndex, $arrivalTime) use ($points, $now) {
            $point = $points[$pointIndex];
            if (isset($point['expected_time']) && !empty($point['expected_time'])) {
                $deadline = strtotime($point['expected_time']);
                $overTime = ($now + $arrivalTime) - $deadline;
                return max(0, $overTime);
            }
            return 0;
        };

        // 尝试访问未访问的点
        for ($nextIndex = 0; $nextIndex < count($points); $nextIndex++) {
            if ($visited[$nextIndex]) {
                continue;
            }

            // 检查约束：不能在取货前送货
            $nextPointId = $points[$nextIndex]['id'];
            if (isset($constraints[$nextPointId])) {
                $requiredPointId = $constraints[$nextPointId];
                $requiredIndex = $pointIndexMap[$requiredPointId];

                if (!$visited[$requiredIndex]) {
                    // 对应的取货点尚未访问，不能访问该送货点
                    continue;
                }
            }

            // 如果时间矩阵中没有有效值，跳过
            if ($timeMatrix[$lastIndex][$nextIndex] == PHP_INT_MAX) {
                continue;
            }

            // 计算新的路径时间
            $segmentTime = $timeMatrix[$lastIndex][$nextIndex];
            $newTime = $currentTime + $segmentTime;
            
            // 检查此点是否超时
            $isOverdue = $checkOverdue($nextIndex, $newTime);
            $overdueSeverity = $calculateOverdueSeverity($nextIndex, $newTime);
            
            // 如果是送货点，检查其任务的超时情况
            $taskId = null;
            if (isset($points[$nextIndex]['task_id'])) {
                $taskId = $points[$nextIndex]['task_id'];
            }

            // 优化3: 估计完成剩余点的最小时间
            $remainingTime = 0;
            if (count($currentPath) < count($points) - 1) {
                // 估算剩余路径的最小时间成本
                $remainingTime = $this->estimateRemainingTime($points, $timeMatrix, $visited, $constraints, $pointIndexMap);
            }

            // 如果估计的总时间已经超过当前最优路径，剪枝
            if ($newTime + $remainingTime >= $bestTime) {
                continue;
            }

            // 优先级计算 - 分数越低越优先
            $priority = $segmentTime;

            // 优化4: 如果是取货点，且有对应的送货点，考虑一起访问的成本
            if (strpos($nextPointId, 'pickup_') === 0 && isset($pickupToDelivery[$nextIndex])) {
                $deliveryIndex = $pickupToDelivery[$nextIndex];
                // 取货后立即送货的总成本
                $combinedCost = $segmentTime + $timeMatrix[$nextIndex][$deliveryIndex];
                // 如果取送货组合成本低，给予优先级提升
                if ($combinedCost < $segmentTime * 2.5) {
                    $priority *= 0.8; // 提高优先级
                }
            }

            // 优化5: 给紧急任务更高优先级，特别是超时或即将超时的任务
            if (isset($points[$nextIndex]['expected_time']) && !empty($points[$nextIndex]['expected_time'])) {
                $deadline = strtotime($points[$nextIndex]['expected_time']);
                $urgency = max(0, $deadline - $now);
                
                // 如果任务超时，大幅提高优先级
                if ($isOverdue) {
                    $priority *= 0.4; // 大幅提高优先级
                    // 超时越严重，优先级越高
                    $priority -= $overdueSeverity * 0.01;
                }
                // 如果即将超时（剩余时间不到1小时），适度提高优先级
                else if ($urgency < 3600) {
                    $priority *= 0.7; // 提高优先级
                    // 根据紧急程度调整优先级
                    $priority -= (3600 - $urgency) / 3600 * 10;
                }
            }

            // 添加到候选列表
            $candidates[] = [
                'index' => $nextIndex,
                'time' => $newTime,
                'priority' => $priority,
                'isOverdue' => $isOverdue,
                'overdueSeverity' => $overdueSeverity,
                'segmentTime' => $segmentTime,
                'taskId' => $taskId
            ];
        }

        // 如果没有候选点，表示路径无法继续
        if (empty($candidates)) {
            // 如果当前路径比之前的最优路径更长，即使没有访问所有点，也考虑更新最优路径
            if (count($currentPath) > count($bestPath) && $currentTime < $bestTime) {
                $bestPath = $currentPath;
                $bestTime = $currentTime;
            }
            return;
        }

        // 确定性地排序候选点 - 首先考虑超时情况，然后按优先级和时间成本排序
        usort($candidates, function($a, $b) {
            // 首先处理超时的任务
            if ($a['isOverdue'] != $b['isOverdue']) {
                return $b['isOverdue'] <=> $a['isOverdue']; // true排在前面
            }
            
            // 如果都是超时任务，按超时严重程度排序
            if ($a['isOverdue'] && $b['isOverdue'] && abs($a['overdueSeverity'] - $b['overdueSeverity']) > 1) {
                return $b['overdueSeverity'] <=> $a['overdueSeverity']; // 超时越严重越优先
            }
            
            // 然后按优先级排序
            if (abs($a['priority'] - $b['priority']) > 0.01) {
                return $a['priority'] <=> $b['priority'];
            }
            
            // 其次按累计时间排序
            if ($a['time'] != $b['time']) {
                return $a['time'] <=> $b['time'];
            }
            
            // 如果有任务ID，按任务ID排序确保确定性
            if ($a['taskId'] !== null && $b['taskId'] !== null && $a['taskId'] != $b['taskId']) {
                return $a['taskId'] <=> $b['taskId'];
            }
            
            // 最后按索引排序确保确定性
            return $a['index'] <=> $b['index'];
        });

        // 按照确定性顺序递归搜索
        foreach ($candidates as $candidate) {
            $nextIndex = $candidate['index'];

            // 访问该点
            $visited[$nextIndex] = true;
            $currentPath[] = $nextIndex;

            // 递归搜索
            $this->searchPathDeterministic($points, $timeMatrix, $pointIndexMap, $constraints, $visited, $currentPath, $candidate['time'], $bestPath, $bestTime);

            // 回溯
            array_pop($currentPath);
            $visited[$nextIndex] = false;
        }
    }

    /**
     * 估算完成剩余未访问点的最小时间
     */
    private function estimateRemainingTime(array $points, array $timeMatrix, array $visited, array $constraints, array $pointIndexMap): float
    {
        $remainingTime = 0;
        $remainingPoints = [];

        // 收集所有未访问点
        for ($i = 0; $i < count($points); $i++) {
            if (!$visited[$i]) {
                $remainingPoints[] = $i;
            }
        }

        // 如果没有剩余点，返回0
        if (empty($remainingPoints)) {
            return 0;
        }

        // 找出每个点的最小连接时间
        foreach ($remainingPoints as $point) {
            $minTime = PHP_INT_MAX;

            // 对于每个点，找到到其他点的最小时间
            foreach ($remainingPoints as $otherPoint) {
                if ($point != $otherPoint && $timeMatrix[$point][$otherPoint] < $minTime) {
                    $minTime = $timeMatrix[$point][$otherPoint];
                }
            }

            // 如果找不到有效的最小时间，使用0
            if ($minTime == PHP_INT_MAX) {
                $minTime = 0;
            }

            // 将最小时间的一半加入估计
            $remainingTime += $minTime / 2;
        }

        return $remainingTime;
    }

    /**
     * 获取骑手多任务的最优路径规划
     *
     * @param int $riderId 骑手ID
     * @param array $riderLocation 骑手当前位置 ['lat' => 纬度, 'lng' => 经度]
     * @param array $taskIds 任务ID数组
     * @return array 优化后的路径详情
     */
    public function getMultiTaskRoute(int $riderId, array $riderLocation, array $taskIds): array
    {
        // 获取指定任务的详细信息
        $tasks = $this->getTasksByIds($riderId, $taskIds);

        if (empty($tasks)) {
            return [
                'route' => [],
                'total_distance' => 0,
                'estimated_time' => 0,
                'waypoints' => [],
                'tasks' => []
            ];
        }

        // 将任务转换为订单格式，用于路径规划
        $orders = $this->normalizeTasksForRouting($tasks);

        // 获取考虑约束的最优路径
        $constrainedRoute = $this->getConstrainedRoute($riderLocation, $orders);

        // 计算路径详情（总距离、预计时间等）
        $totalDistance = 0;
        $estimatedTime = 0;
        $waypoints = [];

        for ($i = 0; $i < count($constrainedRoute) - 1; $i++) {
            $from = $constrainedRoute[$i];
            $to = $constrainedRoute[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                $totalDistance += $distance;

                // 检查是否存在duration字段
                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;
                $estimatedTime += $duration;

                // 构建导航点
                $waypoints[] = [
                    'from' => $from,
                    'to' => $to,
                    'distance' => $distance,
                    'duration' => $duration
                ];
            }
        }

        return [
            'route' => $constrainedRoute,
            'total_distance' => $totalDistance,
            'estimated_time' => $estimatedTime,
            'waypoints' => $waypoints,
            'tasks' => $tasks
        ];
    }

    /**
     * 获取指定任务IDs的任务详情
     *
     * @param int $riderId 骑手ID
     * @param array $taskIds 任务ID数组
     * @return array 任务详情数组
     */
    private function getTasksByIds(int $riderId, array $taskIds): array
    {
        // 获取骑手未完成的任务
        $orders = \App\Models\O2oErrandOrder::where('rider_id', $riderId)
            ->whereIn('id', $taskIds)
            ->whereIn('order_status', [
                \App\Models\O2oErrandOrder::STATUS_PAID,           // 待接单
                \App\Models\O2oErrandOrder::STATUS_PICKUP,         // 待取货
                \App\Models\O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, // 已到取货点
                \App\Models\O2oErrandOrder::STATUS_DELIVERY        // 派送中
            ])
            ->get();

        // 将O2oErrandOrder模型转换为任务格式
        $tasks = [];
        foreach ($orders as $order) {
            // 根据订单类型和状态决定任务类型
            $taskType = 'delivery'; // 默认为配送任务
            if ($order->order_status == \App\Models\O2oErrandOrder::STATUS_PAID ||
                $order->order_status == \App\Models\O2oErrandOrder::STATUS_PICKUP) {
                $taskType = 'pickup'; // 尚未取货的任务
            }

            // 状态映射
            $taskStatus = 'accepted';
            if ($order->order_status == \App\Models\O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                $taskStatus = 'arrived';
            } else if ($order->order_status == \App\Models\O2oErrandOrder::STATUS_DELIVERY) {
                $taskStatus = 'picked_up';
            }

            // 构建任务
            $task = [
                'id' => $order->id,
                'type' => $taskType,
                'status' => $taskStatus,
                'order' => [
                    'id' => $order->id,
                    'pickup_lat' => $order->pickup_lat,
                    'pickup_lng' => $order->pickup_lng,
                    'delivery_lat' => $order->deliver_lat,
                    'delivery_lng' => $order->deliver_lng,
                    'expected_delivery_time' => $order->estimated_delivery_time ? $order->estimated_delivery_time->format('Y-m-d H:i:s') : null
                ],
                'expected_delivery_time' => $order->estimated_delivery_time ? $order->estimated_delivery_time->format('Y-m-d H:i:s') : null
            ];

            $tasks[] = $task;
        }

        return $tasks;
    }

    /**
     * 计算路径优化节省的时间和距离
     *
     * @param array $optimizedRoute 优化后的路径详情
     * @return array 节省的时间和距离信息
     */
    public function calculateSavings(array $optimizedRoute): array
    {
        // 如果没有足够的任务或没有路径，无法计算节省
        if (empty($optimizedRoute['tasks']) || count($optimizedRoute['tasks']) < 2) {
            return [
                'time_savings' => 0,
                'distance_savings' => 0,
                'percentage_time' => 0,
                'percentage_distance' => 0
            ];
        }

        // 计算默认顺序下的路径（按任务ID排序）
        $tasks = collect($optimizedRoute['tasks'])->sortBy('id')->values()->all();
        $defaultOrders = $this->normalizeTasksForRouting($tasks);
        $riderLocation = $optimizedRoute['route'][0]; // 骑手当前位置

        // 获取默认路径
        $defaultRoute = $this->getConstrainedRoute($riderLocation, $defaultOrders);

        // 计算默认路径的总距离和时间
        $defaultDistance = 0;
        $defaultTime = 0;

        for ($i = 0; $i < count($defaultRoute) - 1; $i++) {
            $from = $defaultRoute[$i];
            $to = $defaultRoute[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                $defaultDistance += $distance;

                // 检查是否存在duration字段
                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;
                $defaultTime += $duration;
            }
        }

        // 计算节省的距离和时间
        $distanceSavings = $defaultDistance - $optimizedRoute['total_distance'];
        $timeSavings = $defaultTime - $optimizedRoute['estimated_time'];

        // 计算节省的百分比
        $percentageDistance = $defaultDistance > 0 ? round(($distanceSavings / $defaultDistance) * 100) : 0;
        $percentageTime = $defaultTime > 0 ? round(($timeSavings / $defaultTime) * 100) : 0;

        return [
            'time_savings' => $timeSavings,
            'distance_savings' => $distanceSavings,
            'percentage_time' => $percentageTime,
            'percentage_distance' => $percentageDistance
        ];
    }

    /**
     * 将任务数组标准化为路径规划所需的订单格式
     *
     * @param array $tasks 任务数组
     * @return array 标准化后的订单数组
     */
    private function normalizeTasksForRouting(array $tasks): array
    {
        $orders = [];

        foreach ($tasks as $task) {
            $order = $task['order'];

            // 如果是取件任务
            if ($task['type'] == 'pickup') {
                $orders[] = [
                    'id' => (string)$task['id'],
                    'pickup' => [
                        'lat' => $order['pickup_lat'],
                        'lng' => $order['pickup_lng']
                    ],
                    'delivery' => [
                        'lat' => $order['delivery_lat'],
                        'lng' => $order['delivery_lng']
                    ]
                ];
            }
            // 如果是送件任务且还未取件
            elseif ($task['type'] == 'delivery' && $task['status'] == 'accepted') {
                $orders[] = [
                    'id' => (string)$task['id'],
                    'pickup' => [
                        'lat' => $order['pickup_lat'],
                        'lng' => $order['pickup_lng']
                    ],
                    'delivery' => [
                        'lat' => $order['delivery_lat'],
                        'lng' => $order['delivery_lng']
                    ]
                ];
            }
            // 如果是送件任务且已取件，只需要去送货点
            elseif ($task['type'] == 'delivery' && in_array($task['status'], ['arrived', 'picked_up'])) {
                $orders[] = [
                    'id' => (string)$task['id'],
                    'pickup' => [
                        'lat' => $order['delivery_lat'],
                        'lng' => $order['delivery_lng']
                    ],
                    'delivery' => [
                        'lat' => $order['delivery_lat'],
                        'lng' => $order['delivery_lng']
                    ]
                ];
            }
        }

        return $orders;
    }

    /**
     * 保存路径规划结果到数据库
     *
     * @param int $riderId 骑手ID
     * @param array $routeResult 路径规划结果
     * @param array $savings 节省信息
     * @return int 保存的路径ID
     */
    public function saveRouteResult(int $riderId, array $routeResult, array $savings): int
    {
        // 创建路径规划记录
        $route = \App\Models\RouteOptimization::create([
            'rider_id' => $riderId,
            'total_distance' => $routeResult['total_distance'],
            'estimated_time' => $routeResult['estimated_time'],
            'time_savings' => $savings['time_savings'],
            'distance_savings' => $savings['distance_savings'],
            'percentage_time' => $savings['percentage_time'],
            'percentage_distance' => $savings['percentage_distance'],
            'route_data' => json_encode($routeResult['route']),
            'waypoints_data' => json_encode($routeResult['waypoints']),
            'status' => 'pending', // 等待骑手确认
            'created_at' => now(),
            'updated_at' => now()
        ]);
        // 保存路径中的任务
        $orderIds = collect($routeResult['tasks'])->pluck('id')->all();
        $route->orders()->attach($orderIds);

        return $route->id;
    }

    /**
     * 获取路径规划详情
     *
     * @param int $routeId 路径ID
     * @param int $riderId 骑手ID
     * @return array|null 路径详情
     */
    public function getRouteById(int $routeId, int $riderId): ?array
    {
        $route = \App\Models\RouteOptimization::where('id', $routeId)
            ->where('rider_id', $riderId)
            ->with(['tasks', 'tasks.order'])
            ->first();

        if (!$route) {
            return null;
        }

        return [
            'id' => $route->id,
            'rider_id' => $route->rider_id,
            'total_distance' => $route->total_distance,
            'estimated_time' => $route->estimated_time,
            'time_savings' => $route->time_savings,
            'distance_savings' => $route->distance_savings,
            'percentage_time' => $route->percentage_time,
            'percentage_distance' => $route->percentage_distance,
            'status' => $route->status,
            'route' => json_decode($route->route_data, true),
            'waypoints' => json_decode($route->waypoints_data, true),
            'tasks' => $route->tasks->toArray()
        ];
    }

    /**
     * 更新路径状态
     *
     * @param int $routeId 路径ID
     * @param int $riderId 骑手ID
     * @param bool $isAccepted 是否接受
     * @return bool 是否更新成功
     */
    public function updateRouteStatus(int $routeId, int $riderId, bool $isAccepted): bool
    {
        $route = \App\Models\RouteOptimization::where('id', $routeId)
            ->where('rider_id', $riderId)
            ->first();

        if (!$route) {
            return false;
        }

        $route->status = $isAccepted ? 'accepted' : 'rejected';
        $route->updated_at = now();
        $route->save();

        return true;
    }

    /**
     * 获取导航信息
     *
     * @param array $routeDetail 路径详情
     * @param array $currentLocation 当前位置
     * @return array 导航信息
     */
    public function getNavigationInfo(array $routeDetail, array $currentLocation): array
    {
        // 路径规划
        $route = $routeDetail['route'];
        $waypoints = $routeDetail['waypoints'];

        // 找到当前位置最近的目标点
        $nearestPoint = null;
        $minDistance = PHP_INT_MAX;

        foreach ($route as $index => $point) {
            if ($index === 0) { // 跳过起点（骑手初始位置）
                continue;
            }

            $fromCoord = $currentLocation['lat'] . ',' . $currentLocation['lng'];
            $toCoord = $point['lat'] . ',' . $point['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];

                if ($distance < $minDistance) {
                    $minDistance = $distance;
                    $nearestPoint = [
                        'index' => $index,
                        'point' => $point,
                        'distance' => $distance,
                        'duration' => isset($response['result']['rows'][0]['elements'][0]['duration'])
                            ? $response['result']['rows'][0]['elements'][0]['duration']
                            : 0
                    ];
                }
            }
        }

        // 计算剩余的路线
        $remainingRoute = array_slice($route, $nearestPoint['index']);
        $remainingDistance = 0;
        $remainingTime = 0;

        for ($i = 0; $i < count($remainingRoute) - 1; $i++) {
            $from = $remainingRoute[$i];
            $to = $remainingRoute[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                $remainingDistance += $distance;

                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;
                $remainingTime += $duration;
            }
        }

        // 获取任务相关信息
        $taskInfo = null;
        if (isset($nearestPoint['point']['order_id'])) {
            $taskId = $nearestPoint['point']['order_id'];
            $task = \App\Models\O2oErrandOrder::find($taskId);

            if ($task) {
                $taskInfo = [
                    'id' => $task->id,
                    'order_no' => $task->order->order_no,
                    'type' => $task->type,
                    'status' => $task->status,
                    'address' => $nearestPoint['point']['type'] == 'pickup'
                        ? $task->order->pickup_address
                        : $task->order->delivery_address,
                    'name' => $nearestPoint['point']['type'] == 'pickup'
                        ? $task->order->pickup_name
                        : $task->order->delivery_name,
                    'phone' => $nearestPoint['point']['type'] == 'pickup'
                        ? $task->order->pickup_phone
                        : $task->order->delivery_phone
                ];
            }
        }

        // 高德地图需要的导航信息
        $amapNavInfo = [
            'origin' => $currentLocation['lng'] . ',' . $currentLocation['lat'],
            'destination' => $nearestPoint['point']['lng'] . ',' . $nearestPoint['point']['lat']
        ];

        // 后续导航点
        $waypointsCoords = [];
        for ($i = $nearestPoint['index'] + 1; $i < count($route); $i++) {
            $waypointsCoords[] = $route[$i]['lng'] . ',' . $route[$i]['lat'];
        }

        if (!empty($waypointsCoords)) {
            $amapNavInfo['waypoints'] = implode(';', $waypointsCoords);
        }

        return [
            'current_location' => $currentLocation,
            'nearest_point' => $nearestPoint['point'],
            'distance_to_nearest' => $minDistance,
            'duration_to_nearest' => $nearestPoint['duration'],
            'remaining_route' => $remainingRoute,
            'remaining_distance' => $remainingDistance,
            'remaining_time' => $remainingTime,
            'task_info' => $taskInfo,
            'amap_navigation' => $amapNavInfo
        ];
    }

    /**
     * 获取骑手当前活跃任务
     *
     * @param int $riderId 骑手ID
     * @return array 活跃任务列表
     */
    public function getRiderActiveTasks(int $riderId,$riderRole): array
    {
        // 获取骑手当前活跃订单
        $orders = \App\Models\O2oErrandOrder::where('rider_id', $riderId)
            ->whereIn('order_status', [
                \App\Models\O2oErrandOrder::STATUS_PICKUP,         // 待取货
                \App\Models\O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, // 已到取货点
                \App\Models\O2oErrandOrder::STATUS_DELIVERY        // 派送中
            ])->where('refund_status', 0)
            ->get();
        // 将O2oErrandOrder模型转换为任务格式
        $tasks = [];
        foreach ($orders as $order) {
            // 根据订单类型和状态决定任务类型
            $taskType = 'delivery'; // 默认为配送任务
            if ( $order->order_status == \App\Models\O2oErrandOrder::STATUS_PICKUP) {
                $taskType = 'pickup'; // 尚未取货的任务
            }

            // 状态映射
            $taskStatus = 'accepted';
            if ($order->order_status == \App\Models\O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                $taskStatus = 'arrived';
            } else if ($order->order_status == \App\Models\O2oErrandOrder::STATUS_DELIVERY) {
                $taskStatus = 'picked_up';
            }

            // 构建任务
            $task = [
                'id' => $order->id,
                'type' => $taskType,
                'status' => $taskStatus,
                'order' => $this->taskService->formatTask($order, [], $riderRole),
                'expected_delivery_time' => $order->estimated_delivery_time ? $order->estimated_delivery_time->format('Y-m-d H:i:s') : null
            ];

            $tasks[] = $task;
        }

        return $tasks;
    }

    /**
     * 计算可能的优化节省
     *
     * @param int $riderId 骑手ID
     * @param array $tasks 任务数组
     * @return array 潜在节省信息
     */
    public function calculatePotentialSavings(int $riderId, array $tasks): array
    {
        // 获取骑手当前位置
        $rider = Rider::find($riderId);
        $riderLocation = [
            'lat' => $rider->last_lat,
            'lng' => $rider->last_lng
        ];

        // 将任务转换为订单格式
        $orders = $this->normalizeTasksForRouting($tasks);

        // 获取考虑约束的最优路径
        $constrainedRoute = $this->getConstrainedRoute($riderLocation, $orders);

        // 计算优化路径的总距离和时间
        $optimizedDistance = 0;
        $optimizedTime = 0;

        for ($i = 0; $i < count($constrainedRoute) - 1; $i++) {
            $from = $constrainedRoute[$i];
            $to = $constrainedRoute[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                $optimizedDistance += $distance;

                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;
                $optimizedTime += $duration;
            }
        }

        // 计算默认顺序下的路径（按任务ID排序）
        $sortedTasks = collect($tasks)->sortBy('id')->values()->all();
        $defaultOrders = $this->normalizeTasksForRouting($sortedTasks);

        // 获取默认路径
        $defaultRoute = $this->getConstrainedRoute($riderLocation, $defaultOrders);

        // 计算默认路径的总距离和时间
        $defaultDistance = 0;
        $defaultTime = 0;

        for ($i = 0; $i < count($defaultRoute) - 1; $i++) {
            $from = $defaultRoute[$i];
            $to = $defaultRoute[$i + 1];

            $fromCoord = $from['lat'] . ',' . $from['lng'];
            $toCoord = $to['lat'] . ',' . $to['lng'];

            $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

            if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                $distance = $response['result']['rows'][0]['elements'][0]['distance'];
                $defaultDistance += $distance;

                $duration = isset($response['result']['rows'][0]['elements'][0]['duration'])
                    ? $response['result']['rows'][0]['elements'][0]['duration']
                    : 0;
                $defaultTime += $duration;
            }
        }

        // 计算节省的距离和时间
        $distanceSavings = $defaultDistance - $optimizedDistance;
        $timeSavings = $defaultTime - $optimizedTime;

        return [
            'time_savings' => $timeSavings,
            'distance_savings' => $distanceSavings
        ];
    }

    /**
     * 根据时间窗口和位置关联性筛选需要优化的任务
     *
     * @param array $tasks 所有活跃任务
     * @param array $riderLocation 骑手位置 ['lat' => 纬度, 'lng' => 经度]
     * @param int $timeWindow 时间窗口(分钟)
     * @param int $maxDistance 最大距离(米)
     * @return array 可以进行路径优化的任务
     */
    public function filterOptimizableTasks(array $tasks, array $riderLocation, int $timeWindow = 60, int $maxDistance = 5000): array
    {
        // 如果任务数量小于2，直接返回所有任务
        if (count($tasks) < 2) {
            return $tasks;
        }

        // 当前时间
        $now = time();

        // 转换时间窗口为秒
        $timeWindowSeconds = $timeWindow * 60;

        // 位置关联任务组
        $locationGroups = [];

        // 任务时间窗口过滤
        $timeRelatedTasks = [];

        // 第一步: 按照送达时间筛选任务
        foreach ($tasks as $task) {
            // 如果有预计送达时间，则检查是否在时间窗口内
            if (isset($task['expected_delivery_time'])) {
                $deliveryTime = strtotime($task['expected_delivery_time']);
                // 如果送达时间在时间窗口内，加入到时间相关任务
                if (abs($deliveryTime - $now) <= $timeWindowSeconds) {
                    $timeRelatedTasks[] = $task;
                }
            } else {
                // 如果没有预计送达时间，仍然考虑此任务
                $timeRelatedTasks[] = $task;
            }
        }

        // 如果时间相关任务数量小于2，使用所有任务
        if (count($timeRelatedTasks) < 2) {
            $timeRelatedTasks = $tasks;
        }

        // 第二步: 按照地理位置进行聚类
        // 先创建坐标点数组，用于距离计算
        $locations = [];
        foreach ($timeRelatedTasks as $index => $task) {
            // 对于取件任务
            if ($task['type'] == 'pickup') {
                $locations[$index] = [
                    'lat' => $task['order']['pickup_lat'],
                    'lng' => $task['order']['pickup_lng']
                ];
            }
            // 对于送件任务
            else {
                // 如果尚未取件，需要先取件
                if ($task['status'] == 'accepted') {
                    $locations[$index] = [
                        'lat' => $task['order']['pickup_lat'],
                        'lng' => $task['order']['pickup_lng']
                    ];
                }
                // 如果已取件，只需送达
                else {
                    $locations[$index] = [
                        'lat' => $task['order']['delivery_lat'],
                        'lng' => $task['order']['delivery_lng']
                    ];
                }
            }
        }

        // 计算任务间的距离矩阵
        $distanceMatrix = $this->getDistanceMatrix(array_merge([$riderLocation], $locations));

        // 找出与骑手当前位置距离在最大距离范围内的任务
        $proximityTasks = [];
        foreach ($timeRelatedTasks as $index => $task) {
            // 骑手位置在第一行，任务从第二列开始
            if ($distanceMatrix[0][$index + 1] <= $maxDistance) {
                $proximityTasks[] = $task;
            }
        }

        // 如果与骑手距离接近的任务不足2个，退回使用时间相关任务
        if (count($proximityTasks) < 2) {
            $proximityTasks = $timeRelatedTasks;
        }

        // 第三步: 找出任务之间可能形成的路径组
        // 创建邻接表表示任务之间的距离关系
        $adjacencyList = [];
        $taskCount = count($proximityTasks);

        for ($i = 0; $i < $taskCount; $i++) {
            $adjacencyList[$i] = [];
            for ($j = 0; $j < $taskCount; $j++) {
                if ($i == $j) continue;

                // 计算两个任务地点之间的距离
                $taskALocation = $locations[$i];
                $taskBLocation = $locations[$j];

                $fromCoord = $taskALocation['lat'] . ',' . $taskALocation['lng'];
                $toCoord = $taskBLocation['lat'] . ',' . $taskBLocation['lng'];

                $response = $this->mapService->distanceMatrix($fromCoord, $toCoord);

                if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                    $distance = $response['result']['rows'][0]['elements'][0]['distance'];

                    // 如果距离在可接受范围内，添加到邻接表
                    if ($distance <= $maxDistance) {
                        $adjacencyList[$i][] = $j;
                    }
                }
            }
        }

        // 使用DFS查找连通的任务组
        $visited = array_fill(0, $taskCount, false);
        $groups = [];
        $currentGroup = [];

        function dfs($node, &$adjacencyList, &$visited, &$currentGroup) {
            $visited[$node] = true;
            $currentGroup[] = $node;

            foreach ($adjacencyList[$node] as $neighbor) {
                if (!$visited[$neighbor]) {
                    dfs($neighbor, $adjacencyList, $visited, $currentGroup);
                }
            }
        }

        // 查找所有连通组
        for ($i = 0; $i < $taskCount; $i++) {
            if (!$visited[$i]) {
                $currentGroup = [];
                dfs($i, $adjacencyList, $visited, $currentGroup);
                if (count($currentGroup) >= 2) {
                    $groups[] = $currentGroup;
                }
            }
        }

        // 如果没有找到符合条件的连通组，使用所有任务
        if (empty($groups)) {
            return $proximityTasks;
        }

        // 找出最大的连通组
        $maxGroup = $groups[0];
        foreach ($groups as $group) {
            if (count($group) > count($maxGroup)) {
                $maxGroup = $group;
            }
        }

        // 根据最大连通组索引，获取对应的任务
        $result = [];
        foreach ($maxGroup as $index) {
            $result[] = $proximityTasks[$index];
        }

        return $result;
    }

    /**
     * 发送HTTP请求到指定URL
     *
     * @param string $url API请求地址
     * @param array $params 请求参数
     * @return array 响应结果
     */
    protected function sendRequest(string $url, array $params): array
    {
        $queryString = http_build_query($params);
        $fullUrl = $url . '?' . $queryString;

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $fullUrl);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_TIMEOUT, 10);
        $response = curl_exec($ch);
        curl_close($ch);

        return json_decode($response, true) ?: [];
    }

    /**
     * 计算给定路径的总时间
     *
     * @param array $route 路径点ID数组
     * @param array $pointIndexMap 点ID到索引的映射
     * @param array $timeMatrix 时间矩阵
     * @param bool $isDefault 是否是默认路径计算 (用于区分默认路径和优化路径的计算方式)
     * @return float 路径总时间
     */
    private function calculatePathTime(array $route, array $pointIndexMap, array $timeMatrix, bool $isDefault = false): float
    {
        $totalTime = 0;
        $invalidPathCount = 0; // 记录无效路径段数量
        $pathSegments = []; // 记录路径段，用于后续分析

        for ($i = 0; $i < count($route) - 1; $i++) {
            // 检查点ID是否在映射中
            if (!isset($pointIndexMap[$route[$i]]) || !isset($pointIndexMap[$route[$i + 1]])) {
                $invalidPathCount++;
                continue; // 跳过无效的点
            }

            $fromIndex = $pointIndexMap[$route[$i]];
            $toIndex = $pointIndexMap[$route[$i + 1]];

            // 收集路径段信息
            $pathSegments[] = [
                'from_id' => $route[$i],
                'to_id' => $route[$i + 1],
                'from_index' => $fromIndex,
                'to_index' => $toIndex
            ];

            // 检查矩阵中是否有有效的时间值
            if (isset($timeMatrix[$fromIndex][$toIndex]) && $timeMatrix[$fromIndex][$toIndex] != PHP_INT_MAX) {
                $segmentTime = $timeMatrix[$fromIndex][$toIndex];
                $totalTime += $segmentTime;
            } else {
                $invalidPathCount++;

                // 如果没有有效时间值，尝试使用经纬度估算
                $fromId = $route[$i];
                $toId = $route[$i + 1];

                // 查找点的经纬度信息
                $fromLat = null;
                $fromLng = null;
                $toLat = null;
                $toLng = null;

                foreach ($pointIndexMap as $id => $index) {
                    if ($index === $fromIndex && isset($points[$index])) {
                        $fromLat = $points[$index]['lat'] ?? null;
                        $fromLng = $points[$index]['lng'] ?? null;
                    }
                    if ($index === $toIndex && isset($points[$index])) {
                        $toLat = $points[$index]['lat'] ?? null;
                        $toLng = $points[$index]['lng'] ?? null;
                    }
                }

                // 如果有经纬度，使用haversine公式计算距离
                if ($fromLat !== null && $fromLng !== null && $toLat !== null && $toLng !== null) {
                    // 使用haversine公式计算距离（米）
                    $earthRadius = 6371000; // 地球半径，单位米
                    $dLat = deg2rad($toLat - $fromLat);
                    $dLon = deg2rad($toLng - $fromLng);
                    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($fromLat)) * cos(deg2rad($toLat)) * sin($dLon/2) * sin($dLon/2);
                    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                    $distance = $earthRadius * $c;

                    // 估算时间（秒）= 距离/速度，假设平均速度为30km/h
                    $segmentTime = $distance / 8.33;
                    $totalTime += $segmentTime;
                } else {
                    // 如果无法估算，使用两点之间的索引差值和一个固定的时间系数
                    $estimatedTime = abs($toIndex - $fromIndex) * 100; // 每个索引差值假设对应100秒时间
                    $totalTime += $estimatedTime;
                }
            }
        }

        // 如果所有路径段都无效，返回PHP_INT_MAX表示无法计算
        if ($invalidPathCount == count($route) - 1 && count($route) > 1) {
            return PHP_INT_MAX;
        }

        return $totalTime;
    }

    /**
     * 分析路径中取货-送货点对的连续性
     *
     * @param array $route 路径
     * @return int 相邻的取货-送货对数量
     */
    private function analyzePickupDeliveryPairs(array $route): int
    {
        $pairs = 0;

        for ($i = 0; $i < count($route) - 1; $i++) {
            $fromId = $route[$i];
            $toId = $route[$i + 1];

            // 检查是否是取货点后紧跟对应的送货点
            if (strpos($fromId, 'pickup_') === 0) {
                $taskId = substr($fromId, 7); // 提取任务ID
                $expectedDeliveryId = 'delivery_' . $taskId;

                if ($toId === $expectedDeliveryId) {
                    $pairs++;
                }
            }
        }

        return $pairs;
    }

    /**
     * 按照任务送达时间排序的贪心算法
     * 确保遵循取货必须在送货之前的约束
     */
    private function greedyOptimizationWithTimeOrder(array $orderedTasks, array $riderLocation, array $points,
                                         array $timeMatrix, array $pointIndexMap, array $constraints): array
    {
        // 开始于骑手当前位置
        $currentPointIndex = $pointIndexMap['rider'];
        $visited = array_fill(0, count($points), false);
        $visited[$currentPointIndex] = true;

        $route = [$points[$currentPointIndex]['id']];
        $pickedUpTasks = [];
        $totalTime = 0;
        
        // 创建任务ID到点的映射
        $taskToPoints = [];
        foreach ($points as $point) {
            if (isset($point['task_id'])) {
                $taskId = $point['task_id'];
                $type = $point['type'];
                
                if (!isset($taskToPoints[$taskId])) {
                    $taskToPoints[$taskId] = [];
                }
                
                $taskToPoints[$taskId][$type] = [
                    'id' => $point['id'],
                    'index' => $pointIndexMap[$point['id']]
                ];
            }
        }
        
        // 按照已排序的任务顺序添加点
        foreach ($orderedTasks as $task) {
            $taskId = $task['id'];
            
            // 如果任务需要取货且尚未取货
            if (isset($taskToPoints[$taskId]['pickup']) && !in_array($taskId, $pickedUpTasks)) {
                $nextIndex = $taskToPoints[$taskId]['pickup']['index'];
                
                // 如果点还没被访问
                if (!$visited[$nextIndex]) {
                    // 访问该点
                    $visited[$nextIndex] = true;
                    $route[] = $points[$nextIndex]['id'];
                    $totalTime += $timeMatrix[$currentPointIndex][$nextIndex];
                    $currentPointIndex = $nextIndex;
                    $pickedUpTasks[] = $taskId;
                }
            }
            
            // 如果任务已取货，添加送货点
            if (isset($taskToPoints[$taskId]['delivery']) && in_array($taskId, $pickedUpTasks)) {
                $nextIndex = $taskToPoints[$taskId]['delivery']['index'];
                
                // 如果点还没被访问
                if (!$visited[$nextIndex]) {
                    // 访问该点
                    $visited[$nextIndex] = true;
                    $route[] = $points[$nextIndex]['id'];
                    $totalTime += $timeMatrix[$currentPointIndex][$nextIndex];
                    $currentPointIndex = $nextIndex;
                }
            }
        }
        
        return [
            'route' => $route,
            'total_time' => $totalTime
        ];
    }

    /**
     * 统一计算路径总时间的方法，确保所有路径使用相同的计算逻辑
     * 
     * @param array $route 路径点ID数组
     * @param array $points 所有点的详细信息
     * @param array $pointIndexMap 点ID到索引的映射
     * @param array $timeMatrix 时间矩阵
     * @param array $distanceMatrix 距离矩阵
     * @return float 路径总时间
     */
    private function calculateUnifiedPathTime(array $route, array $points, array $pointIndexMap, array $timeMatrix, array $distanceMatrix): float
    {
        $totalTime = 0;
        
        for ($i = 0; $i < count($route) - 1; $i++) {
            // 检查点ID是否在映射中
            if (!isset($pointIndexMap[$route[$i]]) || !isset($pointIndexMap[$route[$i + 1]])) {
                continue; // 跳过无效的点
            }
            
            $fromIndex = $pointIndexMap[$route[$i]];
            $toIndex = $pointIndexMap[$route[$i + 1]];
            
            // 使用时间矩阵中的值，确保有效
            if (isset($timeMatrix[$fromIndex][$toIndex]) && $timeMatrix[$fromIndex][$toIndex] != PHP_INT_MAX) {
                $totalTime += $timeMatrix[$fromIndex][$toIndex];
            } else {
                // 如果时间矩阵中没有有效值，使用距离矩阵并估算时间
                if (isset($distanceMatrix[$fromIndex][$toIndex]) && $distanceMatrix[$fromIndex][$toIndex] != PHP_INT_MAX) {
                    // 根据距离估算时间，假设速度为30km/h，约8.33米/秒
                    $totalTime += $distanceMatrix[$fromIndex][$toIndex] / 8.33;
                } else {
                    // 如果没有距离数据，使用两点间的直线距离
                    $fromPoint = $points[$fromIndex];
                    $toPoint = $points[$toIndex];
                    
                    $lat1 = (float)$fromPoint['lat'];
                    $lng1 = (float)$fromPoint['lng'];
                    $lat2 = (float)$toPoint['lat'];
                    $lng2 = (float)$toPoint['lng'];
                    
                    // 使用haversine公式计算直线距离（米）
                    $earthRadius = 6371000; // 地球半径，单位米
                    $dLat = deg2rad($lat2 - $lat1);
                    $dLon = deg2rad($lng2 - $lng1);
                    $a = sin($dLat/2) * sin($dLat/2) + cos(deg2rad($lat1)) * cos(deg2rad($lat2)) * sin($dLon/2) * sin($dLon/2);
                    $c = 2 * atan2(sqrt($a), sqrt(1-$a));
                    $distance = $earthRadius * $c;
                    
                    // 根据距离估算时间
                    $totalTime += $distance / 8.33;
                }
            }
        }
        
        return $totalTime;
    }
}

