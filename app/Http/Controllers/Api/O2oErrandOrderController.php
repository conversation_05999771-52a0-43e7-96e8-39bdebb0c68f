<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\CancelO2oErrandOrderRequest;
use App\Http\Requests\Api\O2oErrandOrderCreateRequest;
use App\Http\Requests\Api\O2oErrandOrderPreRequest;
use App\Http\Requests\Api\OrderEvaluateRequest;
use App\Http\Requests\Api\OrderPayRecRequest;
use App\Http\Requests\Api\OrderValidateCodeRequest;
use App\Http\Requests\Api\RefundO2oErrandOrderRequest;
use App\Http\Resources\O2oErrandOrderResource;
use App\Http\Resources\OrderEvaluateRecResource;
use App\Http\Resources\OrderPayRecResource;
use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\OrderPayRec;
use App\Services\O2oErrandOrderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Redis;

class O2oErrandOrderController extends Controller
{
    public function index(Request $request)
    {
        $query = O2oErrandOrder::query()->with(["goodsCategory"])->where("user_id", $request->user()->id);
        if ($request->status) {
            $statusArr = explode(",", $request->status);
            $query->where(function ($query) use ($statusArr) {
                foreach ($statusArr as $status) {
                    if ($status >= O2oErrandOrder::STATUS_CANCEL) {
                        $query->orWhere(function ($query) use($status){
                            $query->where("order_status", $status)->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT);
                        });
                    }else{
                        $query->orWhere("order_status", $status)->orWhere("refund_status", "<>", O2oErrandOrder::REFUND_STATUS_INIT);
                    }
                }
            });
        }
        $orders = $query->orderBy("created_at", "desc")
            ->orderBy("id", "desc")
            ->paginate($request->input('page_size', $this->pageSize));
        $result = [
            'total' => $orders->total(),
            'has_next' => $orders->hasMorePages(),
            'data' => [],
        ];
        if ($orders) {
            foreach ($orders as $v) {
                $result['data'][] = new O2oErrandOrderResource($v);
            }
        }
        return $this->success('success', $result);
    }

    public function show($orderNo, Request $request)
    {
        $order = O2oErrandOrder::query()->with(["rider", "goodsCategory", "evaluate",
            'payRecs' => function ($query) { //支付商品费订单信息
                $query->where('type', OrderPayRec::TYPE_GOODS);
            }])->where('order_no', $orderNo)->first();

        if (!$order) $this->errorResponse(500, "订单不存在");

        if ($order->user_id != $request->user()->id) $this->errorResponse(500, "");

        return $this->success("success", new O2oErrandOrderResource($order));
    }

    public function status(CancelO2oErrandOrderRequest $request)
    {
        $order = O2oErrandOrder::query()->where('order_no', $request->order_no)->first();
        if (!$order) $this->errorResponse(500, "该订单不存在");
        if ($order->user_id != $request->user()->id) $this->errorResponse(500, "该订单不存在");
        $pos = null;
        if ($order->rider_id && $order->order_status < O2oErrandOrder::STATUS_FINISH) {
            $pos = Redis::geopos(Location::LOCATION_BELONG_RIDER, [$order->rider_id])[0];
        }
        return $this->success("success", [
            'order_status' => $order->order_status,
            "rider_id" => $order->rider_id,
            'rider_pos' => $pos,
        ]);
    }

    public function pre(O2oErrandOrderPreRequest $request, O2oErrandOrderService $service)
    {
        try {
            $res = $service->preOrder($request->user()->id, $request->all());
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("success", $res);
    }

    public function store(O2oErrandOrderCreateRequest $request, O2oErrandOrderService $service)
    {
        try {
            $order = $service->createOrder($request->user()->id, $request->all());
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("预下单成功", new O2oErrandOrderResource($order));
    }

    //待支付取消订单
    public function cancel(CancelO2oErrandOrderRequest $request, O2oErrandOrderService $service)
    {
        try {
            $order = $service->cancelOrder($request->order_no, $request->user()->id);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("取消订单成功", new O2oErrandOrderResource($order));
    }

    //已支付取消订单
    public function refund(RefundO2oErrandOrderRequest $request, O2oErrandOrderService $service)
    {
        try {
            $order = $service->refundOrder($request->order_no, $request->refund_reason, $request->user()->id);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("取消订单成功", new O2oErrandOrderResource($order));
    }

    //加小费
    public function gratuity(OrderPayRecRequest $request, O2oErrandOrderService $service)
    {
        try {
            $rec = $service->addGratuity($request->order_no, $request->amount);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("加小费预下单成功", new OrderPayRecResource($rec));
    }

    //催单
    public function remind(CancelO2oErrandOrderRequest $request, O2oErrandOrderService $service)
    {
        try {
            $service->remind($request->order_no);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("催单成功", null);
    }

    //验证取货码/收货码
    public function validateCode(OrderValidateCodeRequest $request, O2oErrandOrderService $service)
    {
        try {
            $service->validateCode($request->order_no, $request->code_type, $request->code);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("验证成功", null);
    }

    //打赏
    public function reward(OrderPayRecRequest $request, O2oErrandOrderService $service)
    {
        try {
            $rec = $service->reward($request->order_no, $request->amount);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("打赏预下单成功", new OrderPayRecResource($rec));
    }

    //评价
    public function evaluate(OrderEvaluateRequest $request, O2oErrandOrderService $service)
    {
        $data = $request->only(['order_no', 'is_anonymous', 'is_satisfied', 'reason', 'remark', 'imgs', 'pszs_star',
            'cdgf_star', 'ybzj_star', 'hpwh_star', 'lmrq_star']);
        try {
            $rec = $service->evaluate($data);
        } catch (\Exception $e) {
            $this->errorResponse(500, $e->getMessage());
        }
        return $this->success("评价成功", new OrderEvaluateRecResource($rec));
    }
}
