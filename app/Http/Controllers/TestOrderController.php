<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class TestOrderController extends Controller
{
    public function getOrders()
    {
        // 从数据库中随机获取10个订单
        $orders = DB::table('o2o_errand_orders')
            ->select([
                'id',
                'order_no',
                'deliver_address as address',
                DB::raw('CAST(deliver_lat AS DECIMAL(9,6)) as latitude'),
                DB::raw('CAST(deliver_lng AS DECIMAL(9,6)) as longitude'),
                'estimated_delivery_time as expected_delivery_time'
            ])
            ->where('order_status', '>=', 20) // 只获取待接单及之后的订单
            ->where('deliver_lat', '!=', 0) // 确保有有效的坐标
            ->where('deliver_lng', '!=', 0)
            ->inRandomOrder()
            ->limit(10)
            ->get();

        return response()->json([
            'code' => 0,
            'data' => $orders
        ]);
    }
} 