<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NotifyLog extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $casts = ['is_settled' => 'boolean'];

    protected $fillable = [
        'notify_id', 'user_id', 'is_settled'
    ];

    public function notify(){
        return $this->belongsTo(Notify::class, "notify_id");
    }

    public function user(){
        return $this->belongsTo(User::class, "user_id");
    }
}
