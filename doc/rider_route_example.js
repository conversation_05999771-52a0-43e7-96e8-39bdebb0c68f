/**
 * 骑手路径规划与高德地图集成示例
 * 
 * 此示例展示如何使用骑手路径规划API与高德地图SDK集成
 * 需要先引入高德地图SDK：
 * <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=您的高德地图KEY"></script>
 */

// 骑手位置（定位获取或手动设置）
let riderPosition = {
  lat: 31.230416,  // 纬度
  lng: 121.473701  // 经度
};

// 1. 调用路径规划API
function getRiderRoute() {
  // 显示加载中提示
  showLoading(true);
  
  fetch('/api/v1/rider/path_planning', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + localStorage.getItem('token') // 从本地存储获取认证令牌
    },
    body: JSON.stringify({
      lat: riderPosition.lat,
      lng: riderPosition.lng
    })
  })
  .then(response => response.json())
  .then(result => {
    if (result.code === 0) {
      // 路径规划成功
      const routeData = result.data;
      
      // 显示总距离和预计时间
      document.getElementById('total-distance').innerText = (routeData.total_distance / 1000).toFixed(2) + ' 公里';
      document.getElementById('estimated-time').innerText = formatTime(routeData.estimated_time);
      
      // 渲染订单列表
      renderOrderList(routeData.orders);
      
      // 使用高德地图显示路径规划结果
      renderAmapRoute(routeData.amap);
    } else {
      // 处理错误
      showError(result.message || '获取路径规划失败');
    }
    
    // 隐藏加载提示
    showLoading(false);
  })
  .catch(error => {
    console.error('获取路径规划失败:', error);
    showError('网络错误，请稍后重试');
    showLoading(false);
  });
}

// 2. 使用高德地图SDK显示路径规划
function renderAmapRoute(amapData) {
  // 创建地图实例
  const map = new AMap.Map('map-container', {
    zoom: 13,
    center: amapData.origin.split(',').reverse() // 以起点为中心
  });
  
  // 添加起点标记
  const startMarker = new AMap.Marker({
    position: amapData.origin.split(',').reverse(),
    icon: 'https://webapi.amap.com/theme/v1.3/markers/n/start.png',
    map: map
  });
  
  // 添加终点标记
  const endMarker = new AMap.Marker({
    position: amapData.destination.split(',').reverse(),
    icon: 'https://webapi.amap.com/theme/v1.3/markers/n/end.png',
    map: map
  });
  
  // 添加途经点标记（如果有）
  if (amapData.waypoints) {
    const waypoints = amapData.waypoints.split(';');
    waypoints.forEach((point, index) => {
      const wayPointMarker = new AMap.Marker({
        position: point.split(',').reverse(),
        content: `<div class="way-point-marker">${index + 1}</div>`,
        map: map
      });
    });
  }
  
  // 使用高德驾车路径规划
  const driving = new AMap.Driving({
    map: map,
    panel: "path-result-panel" // 路径详情面板的ID
  });
  
  // 清除可能的旧路径
  driving.clear();
  
  // 构建途经点数组
  const waypoints = amapData.waypoints ? 
    amapData.waypoints.split(';').map(point => new AMap.LngLat(...point.split(','))) : 
    [];
  
  // 发起路径规划
  driving.search(
    new AMap.LngLat(...amapData.origin.split(',')),    // 起点
    new AMap.LngLat(...amapData.destination.split(',')), // 终点
    {
      waypoints: waypoints  // 途经点
    },
    function(status, result) {
      if (status === 'complete') {
        console.log('高德地图路径规划成功');
      } else {
        console.error('高德地图路径规划失败:', result);
      }
    }
  );
}

// 3. 渲染订单列表
function renderOrderList(orders) {
  const orderListContainer = document.getElementById('order-list');
  orderListContainer.innerHTML = '';
  
  if (orders.length === 0) {
    orderListContainer.innerHTML = '<div class="no-orders">当前没有进行中的订单</div>';
    return;
  }
  
  orders.forEach(order => {
    const orderStatusText = getOrderStatusText(order.order_status);
    const orderTypeText = getOrderTypeText(order.type);
    
    const orderEl = document.createElement('div');
    orderEl.className = 'order-item';
    orderEl.innerHTML = `
      <div class="order-header">
        <span class="order-no">${order.order_no}</span>
        <span class="order-status">${orderStatusText}</span>
        <span class="order-type">${orderTypeText}</span>
      </div>
      <div class="order-detail">
        <div class="pickup-info">
          <h4>取货点</h4>
          <p><strong>${order.pickup.name}</strong></p>
          <p>${order.pickup.address}</p>
          <p><a href="tel:${order.pickup.phone}">${order.pickup.phone}</a></p>
        </div>
        <div class="delivery-info">
          <h4>送货点</h4>
          <p><strong>${order.delivery.name}</strong></p>
          <p>${order.delivery.address}</p>
          <p><a href="tel:${order.delivery.phone}">${order.delivery.phone}</a></p>
        </div>
      </div>
    `;
    
    orderListContainer.appendChild(orderEl);
  });
}

// 辅助函数: 格式化时间（秒 -> 时分秒）
function formatTime(seconds) {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;
  
  let result = '';
  if (hours > 0) result += hours + '小时';
  if (minutes > 0) result += minutes + '分钟';
  if (secs > 0 || result === '') result += secs + '秒';
  
  return result;
}

// 辅助函数: 获取订单状态文本
function getOrderStatusText(status) {
  const statusMap = {
    26: '待取货',
    28: '已到达取货点',
    30: '派送中'
  };
  return statusMap[status] || '未知状态';
}

// 辅助函数: 获取订单类型文本
function getOrderTypeText(type) {
  const typeMap = {
    1: '帮我送',
    2: '帮我取',
    3: '帮我买',
    24: '全能帮'
  };
  return typeMap[type] || '未知类型';
}

// 辅助函数: 显示/隐藏加载提示
function showLoading(isShow) {
  const loadingEl = document.getElementById('loading');
  if (loadingEl) {
    loadingEl.style.display = isShow ? 'flex' : 'none';
  }
}

// 辅助函数: 显示错误信息
function showError(message) {
  const errorEl = document.getElementById('error-message');
  if (errorEl) {
    errorEl.innerText = message;
    errorEl.style.display = 'block';
    
    // 3秒后自动隐藏
    setTimeout(() => {
      errorEl.style.display = 'none';
    }, 3000);
  }
}

// 初始化时获取当前位置并加载路径规划
document.addEventListener('DOMContentLoaded', () => {
  // 尝试获取用户当前位置
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(
      // 成功获取位置
      (position) => {
        riderPosition = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        getRiderRoute();
      },
      // 获取位置失败
      (error) => {
        console.error('获取位置失败:', error);
        // 使用默认位置
        getRiderRoute();
      }
    );
  } else {
    // 浏览器不支持地理定位
    console.error('浏览器不支持地理定位');
    // 使用默认位置
    getRiderRoute();
  }
  
  // 绑定刷新按钮事件
  document.getElementById('refresh-button').addEventListener('click', getRiderRoute);
}); 