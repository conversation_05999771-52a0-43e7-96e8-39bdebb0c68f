<?php

namespace App\Http\Controllers\Merchant;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * 创建一个新的控制器实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:merchant');
    }

    /**
     * 显示商家后台首页
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $merchant = Auth::guard('merchant')->user();
        
        // 更新最后登录时间
        if (!$merchant->last_login_at) {
            $merchant->last_login_at = now();
            $merchant->save();
        }
        
        return view('merchant.dashboard', [
            'merchant' => $merchant,
        ]);
    }
} 