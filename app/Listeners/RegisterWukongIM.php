<?php

namespace App\Listeners;

use App\Events\AfterRegister;
use App\Services\WukongIMService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;

class RegisterWukongIM implements ShouldQueue
{
    /**
     * 任务将被发送到的队列的名称
     *
     * @var string|null
     */
    public $queue = 'listeners';

    /**
     * @var WukongIMService
     */
    protected $wukongIMService;

    /**
     * Create the event listener.
     *
     * @param WukongIMService $wukongIMService
     * @return void
     */
    public function __construct(WukongIMService $wukongIMService)
    {
        $this->wukongIMService = $wukongIMService;
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\AfterRegister $event
     * @return void
     */
    public function handle(AfterRegister $event)
    {
        $user = $event->user;

        try {
            // 注册用户到悟空IM
            $registered = $this->wukongIMService->registerUser($user);

            if ($registered) {
                // 发送欢迎消息
                $welcomeMessage = "欢迎使用我们的服务！";
                $this->wukongIMService->sendSystemMessage($user, $welcomeMessage);
            } else {
                Log::error('注册用户到悟空IM失败', [
                    'user_id' => $user->id
                ]);
            }
        } catch (\Exception $e) {
            Log::error('处理用户注册到悟空IM事件异常', [
                'user_id' => $user->id,
                'error' => $e->getMessage()
            ]);
        }
    }
}
