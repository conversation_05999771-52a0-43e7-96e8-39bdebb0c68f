<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\AdvertisementResource;
use App\Http\Resources\CategoryResource;
use App\Models\Advertisement;
use App\Models\Category;
use App\Models\Common;
use App\Models\Community;
use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\SystemConfig;
use App\Services\CommonService;
use Carbon\Carbon;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    //社区食堂首页
    public function indexForSQ()
    {
        $sendingTimes = SystemConfig::getCacheConfigValue(SystemConfig::PlatformSQ, SystemConfig::PARAM_SENDING_TIME);
        $times = [];
        if ($sendingTimes) {
            foreach ($sendingTimes as $v) {
                $reserveTime = "";
                if ($v['reserve_start'] && $v['reserve_end']) {
                    $reserveTime = $v['reserve_start'] . "-" . $v['reserve_end'];
                } elseif ($v['reserve_start']) {
                    $reserveTime = $v['reserve_start'] . "之后";
                    if ($v['reserve_start'] > $v['sending_start']) {
                        $reserveTime = "昨天" . $reserveTime;
                    }
                } elseif ($v['reserve_end']) {
                    $reserveTime = $v['reserve_end'] . "之前";
                    if ($v['reserve_end'] > $v['sending_start']) {
                        $reserveTime = "昨天" . $reserveTime;
                    }
                }
                $times[] = ["meal" => $v["meal"], "reserve_time" => $reserveTime, "sending_time" => $v['sending_start'] . "-" . $v['sending_end']];
            }
        }
        return $this->success("success", [
            'description' => SystemConfig::getCacheConfigValue(SystemConfig::PlatformSQ, SystemConfig::PARAM_TECHNICAL_SUPPORT) ?: "",
            'sending_time' => $times,
        ]);
    }

    private function formatCategory($category)
    {
        return [
            'id' => $category->id,
            'pid' => $category->pid,
            'title' => $category->title,
            'sub_title' => $category->sub_title,
            'view_order' => $category->view_order,
            'pic' => $category->pic ? img_url($category->pic) : "",
            'created_at' => $category->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $category->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'zones' => [],
        ];
    }

    //跑腿端首页
    public function indexForPT()
    {
        $res = [
            "categories" => [],
            "goods_category_id" => 0,
            "banners" => [],
        ];
        $indexMap = [];

        $categories = Category::query()->where("pid", 0)->orderBy("view_order", 'desc')->get();

        if ($categories->count() > 0) {
            foreach ($categories as $category) {
                if ($category->title == "物品类型") {
                    $res["goods_category_id"] = $category->id;
                } else {
                    $res["categories"][] = $this->formatCategory($category);
                    switch ($category->title) {
                        case "帮我送":
                            $indexMap[O2oErrandOrder::TYPE_SEND] = count($res["categories"]) - 1;
                            break;
                        case "帮我取":
                            $indexMap[O2oErrandOrder::TYPE_TAKE] = count($res["categories"]) - 1;
                            break;
                        case "帮我买":
                            $indexMap[O2oErrandOrder::TYPE_BUY] = count($res["categories"]) - 1;
                            break;
                    }
                }
            }
        }

        $advertisements = Advertisement::query()
            ->where("status", Common::STATUS_UP)
            ->whereIn('type', [Advertisement::TYPE_BANNER, Advertisement::TYPE_ZONE_SEND, Advertisement::TYPE_ZONE_TAKE, Advertisement::TYPE_ZONE_BUY])
            ->orderBy("sort",)->get();

        if ($advertisements->count() > 0) {
            foreach ($advertisements as $ad) {
                switch ($ad->type) {
                    case Advertisement::TYPE_BANNER:
                        $res["banners"][] = new AdvertisementResource($ad);
                        break;
                    case Advertisement::TYPE_ZONE_SEND:
                    case Advertisement::TYPE_ZONE_TAKE:
                    case Advertisement::TYPE_ZONE_BUY:
                        if (isset($indexMap[$ad->type - 10])) {
                            $res["categories"][$indexMap[$ad->type - 10]]['zones'][] = new AdvertisementResource($ad);
                        }
                }
            }
        }
        return $this->success("success", $res);
    }

    public function getRiderCount(Request $request)
    {
//        $user = $request->user();
//        if (!$user) {
//            return $this->success("success", ["count" => 0, "text" => '所在区域暂未开通服务', 'status' => 0]);
//        } else {
//            if (!in_array($user->id, [1,2])) {
//                return $this->success("success", ["count" => 0, "text" => '所在区域暂未开通服务', 'status' => 0]);
//            }
//        }

        $lng = $request->lng;
        $lat = $request->lat;
        if (!($lat && $lng)) $this->errorResponse(500, "请确认是否打开位置权限");

        // 判断站点情况
        $point = ["lng" => $lng, "lat" => $lat];
        $site = app(CommonService::class)->getPointSite($point);
        if (!$site) {
            return $this->success("success", ["count" => 0, "text" => '所在区域暂未开通服务', 'status' => 0]);
        }

        $distance = 5;
        if (app()->environment() != 'production') {
            $distance = 5000;
        }
        $redisLocations = Location::getRiderFromRedis($lng, $lat, $distance);

        $riderIds = [];
        foreach ($redisLocations as $location) {
            $riderIds[] = $location[0];
        }

        // 判断骑手状态
        $count = 0;
        if ($riderIds) {
            $count = Rider::query()->whereIn("id", $riderIds)->where("status", Rider::STATUS_UP)->count();
        }
        $text = "";
        if ($count == 0) {
            $text = "附近暂无骑手";
        } else {
            $text = "附近有{$count}位骑手";
        }

        return $this->success("success", ["count" => $count, "text" => $text, 'status' => 1]);
    }
}
