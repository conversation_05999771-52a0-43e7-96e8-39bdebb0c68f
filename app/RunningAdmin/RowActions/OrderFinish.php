<?php


namespace App\RunningAdmin\RowActions;


use App\Jobs\DispatchFinishTask;
use App\Jobs\DispatchHandleNewRiderActivity;
use App\Jobs\DispatchHandleNewUserActivity;
use App\Models\O2oErrandOrder;
use Carbon\Carbon;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;
use Ixudra\Curl\Facades\Curl;

class OrderFinish extends RowAction
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 标题
     *
     * @return string
     */
    public function title()
    {
        return '完成';
    }

    /**
     * 设置确认弹窗信息，如果返回空值，则不会弹出弹窗
     *
     * 允许返回字符串或数组类型
     *
     * @return array|string|void
     */
    public function confirm()
    {
        return [
            // 确认弹窗 title
            "是否确认标记订单完成？",
            // 确认弹窗 content
            $this->row->order_no,
        ];
    }

    /**
     * 处理请求
     *
     * @param Request $request
     *
     * @return \Dcat\Admin\Actions\Response
     */
    public function handle(Request $request)
    {
        // 获取当前行ID
        $order = O2oErrandOrder::query()->with(['user', 'rider', 'rider.user'])->find($this->getKey());
        $order->order_status = O2oErrandOrder::STATUS_FINISH;
        $order->finish_time = Carbon::now();
        $order->save();

        dispatch(new DispatchFinishTask($order));
        dispatch(new DispatchHandleNewUserActivity($order->user, $order));
        dispatch(new DispatchHandleNewRiderActivity($order->rider->user, $order));

        return $this->response()->success("退款成功")->refresh();
    }


    /**
     * 设置要POST到接口的数据
     *
     * @return array
     */
    public function parameters()
    {
        return [
            // 发送当前行 username 字段数据到接口
            'order_no' => $this->row->order_no,
            // 把模型类名传递到接口
//            'model' => $this->model,
        ];
    }

}
