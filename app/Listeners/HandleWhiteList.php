<?php

namespace App\Listeners;

use App\Events\AfterRegister;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserWhiteList;
use App\Services\Amap\GaodeService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\DB;

class HandleWhiteList implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle($event)
    {
        // 根据手机号码查询是否在白名单中
        $user = $event->user;

        $whiteUser = UserWhiteList::query()->where('tel', $user->phone)->first();

        if ($whiteUser) {
            // 通过高德API获取经纬度
            $gaodeService = app(GaodeService::class);
            $geoResult = $gaodeService->geoQuery($whiteUser->address_detail, $whiteUser->city);

            // 更新用户数据
            DB::transaction(function () use ($user, $whiteUser, $geoResult) {
                $user->channel = User::CHANNEL_WHITE_LIST;
                $user->save();

                $geoResult = $geoResult['geocodes'][0] ?? [];

                $location = explode(',', $geoResult['location'] ?? "");
                // 处理收货地址
                UserAddress::create([
                    'user_id' => $user->id,
                    'name' => $whiteUser->name,
                    'tel' => $whiteUser->tel,
                    'province' => $whiteUser->province,
                    'city' => $whiteUser->city,
                    'county' => $whiteUser->county,
                    'address_detail' => $whiteUser->address_detail,
                    'is_default' => true,
                    'area_code' => $geoResult['adcode'] ?? "",
                    'longitude' => $location[0] ?? 0,
                    'latitude' => $location[1] ?? 0,
                    'is_white_list' => true,
                ]);

                // 激活白名单
                $whiteUser->active = true;
                $whiteUser->save();
            });

        }

    }
}
