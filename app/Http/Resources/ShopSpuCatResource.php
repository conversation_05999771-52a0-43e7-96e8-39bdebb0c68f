<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class ShopSpuCatResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'shop_id' => $this->shop_id,
            'pid' => $this->pid,
            'name' => $this->name,
            'cover' => $this->cover ? img_url($this->cover) : "",
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'children' => ShopSpuCatResource::collection($this->children),
        ];
    }
}
