<?php

namespace App\Http\Controllers\Api;

use App\Models\PhoneProtectLog;
use App\Models\PhoneProtectPool;
use App\Models\SystemConfig;
use App\Services\PhoneNumberProtectService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PhoneProtectController extends Controller
{
    public function index(Request $request)
    {
        $phone = $request->get("phone");
        if (empty($phone)) {
            return $this->errorResponse(500, "请提供手机号");
        }
        // 检查手机号是否包含#字符
        if (strpos($phone, '#') !== false) {
            // 按#分隔手机号
            $phones = explode('#', $phone);
            // Convert extension to integer
            $phones[1] = intval($phones[1]);
            return $this->success("SUCCESS", ["phone_x" => $phones[0], 'extension' => $phones[1]]);
        }

        // 15785408018_042
        // Check if phone number contains underscore (privacy number format)
        if (strpos($phone, '_') !== false) {
            // Split phone number and extension
            $parts = explode('_', $phone);
            // Convert extension to integer
            $parts[1] = intval($parts[1]);
            return $this->success("SUCCESS", ["phone_x" => $parts[0], 'extension' => $parts[1], 'full' => $parts[0] . ','. $parts[1]]);
        }

        
        if (!preg_match('/^1\d{10}$/', $phone)) {
            return $this->errorResponse(500, '手机号不正确');
        }

        // 获取过期时间
        $minute = SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_PHONE_PROTECT_EXPIRE_TIME);
        $minute = intval($minute);
        $minute = $minute > 1 ? $minute : 2;

        try {
            $service = new PhoneNumberProtectService();
            // 获取最新的绑定记录
            $log = PhoneProtectLog::query()->where("phone_a", $phone)->where('expire_time', '>', Carbon::now())->orderByDesc("expire_time")->first(); // 没有过期
            if ($log) {
                if ($log->expire_time <= Carbon::now()->addMinutes()) { // 过期小于等于1分钟，更新过期时间
                    $updateRes = $service->updateSubscription($log->subsid, $log->phone_x, "updateExpire", Carbon::now()->addMinutes($minute)->format(Carbon::DEFAULT_TO_STRING_FORMAT));
                }
            }else{
                // ==== 不存在或过期，重新分配 ====
                // 1.获取可用的隐私号，没有则购买一个
                $pool = PhoneProtectPool::query()
                    ->whereHas('logs', function ($query) {
                        $query->where('expire_time', '>', Carbon::now());
                    }, '<', 200)
                    ->where("status", PhoneProtectPool::STATUS_ACTIVE)
                    ->first();
                if (!$pool) {
                    throw new \Exception("暂无可用的隐私号");
                    //购买不支持，需要提交购号申请
                    $buyRes = $service->buySecretNo(1, "宁波");
                    $pool = PhoneProtectPool::query()->create([
                        'phone' => "TODO",
                        'purchase_time' => Carbon::now(),
                        'region' => 982,
                        "vendor" => 0,
                        'status' => PhoneProtectPool::STATUS_ACTIVE,
                    ]);
                }

                // 2.随机分机号，范围：[10,900]
                $list = PhoneProtectLog::query()->where("phone_x", $pool->phone)->where('expire_time', '>', Carbon::now())->pluck("extension")->toArray();
                for ($i = 0; ; $i++) {
                    $extension = mt_rand(10, 900);
                    if(!in_array($extension, $list)) break;
                }

                // 3.添加AXN分机号码的绑定关系
                $expireTime = Carbon::now()->addMinutes($minute)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                $bindRes = $service->bindAxnExtension($phone, $pool->phone, $extension, $expireTime);
                $log = PhoneProtectLog::query()->create([
                    'phone_a' => $phone,
                    'phone_x' => $pool->phone,
                    'extension' => $extension,
                    "subsid" => $bindRes["body"]["SecretBindDTO"]["SubsId"] ?? "",
                    'expire_time' => $expireTime,
                ]);
            }
        } catch (\Exception $e) {
            Log::error("获取隐私号失败", ["message" => $e->getMessage() . " " . $e->getFile() . " " . $e->getLine()]);
            return $this->success("SUCCESS", ["phone_x" => $phone, 'extension' => 0, 'full' => $phone]);
        }
        return $this->success("SUCCESS", ["phone_x" => $log->phone_x, 'extension' => $log->extension]);
    }
}
