<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\TiXianRequest;
use App\Models\RiderAccountFlow;
use App\Models\RiderTxApply;
use Illuminate\Support\Facades\DB;

class TiXianController extends Controller
{
    public function store(TiXianRequest $request)
    {
        $input = $request->only(['amount', 'account_type', 'bank_account', 'real_name']);

        $amount = yuantofen($input['amount']);

        $user = $request->user();

        $account = $user->riderAccount;

        if ($account->amount < $amount) $this->errorResponse(500, '余额不足');

        DB::beginTransaction();
        try {
            // 创建提现申请记录
            $apply = new RiderTxApply();
            $apply->user_id = $user->id;
            $apply->amount = $amount;
            $apply->status = 0;
            $apply->trans_status = 0;
            $apply->remark = '';
            $apply->trans_message = '';
            $apply->account_type = $input['account_type'];
            $apply->bank_name = '支付宝';
            $apply->bank_account = $input['bank_account'];
            $apply->real_name = $input['real_name'];
            $apply->save();

            $account->decrease($amount, RiderAccountFlow::BUSINESS_TYPE_CHECK_OUT, $apply->order_no, '余额提现');

            $user->alipay_account = $input['bank_account'];
            $user->save();

            DB::commit();
            # todo notify

            return $this->success('success', []);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorResponse(500, $e->getMessage());
        }
    }
}
