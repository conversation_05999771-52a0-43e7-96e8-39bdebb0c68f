<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SiteRegion extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'site_id',
        'region_name',
        'points'
    ];

    protected $casts = ['points' => 'json'];

    public function site()
    {
        return $this->belongsTo(Site::class, 'site_id');
    }
}
