<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>骑手路径规划示例</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            line-height: 1.5;
            color: #333;
            background-color: #f5f5f5;
            padding-bottom: 20px;
        }
        .header {
            background-color: #1890ff;
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 15px;
        }
        .stats-bar {
            display: flex;
            justify-content: space-between;
            background-color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .stat-item {
            text-align: center;
            flex: 1;
        }
        .stat-label {
            font-size: 14px;
            color: #888;
        }
        .stat-value {
            font-size: 18px;
            font-weight: bold;
            color: #1890ff;
        }
        .map-container {
            height: 400px;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .order-list {
            background-color: white;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .order-section-title {
            font-size: 18px;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .order-item {
            margin-bottom: 15px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        .order-item:last-child {
            margin-bottom: 0;
            padding-bottom: 0;
            border-bottom: none;
        }
        .order-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .order-no {
            font-weight: bold;
        }
        .order-status {
            color: #1890ff;
        }
        .order-type {
            background-color: #f0f0f0;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }
        .order-detail {
            display: flex;
            flex-wrap: wrap;
            gap: 15px;
        }
        .pickup-info, .delivery-info {
            flex: 1;
            min-width: 250px;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 4px;
        }
        .delivery-info {
            background-color: #f0f7ff;
        }
        .pickup-info h4, .delivery-info h4 {
            color: #888;
            margin-bottom: 5px;
        }
        .loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255,255,255,0.8);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            display: none;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #1890ff;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error-message {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: #ff4d4f;
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1001;
            display: none;
        }
        .refresh-button {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background-color: #1890ff;
            color: white;
            border: none;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
            z-index: 100;
        }
        .refresh-icon {
            width: 24px;
            height: 24px;
        }
        .no-orders {
            text-align: center;
            padding: 20px;
            color: #888;
        }
        .way-point-marker {
            width: 24px;
            height: 24px;
            background-color: #ff8800;
            border-radius: 50%;
            color: white;
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 12px;
            font-weight: bold;
        }
        /* 导航路径详情面板样式 */
        #path-result-panel {
            position: absolute;
            top: 0;
            right: 0;
            width: 280px;
            height: 100%;
            background-color: white;
            overflow-y: auto;
            box-shadow: -2px 0 4px rgba(0,0,0,0.1);
            z-index: 10;
            display: none; /* 默认隐藏，可以通过按钮控制显示 */
        }
        .show-panel-button {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 11;
            background-color: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 5px 10px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>骑手路径规划</h1>
    </div>
    
    <div class="container">
        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-label">总距离</div>
                <div class="stat-value" id="total-distance">0 公里</div>
            </div>
            <div class="stat-item">
                <div class="stat-label">预计用时</div>
                <div class="stat-value" id="estimated-time">0分钟</div>
            </div>
        </div>
        
        <div class="map-container" id="map-container">
            <!-- 高德地图将在此处渲染 -->
            <button class="show-panel-button" onclick="togglePanel()">显示详情</button>
            <div id="path-result-panel"></div>
        </div>
        
        <div class="order-list">
            <h2 class="order-section-title">配送订单</h2>
            <div id="order-list">
                <!-- 订单列表将在此处渲染 -->
                <div class="no-orders">加载中...</div>
            </div>
        </div>
    </div>
    
    <button class="refresh-button" id="refresh-button">
        <svg class="refresh-icon" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4C7.58 4 4.01 7.58 4.01 12C4.01 16.42 7.58 20 12 20C15.73 20 18.84 17.45 19.73 14H17.65C16.83 16.33 14.61 18 12 18C8.69 18 6 15.31 6 12C6 8.69 8.69 6 12 6C13.66 6 15.14 6.69 16.22 7.78L13 11H20V4L17.65 6.35Z" fill="white"/>
        </svg>
    </button>
    
    <div class="loading" id="loading">
        <div class="loading-spinner"></div>
    </div>
    
    <div class="error-message" id="error-message"></div>
    
    <!-- 引入高德地图 SDK -->
    <script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key=您的高德地图KEY"></script>
    
    <!-- 引入我们的路径规划脚本 -->
    <script src="rider_route_example.js"></script>
    
    <script>
        // 控制导航详情面板显示/隐藏
        function togglePanel() {
            const panel = document.getElementById('path-result-panel');
            if (panel.style.display === 'none' || panel.style.display === '') {
                panel.style.display = 'block';
                document.querySelector('.show-panel-button').innerText = '隐藏详情';
            } else {
                panel.style.display = 'none';
                document.querySelector('.show-panel-button').innerText = '显示详情';
            }
        }
    </script>
</body>
</html> 