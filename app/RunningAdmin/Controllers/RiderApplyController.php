<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Actions\Grid\RiderApplyReject;
use App\RunningAdmin\Repositories\RiderApply;
use App\RunningAdmin\Repositories\UserIdCard;
use App\RunningAdmin\RowActions\RiderApplyAgree;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;

class RiderApplyController extends AdminController
{
    protected $title = '骑手申请';

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RiderApply(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->model()->with(['user', 'user.rider', 'user.idcard'])->orderBy('id', 'desc');
            $grid->column('id')->sortable();
            $grid->column('user.id', '用户ID');
            $grid->column('user.idcard.name', '姓名');
            $grid->column('user.phone', '手机号');
            $grid->column('remark');
            $grid->column('idcard', '身份证件')->modal(function ($modal) use ($grid) {
                $modal->title('身份证');
                if ($this->user->idcard) {
                    $show = Show::make($this->user->idcard->id, new UserIdCard(), function (Show $show) {
                        $show->disableDeleteButton();
                        $show->disableEditButton();
                        $show->disableListButton();
                        $show->field('id_card_image', '身份证正面')->image();
                        $show->field('id_card_image_over', '身份证反面')->image();
                        $show->field('name');
                        $show->field('nation', '名族');
                        $show->field('address', '地址');
                        $show->field('id_card', '身份证号');
                        $show->field('issuing_authority', '发证机构');
                        $show->field('sex', '性别');
                        $show->field('birth', '生日');
                        $show->field('issuing_date', '发证日期');
                        $show->field('expiry_date', '有效期');
                    });
                    $card = new Card(null, $show);
                    return "<div style='padding:10px 10px 0'>$card</div>";
                }
                return "未实名认证";
            });
            $grid->column('user.face_certified', '人脸认证')->bool();
            $grid->column('status')->using([0 => '未处理', 1 => '已处理', 2 => '已拒绝'])
                ->dot(
                    [
                        0 => 'warning',
                        1 => 'success',
                        2 => 'danger',
                    ],
                    'primary' // 第二个参数为默认值
                );
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->status == 0) {
                    $actions->append(new RiderApplyAgree());
                    $actions->append(new RiderApplyReject());
                }

            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->equal('user.phone', '手机号')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RiderApply(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('status');
            $show->field('remark');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RiderApply(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('status');
            $form->text('remark');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
