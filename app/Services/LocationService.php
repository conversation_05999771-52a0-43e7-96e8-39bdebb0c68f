<?php


namespace App\Services;


use App\Services\Amap\GaodeService;

class LocationService
{
    /**
     * @param $ip
     * @return array|false|string[]
     * @throws \Exception
     */
    public function getLocationFromIp($ip): array|bool
    {
        $ipQueryRes = (new MapService())->ipQuery($ip);

        if ($ipQueryRes['status'] != 0) return [];

        if (!isset($ipQueryRes['result']['location'])) return [];

        if (($ipQueryRes['result']['location'])) {
            return [$ipQueryRes['result']['location']['lng'], $ipQueryRes['result']['location']['lat']];
        }

        return [];
    }
}
