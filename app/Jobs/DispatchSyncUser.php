<?php

namespace App\Jobs;

use App\Models\SystemConfig;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;

class DispatchSyncUser implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $phone;

    private $password;

    private $platform;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($phone, $password, $platform)
    {
        $this->phone = $phone;
        $this->password = $password;
        $this->platform = $platform;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $url = config('app.sync_url');

        if ($url) {
            $params = [
                'phone' => $this->phone,
                'password' => $this->password,
                'platform' => $this->platform == SystemConfig::PlatformSQ ? SystemConfig::PlatformPT : SystemConfig::PlatformSQ
            ];
            $res = Curl::to($url)->withData($params)->asJson(true)->post();
            if ($res['code'] == 0) {
                // 成功
            } else {
                Log::error(json_encode($res));
                throw new \Exception("用户创建失败");
            }
        }
    }
}
