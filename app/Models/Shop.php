<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;

class Shop extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'community_id',
        'name',
        'promotion_info',
        'logo',
        'cover',
        'tel',
        'province',
        'city',
        'district',
        'address_detail',
        'lng',
        "lat",
        'sort',
        'status'
    ];

    const GEO_KEY = 'shop';

    protected $appends = ["regions"];

    public static function GetShopFromRedis($lng, $lat, $distance)
    {
        return Redis::georadius(self::GEO_KEY, $lng, $lat, $distance, 'km', 'WITHDIST', 'ASC');
    }

    public static function PushLocation()
    {
        $shops = Shop::query()->where('status', Common::STATUS_UP)->get();
        foreach ($shops as $shop) {
            Redis::geoadd(self::GEO_KEY, $shop->lng, $shop->lat, $shop->id);
        }
    }

    public function community()
    {
        return $this->belongsTo(Community::class, "community_id");
    }

    public function cats()
    {
        return $this->hasMany(ShopSpuCat::class, "shop_id");
    }

    public function spus()
    {
        return $this->hasMany(ShopSpu::class, "shop_id");
    }

    public function getRegionsAttribute()
    {
        $res = Region::getRegions($this->province, $this->city, $this->district);
        return [$res["province"] ?: "", $res["city"] ?: "", $res["district"] ?: ""];
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::saved(function ($shop) {
            if ($shop->status == Common::STATUS_UP) {
                Redis::geoadd(self::GEO_KEY, $shop->lng, $shop->lat, $shop->id);
            } else {
                Redis::zrem(self::GEO_KEY, $shop->id);
            }
        });
        static::deleted(function ($model) {
            Redis::zrem(self::GEO_KEY, $model->id);
        });
    }

}
