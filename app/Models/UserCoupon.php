<?php

namespace App\Models;

use Carbon\Carbon;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserCoupon extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const STATUS_WAITING_USE = 1;
    const STATUS_WAITING_EFFECT = 2;
    const STATUS_USED = 3;
    const STATUS_EXPIRED = 4;
    const StatusMap = [
        self::STATUS_WAITING_USE => "可使用",
        self::STATUS_WAITING_EFFECT => "待生效",
        self::STATUS_USED => "已使用",
        self::STATUS_EXPIRED => "已过期",
    ];

    protected $dates = ['start_time', 'end_time'];

    protected $appends = ['status'];

    protected $fillable = [
        'coupon_id', 'user_id', 'start_time', "end_time", 'order_id'
    ];

    public function getStatusAttribute()
    {
        if ($this->order_id > 0) {
            $status = self::STATUS_USED;
        } else {
            if ($this->start_time > Carbon::now()) {
                $status = self::STATUS_WAITING_EFFECT;
            } else {
                if ($this->end_time > Carbon::now()) {
                    $status = self::STATUS_WAITING_USE;
                } else {
                    $status = self::STATUS_EXPIRED;
                }
            }
        }
        return $status;
    }

    public function coupon()
    {
        return $this->belongsTo(Coupon::class, "coupon_id");
    }
}
