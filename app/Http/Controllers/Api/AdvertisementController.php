<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\AdvertisementResource;
use App\Models\Common;
use App\Models\Advertisement;
use Carbon\Carbon;
use Illuminate\Http\Request;

class AdvertisementController extends Controller
{
    public function index(Request $request)
    {
        $now = Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT);
        $data = Advertisement::query()
            ->where("status", Common::STATUS_UP)
            ->where('type', $request->type ?? Advertisement::TYPE_BANNER)
            ->where(function ($query) use ($now) {
                $query->whereNull('start_time')
                    ->orWhere('start_time', '')
                    ->orWhere('start_time', '<=', $now);
            })
            ->where(function ($query) use ($now) {
                $query->whereNull('end_time')
                    ->orWhere('end_time', '')
                    ->orWhere('end_time', '>', $now);
            })
            ->orderBy("sort", "asc")
            ->get();
        return $this->success('success', AdvertisementResource::collection($data));
    }
}
