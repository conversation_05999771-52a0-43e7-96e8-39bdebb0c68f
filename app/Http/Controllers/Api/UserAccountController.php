<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;

class UserAccountController extends Controller
{
    // 充值页面

    public function index(Request $request)
    {
        $user = $request->user();
        $account = $user->userAccount;

        $result = [
            'amount' => fentoyuan($account->amount),
            'recharge_amount' => [100, 200, 500],
            'recharge_agreement' => route('article', 3),
        ];

        return $this->success('success', $result);
    }
}
