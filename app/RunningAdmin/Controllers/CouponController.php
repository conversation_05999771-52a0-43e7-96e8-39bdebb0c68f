<?php

namespace App\RunningAdmin\Controllers;

use App\Models\UserCoupon;
use App\RunningAdmin\Repositories\Coupon;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class CouponController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Coupon(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->fixColumns(2);

            $grid->column('id');
            $grid->column('title');
            $grid->column('type')->using(\App\Models\Coupon::TypeMap)->label();
            $grid->column('discount_price')->display(function ($discountPrice) {
                $txt = $discountPrice;
                switch ($this->type) {
                    case \App\Models\Coupon::TYPE_MANJIAN:
                        $txt .= "元";
                        break;
                    case \App\Models\Coupon::TYPE_ZHEKOU:
                        $txt .= "%";
                        break;
                }
                return $txt;
            });
            $grid->column('start_price');
            $grid->column('validity')->display(function ($validity) {
                $txt = "未知";
                switch ($validity['type']) {
                    case \App\Models\Coupon::VALIDITY_TYPE_DAY:
                        $txt = "领取后" . $validity["day"] . "天有效";
                        break;
                    case \App\Models\Coupon::VALIDITY_TYPE_RANGE:
                        $txt = $validity['start'] . " ~ " . $validity['end'];
                        break;
                }
                return $txt;
            });
            $grid->column('stock')->sortable();
            $grid->column('sales')->sortable();
            $grid->column('status')->using(\App\Models\Common::StatusMap)->label([
                \App\Models\Common::STATUS_INIT => 'info',
                \App\Models\Common::STATUS_UP => 'success',
                \App\Models\Common::STATUS_DOWN => 'danger',
            ]);
            $grid->column('sort')->sortable();
            $grid->column('created_at');
            $grid->column('updated_at');
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('title')->width(3);
                $filter->in('type')->multipleSelect(\App\Models\Coupon::TypeMap)->width(3);
                $filter->in('status')->multipleSelect(\App\Models\Common::StatusMap)->width(3);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Coupon(), function (Form $form) {
            $disabled = false;
            if ($id = $form->getKey()) {
                $disabled = UserCoupon::query()->where("coupon_id", $id)->exists();
            }
            $form->display('id');
            $form->text('title')->required();
            $form->select('type')->options(\App\Models\Coupon::TypeMap)->default(\App\Models\Coupon::TYPE_MANJIAN)->required()->readOnly($disabled);
            $form->decimal('discount_price')->placeholder("输入 优惠金额（20即20元）/折扣（80即8折）")->required()->readOnly($disabled);
            $form->decimal('start_price')->required()->readOnly($disabled);
            $form->embeds('validity', function ($form) {
                $form->select('type', "类型")->options(\App\Models\Coupon::ValidityTypeMap)->required();
                $form->number('day', "天数")->attribute('min', 1)->rules('required_if:validity.type,1');
                $form->datetimeRange("start", "end", '时间')->rules('required_if:validity.type,2');
            });
            $form->editor("rules")->required();
            $form->number('stock')->default(0)->attribute('min', 0)->required();
            $form->number('sales')->default(0)->attribute('min', 0);
            $form->select('status')->options(\App\Models\Common::StatusMap)->default(\App\Models\Common::STATUS_INIT)->required();
            $form->number('sort')->default(0)->attribute('min', 0);
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
