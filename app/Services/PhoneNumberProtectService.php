<?php

namespace App\Services;

use AlibabaCloud\SDK\Dyplsapi\V20170525\Dyplsapi;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\BindAxnExtensionRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\BuySecretNoRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\QueryCallStatusRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\UnbindSubscriptionRequest;
use AlibabaCloud\SDK\Dyplsapi\V20170525\Models\UpdateSubscriptionRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use Carbon\Carbon;
use Darabonba\OpenApi\Models\Config;
use Illuminate\Support\Facades\Log;

class PhoneNumberProtectService
{
    protected Dyplsapi $client;
    protected RuntimeOptions $runtime;
    protected string $poolKey;

    public function __construct()
    {
        $config = new Config([
            // 您的AccessKey ID
            "accessKeyId" => "LTAI5t8N54BPM3JNTK1oTa43",
            // 您的AccessKey Secret
            "accessKeySecret" => "******************************"
        ]);
        // 访问的域名
        $config->endpoint = "dyplsapi.aliyuncs.com";
        $this->client = new Dyplsapi($config);

        $this->poolKey = "FC100000181980039";

        $this->runtime = new RuntimeOptions();
        $this->runtime->maxIdleConns = 3;
        $this->runtime->connectTimeout = 10000;
        $this->runtime->readTimeout = 10000;
    }

    /**
     * 购买号码
     * @param int $specId 号码类型。取值：1：虚商号码，即170或171。2：运营商号码。3：95号码
     * @param string $city 指定号码的归属地。当前仅支持设置归属地为中国大陆地区。95号码不区分地区，如果购买95号码，则该参数应指定为全国通用。
     * @return array
     * @throws \Exception
     */
    public function buySecretNo(int $specId, string $city)
    {
        $request = new BuySecretNoRequest();
        $request->poolKey = $this->poolKey;
        $request->specId = $specId;
        $request->city = $city;
        $res = $this->client->buySecretNoWithOptions($request, $this->runtime);
        if (!Utils::equalString($res->body->code, "OK")) {
            throw new \Exception(sprintf("%s-%s", $res->body->code, $res->body->message));
        }
        return $res->toMap();
    }

    /**
     * 添加AXN分机号码的绑定关系
     * @param string $phoneA AXN中的A号码。 A号码可设置为手机号码或固定电话，固定电话需要加区号，区号和号码中间不需要加连字符。
     * @param string $phoneX AXN中的X号码
     * @param string $extension X号码的分机号码，1~3位。
     * @param string $expiration 绑定关系的过期时间，必须晚于当前时间 1 分钟以上。2019-09-05 12:00:00
     * @return array
     * @throws \Exception
     */
    public function bindAxnExtension(string $phoneA, string $phoneX, string $extension, string $expiration)
    {
        $request = new BindAxnExtensionRequest();
        $request->poolKey = $this->poolKey;
        $request->phoneNoA = $phoneA;
        $request->phoneNoX = $phoneX;
        $request->extension = $extension;
        $request->expiration = $expiration; //绑定关系的过期时间
        $res = $this->client->bindAxnExtensionWithOptions($request, $this->runtime);
        if (!Utils::equalString($res->body->code, "OK")) {
            Log::error("绑定AXN分机号码失败", ["requestId" => $res->body->requestId, "message" => sprintf("%s-%s", $res->body->code, $res->body->message)]);
            throw new \Exception(sprintf("%s-%s", $res->body->code, $res->body->message));
        }
        return $res->toMap();
    }

    /**
     * 修改绑定关系
     * @param string $subsid 绑定关系ID
     * @param string $phoneX 号码绑定关系中的X号码。
     * @param string $operateType 修改绑定关系的操作
     * @param string $operateData 修改绑定关系的数据
     * @return array
     * @throws \Exception
     */
    public function updateSubscription(string $subsid, string $phoneX, string $operateType, string $operateData)
    {
        $request = new UpdateSubscriptionRequest();
        $request->poolKey = $this->poolKey;
        $request->phoneNoX = $phoneX;
        $request->subsId = $subsid;
        $request->operateType = $operateType;
        switch ($operateType) {
            case "updateNoA":
                $request->phoneNoA = $operateData;
                break;
            case "updateNoB":
                $request->phoneNoB = $operateData;
                break;
            case "updateExpire":
                $request->expiration = $operateData;
                break;
            case "updateAxgGroup":
                $request->groupId = $operateData;
                break;
            case "updateCallRestrict":
                $request->callRestrict = $operateData;
                break;
            default:
                throw new \Exception("修改绑定关系的操作不知处");
        }
        $res = $this->client->updateSubscriptionWithOptions($request, $this->runtime);
        if (!Utils::equalString($res->body->code, "OK")) {
            Log::error("修改绑定关系失败", ["message" => sprintf("%s-%s", $res->body->code, $res->body->message)]);
            throw new \Exception(sprintf("%s-%s", $res->body->code, $res->body->message));
        }
        return $res->toMap();
    }

    /**
     * 查询呼叫状态
     * @param string $subsid 绑定关系ID
     * @return array
     * @throws \Exception
     */
    public function queryCallStatus(string $subsid)
    {
        $request = new QueryCallStatusRequest();
        $request->poolKey = $this->poolKey;
        $request->subsId = $subsid;
        $res = $this->client->queryCallStatusWithOptions($request, $this->runtime);
        if (!Utils::equalString($res->body->code, "OK")) {
            Log::error("查询呼叫状态失败", ["message" => sprintf("%s-%s", $res->body->code, $res->body->message)]);
            throw new \Exception(sprintf("%s-%s", $res->body->code, $res->body->message));
        }
        return $res->toMap();
    }

    /**
     * 解除号码的绑定关系
     * @param string $subsid 绑定关系ID
     * @param string $secretNo 隐私号码
     * @return array
     * @throws \Exception
     */
    public function unbindSubscription(string $subsid, string $secretNo)
    {
        $request = new UnbindSubscriptionRequest();
        $request->poolKey = $this->poolKey;
        $request->subsId = $subsid;
        $request->secretNo = $secretNo;
        $res = $this->client->unbindSubscriptionWithOptions($request, $this->runtime);
        if (!Utils::equalString($res->body->code, "OK")) {
            Log::error("解除号码的绑定关系失败", ["message" => sprintf("%s-%s", $res->body->code, $res->body->message)]);
            throw new \Exception(sprintf("%s-%s", $res->body->code, $res->body->message));
        }
        return $res->toMap();
    }
}
