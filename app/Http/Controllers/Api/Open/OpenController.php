<?php

namespace App\Http\Controllers\Api\Open;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller as BaseController;
use Symfony\Component\HttpKernel\Exception\HttpException;

class OpenController extends BaseController
{
    /**
     * appKey
     * @var string
     */
    protected $appKey;

    /**
     * 应用名称
     * @var string
     */
    protected $appName;


    protected $scene = 'openapi';


    protected int $pageSize = 20;


    public function __construct()
    {
        $this->initHeader();
    }

    private function initHeader()
    {
        $this->appKey = request()->header('app-key');
    }

    public function errorResponse($statusCode, $message = null, $code = 0)
    {
        throw new HttpException($statusCode, $message, null, [], $code);
    }

    public function success($message, $data)
    {
        $returnData = [
            'message' => $message,
            'data' => $data,
            'code' => 0
        ];
        return response()->json($returnData);
    }


}
