<?php


namespace App\Services;


use App\Events\AfterRegister;
use App\Jobs\DispatchHandleNewRiderActivity;
use App\Jobs\DispatchHandleNewUserActivity;
use App\Jobs\DispatchSyncUser;
use App\Models\Common;
use App\Models\Coupon;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderApply;
use App\Models\RiderProfile;
use App\Models\SystemConfig;
use App\Models\User;
use App\Models\UserAccount;
use App\Models\UserCoupon;
use App\Models\UserProfile;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class UserService
{
    /**
     * 判断是否完成骑手实名认证
     * @param $userId
     * @return bool
     */
    public function checkRiderCert($userId)
    {
        return true;
    }


    public function registerUser($phone, $password, $inviteCode, $platform, $mode = 1)
    {
        if (!$password) {
            $password = "123456";
        }
        $user = User::create([
            'phone' => $phone,
            'name' => '',
            'nickname' => "用户" . substr($phone, '-4', '4'),
            'level' => User::NORMAL_LEVEL,
            'password' => $password ? Hash::make($password) : '',
            'channel' => '',
            'scene' => '',
            'ip' => request()->getClientIp(),
            'recommend_code' => User::GenInviteCode(),
            'parent_id' => User::ParseRecommendCode($inviteCode),
            'active_shequ' => $platform == SystemConfig::PlatformSQ,
            'active_paotui' => $platform == SystemConfig::PlatformPT,
            'avatar' => 'https://storage.fengqishi.com.cn/resource/user_default_logo.png',
        ]);

        if (!$user->profile) {
            // 创建用户基本信息
            UserProfile::create(['user_id' => $user->id,]);
        }

        if (!$user->userAccount) {
            // 创建用户账户
            UserAccount::create(['user_id' => $user->id, 'amount' => 0]);
        }

        if ($user->parent_id > 0) {
            dispatch(new DispatchHandleNewUserActivity($user, null));
        }

        // if ($mode == 1) {
        //     dispatch(new DispatchSyncUser($phone, $password, $platform));
        // }

        // todo 新人优惠券
        $newCoupon = SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_NEW_USER_COUPON);
        if ($newCoupon > 0) {
            $this->sendCoupon($user->id, $newCoupon);
        }
        event(new AfterRegister($user));

        return $user;
    }

    public function sendCoupon($userId, $couponId)
    {
        $coupon = Coupon::query()->findOrFail($couponId);
        if ($coupon->status != Common::STATUS_UP) {
            throw new \Exception('该优惠券未上架～');
        }
        if ($coupon->validity["type"] == Coupon::VALIDITY_TYPE_RANGE && $coupon->validity["end"] <= Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT)) {
            throw new \Exception('该优惠券已过期～');
        }
        if ($coupon->stock < 1) {
            throw new \Exception('该优惠券库存不足～');
        }
        try {
            if ($coupon->validity['type'] == Coupon::VALIDITY_TYPE_RANGE) {
                $start = $coupon->validity['start'];
                $end = $coupon->validity['end'];
            } else {
                $start = Carbon::now();
                $end = Carbon::now()->addDays($coupon->validity["day"]);
            }

            DB::beginTransaction();
            //创建领取记录、增加销量、较少库存
            $rowsAffected = Coupon::query()->where("id", $coupon->id)->where("stock", ">=", 1)
                ->update(["stock" => DB::raw("stock-1"), "sales" => DB::raw("sales+1")]);
            if ($rowsAffected <= 0) {
                throw new \Exception("该优惠券库存不足～");
            }
            UserCoupon::query()->create([
                'coupon_id' => $coupon->id,
                'user_id' => $userId,
                'start_time' => $start,
                'end_time' => $end,
            ]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error($e->getMessage());
        }


    }

    public function upToRider($id)
    {
        DB::beginTransaction();
        try {
            $apply = RiderApply::query()->with(['user', 'user.idcard'])->find($id);
            $apply->status = 1;
            $apply->save();
            // 创建骑手信息
            $rider = Rider::query()->where('user_id', $apply->user_id)->with('user')->first();
            $user = $apply->user;
            if (!$rider) {
                $rider = new Rider();
                $rider->user_id = $apply->user_id;
                $rider->name = $apply->user->idcard->name;
                $rider->status = 0;
                $rider->phone = $apply->user->phone;
                $rider->avatar = 'https://storage.fengqishi.com.cn/resource/rider_avatar.png';
                $rider->level = 1;
                $rider->verified = 2;
                $rider->health_status = 0;
                $rider->transport_status = 0;
                $rider->id_card_certified = 1;
                $rider->face_certified = 1;
                $rider->save();

                // 初始化骑手信息
                $profile = new RiderProfile();
                $profile->user_id = $rider->user_id;
                $profile->save();

                $account = new RiderAccount();
                $account->amount = 0;
                $account->account_type = RiderAccount::Amount;
                $account->user_id = $rider->user_id;
                $account->save();

                $account = new RiderAccount();
                $account->amount = 0;
                $account->account_type = RiderAccount::Bzj;
                $account->user_id = $rider->user_id;
                $account->save();

                dispatch(new DispatchHandleNewRiderActivity($user, null));
            }

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
