<?php

namespace App\Http\Controllers\Merchant;

use App\Http\Controllers\Controller;
use App\Models\O2oErrandOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class OrderController extends Controller
{
    /**
     * 创建一个新的控制器实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:merchant');
    }

    /**
     * 显示商家订单列表
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $merchant = Auth::guard('merchant')->user();
        $status = $request->input('status');
        
        $query = O2oErrandOrder::where('user_id', $merchant->user_id);
        
        if ($status) {
            $query->where('order_status', $status);
        }
        
        $orders = $query->orderBy('created_at', 'desc')
                        ->paginate(15);

        return view('merchant.orders', [
            'orders' => $orders,
            'currentStatus' => $status,
        ]);
    }

    /**
     * 显示订单详情
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $merchant = Auth::guard('merchant')->user();
        $order = O2oErrandOrder::where('user_id', $merchant->user_id)
                               ->findOrFail($id);
        
        return view('merchant.order-detail', [
            'order' => $order,
        ]);
    }
} 