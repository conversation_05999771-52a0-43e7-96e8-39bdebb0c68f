<?php

namespace App\Http\Controllers\Api;

use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use Carbon\Carbon;
use Illuminate\Http\Request;

class RiderAccountFlowController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();
        $flowType = $request->flow_type;
        switch ($flowType) {
            case 1:
                // 余额
                $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 1)->first();
                break;
            case 2:
                // 保证金
                $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 2)->first();
                break;
            default:
                // 余额
                $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 1)->first();
        }
        $query = RiderAccountFlow::query();
        if ($account) {
            $query->where('user_account_id', $account->id);
        }
        if ($request->type) {
            $query->where('type', $request->type);
        }
        if ($request->bill_month) {
            $start = Carbon::parse($request->bill_nonth)->startOfMonth();
        } else {
            $start = Carbon::now()->startOfMonth();
        }

        $flows = $query->where('created_at', '>', $start)
            ->orderBy('id', 'desc')
            ->paginate($request->input('page_size', $this->pageSize));

        $result = [];

        foreach ($flows as $flow) {
            $result[] = [
                'id' => $flow->id,
                'user_id' => $flow->user_id,
                'chg_amount' => fentoyuan($flow->chg_amount),
                'type' => $flow->type,
                'subject' => RiderAccountFlow::BUSINESS_TYPE_MAP[$flow->business_type] ?? '',
                'logo' => '',
                'no' => $flow->no,
                'created_at' => $flow->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT)
            ];
        }

        return $this->success('success', $result);
    }


    public function show($id, Request $request)
    {
        $flow = RiderAccountFlow::query()->where('user_id', $request->user()->id)->where('id', $id)->first();
        if (!$flow) {
            $this->errorResponse(404, '流水记录不存在');
        }
        $result = [
            'id' => $flow->id,
            'user_id' => $flow->user_id,
            'chg_amount' => fentoyuan($flow->chg_amount),
            'type' => $flow->type,
            'subject' => RiderAccountFlow::BUSINESS_TYPE_MAP[$flow->business_type] ?? '',
            'logo' => '',
            'no' => $flow->no,
            'created_at' => $flow->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT)
        ];

        return $this->success('success', $result);
    }
}
