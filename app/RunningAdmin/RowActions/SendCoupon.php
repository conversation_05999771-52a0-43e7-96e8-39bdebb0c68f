<?php
namespace App\RunningAdmin\RowActions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use App\RunningAdmin\Forms\SendCouponForm;

class SendCoupon extends RowAction
{
    protected $title = '发券';

    public function render()
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->body(SendCouponForm::make()->payload(['id' => $this->getKey()]))
            ->button($this->title);
    }
}
