<?php

namespace App\Http\Controllers\Api;

use App\Models\Activity;
use App\Models\ActivityRec;
use Carbon\Carbon;
use Illuminate\Http\Request;


class ActivityController extends Controller
{
    public function userActivity(Request $request)
    {
        $activity = Activity::query()->where('status', 1)
            ->where('type', 1)
            ->orderByDesc('id')->with('rules')->first();

        $orderActivity = Activity::query()->where('status', 1)
            ->where('type', 3)
            ->orderByDesc('id')->with('rules')->first();

        // 获取已邀请记录和奖励金额以及明细
        $inviteNum = ActivityRec::query()->where('activity_id', $activity->id)->count();

        $rewardAmount = ActivityRec::query()->where('activity_id', $activity->id)
            ->where('status', 1)
            ->sum('reward_amount');

        $activityRec = ActivityRec::query()->where('activity_id', $activity->id)
            ->orderByDesc('id')->with('child')->limit(10)->get();
        $details = [];
        foreach ($activityRec as $rec) {
            $details[] = [
                'phone' => $rec->child->phone,
                'status' => $rec->status,
                'status_text' => $rec->status_text,
                'remark' => $rec->remark,
            ];
        }

        $info = [
            'register_activity_day' => Carbon::parse($activity->end_time)->diffInDays(Carbon::parse($activity->start_time)) + 1,
            'order_activity_day' => Carbon::parse($orderActivity->end_time)->diffInDays(Carbon::parse($orderActivity->start_time)) + 1,
            'register_activity_messages' => $activity->ruleMessage(Carbon::parse($activity->end_time)->diffInDays(Carbon::parse($activity->start_time)) + 1),
            'order_activity_messages' => $orderActivity->ruleMessage(Carbon::parse($orderActivity->end_time)->diffInDays(Carbon::parse($orderActivity->start_time)) + 1),
            'invite_num' => $inviteNum,
            'total_amount' => floatval(fentoyuan($rewardAmount)),
            'details' => $details,
        ];

        return $this->success('success', $info);

    }

    public function riderActivity(Request $request)
    {
        $activity = Activity::query()->where('status', 1)
            ->where('type', 2)
            ->orderByDesc('id')->with('rules')->first();

        $orderActivity = Activity::query()->where('status', 1)
            ->where('type', 4)
            ->orderByDesc('id')->with('rules')->first();

        // 获取已邀请记录和奖励金额以及明细
        $inviteNum = ActivityRec::query()->where('activity_id', $activity->id)->count();

        $rewardAmount = ActivityRec::query()->where('activity_id', $activity->id)
            ->where('status', 1)
            ->sum('reward_amount');

        $activityRec = ActivityRec::query()->where('activity_id', $activity->id)
            ->orderByDesc('id')->with('child')->limit(10)->get();
        $details = [];
        foreach ($activityRec as $rec) {
            $details[] = [
                'phone' => $rec->child->phone,
                'status' => $rec->status,
                'status_text' => $rec->status_text,
                'remark' => $rec->remark,
            ];
        }

        if (count($orderActivity->rules) == 1) {
            $orderRule = $orderActivity->rules[0];
        } else {
            $orderRule = $orderActivity->rules[0];
        }

        $levels = [];
        $startReward = 0;
        $isLast = false;
        $day = Carbon::parse($activity->end_time)->diffInDays(Carbon::parse($activity->start_time)) + 1;
        foreach ($activity->rules as $rule) {
            if ($rule->end > 100) {
                $maxReward = $startReward + 1 * $rule->reward;
                $condition = '>=' . $rule->start;
                $isLast = true;
            } else {
                $condition = $rule->start . '-' . ($rule->end - 1);
                $maxReward = $startReward + ($rule->end - $rule->start) * $rule->reward;
                $isLast = false;
            }
            $startReward = $maxReward;
            $desc = sprintf('%s天内每邀请第%s名新骑手，注册成功并完成1单平台奖励%s元，被邀请的每名新骑手在注册后%s天内跑满%s单奖励%s元。',
                $day, str_replace('>=', '', $condition),$orderRule->reward,$orderActivity->day, $orderRule->order_count, $orderRule->reward);
            $level = [
                'title' => sprintf(Activity::ActivityTypeMessage[$activity->type], $condition),
                'my_max_reward' => $maxReward . '',
                'friend_reward' => $orderRule->reward,
                'desc' => $desc
            ];
            $levels[] = $level;
        }

        $info = [
            'activity_levels' => $levels,
            'invite_num' => $inviteNum,
            'total_amount' => floatval(fentoyuan($rewardAmount)),
            'details' => $details,
            'time' => sprintf("%s-%s", Carbon::parse($activity->start_time)->format('Y.m.d'),
                Carbon::parse($activity->end_time)->format('Y.m.d')),
            'is_last' => $isLast,
        ];

        return $this->success('success', $info);

    }
}
