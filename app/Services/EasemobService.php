<?php


namespace App\Services;


use App\Models\SystemConfig;
use Illuminate\Support\Facades\Cache;
use Ixudra\Curl\Facades\Curl;
use link1st\Easemob\App\EasemobError;
use link1st\Easemob\App\Http;
use link1st\Easemob\Facades\Easemob;

class EasemobService
{
    const SQ_SYSTEM_USER = 'SQ_SYSTEM';

    const PT_SYSTEM_USER = 'YQS_SYSTEM';

    protected $appName;

    protected $orgName;

    protected $client;

    protected $clientId;

    protected $clientSecret;

    // 目标数组 用户，群，聊天室
    public $target_array = ['users', 'chatgroups', 'chatrooms'];

    public function __construct($appName)
    {
        $this->appName = $appName;
        $this->orgName = config('easemob.org_name');
        $this->appName = config('easemob.app_name');
        $this->clientId = config('easemob.client_id');
        $this->clientSecret = config('easemob.client_secret');
        if ($appName == 'easemob_sq') {
            $this->orgName = config('easemob.sqst.org_name');
            $this->appName = config('easemob.sqst.app_name');
            $this->clientId = config('easemob.sqst.client_id');
            $this->clientSecret = config('easemob.sqst.client_secret');
        }
    }

    public function getUrl()
    {
        return sprintf("https://a1.easemob.com/%s/%s/", $this->orgName, $this->appName);
    }

    public function getToken()
    {
        if (Cache::has($this->appName)) {
            return Cache::get($this->appName);
        } else {
            $url = $this->getUrl() . "token";
            $option = [
                'grant_type' => 'client_credentials',
                'client_id' => $this->clientId,
                'client_secret' => $this->clientSecret,
            ];
            $return = Http::postCurl($url, $option);
            Cache::put($this->appName, $return['access_token'], (int)($return['expires_in'] / 60));

            return $return['access_token'];

        }
    }

    public function getUserToken($userName)
    {
        $url = $this->getUrl() . 'token';
        $data = [
            'grant_type' => 'inherit',
            'username' => $userName,
            'autoCreateUser' => false,
            'ttl' => 0,
        ];
        $header = $this->getHeader();
        $res = Curl::to($url)->withHeaders($header)->withData($data)->asJson(true)->post();

        return $res['access_token'] ?? '';
    }

    public function getUser($userName)
    {
        $url = $this->getUrl() . 'users/' . $userName;
        $response = Curl::to($url)->withHeaders($this->getHeader())->asJson(true)->get();
        if (isset($response['error']) && $response['error'] == 'service_resource_not_found') {
            return null;
        } elseif (isset($response['error'])) {
            throw new \Exception($response['error']);
        } else {
            return $response['entities'][0];
        }
    }

    /**
     * 授权注册用户
     *
     * @param        $name [用户名]
     * @param string $password [密码]
     *
     * @return mixed
     * @throws EasemobError
     */
    public function authorizationRegistration($name, $password = '123456')
    {
        $url = $this->getUrl() . 'users';
        $option = [
            'username' => $name,
            'password' => $password,
        ];
        return Curl::to($url)->withData($option)->withHeaders($this->getHeader())->asJson(true)->post();
    }


    public function initEUserInfo($user)
    {
        $nickName = '用户' . substr($user->phone, 7);
        $ext = [
            'role' => 1
        ];
        $defaultImg = 'https://notion-avatar.vercel.app/api/img/eyJmYWNlIjoxNSwibm9zZSI6MTMsIm1vdXRoIjozLCJleWVzIjoyLCJleWVicm93cyI6MTEsImdsYXNzZXMiOjksImhhaXIiOjI0LCJhY2Nlc3NvcmllcyI6MCwiZGV0YWlscyI6MCwiYmVhcmQiOjAsImZsaXAiOjAsImNvbG9yIjoicmdiYSgyNTUsIDAsIDAsIDApIiwic2hhcGUiOiJub25lIn0=';
        $this->editUserInfo($user->easemob_user_id, $nickName, $defaultImg, $ext);
    }

    public function initRUserInfo($user)
    {
        $nickName = '跑男' . substr($user->phone, 7);
        $ext = [
            'role' => 2
        ];
        $defaultImg = 'https://notion-avatar.vercel.app/api/img/eyJmYWNlIjoxNSwibm9zZSI6MTMsIm1vdXRoIjozLCJleWVzIjoyLCJleWVicm93cyI6MTEsImdsYXNzZXMiOjksImhhaXIiOjI0LCJhY2Nlc3NvcmllcyI6MCwiZGV0YWlscyI6MCwiYmVhcmQiOjAsImZsaXAiOjAsImNvbG9yIjoicmdiYSgyNTUsIDAsIDAsIDApIiwic2hhcGUiOiJub25lIn0=';
        $this->editUserInfo($user->easemob_user_id, $nickName, $defaultImg, $ext);
    }

    /**
     * 修改用户信息
     * @param $user_name
     * @param $nickname
     * @param $headImage
     * @param $ext
     * @return mixed
     * @throws EasemobError
     */
    public function editUserInfo($userName, $nickname, $headImage, $ext)
    {
        $url = $this->getUrl() . 'users/' . $userName;
        $option = [];
        if ($nickname) $option['nickname'] = $nickname;
        if ($headImage) $option['avatarurl'] = $headImage;
        if ($ext) $option['ext'] = $ext;

        $accessToken = $this->getUserToken($userName);

        $header = [
            'Authorization' => 'Bearer ' . $accessToken,
            'Content-Type' => 'application/x-www-form-urlencoded',
        ];
        return Curl::to($url)->withData($option)->withHeaders($header)->asJson(true)->put();
    }

    public function getUserInfoList($userIds)
    {
        $url = $this->getUrl() . 'metadata/user/get';
        $option = [
            'properties' => ['nickname', 'avatarurl', 'ext'],
            'targets' => $userIds
        ];
        $header = $this->getHeader();
        return Curl::to($url)->asJson(true)->withHeaders($header)->withData($option)->post();
    }


    public static function GetAppName($platformId)
    {
        switch ($platformId) {
            case SystemConfig::PlatformPT:
                return 'easemob_pt';
            default:
                return 'easemob_sq';
        }
    }

    /***********************   好友操作   **********************************/

    /**
     * 给用户添加好友
     *
     * @param $owner_username [主人]
     * @param $friend_username [朋友]
     *
     * @return mixed
     * @throws EasemobError
     */
    public function addFriend($owner_username, $friend_username)
    {
        $url = $this->getUrl() . 'users/' . $owner_username . '/contacts/users/' . $friend_username;
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->asJson(true)->post();
    }

    /**
     * 发送文本消息
     *
     * @param array $users [接收的对象数组]
     * @param string $target_type [类型]
     * @param string $message [内容]
     * @param string $send_user [消息发送者]
     * @param array $ext [消息扩展体]
     *
     * @return mixed
     * @throws EasemobError
     */
    public function sendMessageText($users, $target_type = 'users', $message = "", $send_user = self::PT_SYSTEM_USER, $ext = [])
    {
        if (!in_array($target_type, $this->target_array)) {
            throw new EasemobError('target_type 参数错误！');
        }

        $url = $this->getUrl() . 'messages';
        $option = [
            'target_type' => $target_type,
            'target' => $users,
            'msg' => [
                'type' => 'txt',
                'msg' => $message
            ],
            'from' => $send_user
        ];

        // 是否有消息扩展
        if (!empty($ext)) {
            $option['ext'] = $ext;
        }

        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }


    /**
     * 发送图片消息
     *
     * @param array $users [接收的对象数组]
     * @param string $target_type [类型]
     * @param string $uuid [文件的uuid]
     * @param string $share_secret [文件的秘钥 上传后生产]
     * @param string $file_name [指定文件名]
     * @param int $width [宽]
     * @param int $height [高]
     * @param string $send_user
     *
     * @return mixed
     * @throws EasemobError
     */
    public function sendMessageImg($users, $target_type = 'users', $uuid, $share_secret, $file_name, $width = 480, $height = 720, $send_user = self::PT_SYSTEM_USER)
    {
        if (!in_array($target_type, $this->target_array)) {
            throw new EasemobError('target_type 参数错误！');
        }

        $url = $this->getUrl() . 'messages';
        $option = [
            'target_type' => $target_type,
            'target' => $users,
            'msg' => [
                'type' => 'img',
                'url' => $this->getUrl() . 'chatfiles/' . $uuid,
                'filename' => $file_name,
                'secret' => $share_secret,
                'size' => [
                    'width' => $width,
                    'height' => $height
                ]
            ],
            'from' => $send_user
        ];
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }


    /**
     * 发送语音消息
     *
     * @param array $users [接收的对象数组]
     * @param string $target_type [类型]
     * @param string $uuid [文件的uuid]
     * @param string $share_secret [文件的秘钥 上传后生产]
     * @param string $file_name [指定文件名]
     * @param int $length [长度]
     * @param string $send_user
     *
     * @return mixed
     * @throws EasemobError
     */
    public function sendMessageAudio($users, $target_type = 'users', $uuid, $share_secret, $file_name, $length = 10, $send_user = self::PT_SYSTEM_USER)
    {
        if (!in_array($target_type, $this->target_array)) {
            throw new EasemobError('target_type 参数错误！');
        }

        $url = $this->getUrl() . 'messages';
        $option = [
            'target_type' => $target_type,
            'target' => $users,
            'msg' => [
                'type' => 'audio',
                'url' => $this->getUrl() . 'chatfiles/' . $uuid,
                'filename' => $file_name,
                'secret' => $share_secret,
                'length' => $length
            ],
            'from' => $send_user
        ];
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }

    /**
     * 发送视频消息
     *
     * @param array $users
     * @param string $target_type [类型]
     * @param string $uuid [文件的uuid]
     * @param string $share_secret [文件的秘钥 上传后生产]
     * @param string $file_name [指定文件名]
     * @param int $length [长度]
     * @param string $send_user
     *
     * @return mixed
     * @throws EasemobError
     */
    /**
     * 发送视频消息
     *
     * @param array $users [接收的对象数组]
     * @param string $target_type [类型]
     * @param        $video_uuid [视频uuid]
     * @param        $video_share_secret [视频秘钥]
     * @param        $video_file_name [下载的时候视频名称]
     * @param int $length [长度]
     * @param int $video_length [视频大小]
     * @param        $img_uuid [缩略图]
     * @param        $img_share_secret [图片秘钥]
     * @param string $send_user [发送者]
     *
     * @return mixed
     * @throws EasemobError
     */
    public function sendMessageVideo($users, $target_type = 'users', $video_uuid, $video_share_secret, $video_file_name, $length = 10, $video_length = 58103, $img_uuid, $img_share_secret, $send_user = self::PT_SYSTEM_USER)
    {
        if (!in_array($target_type, $this->target_array)) {
            throw new EasemobError('target_type 参数错误！');
        }

        $url = $this->getUrl() . 'messages';
        $option = [
            'target_type' => $target_type,
            'target' => $users,
            'msg' => [
                'type' => 'video',
                'url' => $this->getUrl() . 'chatfiles/' . $video_uuid,
                'filename' => $video_file_name,
                'thumb_secret' => $video_share_secret,
                'length' => $length,
                'file_length' => $video_length,
                'thumb' => $this->getUrl() . 'chatfiles/' . $img_uuid,
                'secret' => $img_share_secret
            ],
            'from' => $send_user
        ];
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }


    /**
     * 消息透传
     *
     * @param array $users [接收的对象数组]
     * @param string $target_type [类型]
     * @param string $action [内容]
     * @param string $send_user [消息发送者]
     *
     * @return mixed
     * @throws EasemobError
     */
    public function sendMessagePNS($users, $target_type = 'users', $action = "", $send_user = self::PT_SYSTEM_USER)
    {
        if (!in_array($target_type, $this->target_array)) {
            throw new EasemobError('target_type 参数错误！');
        }

        $url = $this->getUrl() . 'messages';
        $option = [
            'target_type' => $target_type,
            'target' => $users,
            'msg' => [
                'type' => 'cmd',
                'action' => $action
            ],
            'from' => $send_user
        ];
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }

    /**
     * 自定义消息
     *
     * @param array $users [接收的对象数组]
     * @param string $target_type [类型]
     * @param array $customExts [自定义扩展属性。你可以自行设置扩展属性中的字段。]
     * @param string $customEvent [自定义事件类型]
     * @param string $send_user [消息发送者]
     *
     * @return mixed
     * @throws EasemobError
     */
    public function sendMessageEvent($users, $target_type = 'users', $customEvent = "", $customExts = [], $send_user = self::PT_SYSTEM_USER)
    {
        if (!in_array($target_type, $this->target_array)) {
            throw new EasemobError('target_type 参数错误！');
        }

        $url = $this->getUrl() . 'messages';
        $option = [
            'target_type' => $target_type,
            'target' => $users,
            'msg' => [
                'type' => 'custom',
                'customExts' => $customExts,
                'customEvent' => $customEvent
            ],
            'from' => $send_user
        ];
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }

    /**
     * 获取用户离线消息数
     *
     * @param $user_name
     *
     * @return mixed
     * @throws EasemobError
     */
    public function offlineMsgCount($user_name)
    {
        $url = $this->getUrl() . 'users/' . $user_name . '/offline_msg_count';
        $option = [];
        $access_token = $this->getToken();
        $header [] = 'Authorization: Bearer ' . $access_token;

        return Curl::to($url)->withHeaders($this->getHeader())->withData($option)->asJson(true)->post();
    }


    protected function getHeader()
    {
        return [
            'Authorization' => 'Bearer ' . $this->getToken()
        ];
    }
}
