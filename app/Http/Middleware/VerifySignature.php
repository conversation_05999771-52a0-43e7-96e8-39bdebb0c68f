<?php

namespace App\Http\Middleware;

use App\Models\OpenApiAccount;
use App\Models\PhoneList;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;


class VerifySignature
{
    public function handle(Request $request, Closure $next)
    {
        if ($request->route()->getName() == 'api.v1.verificationCodes.store') {
            if (version_compare($request->header('version', '0'), '1.4.10', '>')) {
                return $next($request);
            }
            $phone = PhoneList::query()->where('phone', $request->get('phone'))->first();
            if ($phone) {
                return $next($request);
            }
        }
        $appKey = $request->header('app-key');
        if ($appKey == 'yqs') {
            return $next($request);
        }
        $account = OpenApiAccount::where('app_key', $appKey)->first();
        if (!$account) {
            return $this->errorResponse(401, '应用不存在');
        }

        $signature = $request->header('sign');
        if (!$signature) {
            return $this->errorResponse(401, '签名错误');
        }
        $data = $this->getSortedData($request);

        if ($this->isValidSignature($data, $signature, $account->app_secret)) {
            return $next($request);
        }

        return $this->errorResponse(401, '签名错误');
    }

    private function isValidSignature($data, $signature, $secret)
    {
        $expectedSignature = hash_hmac('sha256', $data, $secret);

        return hash_equals($expectedSignature, $signature);
    }

    private function getSortedData(Request $request)
    {
        $data = $request->all();
        ksort($data);
        $query = http_build_query($data);
        $query = str_replace('+', '%20', $query);

        return $query;
    }

    public function errorResponse($statusCode, $message = null, $code = 0)
    {
        throw new HttpException($statusCode, $message, null, [], $code);
    }
}
