<?php

namespace App\Models;

use App\Traits\SplitTableTrait;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class Location extends Model
{
    use HasFactory, SplitTableTrait;

    public $timestamps = false;

    protected $table = "locations";
    protected $primaryKey = "id";

    const GEO_KEY_PREFIX = "location_";
    const LOCATION_BELONG_USER = self::GEO_KEY_PREFIX."1"; //用户
    const LOCATION_BELONG_RIDER = self::GEO_KEY_PREFIX."2"; //骑手

    protected $fillable = [
        'belong_type', 'belong_id', 'lng', 'lat', 'time'
    ];

    public function __construct(array $attributes = [])
    {
        parent::__construct($attributes);
        // 初始化分表处理
        $this->init();
    }

    //获取关联人
    public function belong()
    {
        return $this->morphTo();
    }

    public static function getRiderFromRedis($lng, $lat, $distance)
    {
        return Redis::georadius(self::LOCATION_BELONG_RIDER, $lng, $lat, $distance, 'km', 'WITHDIST', 'WITHCOORD', 'ASC');
    }

    public static function getUserFromRedis($lng, $lat, $distance)
    {
        return Redis::georadius(self::LOCATION_BELONG_USER, $lng, $lat, $distance, 'km', 'WITHDIST', 'WITHCOORD', 'ASC');
    }
}
