<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\Category;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Column;
use Dcat\Admin\Layout\Content;
use Dcat\Admin\Layout\Row;
use Dcat\Admin\Show;
use Dcat\Admin\Tree;
use Dcat\Admin\Widgets\Box;
use Dcat\Admin\Widgets\Form as WidgetForm;

class CategoryController extends AdminController
{
    public function index(Content $content)
    {
        return $content->title($this->title())->description("分类管理")->body(function (Row $row) {
            $row->column(7, $this->treeView()->render());
            $row->column(5, function (Column $column) {
                $form = new WidgetForm();
                $form->action(admin_url('categories'));
                $form->text('title')->required();
//                $form->text('sub_title')->required();
//                $form->image('pic')->uniqueName()->autoUpload()->autoSave(false)->required();
                $form->select('pid')->options(\App\Models\Category::selectOptions());
                $form->width(9, 2);
                $column->append(Box::make(trans('admin.new'), $form));
            });
        });
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Category(), function (Grid $grid) {
            $grid->id('ID')->bold()->sortable();
            $grid->title->tree(); // 开启树状表格功能
            $grid->order;


            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Category(), function (Show $show) {
            $show->field('id');
            $show->field('title');
            $show->field('sub_title');
            $show->field('pic');
            $show->field('pid');
            $show->field('view_order');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Category(), function (Form $form) {
            $form->display('id');
            $form->text('title')->required();
//            $form->text('sub_title')->required();
//            $form->image('pic')->uniqueName()->autoUpload()->autoSave(false)->required();
            $form->select('pid')->options(\App\Models\Category::selectOptions());

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    /**
     * @return \Dcat\Admin\Tree
     */
    protected function treeView()
    {
        $menuModel = \App\Models\Category::class;

        return new Tree(new $menuModel(), function (Tree $tree) {
            $tree->disableCreateButton();
            $tree->disableQuickCreateButton();
            $tree->disableEditButton();
            $tree->maxDepth(3);

            $tree->actions(function (Tree\Actions $actions) {
                if ($actions->getRow()->extension) {
                    $actions->disableDelete();
                }

                $actions->prepend(new \Dcat\Admin\Http\Actions\Menu\Show());
            });

            $tree->branch(function ($branch) {
                $payload = "<strong>{$branch['title']}</strong>";

                if (! isset($branch['children'])) {
//                    $payload .= "&nbsp;&nbsp;&nbsp;<a href=\"$uri\" class=\"dd-nodrag\">$uri</a>";
                }

                return $payload;
            });
        });
    }
}
