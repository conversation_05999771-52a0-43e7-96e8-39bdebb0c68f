<?php


namespace App\Http\Controllers;


use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\Site;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Services\RiderLocationService;

class MapController extends Controller
{
    public function orderMap(Request $request)
    {
        $query = O2oErrandOrder::query()->where('order_status', O2oErrandOrder::STATUS_PAID)->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT);
        if ($siteId = $request->input('site_id')) {
            $query->where('site_id', $siteId);
        }

        $site = Site::query()->find($siteId);

        $distance = $request->input('distance', 100);

        $result = [];
        $firstOrder = null;
        $orders = $query->get();
        foreach ($orders as $index => $order) {
            if ($index == 0) {
                $firstOrder = $order;
            }
            $result[] = $this->formatTask($order);
        }

        if ($orderNo = $request->get('order_no')) {
            $firstOrder = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        }
        $riderList = [];

        // riders
        if ($firstOrder) {
            if (in_array($firstOrder->type, [O2oErrandOrder::TYPE_BUY, O2oErrandOrder::TYPE_HELP])) {
                $lng = $firstOrder->deliver_lng;
                $lat = $firstOrder->deliver_lat;
            } else {
                $lng = $firstOrder->pickup_lng;
                $lat = $firstOrder->pickup_lat;
            }

            $redisLocations = Location::getRiderFromRedis($lng, $lat, $distance);
            $riderIdList = [];
            $riderDistance = [];
            foreach ($redisLocations as $location) {
                $riderIdList[] = $location[0];
                $riderDistance[$location[0]] = [
                    'distance' => $location[1],
                    'lgn' => $location[2][0],
                    'lat' => $location[2][1],
                ];
            }
            $riders = Rider::query()->where('status', Rider::STATUS_UP)
                ->whereIn('id', $riderIdList)
                ->orderByRaw(DB::raw("FIND_IN_SET(id, '" . implode(',', $riderIdList) . "'" . ')'))
                ->get();
            foreach ($riders as $rider) {
                $orders = O2oErrandOrder::query()->where('rider_id', $rider->id)->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT)
                    ->where('order_status', [O2oErrandOrder::STATUS_PICKUP, O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, O2oErrandOrder::STATUS_DELIVERY])->get();
                $riderList[] = [
                    'id' => $rider->id,
                    'name' => $rider->name,
                    'phone' => $rider->phone,
                    'avatar' => $rider->avatar_url,
                    'order_count' => $rider->getOrderCount(),
                    'distance' => $riderDistance[$rider->id]['distance'],
                    'lgn' => $riderDistance[$rider->id]['lgn'],
                    'lat' => $riderDistance[$rider->id]['lat'],
                    'direction' => $this->getRiderDirection($riderDistance[$rider->id]['lgn'], $riderDistance[$rider->id]['lat'], $orders),
                ];
            }
        }
        return view('order_map', ['orders' => $result, 'riders' => $riderList, 'site' => $site, 'firstOrder' => $firstOrder ? $firstOrder->toArray() : []]);
    }

    public function riderMap(Request $request)
    {
        $siteId = $request->input('site_id');
        $site = Site::query()->find($siteId);

        $riderList = Rider::query()->where('site_id', $siteId)
            ->where('status', Rider::STATUS_UP)
            ->get();
        $riderLocationService = app(RiderLocationService::class);
        
        foreach ($riderList as $key => $rider) {
            $location = $riderLocationService->getRiderLocation($rider->id);
            $riderList[$key]->order_count = $rider->getOrderCount();
            $riderList[$key]->location = $location ? [
                'lng' => $location['lng'],
                'lat' => $location['lat']
            ] : null;
        }
        return view('rider_map', ['riders' => $riderList, 'site' => $site]);
    }

    private function getRiderDirection($lng, $lat, $orders)
    {
        $direction = [];
        foreach ($orders as $order) {
            if ($order->order_status == O2oErrandOrder::STATUS_PICKUP) {
                if ($order->pickup_lng) {
                    $direction[] = getAngle($lng, $lat, $order->pickup_lng, $order->pickup_lat, 2);
                } else {
                    $direction[] = getAngle($lng, $lat, $order->deliver_lng, $order->deliver_lat, 2);
                }
            } else {
                $direction[] = getAngle($lng, $lat, $order->deliver_lng, $order->deliver_lat, 2);
            }
        }
        if ($direction) {
            return $direction[0];
        }
        return '';
    }


    private function formatTask($order)
    {
        $minute = Carbon::parse($order->estimated_delivery_time)->diffInMinutes(Carbon::now());
        if ($minute > 90) {
            $timeText = Carbon::parse($order->estimated_delivery_time)->diffInHours(Carbon::now()) . '小时内送达';
        } else {
            $timeText = $minute . '分钟内送达';
        }
        if (Carbon::parse($order->estimated_delivery_time)->lt(Carbon::now())) {
            if ($minute > 90) {
                $hours = Carbon::parse($order->estimated_delivery_time)->diffInHours(Carbon::now());
                $timeText = '已超时' .  $hours . '小时';
            } else {
                $timeText = '已超时' .  $minute . '分钟';
            }
        }
        return [
            'order_no' => $order->order_no,
            'time' => Carbon::parse($order->estimated_delivery_time)->diffInMinutes(Carbon::now()),
            'time_text' => $timeText,
            'order_price_normal' => fentoyuan($order->reward_amount_part),
            'order_price_pro' => fentoyuan($order->reward_amount_full),
            'pickup_address' => $order->pickup_address,
            'deliver_address' => $order->deliver_address,
            'deliver_distance' => formatDistance($order->distance / 1000),
            'type' => $order->type,
            'type_text' => O2oErrandOrder::TypeMap[$order->type],
            'remark' => $order->remark,
            'appointment' => is_null($order->appointment_end_time),
            'goods_desc' => $order->goods_desc,
            'goods_price' => fentoyuan($order->goods_price),
            'goods_protected_price' => fentoyuan($order->goods_protected_price),
            'order_status' => $order->order_status,
            'order_status_text' => O2oErrandOrder::StatusMap[$order->order_status],
            'is_special' => $order->is_special,
            'gratuity' => fentoyuan($order->gratuity),
            'is_trans' => $order->is_trans,
            'dispatch_status' => $order->dispatch_status,
            'dispatch_status_text' => O2oErrandOrder::DispatchStatusMap[$order->dispatch_status],
        ];
    }
}
