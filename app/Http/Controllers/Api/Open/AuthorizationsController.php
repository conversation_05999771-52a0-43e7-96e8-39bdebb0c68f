<?php

namespace App\Http\Controllers\Api\Open;

use App\Http\Requests\Api\OpenAuthorizationRequest;
use App\Models\SystemConfig;
use App\Models\User;
use App\Services\UserService;
use Illuminate\Support\Facades\Auth;

class AuthorizationsController extends OpenController
{
    public function auth(OpenAuthorizationRequest $request, UserService $service)
    {
        // 判断手机号是否存在
        $user = User::query()->where('phone', $request->phone)->first();
        if (!$user) {
            $user = $service->registerUser($request->phone, '', '', SystemConfig::PlatformPT);
            $needUpdate = false;
            if ($request->nickname) {
                $needUpdate = true;
                $user->nickname = $request->nickname;
            }

            if ($request->avatar) {
                $needUpdate = true;
                $user->avatar = $request->avatar;
            }
            if ($needUpdate) $user->save();
        }
        $ttl = 365 * 24 * 60;
        $token = auth('api')->setTTL($ttl)->login($user);

        return $this->respondWithToken($token);


    }

    protected function respondWithToken($token)
    {
        return $this->success('登录成功', [
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_in' => Auth::guard('api')->factory()->getTTL() * 60
        ]);
    }

}
