<?php

namespace App\RunningAdmin\Controllers;

use App\Models\AdminSiteRel;
use App\Models\Site;
use App\RunningAdmin\Repositories\Rider;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Show;
use Illuminate\Support\Facades\Config;

class RiderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Rider(), function (Grid $grid) {
            if (!Admin::user()->isAdministrator()) {
                $siteIds = AdminSiteRel::query()->where('user_id', Admin::user()->id)->pluck('site_id');
                $grid->model()->with(['parent', 'site', 'accountBzj'])
                    ->whereIn('site_id', $siteIds)
                    ->orderBy('id', 'desc');
            } else {
                $grid->model()->with(['parent', 'site', 'accountBzj'])->orderBy('id', 'desc');
            }
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->showQuickEditButton();
            $grid->fixColumns(2);

            $grid->column("id");
            $grid->column('avatar')->image(Config::get('app.img_url_prefix'), 80);
            $grid->column('site.name', '站点');
            $grid->column('name')->display(function () {
                $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px">用户名： %s</div>
    <div style="margin: 5px">手机号： %s</div>
    <div style="margin: 5px">健康证： %s</div>
    <div style="margin: 5px">保证金： %s</div>
</div>
HTML;
                $healthStatus = \App\Models\Rider::TypeStatusMap[$this->health_status];
                return sprintf($html, $this->name, $this->phone, common_admin_label($healthStatus, $this->health_status), $this->accountBzj ? fentoyuan($this->accountBzj->amount) : 0);
            });

            $grid->column('level')->display(function () {
                $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px">等级： <span class="label" style="background:#21b978">%s级</span></div>
    <div style="margin: 5px">订单限制： %s</div>
    <div style="margin: 5px">转单限制： %s</div>
</div>
HTML;
                return sprintf($html, \App\Models\Rider::LEVEL_MAP[$this->level], $this->order_limit, $this->trans_limit);

            });
            $grid->column('black', '拉黑')->switch();
            $grid->column("verified")->using(\App\Models\Rider::TypeStatusMap)->label();
            $grid->column("transport_status")->using(\App\Models\Rider::TypeStatusMap)->label();
            $grid->column("status")->using(\App\Models\Rider::StatusMap)->label(
                [
                    0 => 'warning',
                    1 => 'success',
                    2 => 'danger',
                ],
                'primary'
            );
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('name')->width(3);
                $filter->like('phone')->width(3);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Rider(), function (Show $show) {
            $show->model()->with(['user', 'user.riderProfile', 'user.idcard']);
            $show->panel()->tools(function ($tools) {
                $tools->disableEdit();
                $tools->disableList();
                $tools->disableDelete();
                $tools->showQuickEdit();
            });
            $show->html(function () {
                // 获取字段信息
                return view('rider_profile', ['rider' => $this]);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Rider(), function (Form $form) {
            $form->disableViewButton();
            $form->disableDeleteButton();
            $form->disableViewCheck();
            $form->display('id');
            $form->text('name')->required();
            $form->text('phone')->required();
            $form->switch('black', '拉黑');
            $form->datetimeRange("start_time", "end_time", '有效期');
            $form->select('site_id')->options(Site::query()->pluck('name', 'id'))->required();
            $form->number('level')->default(1)->attribute('min', 1)->required();
            $form->select('verified')->default(0)->options(\App\Models\Rider::TypeStatusMap)->required();
            $form->select('health_status')->default(0)->options(\App\Models\Rider::TypeStatusMap)->required();
            $form->select('transport_status')->default(0)->options(\App\Models\Rider::TypeStatusMap)->required();
            $form->select('status')->default(0)->options(\App\Models\Rider::StatusMap)->required();
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
