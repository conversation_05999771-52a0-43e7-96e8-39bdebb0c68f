<?php

namespace App\Jobs;

use App\Models\O2oErrandOrder;
use App\Models\TransOrder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class CloseTransOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $transOrderId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($transOrderId, $delay)
    {
        $this->transOrderId = $transOrderId;
        $this->delay($delay);
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $transOrder = TransOrder::query()->find($this->transOrderId);

        if ($transOrder && $transOrder->status == TransOrder::StatusWait) {
            $transOrder->status = TransOrder::StatusClosed;
            $transOrder->save();

            $order = O2oErrandOrder::query()->where('order_no', $transOrder->order_no)->first();
            if ($order && $order->rider_id == 0) {
                $order->rider_id = $order->ori_rider_id;
                $order->order_status = O2oErrandOrder::STATUS_PICKUP;
                $order->is_trans = 0;
                $order->save();
            }
        }

    }
}
