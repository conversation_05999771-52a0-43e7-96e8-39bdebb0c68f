<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderPayRec extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const ORDER_PREFIX = '40';

    //1-加小费，2-打赏，3-支付商品费
    const TYPE_GRATUITY = 1;
    const TYPE_REWARD = 2;
    const TYPE_GOODS = 3;
    const TypeMap = [
        self::TYPE_GRATUITY => "加小费",
        self::TYPE_REWARD => "打赏",
        self::TYPE_GOODS => "支付商品费",
    ];

    protected $dates = ['paid_at', 'refund_at'];

    protected $fillable = [
        'order_id', 'user_id', 'order_no', 'type', 'amount', 'paid_at', 'pay_method', "payment_no", 'refund_no', 'refund_at'
    ];

    public function order(){
        return $this->belongsTo(O2oErrandOrder::class, "order_id");
    }

    public function user(){
        return $this->belongsTo(User::class, "user_id");
    }

    public static function findAvailableNo()
    {
        // 订单流水号前缀
        $prefix = self::ORDER_PREFIX . date('YmdHis');
        for ($i = 0; $i < 10; $i++) {
            // 随机生成 6 位的数字
            $no = $prefix . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            // 判断是否已经存在
            if (!static::query()->where('order_no', $no)->exists()) {
                return $no;
            }
        }
        return false;
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::creating(function ($model) {
            // 如果模型的 no 字段为空
            if (!$model->order_no) {
                // 调用 findAvailableNo 生成订单流水号
                $model->order_no = static::findAvailableNo();
                // 如果生成失败，则终止创建订单
                if (!$model->order_no) {
                    return false;
                }
            }
        });
    }
}
