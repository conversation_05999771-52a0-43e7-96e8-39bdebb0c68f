<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\Amap\GaodeService;
use App\Models\SystemConfig;

class TestGaodeRidingCommand extends Command
{
    protected $signature = 'test:gaode-riding';
    protected $description = '测试高德地图骑行路线规划功能';

    public function handle()
    {
        $gaodeService = new GaodeService();
        
        // 测试起点和终点坐标
        $fromLng = '119.997052';
        $fromLat = '30.275627';
        $toLng = '120.001544';
        $toLat = '30.280125';
        
        try {
            $result = $gaodeService->electrobike($fromLng, $fromLat, $toLng, $toLat);
            $this->info('路线规划结果:');
            $this->info(json_encode($result, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));
            $paths = $result["route"]['paths'] ?? [[]];
            $index = 0;
            if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                $index = count($paths) - 1;
            }
            $distance = intval($paths[$index]["distance"] ?? 0);
            if ($distance > 10000) throw new \Exception(sprintf('暂不支持超过%s公里的配送(订单距离:%s公里)', 10000 / 1000, $distance / 1000));

            $needSeconds = intval($paths[$index]["duration"] ?? 0);

            $result = [
                'distance' => $distance,
                'distance_text' => number_format($distance / 1000, 1, '.', '') . "公里",
                'normal' => [],
                'special' => [],
            ];
            dd($result);
        } catch (\Exception $e) {
            $this->error('路线规划失败: ' . $e->getMessage());
        }
    }
} 