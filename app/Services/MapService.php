<?php

namespace App\Services;

use App\Services\Amap\GaodeService;
use Exception;
use Illuminate\Support\Facades\Log;

class MapService
{
    protected GaodeService $gaodeService;

    public function __construct()
    {
        $this->gaodeService = new GaodeService();
    }

    public function around($lng, $lat, $page, $pageSize, $category)
    {
        try {
            $response = $this->gaodeService->queryPoi($lat, $lng, $page, $pageSize, $category);
            
            // 转换为腾讯地图API返回格式
            return [
                'status' => $response['status'],
                'count' => $response['count'] ?? 0,
                'data' => array_map(function($item) {
                    return [
                        'id' => $item['id'] ?? '',
                        'title' => $item['name'] ?? '',
                        'address' => $item['address'] ?? '',
                        'tel' => $item['tel'] ?? '',
                        'category' => $item['type'] ?? '',
                        'location' => [
                            'lat' => explode(',', $item['location'])[1] ?? 0,
                            'lng' => explode(',', $item['location'])[0] ?? 0
                        ],
                        '_distance' => $item['distance'] ?? 0
                    ];
                }, $response['pois'] ?? [])
            ];
        } catch (Exception $e) {
            Log::error('高德地图周边搜索失败', ['error' => $e->getMessage()]);
            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    public function search($keyword, $city, $lng, $lat, $page, $pageSize, $category)
    {
        try {
            $params = [
                'keywords' => $keyword,
                'page_size' => $pageSize,
                'page_num' => $page
            ];
            
            if ($city) {
                $params['city'] = $city;
            } else {
                $params['location'] = "$lng,$lat";
                $params['radius'] = 10000;
            }

            $response = $this->gaodeService->searchText($params);
            
            // 转换为腾讯地图API返回格式
            return [
                'status' => $response['status'],
                'count' => $response['count'] ?? 0,
                'data' => array_map(function($item) {
                    return [
                        'id' => $item['id'] ?? '',
                        'title' => $item['name'] ?? '',
                        'address' => $item['address'] ?? '',
                        'tel' => $item['tel'] ?? '',
                        'category' => $item['type'] ?? '',
                        'location' => [
                            'lat' => explode(',', $item['location'])[1] ?? 0,
                            'lng' => explode(',', $item['location'])[0] ?? 0
                        ],
                        '_distance' => $item['distance'] ?? 0
                    ];
                }, $response['pois'] ?? [])
            ];
        } catch (Exception $e) {
            Log::error('高德地图搜索失败', ['error' => $e->getMessage()]);
            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    public function geocoder($lng, $lat, $get_poi)
    {
        try {
            $response = $this->gaodeService->reGeoQuery($lng, $lat);
            // 转换为腾讯地图API返回格式
            $regeocode = $response['regeocode'] ?? [];
            return [
                'status' => $response['status'],
                'result' => [
                    'location' => [
                        'lat' => $lat,
                        'lng' => $lng
                    ],
                    'address' => $regeocode['formatted_address'] ?? '',
                    'address_component' => [
                        'province' => $regeocode['addressComponent']['province'] ?? '',
                        'city' => $regeocode['addressComponent']['city'] ?? '',
                        'district' => $regeocode['addressComponent']['district'] ?? '',
                        'street' => $regeocode['addressComponent']['street'] ?? '',
                        'street_number' => $regeocode['addressComponent']['streetNumber'] ?? ''
                    ],
                    'pois' => $get_poi ? ($regeocode['pois'] ?? []) : []
                ]
            ];
        } catch (Exception $e) {
            Log::error('高德地图逆地理编码失败', [
                'error' => $e->getMessage(),
                'lng' => $lng,
                'lat' => $lat
            ]);
            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    public function ebicycling($lng, $lat, $lng2, $lat2)
    {
        try {
            return $this->gaodeService->electrobike($lng, $lat, $lng2, $lat2);
        } catch (Exception $e) {
            Log::error('高德地图电动车路线规划失败', ['error' => $e->getMessage()]);
            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    public function bicycling($lng, $lat, $lng2, $lat2)
    {
        try {
            return $this->gaodeService->bicycling($lng, $lat, $lng2, $lat2);
        } catch (Exception $e) {
            Log::error('高德地图自行车路线规划失败', ['error' => $e->getMessage()]);    
            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    public function ipQuery($ip)
    {
        try {
            $response = $this->gaodeService->ipQuery($ip);
            
            // 转换为腾讯地图API返回格式
            return [
                'status' => $response['status'],
                'result' => [
                    'ip' => $ip,
                    'location' => [
                        'lat' => explode(',', $response['location'])[1] ?? 0,
                        'lng' => explode(',', $response['location'])[0] ?? 0
                    ],
                    'ad_info' => [
                        'nation' => $response['country'] ?? '中国',
                        'province' => $response['province'] ?? '',
                        'city' => $response['city'] ?? '',
                        'district' => $response['district'] ?? '',
                        'adcode' => $response['adcode'] ?? ''
                    ]
                ]
            ];
        } catch (Exception $e) {
            Log::error('高德地图IP定位失败', ['error' => $e->getMessage()]);
            return ['status' => 0, 'message' => $e->getMessage()];
        }
    }

    public function distanceMatrix($from, $to)
    {
        try {
            $results = $this->gaodeService->distanceQuery($from, $to);
            
            // 转换为腾讯地图API返回格式
            return [
                'status' => 0,
                'message' => 'OK',
                'result' => [
                    'rows' => [[
                        'elements' => array_map(function($item) {
                            return [
                                'distance' => $item['distance'] ?? 0,
                                'duration' => $item['duration'] ?? 0
                            ];
                        }, $results)
                    ]]
                ]
            ];
        } catch (Exception $e) {
            Log::error('高德地图距离计算失败', ['error' => $e->getMessage()]);
            return ['status' => 1, 'message' => $e->getMessage()];
        }
    }



    /**
     * 批量计算多个点之间的距离
     *
     * @param array $origins 起始点数组，每个元素包含 ['lat' => 纬度, 'lng' => 经度]
     * @param array $destinations 目标点数组，每个元素包含 ['lat' => 纬度, 'lng' => 经度]
     * @return array 包含距离和时间的结果数组
     */
    public function batchDistanceMatrix(array $origins, array $destinations): array
    {
        try {
            $results = [
                'status' => 0,
                'message' => 'OK',
                'results' => []
            ];

            foreach ($origins as $i => $origin) {
                foreach ($destinations as $j => $destination) {
                    $fromCoord = $origin['lat'] . ',' . $origin['lng'];
                    $toCoord = $destination['lat'] . ',' . $destination['lng'];
                    
                    $response = $this->distanceMatrix($fromCoord, $toCoord);
                    
                    if (isset($response['result']['rows'][0]['elements'][0])) {
                        $element = $response['result']['rows'][0]['elements'][0];
                        $results['results'][] = [
                            'origin_index' => $i,
                            'destination_index' => $j,
                            'distance' => $element['distance'] ?? 0,
                            'duration' => $element['duration'] ?? 0
                        ];
                    } else {
                        $results['results'][] = [
                            'origin_index' => $i,
                            'destination_index' => $j,
                            'distance' => 0,
                            'duration' => 0
                        ];
                    }
                }
            }
            
            return $results;
        } catch (Exception $e) {
            Log::error('高德地图批量距离计算失败', ['error' => $e->getMessage()]);
            return [
                'status' => 1,
                'message' => $e->getMessage(),
                'results' => []
            ];
        }
    }
}
