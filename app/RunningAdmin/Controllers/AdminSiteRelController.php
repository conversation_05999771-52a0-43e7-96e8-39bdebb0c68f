<?php

namespace App\RunningAdmin\Controllers;

use App\Models\Site;
use App\RunningAdmin\Repositories\AdminSiteRel;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Models\Administrator;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class AdminSiteRelController extends AdminController
{
    protected $title = '站点分配';
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new AdminSiteRel(), function (Grid $grid) {
            $grid->model()->with(['site', 'user']);
            $grid->column('id')->sortable();
            $grid->enableDialogCreate();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->column('site.name', '站点');
            $grid->column('user.username', '管理员');
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new AdminSiteRel(), function (Show $show) {
            $show->field('id');
            $show->field('site_id');
            $show->field('user_id');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new AdminSiteRel(), function (Form $form) {
            $form->display('id');
            $sites = Site::all()->pluck('name', 'id');
            $form->select('site_id')->options($sites);
            $admins = Administrator::all()->pluck('username', 'id');
            $form->select('user_id')->options($admins);

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
