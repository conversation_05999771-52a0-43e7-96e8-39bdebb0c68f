<?php

namespace App\RunningAdmin\Forms;

use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use App\Models\RiderTxApply;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class RiderTxApplyReject extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $apply = RiderTxApply::find($this->payload['id']);
        if ($apply) {
            DB::beginTransaction();
            try {
                $apply->status = 2;
                $apply->remark = $input['remark'];
                $apply->save();

                // 余额退回
                $account = RiderAccount::query()->where('user_id', $apply->user_id)->where('account_type', 1)->first();

                $account->add(yuantofen($apply->amount), RiderAccountFlow::BUSINESS_TYPE_APPLY_REJECT, $apply->order_no, '提现申请拒绝');

                DB::commit();

            } catch (\Exception $e) {
                DB::rollBack();
                return $this->response()->error($e->getMessage())->refresh();
            }


        }
        return $this->response()->success("处理成功")->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->textarea('remark', '拒绝理由')->required();
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'remark' => '',
        ];
    }
}
