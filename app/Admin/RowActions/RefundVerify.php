<?php
namespace App\Admin\RowActions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use App\Admin\Actions\Form\RefundVerifyForm;

class RefundVerify extends RowAction
{
    protected $title = '审核';

    public function render()
    {
        return Modal::make()
            ->lg()
            ->title($this->title)
            ->body(RefundVerifyForm::make()->payload(['id' => $this->getKey()]))
            ->button($this->title);
    }
}
