<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\ActivityRule;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ActivityRuleController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ActivityRule(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('activity_id');
            $grid->column('start', '开始');
            $grid->column('end', '结束');
            $grid->column('order_count', '订单数');
            $grid->column('reward', '奖励');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new ActivityRule(), function (Show $show) {
            $show->field('id');
            $show->field('activity_id');
            $show->field('start');
            $show->field('end');
            $show->field('order_count');
            $show->field('reward');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ActivityRule(), function (Form $form) {
            $form->display('id');
            $form->text('activity_id');
            $form->text('start');
            $form->text('end');
            $form->text('order_count');
            $form->text('reward');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
