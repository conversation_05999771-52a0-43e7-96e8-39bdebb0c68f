<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\SuggestionCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class SuggestionCategoryController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SuggestionCategory(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('platform');
            $grid->column('view_order');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SuggestionCategory(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('platform');
            $show->field('view_order');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SuggestionCategory(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('platform');
            $form->text('view_order');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
