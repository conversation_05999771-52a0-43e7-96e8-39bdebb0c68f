<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\ShopSpuRequest;
use App\Http\Resources\ShopSpuResource;
use App\Models\Common;
use App\Models\ShopSpu;
use Carbon\Carbon;

class ShopSpuController extends Controller
{

    public function index(ShopSpuRequest $request)
    {
        $query = ShopSpu::query()
            ->where("status", Common::STATUS_UP)
            ->where("shop_id", $request->shop_id)
            ->where("stock", ">", 0);

        if ($request->cat_id) $query->where("cat_id", $request->cat_id);

        $spus = $query->orderBy("is_signboard", "desc")
            ->orderBy('sort')
            ->orderBy('id', 'desc')
            ->paginate($request->input('page_size', $this->pageSize));

        $result = [
            'total' => $spus->total(),
            'has_next' => $spus->hasMorePages(),
            'data' => [],
        ];

        foreach ($spus as $spu) {
            $result['data'][] = $this->formatGoods($spu);
        }

        return $this->success('success', $result);
    }

    private function formatGoods($goods)
    {
        return [
            'id' => $goods->id,
            'shop_id' => $goods->shop_id,
            'cat_id' => $goods->cat_id,
            'name' => $goods->name,
            'cover' => $goods->cover ? img_url($goods->cover) : "",
            'description' => $goods->description,
            'price' => $goods->price,
            'discount_price' => $goods->discount_price,
            'sell_tags' => $goods->sell_tags ?: [],
            'stock' => $goods->stock,
            'sales' => $goods->sales,
            'is_signboard' => $goods->is_signboard,
            'created_at' => $goods->created_at,
            'updated_at' => $goods->updated_at,
        ];
    }
}
