<?php

namespace App\Console\Commands;


use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderProfile;
use App\Models\User;
use App\Services\EasemobService;
use App\Services\WukongIMService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redis;
use App\Jobs\DispatchOrderNotify;
use App\Services\CommonService;
use App\Services\O2oErrandOrderService;
use App\Services\Amap\GaodeService;

class FixData extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix_data';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '修数据';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        
        $res = (new GaodeService())->electrobike(strval('120.019292'), strval('30.278529'), strval('120.027567'), strval('30.290877'));

        dd($res);
    //     $point = ' {"lng":"108.819643","lat":"34.251988"}';
    //     $point = json_decode($point, true);
    //     Log::info(json_encode($point));
    //     $site = app(CommonService::class)->getPointSite($point);
    //     dd($site);

    //     $order = O2oErrandOrder::query()->find(432);
    //     $service = new O2oErrandOrderService();
    //     $service->refundOrder('3020250324225959612090', '测试退款', '', true);

    //     dd(123);


    //     // 测试发送消息
    //     $user = User::query()->first();
    //     if (!$user) {
    //         $this->error('No user found');
    //         return Command::FAILURE;
    //     }

    //     $wukongService = app(WukongIMService::class);

    //     // 1. 同步并统计消息
    //     // $messageStats = $this->syncMessages($user, $wukongService);
        
    //     // 2. 测试发送普通文本消息
    //     $textResult = $wukongService->sendPrivateMessage($user, "哈哈哈哈");
    //     $this->info('Text message sent: ' . ($textResult ? 'success' : 'failed'));

    //     // 3. 测试发送派单消息(自定义消息)
    //     $customResult = $wukongService->sendCustomMessage($user, [
    //         'type' => 9999,
    //         'cmd' => 'dispatch_order',
    //         'param' => [
    //             'order_no' => '3020241221210723140530',
    //             'dispatch_order_id' => 424,
    //             'create_time' => now()->format('Y-m-d H:i:s'),
    //             'close_time' => now()->addMinutes(5)->format('Y-m-d H:i:s')
    //         ]
    //     ]);
    //     $this->info('Custom message sent: ' . ($customResult ? 'success' : 'failed'));

    //    // 4. 测试发送图片消息
    //     $imageResult = $wukongService->sendCustomMessage($user, [
    //         'type' => 2, // 图片消息类型
    //         'content' => [
    //             'width' => 100,
    //             'height' => 100,
    //             'url' => 'https://storage.fengqishi.com.cn/resource/user_default_logo.png'
    //         ]
    //     ]);
    //     $this->info('Image message sent: ' . ($imageResult ? 'success' : 'failed'));

    //     return Command::SUCCESS;

    // 创建订单通知频道
    $this->info('开始创建订单通知频道...');

    // $wukongService = app(WukongIMService::class);

    // $channelName = 'order_notification'; // 可以根据需要修改频道名称
    // $description = '系统订单通知频道 - 创建于 ' . date('Y-m-d H:i:s');

    // $channelId = $wukongService->createOrderNotificationChannel($channelName, $description);

    // if ($channelId) {
    //     $this->info('订单通知频道创建成功!');
    //     $this->info('频道ID: ' . $channelId);
    //     $this->info('频道名称: ' . $channelName);
        
    //     // 可选：将频道ID保存到配置或数据库中
    //     // 例如，可以将频道ID保存到Redis或数据库中，以便其他地方使用
    //     Redis::set('order_notification_channel_id', $channelId);
    //     $this->info('频道ID已保存到Redis中，键名: order_notification_channel_id');
        
    //     // 可选：添加管理员用户到频道
    //     // $adminUsers = User::whereIn('role', ['admin', 'manager'])->get();
    //     // $result = $wukongService->addUsersToChannel($channelId, $adminUsers);
    //     // $this->info('添加管理员到频道: ' . ($result ? '成功' : '失败'));
        
    //     // 可选：发送测试消息
    //     $testOrderData = [
    //         'order_id' => 'TEST_' . time(),
    //         'status' => 'created',
    //         'amount' => 100.00,
    //         'created_at' => date('Y-m-d H:i:s')
    //     ];
    //     $testMessage = '这是一条测试订单通知消息';
    //     $result = $wukongService->sendOrderNotification($channelId, $testOrderData, $testMessage);
    //     $this->info('发送测试消息: ' . ($result ? '成功' : '失败'));
    // } else {
    //     $this->error('订单通知频道创建失败!');
    // }

    // 测试发送订单通知消息
    $this->info('开始测试发送订单通知消息...');

    $wukongService = app(WukongIMService::class);

    // 从Redis获取之前创建的频道ID
    $channelId  = 'order_notification';

    // 准备订单数据
    $orderData = [
        'order_id' => 'ORDER_' . time(),
        'order_no' => '3020' . date('YmdHis') . rand(100000, 999999),
        'status' => 'created',
        'amount' => rand(1000, 50000) / 100,
        'user_name' => '测试用户_' . rand(1000, 9999),
        'created_at' => date('Y-m-d H:i:s'),
        'items' => [
            [
                'name' => '商品1',
                'price' => rand(500, 2000) / 100,
                'quantity' => rand(1, 5)
            ],
            [
                'name' => '商品2',
                'price' => rand(500, 2000) / 100,
                'quantity' => rand(1, 5)
            ]
        ]
    ];

    // 消息内容
    $message = sprintf(
        "新订单通知: 订单号 %s, 金额 ¥%.2f, 用户: %s", 
        $orderData['order_no'], 
        $orderData['amount'], 
        $orderData['user_name']
    );

    $this->info('发送订单通知消息到频道ID: ' . $channelId);
    $this->info('消息内容: ' . $message);
    $this->info('订单数据: ' . json_encode($orderData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT));

    // 发送通知
    $result = $wukongService->sendOrderNotification($channelId, $orderData, $message);

    if ($result) {
        $this->info('订单通知发送成功!');
    } else {
        $this->error('订单通知发送失败!');
        
        // 可选: 如果失败，尝试使用备用方法
        $this->info('尝试使用自定义消息API发送...');
        $customMessageData = [
            'type' => 10,
            'content' => [
                'text' => $message,
                'order' => $orderData
            ],
            'extra' => [
                'notification_type' => 'order',
                'time' => time()
            ]
        ];
        
        // 获取一个测试用户
        $testUser = User::query()->first();
        if ($testUser) {
            $backupResult = $wukongService->sendCustomMessage($testUser, $customMessageData);
            $this->info('备用方法发送结果: ' . ($backupResult ? '成功' : '失败'));
        } else {
            $this->warn('未找到测试用户，无法使用备用方法发送');
        }
    }

    return Command::SUCCESS;
    }

    /**
     * 同步并统计消息
     * @param User $user
     * @param WukongIMService $wukongService
     * @return array 返回消息统计结果
     */
    private function syncMessages(User $user, WukongIMService $wukongService): array
    {
        $this->info('Testing conversation sync...');
        
        $totalMessages = 0;
        $recentMessages = 0;
        $historyMessages = 0;

        $result = $wukongService->syncConversation($user);
        if ($result === null) {
            $this->error('Conversation sync failed');
            return [
                'recent' => 0,
                'history' => 0,
                'total' => 0
            ];
        }

        $this->info('Conversation sync result:');
        foreach ($result as $conversation) {
            $this->info("\nConversation with channel_id: " . $conversation['channel_id']);
            $this->info("Unread messages: " . $conversation['unread']);
            
            // 获取最近消息
            if (isset($conversation['recents'])) {
                $count = count($conversation['recents']);
                $recentMessages += $count;
                $this->info("Recent messages in this conversation: " . $count);
                
                foreach ($conversation['recents'] as $message) {
                    $this->info("\nMessage from: " . $message['from_uid']);
                    $this->info("Time: " . date('Y-m-d H:i:s', $message['timestamp']));
                    if (isset($message['decoded_payload'])) {
                        $this->info("Decoded content: " . json_encode($message['decoded_payload'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    }
                }
            }

            // 获取历史消息
            $historyMessages += $this->getChannelHistoryMessages($user, $wukongService, $conversation);
        }

        $totalMessages = $recentMessages + $historyMessages;
        
        $this->info("\n=== Message Count Summary ===");
        $this->info("Recent messages: " . $recentMessages);
        $this->info("History messages: " . $historyMessages); 
        $this->info("Total messages: " . $totalMessages);

        return [
            'recent' => $recentMessages,
            'history' => $historyMessages,
            'total' => $totalMessages
        ];
    }

    /**
     * 获取频道的历史消息
     * @param User $user
     * @param WukongIMService $wukongService
     * @param array $conversation
     * @return int 返回获取到的消息数量
     */
    private function getChannelHistoryMessages(User $user, WukongIMService $wukongService, array $conversation): int
    {
        $this->info("\nTesting message sync for channel " . $conversation['channel_id']);
        
        $minMessageSeq = 0;
        $hasMoreMessages = true;
        $messageCount = 0;
        
        while ($hasMoreMessages) {
            $messages = $wukongService->getChannelMessages(
                $user,
                $conversation['channel_id'],
                $conversation['channel_type'],
                0,  // start_message_seq
                $minMessageSeq,  // end_message_seq
                100,  // 增加单次获取的消息数量
                1  // pull_mode: 1 向上拉取
            );

            if ($messages === null) {
                $this->error('Channel message sync failed');
                break;
            }

            if (empty($messages['messages'])) {
                $this->info('No more messages found in channel');
                $hasMoreMessages = false;
            } else {
                $count = count($messages['messages']);
                $messageCount += $count;
                $this->info('Found ' . $count . ' messages in this batch');
                
                // 更新最小消息序号用于下次查询
                foreach ($messages['messages'] as $message) {
                    if ($minMessageSeq == 0 || $message['message_seq'] < $minMessageSeq) {
                        $minMessageSeq = $message['message_seq'];
                    }
                    
                    $this->info("\nMessage ID: " . $message['message_id']);
                    $this->info("Message Seq: " . $message['message_seq']);
                    $this->info("From: " . $message['from_uid']);
                    $this->info("Time: " . date('Y-m-d H:i:s', $message['timestamp']));
                    if (isset($message['decoded_payload'])) {
                        $this->info("Decoded content: " . json_encode($message['decoded_payload'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE));
                    }
                }
                
                // 如果获取的消息数小于请求的数量,说明没有更多消息了
                if ($count < 100) {
                    $hasMoreMessages = false;
                }
            }
        }

        return $messageCount;
    }

    public function uploadLocation()
    {
        // 119.99,30.29
        // 120.04,30.25
        $riders = Rider::query()->get();
        foreach ($riders as $rider) {
            $lng = random_int(11999,12004);
            $lat = random_int(3025,3029);
            Redis::geoadd(Location::LOCATION_BELONG_RIDER, fentoyuan($lng), fentoyuan($lat), $rider->id);
        }
    }

    public function createRiders()
    {
        $users = User::with('rider')->get();
        foreach ($users as $user) {
            if (!$user->rider) {
                try {
                    // 骑手记录初始化
                    $rider = new Rider();
                    $rider->user_id = $user->id;
                    $rider->name = $user->nickname;
                    $rider->status = 0;
                    $rider->phone = $user->phone;
                    $rider->avatar = $user->avatar;
                    $rider->level = 1;
                    $rider->verified = 2;
                    $rider->health_status = 0;
                    $rider->transport_status = 0;
                    $rider->save();

                    // 初始化骑手信息
                    $profile = new RiderProfile();
                    $profile->user_id = $rider->user_id;
                    $profile->save();

                    $account = new RiderAccount();
                    $account->amount = 0;
                    $account->account_type = RiderAccount::Amount;
                    $account->user_id = $rider->user_id;
                    $account->save();

                    $account = new RiderAccount();
                    $account->amount = 0;
                    $account->account_type = RiderAccount::Bzj;
                    $account->user_id = $rider->user_id;
                    $account->save();

                    try {
                        $easemobService = new EasemobService('easemob_pt');
                        // 查询是否注册环信
                        try {
                            $easemob = app('easemob_pt')->getUser($user->easemob_rider_id);
                        } catch (\Exception $e) {
                            $easemob = null;
                        }
                        if (!$easemob) {
                            app('easemob_pt')->authorizationRegistration($user->easemob_rider_id);
                            app('easemob_pt')->addFriend($user->easemob_rider_id, EasemobService::PT_SYSTEM_USER);
                            $easemobService->initRUserInfo($apply->user);
                        }
                    } catch (\Exception $e) {
                        Log::error('骑手注册环信报错' . PHP_EOL . $e->getMessage());
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }
        }
    }
}
