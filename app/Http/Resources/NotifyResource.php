<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;

class NotifyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $isSettled = false;
        if (!($this->whenLoaded('logs') instanceof MissingValue)) {
            if($this->logs->count() > 0) {
                $isSettled = $this->logs[0]->is_settled;
            }
        }
        return [
            'id' => $this->id,
            'title' => $this->title,
            'cover' => $this->cover ? img_url($this->cover) : "",
            'type' => $this->type,
//            'content' => $this->content,
            'is_important' => $this->is_important,
            "link" => route('notify', $this->id),
            "is_settled" => $isSettled,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
