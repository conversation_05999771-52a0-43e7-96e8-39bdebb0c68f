<?php

namespace App\Http\Controllers\Api;

use App\Models\RiderApply;
use App\Models\SystemConfig;
use App\Services\UserService;
use Illuminate\Http\Request;

class RiderApplyController extends Controller
{
    public function store(Request $request, UserService $service)
    {
        $apply = RiderApply::query()->where('user_id', $request->user()->id)->first();
        if (!$apply) {
            $apply = new RiderApply();
            $apply->user_id = $request->user()->id;
            $apply->status = 0;
            $apply->remark = '';
            $apply->save();
        } else {
            if ($apply->status == 1) {
                $this->errorResponse(500, '你已经申请通过了');
            }

            if ($apply->status == 2) {
                $apply->remark = '';
                $apply->status = 0;
                $apply->save();
            }
        }

        // if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_RIDER_AUTO_APPLY) && $apply->status == 1) {
            $service->upToRider($apply->id);
        // }

        return $this->success('success', []);
    }
}
