<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class RefundOrderRequest extends FormRequest
{
    public function rules()
    {
        return [
            'order_id' => 'required|integer|min:1',
            'refund_reason' => 'required',
        ];
    }

    public function attributes()
    {
        return [
            'order_id' => "订单",
            'refund_reason' => '退款原因',
        ];
    }
}
