<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class TransportTool extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'type',
        'card_type',
        'card_no',
        'image',
        'image2',
        'status'
    ];
}
