<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\ShopSpuCat;
use App\Models\Common;
use App\Models\Shop;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Config;

class ShopSpuCatController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new ShopSpuCat(), function (Grid $grid) {
            $grid->model()->with(["shop", "parent"])->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->disableEditButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->fixColumns(2);

            $grid->column("id");
            $grid->column('name');
            $grid->column('cover')->image(Config::get('app.img_url_prefix'));
            $grid->column('shop.name', "店铺");
            $grid->column('parent.name', "父类目");
            $grid->column('sort')->sortable();
            $grid->column('status')->using(Common::StatusMap)->label(Common::StatusLabelMap);
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('name')->width(2);
                $filter->in('shop_id')->multipleSelect(Shop::query()->pluck("name", "id"))->width(2);
                $filter->in('status')->multipleSelect(Common::StatusMap)->width(2);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new ShopSpuCat(), function (Form $form) {
            $form->display('id');
            $form->text('name')->required();
            $form->image('cover')->uniqueName()->autoUpload()->autoSave(false);
            $form->select('shop_id')->options(Shop::query()->pluck("name", "id"))->required();
            $form->select('pid')->options(\App\Models\ShopSpuCat::query()->pluck("name", "id"));
            $form->select('status')->options(Common::StatusMap)->default(Common::STATUS_INIT)->required();
            $form->number('sort')->default(0)->attribute('min', 0)->required();
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
