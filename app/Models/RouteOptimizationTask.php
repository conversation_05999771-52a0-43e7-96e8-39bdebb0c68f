<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\Pivot;

class RouteOptimizationTask extends Pivot
{
    /**
     * 指示模型是否应该被标记时间戳
     *
     * @var bool
     */
    public $timestamps = true;

    /**
     * 与模型关联的表名
     *
     * @var string
     */
    protected $table = 'route_optimization_tasks';

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'route_id',
        'task_id',
        'sequence',
        'created_at',
        'updated_at'
    ];

    /**
     * 获取关联的路径规划
     */
    public function route()
    {
        return $this->belongsTo(RouteOptimization::class, 'route_id');
    }

    /**
     * 获取关联的任务
     */
    public function task()
    {
        return $this->belongsTo(Task::class, 'task_id');
    }
} 