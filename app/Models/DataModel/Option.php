<?php


namespace App\Models\DataModel;


class Option
{
    const TypeTransport = 'transport';
    const TypeEdu = 'edu';
    const TypeJob = 'job';
    const TypeMarriage = 'marriage';
    const TypeChildren = 'children';

    const TypeTransportOptions = [
        1 => '我还没有车',
        2 => '自行车',
        3 => '电动车',
        4 => '摩托车',
        5 => '三轮车',
        6 => '四轮机动车',
    ];


    const TypeEduOptions = [
        1 => '初中及以下',
        2 => '中专',
        3 => '高中',
        4 => '大专',
        5 => '本科',
        6 => '硕士及以上'
    ];

    const TypeJobOptions = [
        1 => '普工/技工',
        2 => '家政保洁/保安/酒店',
        3 => '美容/美发',
        4 => '保健/健身/旅游',
        5 => '餐饮',
        6 => '物流/仓储',
        7 => '人事/行政/司机',
        8 => '教育培训/保险/房产中介',
        9 => '销售/采购/客服'
    ];

    const TypeMarriageOptions = [
        1 => '未婚',
        2 => '已婚'
    ];

    const TypeChildrenOptions = [
        0 => 0,
        1 => 1,
        2 => 2,
        3 => 3
    ];

}
