<?php

namespace App\RunningAdmin\Controllers;

use App\Models\AdminSiteRel;
use App\Models\Common;
use App\Models\Rider;
use App\Models\User;
use App\RunningAdmin\Actions\Grid\OrderTransfer;
use App\RunningAdmin\Repositories\O2oErrandOrder;
use App\RunningAdmin\RowActions\OrderDispatch;
use App\RunningAdmin\RowActions\OrderFinish;
use App\RunningAdmin\RowActions\OrderRefund;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Tab;
use Dcat\Admin\Widgets\Table;
use Illuminate\Http\Request;

class O2oErrandOrderController extends AdminController
{

    protected $status;
    protected $query;

    public function __construct(Request $request)
    {
        $this->query = $request->all();
        $this->status = $request->status;
        if (!$this->status) $this->status = 1;
        return $this;
    }

    private function getOrderCountByStatus($status): int
    {
        $query = \App\Models\O2oErrandOrder::query();
        switch ($status) {
            case 1:
                $query->whereIn("order_status", [\App\Models\O2oErrandOrder::STATUS_PAID])->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                break;
            case 2:
                $query->where("order_status", ">", \App\Models\O2oErrandOrder::STATUS_PAID)->where("order_status", "<", \App\Models\O2oErrandOrder::STATUS_FINISH)->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                break;
            case 3:
                $query->whereIn("order_status", [\App\Models\O2oErrandOrder::STATUS_FINISH])->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                break;
            case 4:
                $query->where("refund_status", "<>", \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                break;
            case 5:
                $query->where("order_status", \App\Models\O2oErrandOrder::STATUS_CANCEL);
                break;
        }
        if (!Admin::user()->isAdministrator()) {
            $siteIds = AdminSiteRel::query()->where('user_id', Admin::user()->id)->pluck('site_id');
            $query->whereIn('site_id', $siteIds);
        }
        foreach (["type", "user_id", "rider_id", "order_status"] as $key) {
            if (isset($this->query[$key]) && $this->query[$key]) {
                $query->whereIn($key, $this->query[$key]);
            }
        }
        foreach (["order_no", "pickup_address", "deliver_address"] as $key) {
            if (isset($this->query[$key]) && $this->query[$key]) {
                $query->where($key, "like", "%" . $this->query[$key] . "%");
            }
        }
        foreach (["create_time", "paid_at", "refund_at"] as $key) {
            if (isset($this->query[$key]) && $this->query[$key]) {
                $time = $this->query[$key];
                if (isset($time['start']) && isset($time['end'])) {
                    $query->whereBetween($key, [$time['start'], $time['end']]);
                } elseif (isset($time['start'])) {
                    $query->where($key, '>=', $time['start']);
                } elseif (isset($time['end'])) {
                    $query->where($key, '<=', $time['end']);
                }
            }
        }

        return $query->count();
    }


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new O2oErrandOrder(), function (Grid $grid) {
            $grid->header(function () {
                $tab = Tab::make();
                $status = [
                    1 => "待接单",
                    2 => "配送中",
                    3 => "已完成",
                    4 => "售后",
                    5 => "已取消",
                ];
                foreach ($status as $k => $v) {
                    $q = $this->query;
                    $q["status"] = $k;
                    $tab->addLink(sprintf("%s(%d)", $v, $this->getOrderCountByStatus($k)), '?'.http_build_query($q), $this->status == $k ? true : false);
                }
                return $tab;
            });

            if (!Admin::user()->isAdministrator()) {
                $siteIds = AdminSiteRel::query()->where('user_id', Admin::user()->id)->pluck('site_id');
                $grid->model()->with(["user", "rider", "category", "goodsCategory", "site"])->whereIn('site_id', $siteIds)->when($this->status, function ($query) {
                    switch ($this->status) {
                        case 1:
                            $query->whereIn("order_status", [\App\Models\O2oErrandOrder::STATUS_PAID])->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                            break;
                        case 2:
                            $query->where("order_status", ">", \App\Models\O2oErrandOrder::STATUS_PAID)->where("order_status", "<", \App\Models\O2oErrandOrder::STATUS_FINISH)->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                            break;
                        case 3:
                            $query->whereIn("order_status", [\App\Models\O2oErrandOrder::STATUS_FINISH])->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                            break;
                        case 4:
                            $query->where("refund_status", "<>", \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                            break;
                        case 5:
                            $query->where("order_status", \App\Models\O2oErrandOrder::STATUS_CANCEL);
                            break;
                    }
                })->orderBy('id', 'desc');;
            } else {
                $grid->model()->with(["user", "rider", "category", "goodsCategory", "site"])
                    ->when($this->status, function ($query) {
                        switch ($this->status) {
                            case 1:
                                $query->whereIn("order_status", [\App\Models\O2oErrandOrder::STATUS_PAID])->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                                break;
                            case 2:
                                $query->where("order_status", ">", \App\Models\O2oErrandOrder::STATUS_PAID)->where("order_status", "<", \App\Models\O2oErrandOrder::STATUS_FINISH)->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                                break;
                            case 3:
                                $query->whereIn("order_status", [\App\Models\O2oErrandOrder::STATUS_FINISH])->where('refund_status', \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                                break;
                            case 4:
                                $query->where("refund_status", "<>", \App\Models\O2oErrandOrder::REFUND_STATUS_INIT);
                                break;
                            case 5:
                                $query->where("order_status", \App\Models\O2oErrandOrder::STATUS_CANCEL);
                                break;
                        }
                    })->orderBy('id', 'desc');
            }
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->setActionClass(Grid\Displayers\DropdownActions::class);
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->paid_at) {
                    if ($this->order_status < \App\Models\O2oErrandOrder::STATUS_FINISH){
                        //退款
                        if (in_array($this->refund_status, [\App\Models\O2oErrandOrder::REFUND_STATUS_INIT, \App\Models\O2oErrandOrder::REFUND_STATUS_FAIL])) {
                            $actions->append(new OrderRefund());
                        }

                        if($this->refund_status == \App\Models\O2oErrandOrder::REFUND_STATUS_INIT) {
                            if ($this->rider_id > 0) { //完成
                                $actions->append(new OrderFinish());
                            }else{ //派单
                                $url = route('admin.order.map', ['site_id' => $this->site_id, 'order_no' => $this->order_no]);
                                $actions->append("<a href='" . $url . "' target='_blank'> 人工派单</a>");
                            }
                        }

                        $actions->append(new OrderTransfer());
                    }
                }
            });

            $grid->column('order_no');
            $grid->column("type")->display(function () {
                $html = <<< HTML
<div style="margin: 5px">%s</div>
HTML;
                $append = "";
                if ($this->is_special) {
                    $append = <<< HTML
<img style="margin: 3px" src="https://storage.fengqishi.com.cn/resource/VIP.png" width="20px"/>
HTML;
                }
                return sprintf($html, common_admin_label(\App\Models\O2oErrandOrder::TypeMap[$this->type], $this->type) . $append);
            });
            $grid->column("order_status")->using(\App\Models\O2oErrandOrder::StatusMap)->label();
//            $grid->column("user.phone", "用户");
            $grid->column("rider", "骑手")->display(function () {
                if ($this->rider) {
                    $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px">%s</div>
    <div style="margin: 5px">%s</div>
</div>
HTML;
                    return sprintf($html, $this->rider->phone ?? "", $this->rider->name ?? "");
                }
                return "";
            });
            $grid->column('site.name', '站点');
//            $grid->column('goods_info', '商品信息')->display('详情')->modal(function (Grid\Displayers\Modal $modal) {
//                $modal->title("商品信息");
//                \Admin::style('.table td{padding: .85rem .55rem}');
//                $buyImgs = [];
//                if ($this->buy_imgs) {
//                    foreach ($this->buy_imgs as $v) {
//                        if ($v) $buyImgs[] = img_url($v);
//                    }
//                }
//                $data = [
//                    ['name' => '一级类目', 'value' => $this->category->title ?? ""],
//                    ['name' => '二级类目', 'value' => $this->goodsCategory->title ?? ""],
//                    ['name' => '商品描述', 'value' => $this->goods_desc],
//                    ['name' => '商品图片', 'value' => implode("，", img_url_array($this->goods_imgs))],
//                    ['name' => '购买凭证', 'value' => implode("，", $buyImgs)],
//                    ['name' => '商品价格/元', 'value' => fentoyuan($this->goods_price)],
//                    ['name' => '商品保价/元', 'value' => fentoyuan($this->goods_protected_price)],
//                    ['name' => '重量/kg', 'value' => $this->weight],
//                    ['name' => '体积/cm³', 'value' => $this->volume],
//                ];
//                return Table::make(['类目', '信息'], $data);
//            });
            $grid->column("pickup_info", '取货信息')->display(function () {
                $address = $this->pickup_address;
                if ($address) {
                    if (empty($this->pickup_name) && empty($this->pickup_phone)) $address = "指定店铺：" . $address;
                } else {
                    $address .= "就近购买";
                }
                return $address;
            })->expand(function () {
                \Admin::style('.table td{padding: .85rem .55rem}');
                $address = $this->pickup_address;
                if ($address) {
                    if (empty($this->pickup_name) && empty($this->pickup_phone)) $address = "指定店铺：" . $address;
                    $address .= "（经纬度：" . $this->pickup_lng . "，" . $this->pickup_lat . "）";
                } else {
                    $address .= "就近购买";
                }
                $data = [
                    ['name' => '联系人', 'value' => $this->pickup_name],
                    ['name' => '联系电话', 'value' => $this->pickup_phone],
                    ['name' => '地址', 'value' => $address],
                    ['name' => '取货码', 'value' => $this->pickup_code ?: "未开启"],
                    ['name' => '验证方式', 'value' => \App\Models\O2oErrandOrder::ValidateMap[$this->pickup_code_mode] ?? ""],
                ];
                return Table::make(['类目', '信息'], $data);
            });
            $grid->column("deliver_info", '收货信息')->display(function () {
                return $this->deliver_address;
            })->expand(function () {
                \Admin::style('.table td{padding: .85rem .55rem}');
                $data = [
                    ['name' => '联系人', 'value' => $this->deliver_name],
                    ['name' => '联系电话', 'value' => $this->deliver_phone],
                    ['name' => '地址', 'value' => $this->deliver_address . "（经纬度：" . $this->deliver_lng . "，" . $this->deliver_lat . "）"],
                    ['name' => '收货码', 'value' => $this->receive_code ?: "未开启"],
                    ['name' => '验证方式', 'value' => \App\Models\O2oErrandOrder::ValidateMap[$this->receive_code_mode] ?? ""],
                ];
                return Table::make(['类目', '信息'], $data);
            });
            $grid->column('buy_imgs')->display(function ($pictures) {
                return $pictures;
            })->image('', 100, 100);
            $grid->column("distance");
            $grid->column('order_amount_detail', '费用')->display(function () {
                return fentoyuan($this->actual_amount + $this->gratuity + $this->goods_protected_price);
            })->expand(function () {
                \Admin::style('.table td{padding: .85rem .55rem}');
                $data = [
                    ['name' => '基础配送费', 'value' => fentoyuan($this->freight)],
                    ['name' => '距离附加费', 'value' => fentoyuan($this->distance_price)],
                    ['name' => '特殊时段费', 'value' => fentoyuan($this->time_price)],
                    ['name' => '恶劣天气费', 'value' => fentoyuan($this->weather_price)],
                    ['name' => '重量附加费', 'value' => fentoyuan($this->weight_price)],
                    ['name' => '小费', 'value' => fentoyuan($this->gratuity)],
                    ['name' => '物品保费', 'value' => fentoyuan($this->goods_protected_price)],
                    ['name' => '优惠', 'value' => fentoyuan($this->coupon_amount)],
                    ['name' => '总额', 'value' => fentoyuan($this->actual_amount + $this->gratuity + $this->goods_protected_price)],
                ];
                return Table::make(['收费科目', '金额/元'], $data);
            });
//            $grid->column('pay_method')->using(Common::PayMethodMap)->label();
            $grid->column('refund_info', '退款')->display(function () {
                if ($this->refund_status == 0) {
                    $content = '-';
                } else {
                    $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px">状态： %s</div>
    <div style="margin: 5px">退款时间： %s</div>
    <div style="margin: 5px">退款金额： %s</div>
</div>
HTML;
                    $content = sprintf($html, common_admin_label(\App\Models\O2oErrandOrder::RefundStatusMap[$this->refund_status], $this->refund_status), $this->refund_at, fentoyuan($this->refund_amount));
                }

                return $content;
            });
            $grid->column("timeline", '时间线')->display(function () {
                return array_reverse($this->timeline)[0]['remark'];
            })->expand(function () {
                \Admin::style('.table td{padding: .85rem .55rem}');
                $data = [];
                foreach ($this->timeline as $v) {
                    $data[] = ['name' => $v['time'], 'value' => $v['remark']];
                }
                return Table::make(['时间', '操作'], $data);
            });
            $grid->column("appointment_time", "预约时间")->display(function () {
                $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px"><span style="color: green">起：</span> %s</div>
    <div style="margin: 5px"><span style="color: red">终：</span> %s</div>
</div>
HTML;
                return sprintf($html, substr($this->appointment_start_time, 5, 11), substr($this->appointment_end_time, 5, 11));
            });
            $grid->column('time_info', '时间')->display(function () {
                $content = "";
                switch ($this->order_status) {
                    case 10:
                        $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px"><span style="color: grey">下单</span> %s</div>
</div>
HTML;
                        $content = sprintf($html, $this->created_at);
                        break;
                    default:
                        $html = <<< HTML
<div style="display: flex;flex-direction:column">
    <div style="margin: 5px"><span style="color: grey">下单</span> %s</div>
    <div style="margin: 5px"><span style="color: green">支付</span> %s</div>
</div>
HTML;
                        $content = sprintf($html, $this->created_at, $this->paid_at);
                }
                return $content;
            });
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->in('type')->multipleSelect(\App\Models\O2oErrandOrder::TypeMap)->width(2);
                $filter->like('order_no')->width(3);
//                $filter->in('user_id')->multipleSelect(User::query()->pluck("name", "id"))->width(2);
//                $filter->in('rider_id')->multipleSelect(Rider::query()->pluck("name", "id"))->width(2);
//                $filter->like('pickup_address')->width(3);
//                $filter->like('deliver_address')->width(3);
                $filter->between('create_time')->datetime()->width(4);
                $filter->between('paid_at')->datetime()->width(4);
                $filter->between('refund_at')->datetime()->width(4);
//                $filter->in('order_status')->multipleSelect(\App\Models\O2oErrandOrder::StatusMap)->width(2);
            });
        });
    }

}
