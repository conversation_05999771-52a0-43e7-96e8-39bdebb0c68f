<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Cooperate extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id', 'company', 'type', 'province', 'city', 'district', 'address', 'name', 'phone',
    ];

    public function user(){
        return $this->belongsTo(User::class, "user_id");
    }
}
