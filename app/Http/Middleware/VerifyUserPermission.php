<?php

namespace App\Http\Middleware;

use App\Models\OpenApiAccount;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;


class VerifyUserPermission
{
    public function handle(Request $request, Closure $next)
    {
        $user = $request->user();

        if ($user && $user->status != 1) {
            return $this->errorResponse(401, '用户被禁用');
        }

        return $next($request);
    }

    public function errorResponse($statusCode, $message = null, $code = 0)
    {
        throw new HttpException($statusCode, $message, null, [], $code);
    }
}
