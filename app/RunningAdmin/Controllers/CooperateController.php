<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\Cooperate;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class CooperateController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Cooperate(), function (Grid $grid) {
            $grid->model()->with("user")->orderBy('id', 'desc');
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->disableActions();
            $grid->fixColumns(2);

            $grid->column("id");
            $grid->column('user.name', "用户");
            $grid->column('company');
            $grid->column('type')->label();
            $grid->column('province');
            $grid->column('city');
            $grid->column('district');
            $grid->column('address');
            $grid->column('name');
            $grid->column('phone');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('company')->width(3);
                $filter->like('name')->width(3);
                $filter->like('phone')->width(3);
            });
        });
    }
}
