# 骑手多路径规划API文档

本目录包含了骑手APP多路径规划功能的接口文档。

## 文档列表

1. `rider_route_api.json` - APIFox格式的接口定义，可直接导入到APIFox工具中
2. `rider_route_api.md` - Markdown格式的接口文档，方便在GitHub或其他文档系统中查看

## 如何使用APIFox文档

1. 打开APIFox应用
2. 选择"导入"选项
3. 选择"OpenAPI 3.0"格式
4. 选择`rider_route_api.json`文件导入
5. 导入后即可查看接口详情、进行测试等操作

## 接口概览

骑手多路径规划功能包含以下接口：

| 接口路径 | 方法 | 说明 |
|---------|------|-----|
| /rider/route/multi_task | POST | 获取骑手多任务的最优路径规划 |
| /rider/route/detail/{id} | GET | 获取路径规划详情 |
| /rider/route/save | POST | 保存用户选择的路径 |
| /rider/route/navigate | POST | 开始导航 |
| /rider/route/savings | GET | 查询路径优化节省信息 |

详细的接口参数和响应格式请参考具体的文档文件。

## 业务流程

1. 骑手APP调用`/rider/route/savings`检查当前是否有路径优化空间
2. 如果有优化空间，显示优化提示，骑手点击"立即优化"
3. 调用`/rider/route/multi_task`获取最优路径规划
4. 骑手查看优化结果后，调用`/rider/route/save`确认是否接受优化
5. 骑手点击"开始导航"，调用`/rider/route/navigate`开始导航

## 注意事项

- 所有接口需要骑手身份验证
- 路径规划算法确保同一订单的送货点一定排在取货点之后
- 系统会自动根据时间窗口和位置关联性选择需要优化的任务 