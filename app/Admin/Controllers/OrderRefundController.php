<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\OrderRefund;
use App\Admin\RowActions\RefundVerify;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Card;
use Dcat\Admin\Widgets\Table;

class OrderRefundController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new OrderRefund(), function (Grid $grid) {
            $grid->model()->with(['order', 'order.details'])->orderBy('id', 'desc');

            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->fixColumns(2);

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($this->refund_status == \App\Models\OrderRefund::REFUND_STATUS_APPLY){
                    $actions->append(new RefundVerify());
                }
            });

            $grid->column("id");
            $grid->column('no')->width(200);
            $grid->column('order.order_no', '订单号')->width(200);
            $grid->column('spu_info', '退款商品')->display('详情')->modal(function (Grid\Displayers\Modal $modal) {
                $modal->title("退款商品");
                \Admin::style('.table td{padding: .85rem .55rem}');
                $data = [];
                foreach($this->spu_info as $v){
                    foreach ($this->order->details as $item){
                        if ($v["spu_id"] == $item->spu_id) {
                            $data[] = [
                                $item->spu_name, fentoyuan($item->price), fentoyuan($item->discount_price), $item->quantity, $v["quantity"]
                            ];
                        }
                    }
                }
                return Table::make(['商品名称', '原价/元', '优惠价/元', '购买数量', "退款数量"], $data);
            })->width(100);
            $grid->column('refund_reason')->width(200);
            $grid->column('refund_remark')->width(200);
            $grid->column('退款凭证', "退款凭证")->display('查看')->modal(function (Grid\Displayers\Modal $modal) {
                $modal->title("退款凭证");
                $html = "";
                foreach ($this->refund_pics as $img) {
                    $html .= '<div style="display: inline-table;width: 32%;margin-right: 1%;"><img src="'.$img.'" width="100%" height="auto"></div>';
                }
                return Card::make('', $html);
            })->width(100);
            $grid->column('refund_amount')->display(function($value){
                return fentoyuan($value);
            });
            $grid->column('refund_status')->using(\App\Models\OrderRefund::RefundStatusMap)->label();
            $grid->column('verify_at');
            $grid->column('reject_reason')->width(200);
            $grid->column('refund_at');
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('no')->width(3);
                $filter->like('order.order_no', '订单号')->width(3);
                $filter->in('refund_status')->multipleSelect(\App\Models\OrderRefund::RefundStatusMap)->width(2);
                $filter->between('created_at')->datetime()->width(3);
            });
        });
    }
}
