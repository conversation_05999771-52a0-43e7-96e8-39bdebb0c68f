<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\LocationRequest;
use App\Jobs\PushRiderPos;
use App\Models\Location;
use App\Models\Rider;
use App\Services\RiderStatusService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;

class LocationController extends Controller
{
    public function store(LocationRequest $request, RiderStatusService $riderStatusService){
        $belongId = 0;
        switch ($request->type){
            case 1: //用户
                $belongId = $request->user()->id;
                Redis::geoadd(Location::GEO_KEY_PREFIX.$request->type, $request->lng, $request->lat, $belongId);
                break;
            case 2: //骑手
                $rider = Rider::query()->where("user_id", $request->user()->id)->first();
                if(!$rider) return $this->success("success", null);
                $belongId = $rider->id;
                
                // 更新骑手心跳
                $riderStatusService->updateHeartbeat($belongId);
                
                // 检查Redis中是否需要push位置
                $needPush = Redis::sismember('myt_push_riders', $belongId);
                if ($needPush) {
                    Redis::geoadd(Location::GEO_KEY_PREFIX.$request->type, $request->lng, $request->lat, $belongId);
                    dispatch(new PushRiderPos($belongId, $request->lng, $request->lat, 0));
                }
                break;
        }
        
        $current = Carbon::now();
        $data = Location::suffix($current->format("Ym"))->create([
            "belong_type" => Location::GEO_KEY_PREFIX.$request->type,
            "belong_id" => $belongId,
            "lng" => $request->lng,
            "lat" => $request->lat,
            "time" => $current->timestamp
        ]);
        
        return $this->success("success", null);
    }
}
