<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserAddress extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'user_id',
        'name',
        'tel',
        'province',
        'city',
        'county',
        'address_detail',
        'is_default',
        'area_code',
        'latitude',
        'longitude',
        'is_white_list',
        'address_remark'
    ];

    protected $appends = ['full_address'];

    protected $casts = ['is_default' => 'boolean', 'is_white_list' => 'boolean'];

    public function getFullAddressAttribute()
    {
        return $this->province . $this->city . $this->county . $this->address_detail;
    }
}
