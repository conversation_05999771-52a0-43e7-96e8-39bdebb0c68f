<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'mailgun' => [
        'domain' => env('MAILGUN_DOMAIN'),
        'secret' => env('MAILGUN_SECRET'),
        'endpoint' => env('MAILGUN_ENDPOINT', 'api.mailgun.net'),
        'scheme' => 'https',
    ],

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'socialite' => [
        'wechat' => [
            'client_id' => env('WEIXIN_KEY'),
            'client_secret' => env('WEIXIN_SECRET'),
            'redirect' => env('WEIXIN_REDIRECT_URI'),
        ],
    ],

    'amap' => [
        'key' => env('GAODE_KEY'),
    ],

    'map_key' => [
        'gaode' => env('GAODE_KEY', '83baebbe6e057936de8753fdbdc14950'),
        'baidu' => env('BAIDU_KEY', 'QyDt9UULFw27D1Mmq6sBPG72YAgs0iXv'),
        'tencent' => env('TENCENT_KEY', '7HEBZ-RIJ3I-FGLG3-U4SBR-6TZGV-H7BUV'),
    ],

    'jpush' => [
        // 极光 app kye
        'app_key' => env('JPUSH_APP_KEY'),
        // 极光 master secret
        'master_secret' => env('JPUSH_MASTER_SECRET'),
        // 仅对iOS有效 开发环境设置为false 生产环境设置为true
        'apns_production' => env('JPUSH_APNS_PRODUCTION', false),
        // 接口请求日志文件 为 null 不记录日志
        'log_file' => storage_path('logs/jpush.log'),
    ],

    'wukong_im' => [
        'base_url' => [
            'local' => env('WUKONG_IM_LOCAL_URL', 'http://localhost:5001'),
            'production' => env('WUKONG_IM_PRODUCTION_URL', 'http://*************:5001'),
        ],
    ],

    'kefu' => [
        'base_url' => env('KEFU_BASE_URL', 'https://kefu.fengqishi.com.cn'),
        'customer_id' => env('KEFU_CUSTOMER_ID', 1),
        'secret_key' => env('KEFU_SECRET_KEY', 'yuqishi'),
    ],

];
