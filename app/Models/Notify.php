<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Notify extends Model
{
    use HasFactory, HasDateTimeFormatter, SoftDeletes;

    const TYPE_NOTIFY = 1;
    const TYPE_ACTIVITY = 2;
    const TypeMap = [
        self::TYPE_NOTIFY => '通知',
        self::TYPE_ACTIVITY => '活动',
    ];

    protected $casts = ['is_important' => 'boolean'];

    protected $fillable = [
        'type', 'title', 'cover', 'content', 'is_important'
    ];

    public function logs(){
        return $this->hasMany(NotifyLog::class, "notify_id");
    }
}
