<?php

namespace App\Jobs;

use App\Models\O2oErrandOrder;
use App\Services\MaiYaTianService;
use App\Services\WrcService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

// 代表这个类需要被放到队列中执行，而不是触发时立即执行
class PushRiderPos implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $riderId;
    protected $lng;
    protected $lat;

    public function __construct($riderId, $lng, $lat, $delay)
    {
        $this->riderId = $riderId;
        $this->lng = $lng;
        $this->lat = $lat;
        // 设置延迟的时间，delay() 方法的参数代表多少秒之后执行
        $this->delay($delay);
    }

    // 定义这个任务类具体的执行逻辑
    // 当队列处理器从队列中取出任务时，会调用 handle() 方法
    public function handle()
    {
        $orders = O2oErrandOrder::query()->whereIn("app_key", [O2oErrandOrder::APP_KEY_MYT, O2oErrandOrder::APP_KEY_WRC])->where("rider_id", $this->riderId)
            ->whereIn("order_status", [26, 28, 30])->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT)
            ->get();
        if ($orders->count() <= 0) {
            return;
        }
        foreach ($orders as $order) {
            try {
                switch ($order->app_key) {
                    case O2oErrandOrder::APP_KEY_MYT:
                        (new MaiYaTianService())->locationChange($order->user_id, $order->order_no, $order->out_order_no, $order->getMytStatus(), $this->lng, $this->lat);
                        break;
                    case O2oErrandOrder::APP_KEY_WRC:
                        (new WrcService())->locationChange($order->user_id, $order->order_no, $order->out_order_no, $order->getMytStatus(), $this->lng, $this->lat);
                        break;
                }

            } catch (\Exception $e) {
                Log::error($order->app_key."-快递轨迹回传失败：" . $e->getMessage());
            }
        }
    }
}
