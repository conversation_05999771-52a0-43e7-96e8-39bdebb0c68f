<?php

namespace App\Console\Commands;

use App\Services\RouteOptimizationService;
use Illuminate\Console\Command;

class TestRouteOptimization extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'route:test {--orders=3 : 订单数量} {--mode=constrained : 规划模式 (optimized/constrained)}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '测试骑手多订单路径规划功能';

    /**
     * 路径规划服务
     *
     * @var RouteOptimizationService
     */
    protected $routeOptimizationService;

    /**
     * 构造函数
     *
     * @param RouteOptimizationService $routeOptimizationService
     */
    public function __construct(RouteOptimizationService $routeOptimizationService)
    {
        parent::__construct();
        $this->routeOptimizationService = $routeOptimizationService;
    }

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle()
    {
        $orderCount = $this->option('orders');
        $mode = $this->option('mode');

        $this->info("开始测试骑手多订单路径规划功能 (订单数量: $orderCount, 模式: $mode)");

        // 模拟骑手当前位置 - 以上海为例
        $riderLocation = [
            'lat' => 31.230416,  // 上海
            'lng' => 121.473701
        ];

        // 生成随机订单进行测试
        $orders = $this->generateRandomOrders($orderCount, $riderLocation);
        
        // 打印订单信息
        $this->info("订单信息:");
        foreach ($orders as $index => $order) {
            $this->info("订单 #" . ($index + 1) . " (ID: " . $order['id'] . "):");
            $this->info("  取货点: 纬度 " . $order['pickup']['lat'] . ", 经度 " . $order['pickup']['lng']);
            $this->info("  送货点: 纬度 " . $order['delivery']['lat'] . ", 经度 " . $order['delivery']['lng']);
        }

        // 计算路径规划
        $startTime = microtime(true);
        
        if ($mode === 'optimized') {
            $routeDetails = $this->routeOptimizationService->getRouteDetails($riderLocation, $orders);
            $route = $routeDetails['route'];
            $totalDistance = $routeDetails['total_distance'];
            $estimatedTime = $routeDetails['estimated_time'];
        } else {
            $route = $this->routeOptimizationService->getConstrainedRoute($riderLocation, $orders);
            // 计算总距离和时间
            $totalDistance = 0;
            $estimatedTime = 0;
            for ($i = 0; $i < count($route) - 1; $i++) {
                $from = $route[$i]['lat'] . ',' . $route[$i]['lng'];
                $to = $route[$i + 1]['lat'] . ',' . $route[$i + 1]['lng'];
                
                $response = app()->make('App\\Services\\MapService')->distanceMatrix($from, $to);
                
                if (isset($response['result']['rows'][0]['elements'][0]['distance'])) {
                    $totalDistance += $response['result']['rows'][0]['elements'][0]['distance'];
                    // 检查是否存在duration字段
                    if (isset($response['result']['rows'][0]['elements'][0]['duration'])) {
                        $estimatedTime += $response['result']['rows'][0]['elements'][0]['duration'];
                    }
                }
            }
        }
        
        $endTime = microtime(true);
        $executionTime = round($endTime - $startTime, 2);

        // 打印结果
        $this->info("\n规划结果:");
        $this->info("计算耗时: " . $executionTime . " 秒");
        $this->info("路径总距离: " . $totalDistance . " 米");
        $this->info("预计送达时间: " . $this->formatSeconds($estimatedTime) . " (秒)");
        
        $this->info("\n路径详情:");
        foreach ($route as $index => $point) {
            $type = $point['type'];
            $typeText = $type === 'rider' ? '骑手位置' : ($type === 'pickup' ? '取货点' : '送货点');
            $orderText = $point['order_id'] ? " (订单ID: " . $point['order_id'] . ")" : "";
            
            $this->info(($index + 1) . ". " . $typeText . $orderText);
        }

        return 0;
    }

    /**
     * 生成随机订单进行测试
     *
     * @param int $count 订单数量
     * @param array $centerPoint 中心点
     * @return array 随机订单数组
     */
    private function generateRandomOrders(int $count, array $centerPoint): array
    {
        $orders = [];
        
        for ($i = 0; $i < $count; $i++) {
            // 生成随机偏移量，范围约为5公里内
            $pickupLatOffset = $this->getRandomOffset(0.045);
            $pickupLngOffset = $this->getRandomOffset(0.045);
            $deliveryLatOffset = $this->getRandomOffset(0.045);
            $deliveryLngOffset = $this->getRandomOffset(0.045);
            
            $orders[] = [
                'id' => 'ORD-' . ($i + 1),
                'pickup' => [
                    'lat' => $centerPoint['lat'] + $pickupLatOffset,
                    'lng' => $centerPoint['lng'] + $pickupLngOffset
                ],
                'delivery' => [
                    'lat' => $centerPoint['lat'] + $deliveryLatOffset,
                    'lng' => $centerPoint['lng'] + $deliveryLngOffset
                ]
            ];
        }
        
        return $orders;
    }

    /**
     * 获取随机偏移量
     *
     * @param float $maxOffset 最大偏移量
     * @return float 随机偏移量
     */
    private function getRandomOffset(float $maxOffset): float
    {
        return (mt_rand(-1000, 1000) / 1000) * $maxOffset;
    }

    /**
     * 格式化秒数为时分秒
     *
     * @param int $seconds 秒数
     * @return string 格式化后的时间
     */
    private function formatSeconds(int $seconds): string
    {
        $hours = floor($seconds / 3600);
        $minutes = floor(($seconds % 3600) / 60);
        $secs = $seconds % 60;
        
        $result = '';
        if ($hours > 0) {
            $result .= $hours . '小时';
        }
        if ($minutes > 0) {
            $result .= $minutes . '分钟';
        }
        if ($secs > 0 || $result === '') {
            $result .= $secs . '秒';
        }
        
        return $result;
    }
} 