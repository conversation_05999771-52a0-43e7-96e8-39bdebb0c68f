<?php

namespace App\Models;

use Carbon\Carbon;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Pricing extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const TYPE_USER = 1;
    const TYPE_RIDER = 2;
    const TypeMap = [
        self::TYPE_USER => "用户",
        self::TYPE_RIDER => "骑手",
    ];

    const COMPARE_GT = 1;
    const COMPARE_EGT = 2;
    const COMPARE_LT = 3;
    const COMPARE_ELT = 4;
    const COMPARE_EQ = 5;
    const CompareMap = [
        self::COMPARE_GT => '>',
        self::COMPARE_EGT => '≥',
        self::COMPARE_LT => '<',
        self::COMPARE_ELT => '≤',
        self::COMPARE_EQ => '=',
    ];

    protected $casts = [
        'is_special' => 'boolean', 
        'distance_price' => 'json', 
        'period_price' => 'json',
        'use_yuqishi_pricing' => 'boolean',
        'normal_pricing' => 'json',
        'night_pricing' => 'json',
        'weather_normal_pricing' => 'json',
        'weather_night_pricing' => 'json'
    ];

    protected $fillable = [
        'type', 'title', 'is_special', 'site_id', 'base_price', 'distance_price', 'period_price', 'weather_price',
        'use_yuqishi_pricing', 'normal_pricing', 'night_pricing', 'weather_normal_pricing', 'weather_night_pricing'
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function site(){
        return $this->belongsTo(Site::class, "site_id");
    }

    protected static function boot()
    {
        parent::boot();
        static::saving(function ($model) {
            $has = Pricing::query()->where("type", $model->type)->where("site_id", $model->site_id)->where("is_special", $model->is_special)->first();
            if ($has) {
                if ($has->id != ($model->id ?? 0)) {
                    throw new \Exception("收费区域重复");
                }
            }
            if ($model->distance_price) {
                $data = [];
                foreach ($model->distance_price as $v) {
                    $data[] = ["egt" => floatval($v["egt"]), "base" => floatval($v["base"]), "unit" => floatval($v["unit"])];
                }
                $model->distance_price = collect($data)->sortBy('egt')->values()->all();
            }
            if ($model->period_price) {
                $data = [];
                foreach ($model->period_price as $v) {
                    if ($v["to"] <= $v["from"]) throw new \Exception("特殊时段费 的 结束时间要大于开始时间");
                    $data[] = ["ft" => intval($v["ft"]), "from" => $v["from"], "tt" => intval($v["tt"]), "to" => $v["to"], "price" => floatval($v["price"])];
                }
                $model->period_price = $data;
            }
        });
    }

    /**
     * 计算运费
     * @param int $siteId 站点ID
     * @param int $distance 距离，米
     * @param string $appointmentStartTime 时间，例：2023-01-18 08:36
     * @param bool $isSpecial 是否专人/全职
     * @param bool $isBadWeather 是否恶劣天气
     * @param float $weight 重量，公斤
     */
    public static function calculateFreight($type, $siteId, $distance, $appointmentStartTime, $isSpecial, $isBadWeather = false, $weight = 0): array
    {
        $res = ["freight" => 0, "distance_price" => 0, 'time_price' => 0, 'weather_price' => 0, 'weight_price' => 0];

        $pricing = Pricing::query()->where("type", $type)->where("site_id", $siteId)->where("is_special", $isSpecial)->first();
        if (!$pricing) return $res;
        
        // 如果启用了雨骑士新版本定价策略
        if ($pricing->use_yuqishi_pricing) {
            return self::calculateYuqishiFreight($pricing, $distance, $appointmentStartTime, $isBadWeather, $weight);
        }
        
        // 原有的定价逻辑
        $res["freight"] = yuantofen($pricing->base_price);
        
        if ($pricing->distance_price) {
            $distance = intval(ceil($distance / 1000));
            //[{"egt":2,"base":2,"unit":1},{"egt":4,"base":4,"unit":2}]
            $item = [];
            foreach ($pricing->distance_price as $v) {
                if ($v['egt'] > $distance) {
                    break;
                }
                $item = $v;
            }
            if ($item) {
                $res["distance_price"] = yuantofen($item["base"]) + ($distance - $item["egt"]) * yuantofen($item["unit"]);
            }
        }
        
        if (substr($appointmentStartTime, 0, 10) == Carbon::now()->format("Y-m-d")){
            $res["weather_price"] = yuantofen($pricing->weather_price);
        }
        
        if ($pricing->period_price) {
            $time = substr($appointmentStartTime, 11, 5);
            //[{"ft":2,"from":"00:00","tt":3,"to":"07:00","price":5}]
            foreach ($pricing->period_price as $v) {
                $isFrom = false;
                $isTo = false;
                switch ($v["ft"]) {
                    case self::COMPARE_GT:
                        if ($time > $v["from"]) $isFrom = true;
                        break;
                    case self::COMPARE_EGT:
                        if ($time >= $v["from"]) $isFrom = true;
                        break;
                    case self::COMPARE_LT:
                        if ($time < $v["from"]) $isFrom = true;
                        break;
                    case self::COMPARE_ELT:
                        if ($time <= $v["from"]) $isFrom = true;
                        break;
                    case self::COMPARE_EQ:
                        if ($time == $v["from"]) $isFrom = true;
                        break;
                }
                switch ($v["tt"]) {
                    case self::COMPARE_GT:
                        if ($time > $v["to"]) $isTo = true;
                        break;
                    case self::COMPARE_EGT:
                        if ($time >= $v["to"]) $isTo = true;
                        break;
                    case self::COMPARE_LT:
                        if ($time < $v["to"]) $isTo = true;
                        break;
                    case self::COMPARE_ELT:
                        if ($time <= $v["to"]) $isTo = true;
                        break;
                    case self::COMPARE_EQ:
                        if ($time == $v["to"]) $isTo = true;
                        break;
                }
                if ($isFrom && $isTo) {
                    $res["time_price"] = yuantofen($v["price"]);
                    break;
                }
            }
        }

        return $res;
    }
    
    /**
     * 雨骑士定价策略计算
     * @param Pricing $pricing 定价配置
     * @param int $distance 距离，米
     * @param string $appointmentStartTime 时间，例：2023-01-18 08:36
     * @param bool $isBadWeather 是否恶劣天气
     * @param float $weight 重量，公斤
     */
    private static function calculateYuqishiFreight($pricing, $distance, $appointmentStartTime, $isBadWeather, $weight): array
    {
        $res = ["freight" => 0, "distance_price" => 0, 'time_price' => 0, 'weather_price' => 0, 'weight_price' => 0];
        
        $time = substr($appointmentStartTime, 11, 5);
        $isNightTime = $time >= '22:00' || $time < '08:00'; // 夜间时间：22:00-次日8:00
        
        // 计算当前条件下的价格
        $currentPrice = self::calculatePriceByStrategy($pricing, $distance, $weight, $isNightTime, $isBadWeather);
        $res["freight"] = $currentPrice["freight"];
        $res["distance_price"] = $currentPrice["distance_price"];
        $res["weight_price"] = $currentPrice["weight_price"];
        
        // 如果是恶劣天气，计算恶劣天气加收费用
        if ($isBadWeather) {
            // 计算同样时间段正常天气的价格
            $normalWeatherPrice = self::calculatePriceByStrategy($pricing, $distance, $weight, $isNightTime, false);
            
            // weather_price = 恶劣天气总价 - 正常天气总价
            $badWeatherTotal = $currentPrice["freight"] + $currentPrice["distance_price"] + $currentPrice["weight_price"];
            $normalWeatherTotal = $normalWeatherPrice["freight"] + $normalWeatherPrice["distance_price"] + $normalWeatherPrice["weight_price"];
            $res["weather_price"] = $badWeatherTotal - $normalWeatherTotal;
        }
        
        return $res;
    }
    
    /**
     * 根据策略计算价格
     * @param Pricing $pricing 定价配置
     * @param int $distance 距离，米
     * @param float $weight 重量，公斤
     * @param bool $isNightTime 是否夜间时间
     * @param bool $isBadWeather 是否恶劣天气
     * @return array
     */
    private static function calculatePriceByStrategy($pricing, $distance, $weight, $isNightTime, $isBadWeather): array
    {
        $result = ["freight" => 0, "distance_price" => 0, "weight_price" => 0];
        
        // 根据时间段和天气条件选择对应的定价策略
        $pricingStrategy = null;
        if ($isNightTime && $isBadWeather) {
            $pricingStrategy = $pricing->weather_night_pricing; // 夜间+恶劣天气
        } elseif ($isNightTime && !$isBadWeather) {
            $pricingStrategy = $pricing->night_pricing; // 夜间+正常天气
        } elseif (!$isNightTime && $isBadWeather) {
            $pricingStrategy = $pricing->weather_normal_pricing; // 正常时间+恶劣天气
        } else {
            $pricingStrategy = $pricing->normal_pricing; // 正常时间+正常天气
        }
        
        if (!$pricingStrategy) {
            return $result;
        }
        
        $distanceKm = ceil($distance / 1000); // 转换为公里并向上取整
        
        // 根据距离阶梯计算价格
        // 新版本策略格式：[{"distance_range": "0-3", "base_price": 2, "extra_price": 1, "weight_pricing": [...] }, ...]
        foreach ($pricingStrategy as $rule) {
            if (strpos($rule['distance_range'], '+') !== false) {
                // 处理3+这种情况
                $minDistance = intval(str_replace('+', '', $rule['distance_range']));
                if ($distanceKm > $minDistance) {
                    $result["freight"] = yuantofen($rule['base_price']);
                    
                    // 计算超出基础距离的费用
                    if (isset($rule['extra_price']) && $rule['extra_price'] > 0) {
                        $extraDistance = $distanceKm - $minDistance;
                        $result["distance_price"] = yuantofen($extraDistance * $rule['extra_price']);
                    }
                    
                    // 计算重量费用
                    if (isset($rule['weight_pricing']) && $weight > 0) {
                        $result["weight_price"] = self::calculateWeightPrice($rule['weight_pricing'], $weight);
                    }
                    break;
                }
            } else {
                // 处理0-3这种范围
                $range = explode('-', $rule['distance_range']);
                $minDistance = intval($range[0]);
                $maxDistance = intval($range[1]);
                
                if ($distanceKm >= $minDistance && $distanceKm <= $maxDistance) {
                    // 新版本：支持范围内的阶梯计费
                    $result["freight"] = yuantofen($rule['base_price']);
                    
                    // 如果配置了extra_price，则在范围内也按距离计费
                    if (isset($rule['extra_price']) && $rule['extra_price'] > 0 && $distanceKm > $minDistance) {
                        $extraDistance = $distanceKm - $minDistance;
                        $result["distance_price"] = yuantofen($extraDistance * $rule['extra_price']);
                    }
                    
                    // 计算重量费用
                    if (isset($rule['weight_pricing']) && $weight > 0) {
                        $result["weight_price"] = self::calculateWeightPrice($rule['weight_pricing'], $weight);
                    }
                    break;
                }
            }
        }
        
        return $result;
    }
    
    /**
     * 计算重量价格
     * @param array $weightPricing 重量定价策略
     * @param float $weight 重量，公斤
     * @return int 重量费用，分
     */
    private static function calculateWeightPrice($weightPricing, $weight): int
    {
        if (!$weightPricing || !is_array($weightPricing)) {
            return 0;
        }
        
        foreach ($weightPricing as $rule) {
            if (strpos($rule['weight_range'], '+') !== false) {
                // 处理5+这种情况
                $minWeight = floatval(str_replace('+', '', $rule['weight_range']));
                if ($weight > $minWeight) {
                    $basePrice = yuantofen($rule['base_price']);
                    
                    // 计算超出基础重量的费用
                    if (isset($rule['extra_price']) && $rule['extra_price'] > 0) {
                        $extraWeight = $weight - $minWeight;
                        $extraPrice = yuantofen($extraWeight * $rule['extra_price']);
                        return $basePrice + $extraPrice;
                    }
                    return $basePrice;
                }
            } else {
                // 处理0-3, 3-5这种范围
                $range = explode('-', $rule['weight_range']);
                $minWeight = floatval($range[0]);
                $maxWeight = floatval($range[1]);
                
                if ($weight >= $minWeight && $weight <= $maxWeight) {
                    return yuantofen($rule['base_price']);
                }
            }
        }
        
        return 0;
    }
}
