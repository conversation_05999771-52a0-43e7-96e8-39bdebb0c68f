<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class IdCardOcrRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'image_url' => 'required|URL',
            'oct_type' => ['required', Rule::in([0, 1]),]
        ];
    }

    public function messages()
    {
        return [
            'image_url.required' => '身份证图片地址必须上传',
            'image_url.URL' => '请填写正确的身份证图片'
        ];
    }
}
