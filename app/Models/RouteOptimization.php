<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Factories\HasFactory;

class RouteOptimization extends Model
{
    use HasFactory;

    /**
     * 路径优化状态
     */
    const STATUS_PENDING = 'pending';   // 等待确认
    const STATUS_ACCEPTED = 'accepted'; // 已接受
    const STATUS_REJECTED = 'rejected'; // 已拒绝

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'rider_id',
        'total_distance',
        'estimated_time',
        'time_savings',
        'distance_savings',
        'percentage_time',
        'percentage_distance',
        'route_data',
        'waypoints_data',
        'status',
        'created_at',
        'updated_at'
    ];

    /**
     * 获取关联的骑手信息
     */
    public function rider()
    {
        return $this->belongsTo(Rider::class);
    }

    /**
     * 获取关联的任务
     */
    public function orders()
    {
        return $this->belongsToMany(O2oErrandOrder::class, 'route_optimization_orders', 'route_id', 'order_id')
            ->withTimestamps();
    }
} 