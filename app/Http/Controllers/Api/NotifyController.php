<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\NotifyResource;
use App\Models\Notify;
use Illuminate\Http\Request;

class NotifyController extends Controller
{
    public function index(Request $request)
    {
        $query = Notify::query()->with(["logs" => function ($query) use ($request) {
            $query->where('user_id', $request->user()->id ?? 0);
        }]);
        if ($request->type) $query->where("type", $request->type);
        if ($request->is_important) $query->where("is_important", true);
        if ($request->is_read) {
            $query->whereDoesntHave('logs', function ($query) use ($request) {
                $query->where('user_id', $request->user()->id ?? 0);
            });
        }
        $data = $query->orderBy("id", "desc")->paginate($request->input('page_size', $this->pageSize));
        $result = [
            'total' => $data->total(),
            'has_next' => $data->hasMorePages(),
            'data' => [],
        ];
        if ($data) {
            foreach ($data as $v) {
                $result['data'][] = new NotifyResource($v);
            }
        }
        return $this->success('success', $result);
    }
}
