<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller as BaseController;
use Symfony\Component\HttpKernel\Exception\HttpException;

class Controller extends BaseController
{
    /**
     * 客户端版本
     * @var string
     */
    protected $version;

    /**
     * 客户段系统 1--Android 2--IOS
     * @var string
     */
    protected $system;

    /**
     * 应用类型 1--APP 2--微信小程序 3--H5
     * @var
     */
    protected $appType;

    /**
     *
     * @var
     */
    protected $deviceType;

    /**
     * @var
     */
    protected $platform;
    /**
     *
     * @var
     */
    protected $deviceValue;


    protected int $pageSize = 20;


    public function __construct()
    {
        $this->initHeader();
    }

    private function initHeader()
    {
        $this->version = request()->header('version');
        $this->appType = request()->header('app-type');
        $this->system = request()->header('system');
        if ($this->system == 'Android') $this->system = 1;
        if ($this->system == 'IOS') $this->system = 2;
        $this->platform = request()->header('platform');
        $this->deviceType = request()->header('device-type');
        $this->deviceValue = request()->header('device-value');
    }

    public function errorResponse($statusCode, $message = null, $code = 0)
    {
        throw new HttpException($statusCode, $message, null, [], $code);
    }

    public function success($message, $data)
    {
        $returnData = [
            'message' => $message,
            'data' => $data,
            'code' => 0
        ];
        return response()->json($returnData);
    }


}
