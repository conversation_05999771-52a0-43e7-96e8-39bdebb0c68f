<?php

namespace App\Providers;

use App\Events\AfterRegister;
use App\Events\EarnestRechargeOrderPaid;
use App\Events\ImportWhiteList;
use App\Events\OrderCreated;
use App\Events\RechargeOrderPaid;
use App\Listeners\AddRiderEarnestAmount;
use App\Listeners\AddUserAmount;
use App\Listeners\HandleUserInfo;
use App\Listeners\HandleWhiteList;
use App\Listeners\PlaySoundOnOrderCreated;
use App\Listeners\RegisterEasemob;
use App\Listeners\RegisterWukongIM;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        AfterRegister::class => [
            // RegisterEasemob::class,
            HandleWhiteList::class,
            RegisterWukongIM::class,
        ],
        RechargeOrderPaid::class => [
            AddUserAmount::class
        ],
        EarnestRechargeOrderPaid::class => [
            AddRiderEarnestAmount::class
        ],
        ImportWhiteList::class => [
            HandleWhiteList::class
        ],
        OrderCreated::class => [
            PlaySoundOnOrderCreated::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     *
     * @return bool
     */
    public function shouldDiscoverEvents()
    {
        return false;
    }
}
