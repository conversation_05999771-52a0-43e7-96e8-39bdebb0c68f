<?php

namespace App\Admin\Actions\Form;

use App\Models\Order;
use App\Models\OrderRefund;
use App\Services\OrderService;
use Carbon\Carbon;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class RefundVerifyForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $refundId = $this->payload['id'] ?? null;
        $refund = OrderRefund::query()->find($refundId);
        if (!$refund) return $this->response()->error("该退款单无效");
        $refundStatus = intval($input['status'] ?? 1);
        $rejectReason = "";
        if ($refundStatus == 2) {
            $rejectReason = $input['reason'] ?? "";
            if (empty($rejectReason)) return $this->response()->error("请填写拒绝原因");
        }
        //'refund_status', 'verify_at', 'reject_reason'
        try {
            DB::beginTransaction();
            $refund->update(["refund_status" => $refundStatus, "verify_at" => Carbon::now(), "reject_reason" => $rejectReason]);
            Order::query()->where("id", $refund->order_id)->where("refund_type", ">", 0)->update(["refund_type" => $refundStatus+10]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->response()->error($e->getMessage());
        }
        app(OrderService::class)->refundToUser($refund);
        return $this->response()->success('审核成功')->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->radio('status', "审核结果")
            ->when(2, function (Form $form) {
                $this->textarea("reason", "拒绝原因")->rows(3);
            })
            ->options([
                1 => '通过',
                2 => '拒绝',
            ])
            ->default(1);
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'status' => 1,
            'reason' => "",
        ];
    }
}
