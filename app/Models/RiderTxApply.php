<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;

class RiderTxApply extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const ORDER_PREFIX = '50';

    const StatusMap = [
        0 => '待处理',
        1 => '审核通过',
        2 => '拒绝'
    ];

    const TransferStatusMap = [
        0 => '待打款',
        1 => '打款成功',
        2 => '打款失败',
    ];

    const AccountTypeMap = [
        1 => '支付宝',
        2 => '微信',
        3 => '银行卡'
    ];

    public function getAmountAttribute($amount)
    {
        return fentoyuan($amount);
    }

    public function user()
    {
        return $this->belongsTo(User::class);
    }

    public static function findAvailableNo()
    {
        // 订单流水号前缀
        $prefix = self::ORDER_PREFIX . date('YmdHis');
        for ($i = 0; $i < 10; $i++) {
            // 随机生成 6 位的数字
            $no = $prefix . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            // 判断是否已经存在
            if (!static::query()->where('order_no', $no)->exists()) {
                return $no;
            }
        }
        Log::warning('find order no failed');

        return false;
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::creating(function ($model) {
            // 如果模型的 no 字段为空
            if (!$model->order_no) {
                // 调用 findAvailableNo 生成订单流水号
                $model->order_no = static::findAvailableNo();
                // 如果生成失败，则终止创建订单
                if (!$model->order_no) {
                    return false;
                }
            }
        });
    }
}
