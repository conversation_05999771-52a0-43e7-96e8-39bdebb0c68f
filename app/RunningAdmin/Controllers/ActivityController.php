<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Forms\ActivityRule;
use App\RunningAdmin\Renderable\ActivityRuleTable;
use App\RunningAdmin\Repositories\Activity;
use Carbon\Carbon;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class ActivityController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Activity(), function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->column('id')->expand(function () {
                return ActivityRuleTable::make(['activity_id' => $this->id]);
            });
            $grid->column('name');
            $grid->column('image');
            $grid->column('status', '状态')->using(\App\Models\Activity::ActivityStatusMap)->label(
                [
                    0 => \Admin::color()->danger(),
                    1 => \Admin::color()->success()
                ]
            );
            $grid->column('type', '活动类型')->using(\App\Models\Activity::ActivityTypeMap)->label();
            $grid->column('activityRule', '活动规则')->display('活动规则')->modal(function (Grid\Displayers\Modal $modal) {
                // 标题
                $modal->title('活动规则');
                // 自定义图标
                $modal->icon('feather icon-edit');
                // 传递当前行字段值
                return ActivityRule::make()->payload(['activity_id' => $this->id]);
            });
            $grid->column('timeinfo', '活动有效期')->display(function () {
                if ($this->time_type == 1) {
                    return Carbon::parse($this->start_time)->format('Y-m-d') . '~' . Carbon::parse($this->end_time)->format('Y-m-d');
                } else {
                    $info = Carbon::parse($this->start_time)->format('Y-m-d') . '~' . Carbon::parse($this->end_time)->format('Y-m-d');
                    return $info . "</br>" . '注册后' . $this->day . '天内完成任务';
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('name')->width(4);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Activity(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('image');
            $show->field('status');
            $show->field('type');
            $show->field('start_time');
            $show->field('end_time');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Activity(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->image('image')->autoUpload()->uniqueName();
            $form->radio('status')->options(\App\Models\Activity::ActivityStatusMap)->default(1);
            $form->radio('type')->options(\App\Models\Activity::ActivityTypeMap)->default(1);
            $form->dateRange('start_time', 'end_time', '有效期');
            $form->radio('time_type', '时间类型')->options(\App\Models\Activity::TimeType)->default(1)
                ->when(2, function ($form) {
                    $form->number('day', '天数');
                });
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
