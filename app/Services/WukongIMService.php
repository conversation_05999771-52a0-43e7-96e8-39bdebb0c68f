<?php

namespace App\Services;

use App\Models\User;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WukongIMService
{
    protected $baseUrl;
    protected $systemUser = 'system_notice'; // 系统通知账号
    protected $orderChannel = 'order_notification'; // 订单通知频道
    
    public function __construct()
    {
        $env = app()->environment();
        $urls = config('services.wukong_im.base_url');
        
        // 如果是生产环境使用production的URL,否则使用local的URL
        $this->baseUrl = $env === 'production' ? $urls['production'] : $urls['local'];
    }

    /**
     * 获取基础URL
     * @return string
     */
    public function getBaseUrl(): string
    {
        return $this->baseUrl;
    }

    /**
     * 格式化用户的alias
     * @param User $user
     * @return string
     */
    public function formatAlias(User $user): string 
    {
        $prev = app()->environment() == 'production' ? 1 : 2;
        return $prev . '_' . $user->recommend_code;
    }

    /**
     * 注册用户到悟空IM
     * @param User $user
     * @return bool
     */
    public function registerUser(User $user)
    {
        try {
            // 使用统一的alias格式
            $uid = $this->formatAlias($user);
            
            $response = Http::post($this->baseUrl . '/user/token', [
                'uid' => $uid,  // 使用统一格式的alias作为悟空IM的uid
                'token' => $user->recommend_code, // 使用用户token作为悟空IM的token
                'device_flag' => 0,  // app设备
                'device_level' => 1  // 主设备
            ]);
            if ($response->successful()) {
                // 确保系统通知账号是系统账号
                $this->ensureSystemUser();
                return true;
            }

            Log::error('WukongIM注册用户失败', [
                'user_id' => $user->id,
                'uid' => $uid,
                'response' => $response->json(),
                'env' => app()->environment()
            ]);
            return false;
        } catch (\Exception $e) {
            Log::error('WukongIM注册用户异常', [
                'user_id' => $user->id,
                'uid' => $uid ?? null,
                'error' => $e->getMessage(),
                'env' => app()->environment()
            ]);
            return false;
        }
    }

    /**
     * 确保系统通知账号是系统账号
     */
    public function ensureSystemUser()
    {
        try {
            $response = Http::post($this->baseUrl . '/user/systemuids_add', [
                'uids' => [$this->systemUser, $this->orderChannel]
            ]);
            if (!$response->successful()) {
                Log::error('WukongIM添加系统账号失败', [
                    'response' => $response->json()
                ]);
            }
        } catch (\Exception $e) {
            Log::error('WukongIM添加系统账号异常', [
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 发送单聊消息
     * @param User $user 接收消息的用户
     * @param string $message 消息内容
     * @param array $extra 额外的消息数据
     * @return bool
     */
    public function sendPrivateMessage(User $user, string $message, array $extra = [])
    {
        try {
            $uid = $this->formatAlias($user);
            
            // 发送消息
            $messageData = $this->formatMessage($message, $extra);
            $response = Http::post($this->baseUrl . '/message/send', [
                'channel_id' => $uid,  // 接收者的uid作为channel_id
                'channel_type' => 1,   // 个人频道
                'from_uid' => $this->systemUser,
                'payload' => $messageData
            ]);

            if (!$response->successful()) {
                Log::error('WukongIM发送单聊消息失败', [
                    'user_id' => $user->id,
                    'uid' => $uid,
                    'response' => $response->json()
                ]);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('WukongIM发送单聊消息异常', [
                'user_id' => $user->id,
                'uid' => $uid ?? null,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送系统消息给用户
     * @param User|array $users 用户对象或用户对象数组
     * @param string $message 消息内容
     * @param array $extra 额外的消息数据
     * @return bool
     */
    public function sendSystemMessage($users, string $message, array $extra = [])
    {
        if (!is_array($users)) {
            $users = [$users];
        }

        $success = true;
        foreach ($users as $user) {
            if (!$this->sendPrivateMessage($user, $message, $extra)) {
                $success = false;
            }
        }

        return $success;
    }

    /**
     * 格式化消息数据
     * @param string $message 消息内容
     * @param array $extra 额外数据
     * @return string
     */
    protected function formatMessage(string $message, array $extra = []): string
    {
        // 如果消息已经是JSON格式,解析它
        if ($this->isJson($message)) {
            $messageData = json_decode($message, true);
        } else {
            $messageData = [
                'type' => 1, // 文本消息类型
                'content' => [
                    'text' => $message
                ],
                'extra' => $extra
            ];
        }

        // 将消息数据转换为JSON字符串并base64编码
        return base64_encode(json_encode($messageData));
    }

    /**
     * 检查字符串是否是有效的JSON
     * @param string $string
     * @return bool
     */
    protected function isJson(string $string): bool
    {
        json_decode($string);
        return json_last_error() === JSON_ERROR_NONE;
    }

    /**
     * 发送自定义消息
     * @param User|array $users 用户对象或用户对象数组
     * @param array $messageData 消息数据
     * @return bool
     */
    public function sendCustomMessage($users, array $messageData)
    {
        if (!is_array($users)) {
            $users = [$users];
        }

        $success = true;
        foreach ($users as $user) {
            try {
                $uid = $this->formatAlias($user);
                
                // 发送自定义消息
                $response = Http::post($this->baseUrl . '/message/send', [
                    'channel_id' => $uid,  // 接收者的uid作为channel_id
                    'channel_type' => 1,   // 个人频道
                    'from_uid' => $this->systemUser,
                    'payload' => base64_encode(json_encode($messageData))
                ]);

                if (!$response->successful()) {
                    Log::error('WukongIM发送自定义消息失败', [
                        'user_id' => $user->id,
                        'uid' => $uid,
                        'response' => $response->json()
                    ]);
                    $success = false;
                }
            } catch (\Exception $e) {
                Log::error('WukongIM发送自定义消息异常', [
                    'user_id' => $user->id,
                    'uid' => $uid ?? null,
                    'message_data' => $messageData,
                    'error' => $e->getMessage()
                ]);
                $success = false;
            }
        }

        return $success;
    }

    /**
     * 解码消息内容
     * @param string $payload base64编码的消息内容
     * @return array|null
     */
    protected function decodePayload(string $payload): ?array
    {
        try {
            $decoded = base64_decode($payload);
            if ($decoded === false) {
                return null;
            }
            
            $data = json_decode($decoded, true);
            if (json_last_error() !== JSON_ERROR_NONE) {
                return null;
            }
            
            return $data;
        } catch (\Exception $e) {
            Log::error('WukongIM解码消息失败', [
                'payload' => $payload,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 同步最近会话
     * @param User $user 用户
     * @param int $version 当前客户端的会话最大版本号
     * @param string|null $lastMsgSeqs 最后消息序列号串
     * @param int $msgCount 每个会话获取最大消息数
     * @return array|null
     */
    public function syncConversation(User $user, int $version = 0, ?string $lastMsgSeqs = null, int $msgCount = 20): ?array
    {
        try {
            $uid = $this->formatAlias($user);
            
            $params = [
                'uid' => $uid,
                'version' => $version,
                'msg_count' => $msgCount
            ];
            
            if ($lastMsgSeqs) {
                $params['last_msg_seqs'] = $lastMsgSeqs;
            }

            $response = Http::post($this->baseUrl . '/conversation/sync', $params);

            if (!$response->successful()) {
                Log::error('WukongIM同步会话失败', [
                    'user_id' => $user->id,
                    'uid' => $uid,
                    'response' => $response->json()
                ]);
                return null;
            }

            $data = $response->json();
            
            // 解码每个会话中的消息payload
            if (is_array($data)) {
                foreach ($data as &$conversation) {
                    if (isset($conversation['recents']) && is_array($conversation['recents'])) {
                        foreach ($conversation['recents'] as &$message) {
                            if (isset($message['payload'])) {
                                $message['decoded_payload'] = $this->decodePayload($message['payload']);
                            }
                        }
                    }
                }
            }

            return $data;
        } catch (\Exception $e) {
            Log::error('WukongIM同步会话异常', [
                'user_id' => $user->id,
                'uid' => $uid ?? null,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 获取频道消息
     * @param User $user 当前登录用户
     * @param string $channelId 频道ID
     * @param int $channelType 频道类型
     * @param int $startMessageSeq 开始消息序号
     * @param int $endMessageSeq 结束消息序号
     * @param int $limit 消息数量限制
     * @param int $pullMode 拉取模式 0:向下拉取 1:向上拉取
     * @return array|null
     */
    public function getChannelMessages(
        User $user,
        string $channelId,
        int $channelType,
        int $startMessageSeq = 0,
        int $endMessageSeq = 0,
        int $limit = 100,
        int $pullMode = 1
    ): ?array {
        try {
            $uid = $this->formatAlias($user);
            
            $response = Http::post($this->baseUrl . '/channel/messagesync', [
                'login_uid' => $uid,
                'channel_id' => $channelId,
                'channel_type' => $channelType,
                'start_message_seq' => $startMessageSeq,
                'end_message_seq' => $endMessageSeq,
                'limit' => $limit,
                'pull_mode' => $pullMode
            ]);

            if (!$response->successful()) {
                Log::error('WukongIM获取频道消息失败', [
                    'user_id' => $user->id,
                    'uid' => $uid,
                    'channel_id' => $channelId,
                    'response' => $response->json()
                ]);
                return null;
            }

            $data = $response->json();
            
            // 解码消息payload
            if (isset($data['messages']) && is_array($data['messages'])) {
                foreach ($data['messages'] as &$message) {
                    if (isset($message['payload'])) {
                        $message['decoded_payload'] = $this->decodePayload($message['payload']);
                    }
                }
            }

            return $data;
        } catch (\Exception $e) {
            Log::error('WukongIM获取频道消息异常', [
                'user_id' => $user->id,
                'uid' => $uid ?? null,
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ]);
            return null;
        }
    }

    /**
     * 创建订单通知频道
     * @param string $channelName 频道名称
     * @param string $description 频道描述
     * @return string|bool 成功返回频道ID，失败返回false
     */
    public function createOrderNotificationChannel(string $channelName = 'order_notification', string $description = '订单通知频道')
    {
        try {
            // 创建频道
            $response = Http::post($this->baseUrl . '/channel', [
                'channel_id' => $channelName,
                'channel_type' => 2, // 群组频道
                'name' => $channelName,
                'owner' => $this->systemUser,
                'mute' => false,
                'large' => true, // 大群
                'ban_mode' => false, // 禁言模式
                'subscribers_only' => false, // 是否仅订阅者可见
                'desp' => $description,
                'extra' => [
                    'created_at' => date('Y-m-d H:i:s'),
                    'type' => 'notification'
                ]
            ]);

            if (!$response->successful()) {
                Log::error('WukongIM创建订单通知频道失败', [
                    'channel_name' => $channelName,
                    'description' => $description,
                    'response' => $response->json()
                ]);
                return false;
            }

            $result = $response->json();
            if (isset($result['channel_id'])) {
                return $result['channel_id'];
            }

            return $channelName;
        } catch (\Exception $e) {
            Log::error('WukongIM创建订单通知频道异常', [
                'channel_name' => $channelName,
                'description' => $description,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 添加用户到频道
     * @param string $channelId 频道ID
     * @param User|array $users 用户对象或用户对象数组
     * @return bool
     */
    public function addUsersToChannel(string $channelId, $users)
    {
        if (!is_array($users)) {
            $users = [$users];
        }

        try {
            $userIds = [];
            foreach ($users as $user) {
                $userIds[] = $this->formatAlias($user);
            }

            if (empty($userIds)) {
                return false;
            }

            $response = Http::post($this->baseUrl . '/channel/subscriber/add', [
                'channel_id' => $channelId,
                'channel_type' => 2, // 群组频道
                'uids' => $userIds
            ]);

            if (!$response->successful()) {
                Log::error('WukongIM添加用户到频道失败', [
                    'channel_id' => $channelId,
                    'user_ids' => $userIds,
                    'response' => $response->json()
                ]);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('WukongIM添加用户到频道异常', [
                'channel_id' => $channelId,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }

    /**
     * 发送订单通知到订单频道
     * @param string $channelId 频道ID，通常是'order_notification'
     * @param array $orderData 订单数据
     * @param string $message 消息内容
     * @return bool
     */
    public function sendOrderNotification(string $channelId, array $orderData, string $message)
    {
        try {
            // 准备消息数据
            $messageData = [
                'type' => 10, // 自定义类型，用于订单消息
                'content' => [
                    'text' => $message,
                    'order' => $orderData
                ],
                'extra' => [
                    'notification_type' => 'order',
                    'time' => time()
                ]
            ];

            // 发送到频道
            $response = Http::post($this->baseUrl . '/channel/message/send', [
                'channel_id' => $channelId,
                'channel_type' => 2, // 群组频道
                'from_uid' => $this->systemUser,
                'payload' => base64_encode(json_encode($messageData))
            ]);

            if (!$response->successful()) {
                Log::error('WukongIM发送订单通知失败', [
                    'channel_id' => $channelId,
                    'order_data' => $orderData,
                    'message' => $message,
                    'response' => $response->json()
                ]);
                return false;
            }

            return true;
        } catch (\Exception $e) {
            Log::error('WukongIM发送订单通知异常', [
                'channel_id' => $channelId,
                'order_data' => $orderData,
                'message' => $message,
                'error' => $e->getMessage()
            ]);
            return false;
        }
    }
} 