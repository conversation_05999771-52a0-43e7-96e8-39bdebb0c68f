<?php

namespace App\Http\Resources;

use App\Models\OrderRefund;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderRefundResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'no' => $this->no,
            'refund_reason' => $this->refund_reason,
            'refund_remark' => $this->refund_remark,
            'refund_pics' => img_url_array($this->refund_pics),
            'spu_info' => $this->spu_info,
            'refund_amount' => fentoyuan($this->refund_amount),
            'refund_status' => $this->refund_status,
            'refund_status_text' => OrderRefund::RefundStatusMap[$this->refund_status] ?? "",
            'verify_at' => $this->verify_at ? $this->verify_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'reject_reason' => $this->reject_reason,
            'refund_at' => $this->refund_at ? $this->refund_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'refund_no' => $this->refund_no,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
