<?php

namespace App\Http\Resources;

use App\Models\UserCoupon;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class UserCouponResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'start_time' => $this->start_time->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'end_time' => $this->end_time->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            "status" => $this->status,
            "status_txt" => UserCoupon::StatusMap[$this->status],
            'coupon' => new CouponResource($this->coupon),
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
