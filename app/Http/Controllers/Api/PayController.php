<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\PayRequest;
use App\Services\PayService;

class PayController extends Controller
{
    // 统一支付接口
    public function pay(PayRequest $request, PayService $payService)
    {
        $orderNo = $request->order_no;

        $payResult = $payService->pay($orderNo, $request->pay_method, $request->pay_way, $request->getClientIp());

        return $this->success('success', ['pay_params' => $payResult]);
    }
}
