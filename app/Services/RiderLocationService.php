<?php

namespace App\Services;

use App\Models\Location;
use App\Models\Rider;
use Carbon\Carbon;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Schema;

class RiderLocationService
{
    /**
     * 获取骑手位置
     * 首先从Redis获取，如果Redis中没有，则从locations表获取最新位置
     *
     * @param int $riderId 骑手ID
     * @return array|null 返回[经度, 纬度]数组或null
     */
    public function getRiderLocation(int $riderId): ?array
    {
        // 先从Redis获取位置
        $pos = Redis::geopos(Location::LOCATION_BELONG_RIDER, [$riderId])[0];
        
        // 如果Redis中有位置，直接返回
        if ($pos) {
            return [
                'lng' => $pos[0],
                'lat' => $pos[1]
            ];
        }
        
        // Redis中没有位置，从locations表获取
        $rider = Rider::query()->where("id", $riderId)->first();
        if (!$rider) {
            return null;
        }
        
        // 获取当前月和上个月的日期格式
        $current = Carbon::now();
        $currentMonth = $current->format("Ym");
        $lastMonth = $current->copy()->subMonth()->format("Ym");
        
        // 尝试从当前月和上个月的表中查询
        $suffixArr = [$currentMonth, $lastMonth];
        
        foreach ($suffixArr as $suffix) {
            if (Schema::hasTable("locations_" . $suffix)) {
                $location = Location::suffix($suffix)
                    ->where("belong_type", Location::LOCATION_BELONG_RIDER)
                    ->where("belong_id", $riderId)
                    ->orderBy("time", "desc")
                    ->first();
                
                if ($location) {
                    // 将位置更新到Redis中
                    Redis::geoadd(
                        Location::LOCATION_BELONG_RIDER, 
                        $location->lng, 
                        $location->lat, 
                        $riderId
                    );
                    
                    return [
                        'lng' => $location->lng,
                        'lat' => $location->lat
                    ];
                }
            }
        }
        
        return null;
    }
    
    /**
     * 获取骑手位置（仅经纬度数组）
     * 
     * @param int $riderId 骑手ID
     * @return array|null 返回[经度, 纬度]数组或null
     */
    public function getRiderLocationCoordinates(int $riderId): ?array
    {
        $location = $this->getRiderLocation($riderId);
        if ($location) {
            return [
                number_format($location['lng'], 17, '.', ''),
                number_format($location['lat'], 17, '.', '')
            ];
        }
        return null;
    }
} 