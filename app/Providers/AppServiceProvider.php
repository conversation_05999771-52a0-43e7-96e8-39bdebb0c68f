<?php

namespace App\Providers;

use App\Models\Location;
use App\Services\EasemobService;
use Illuminate\Container\Container;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\ServiceProvider;
use link1st\Easemob\App\Easemob;
use Monolog\Logger;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {

        $this->app->singleton('easemob_sq', function () {
            return new EasemobService(EasemobService::GetAppName(1));
        });

        $this->app->singleton('easemob_pt', function () {
            return new EasemobService(EasemobService::GetAppName(2));
        });
    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        if (!app()->environment('production')) {
//            DB::listen(function ($query) {
//                $tmp = str_replace('?', '"' . '%s' . '"', $query->sql);
//                $tmp = vsprintf($tmp, $query->bindings);
//                $tmp = str_replace("\\", "", $tmp);
//                Log::info(' execution time: ' . $query->time . 'ms; ' . $tmp . "\t");
//            });
        }

        Relation::morphMap([
            Location::LOCATION_BELONG_USER => 'App\Models\User',
            Location::LOCATION_BELONG_RIDER => 'App\Models\Rider',
        ]);
    }
}
