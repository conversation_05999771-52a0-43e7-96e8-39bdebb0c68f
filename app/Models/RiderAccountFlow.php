<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RiderAccountFlow extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const TYPE_IN = 'in';
    const TYPE_OUT = 'out';

    const BUSINESS_TYPE_ORDER = 1001;
    const BUSINESS_TYPE_ORDER_TIP = 1002;
    const BUSINESS_TYPE_ORDER_REWARD = 1003;
    const BUSINESS_TYPE_SUBSIDE = 1004;
    const BUSINESS_TYPE_CHECK_OUT = 1005;
    const BUSINESS_TYPE_TRANS_TO_BZJ = 1006;
    const BUSINESS_TYPE_TRANS_TO_AMOUNT = 1007;
    const BUSINESS_TYPE_EARNEST_ORDER = 1008;
    const BUSINESS_TYPE_ACT = 1009;
    const BUSINESS_TYPE_APPLY_REJECT = 1010;
    const BUSINESS_TYPE_BUY_FEE = 1011;

    const BUSINESS_TYPE_MAP = [
        self::BUSINESS_TYPE_ORDER => '完成订单',
        self::BUSINESS_TYPE_ORDER_TIP => '小费',
        self::BUSINESS_TYPE_ORDER_REWARD => '打赏',
        self::BUSINESS_TYPE_SUBSIDE => '补贴',
        self::BUSINESS_TYPE_CHECK_OUT => '提现',
        self::BUSINESS_TYPE_TRANS_TO_BZJ => '转移到保证金',
        self::BUSINESS_TYPE_TRANS_TO_AMOUNT => '转移到余额',
        self::BUSINESS_TYPE_EARNEST_ORDER => '缴纳保证金',
        self::BUSINESS_TYPE_ACT => '平台活动',
        self::BUSINESS_TYPE_APPLY_REJECT => '提现拒绝',
        self::BUSINESS_TYPE_BUY_FEE => '垫付结算',
    ];

    protected $fillable = [
        'user_id',
        'user_account_id',
        'chg_amount',
        'before_amount',
        'after_amount',
        'type',
        'business_type',
        'no',
        'remark'
    ];

    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
