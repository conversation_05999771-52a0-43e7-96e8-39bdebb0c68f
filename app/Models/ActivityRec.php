<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityRec extends Model
{
    use HasFactory;

    const StatusTextMap = [
        0 => '已注册',
        1 => '已完成',
    ];

    const RemarkTextMap = [
        0 => '未完成任务',
        1 => '奖励已发放'
    ];

    protected $fillable = [
        'activity_id',
        'user_id',
        'child_id',
        'reward_amount',
        'status'
    ];

    protected $appends = ['status_text', 'remark'];

    public function getStatusTextAttribute()
    {
        return self::StatusTextMap[$this->status] ?? '';
    }

    public function getRemarkAttribute() {
        return self::RemarkTextMap[$this->status] ?? '';
    }

    public function activity()
    {
        return $this->belongsTo(Activity::class, 'activity_id');
    }

    public function child()
    {
        return $this->belongsTo(User::class, 'child_id');
    }

    public function parent()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
