<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\BusinessException;
use App\Models\Location;
use App\Models\O2oErrandOrder;
use App\Models\Region;
use App\Models\Rider;
use App\Models\SystemConfig;
use App\Models\UserAccount;
use App\Models\UserAccountFlow;
use App\Models\UserAddress;
use App\Services\Amap\GaodeService;
use App\Services\Amap\BaiduService;
use App\Services\CommonService;
use App\Services\MaiYaTianService;
use App\Services\O2oErrandOrderService;
use App\Services\WrcService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Facades\Log;
use App\Models\Merchant;
use App\Services\RiderLocationService;
use App\Models\MerchantAccountLog;
use App\Models\MerchantToken;

class MaiYaTianController extends Controller
{
    public function authCallback(Request $request)
    {
        try {
            // 记录麦芽田回调信息
            Log::channel('maiyatian')->info('麦芽田授权回调', $request->all());

            $code = $request->get('code');
            $redirectUrl = $request->get('redirect_uri');
            $state = $request->get('state');

            // 重定向到商家登录页面，并携带所有参数
            $merchantLoginUrl = route('merchant.login.maiyatian');
            return redirect($merchantLoginUrl . '?' . http_build_query([
                'code' => $code,
                'redirect_uri' => $redirectUrl,
                'state' => $state,
                'source' => O2oErrandOrder::APP_KEY_MYT
            ]));
        } catch (\Exception $e) {
            Log::channel('maiyatian')->error('麦芽田授权回调异常', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(["code" => 500, "message" => $e->getMessage(), "data" => ""]);
        }
    }

    public function callback(Request $request, $command)
    {
        Log::channel('maiyatian')->info("麦芽田回调", [
            'command' => $command,
            'data' => $request->all()
        ]);

        try {
            $postData = $request->all();
            $data = json_decode($postData['data'], true);
            $userId = 0;
            $merchantId = 0;
            $shopId = $data["shop_id"] ?? '';

            if (!empty($shopId)) {
                $maiYaTianService = new MaiYaTianService();
                $merchantInfo = $maiYaTianService->getMerchantInfoByShopId($shopId);

                if ($merchantInfo) {
                    $userId = $merchantInfo['user_id'] ?? 0;
                    $merchantId = $merchantInfo['merchant_id'] ?? 0;

                    Log::channel('maiyatian')->info('获取到商家信息', [
                        'shop_id' => $shopId,
                        'merchant_id' => $merchantId,
                        'user_id' => $userId
                    ]);
                } else {
                    Log::channel('maiyatian')->warning('未找到对应商家信息', ['shop_id' => $shopId]);
                    throw new \Exception("该门店未授权");
                }
            }

            $order = null;
            if (($data["source_order_no"] ?? "")) {
                $order = O2oErrandOrder::query()->where("out_order_no", $data["source_order_no"])->where("app_key", O2oErrandOrder::APP_KEY_MYT)->first();
            }
            switch ($command) {
                case "valuating": // 订单计费接口
                    $result = $this->valuating($userId, $order, $data);
                    break;
                case "send": // 下单接口
                    Log::info("user_id " . $userId);
                    if ($order) throw new \Exception("订单已存在");
                    $result = $this->send($userId, $data, O2oErrandOrder::APP_KEY_MYT);
                    break;
                case "tips": // 添加小费接口
                    if (!$order) throw new \Exception("订单不存在");
                    $result = $this->tips($order, $data["tips"]);
                    break;
                case "cancel": // 取消配送
                    if (!$order) throw new \Exception("订单不存在");
                    $reason = $data["cancel_reason"];
                    if (empty($reason)) {
                        $reason = [1000 => "订单信息填写错误", 1001 => "已用其他发单工具配送", 1002 => "没有骑手接单", 1005 => "配送方系统超时", 1007 => "订单取消", 1008 => "其他原因"][$data["cancel_reason_type"]] ?? "其他原因";
                    }
                    (new O2oErrandOrderService())->refundOrder($order->order_no, $reason);
                    $result = ["status" => 1];
                    break;
                case "query_info": // 查询配送详情
                    if (!$order) throw new \Exception("订单不存在");
                    // 订单状态转换
                    $status = $order->getMytStatus();
                    $statusText = $order->getMytStatusText();
                    $result = [
                        "order_no" => $order->order_no,
                        "source_order_no" => $order->out_order_no,
                        "status" => $status,
                        "status_name" => $statusText,
                        "pay_amount" => $order->actual_amount + $order->gratuity + $order->goods_protected_price,
                        "coupon" => $order->coupon_amount,
                        "premium" => 0,
                        "tips" => $order->gratuity,
                        "distance" => $order->distance,
                        "create_time" => $order->create_time ? $order->create_time->timestamp : 0,
                        "accept_time" => $order->receipt_time ? $order->receipt_time->timestamp : 0,
                        "fetch_time" => $order->pickup_at ? $order->pickup_at->timestamp : 0,
                        "finish_time" => $order->finish_time ? $order->finish_time->timestamp : 0,
                        "cancel_time" => $order->refund_at ? $order->refund_at->timestamp : 0,
                        "rider_name" => "", "rider_phone" => "", "longitude" => "", "latitude" => "",
                        "is_transship" => $order->is_trans,
                    ];
                    if ($order->rider_id) {
                        $rider = Rider::query()->where("id", $order->rider_id)->first();
                        $result["rider_name"] = $rider->name;
                        $result["rider_phone"] = $rider->phone;
                        if ($order->order_status < O2oErrandOrder::STATUS_FINISH) {
                            $pos = Redis::geopos(Location::LOCATION_BELONG_RIDER, [$order->rider_id])[0];
                            $result["longitude"] = $pos[0]."";
                            $result["latitude"] = $pos[1]."";
                        }
                    }
                    break;
                case "rider_location": // 获取骑手当前位置
                    if (!$order) throw new \Exception("订单不存在");
                    $result = [
                        "order_no" => $order->order_no,
                        "source_order_no" => $order->out_order_no,
                        "rider_name" => "", "rider_phone" => "", "longitude" => "", "latitude" => "",
                        "at_time" => time(),
                    ];
                    if ($order->rider_id) {
                        $rider = Rider::query()->where("id", $order->rider_id)->first();
                        $result["rider_name"] = $rider->name;
                        $result["rider_phone"] = $rider->phone;
                        if ($order->order_status < O2oErrandOrder::STATUS_FINISH) {
                            $riderLocationService = app(RiderLocationService::class);
                            $location = $riderLocationService->getRiderLocation($order->rider_id);
                            if ($location) {
                                $result["longitude"] = $location['lng']."";
                                $result["latitude"] = $location['lat']."";
                            }
                        }
                    }
                    break;
                case "multi_rider_locations": // 批量获取骑手当前位置
                    $result = [];
                    if ($data["orders"]) {
                        $outOrderNoArr = [];
                        foreach ($data["orders"] as $v) {
                            $outOrderNoArr[] = $v["source_order_no"];
                        }
                        $orders = O2oErrandOrder::query()->with(["rider"])->whereIn("out_order_no", $outOrderNoArr)->where("app_key", O2oErrandOrder::APP_KEY_MYT)->get();
                        $riderLocationService = app(RiderLocationService::class);

                        foreach ($data["orders"] as $v) {
                            $order = null;
                            foreach ($orders as $item) {
                                if ($item->out_order_no == $v["source_order_no"]) {
                                    $order = $item;
                                    break;
                                }
                            }
                            if (!$order) throw new \Exception("订单不存在");
                            $riderName = "";
                            $riderPhone = "";
                            $longitude = "";
                            $latitude = "";
                            if ($order->rider_id) {
                                $riderName = $order->rider->name;
                                $riderPhone = $order->rider->phone;
                                if ($order->order_status < O2oErrandOrder::STATUS_FINISH) {
                                    $location = $riderLocationService->getRiderLocation($order->rider_id);
                                    if ($location) {
                                        $longitude = $location['lng']."";
                                        $latitude = $location['lat']."";
                                    }
                                }
                            }
                            $result[] = [
                                "order_no" => $order->order_no,
                                "source_order_no" => $order->out_order_no,
                                "rider_name" => $riderName, "rider_phone" => $riderPhone, "longitude" => $longitude, "latitude" => $latitude,
                                "at_time" => time(),
                            ];
                        }
                    }
                    break;
                case "token_unbind": // 授权解绑
                    // 删除商家token记录
                    if (!empty($shopId)) {
                        Log::channel('maiyatian')->info('麦芽田授权解绑请求', [
                            'shop_id' => $shopId,
                            'merchant_id' => $merchantId,
                            'user_id' => $userId,
                            'data' => $data
                        ]);

                        // 使用Service方法解绑token
                        $success = $maiYaTianService->unbindToken($shopId);

                        Log::channel('maiyatian')->info('麦芽田授权解绑完成', [
                            'shop_id' => $shopId,
                            'success' => $success
                        ]);

                        $result = [
                            'success' => true,
                            'at_time' => time()
                        ];
                    } else {
                        Log::channel('maiyatian')->warning('麦芽田授权解绑请求缺少shop_id参数');

                        $result = [
                            'success' => false,
                            'message' => '解绑失败：缺少shop_id参数',
                            'at_time' => time()
                        ];
                    }

                    break;
                case "balance": // 查询当前账号余额
                    // 使用商家ID获取商家信息及余额
                    if ($merchantId) {
                        $merchant = Merchant::query()->find($merchantId);
                        if ($merchant) {
                            $result = [
                                "balance" => $merchant->balance,
                                "at_time" => time(),
                            ];
                            Log::channel('maiyatian')->info('查询商家余额', [
                                'shop_id' => $shopId,
                                'merchant_id' => $merchantId,
                                'balance' => $merchant->balance
                            ]);
                        } else {
                            Log::channel('maiyatian')->warning('查询余额失败：商家不存在', [
                                'merchant_id' => $merchantId
                            ]);
                            throw new \Exception("查询余额失败：商家不存在");
                        }
                    } else {
                        Log::channel('maiyatian')->warning('查询余额失败：未找到商家ID');
                        throw new \Exception("查询余额失败：未找到商家ID");
                    }
                    break;
                case "recharge_url":
                    // 返回商家充值页面链接
                    if ($merchantId) {
                        $rechargeUrl = route('merchant.recharge', [
                            'merchant_id' => $merchantId,
                            'source' => O2oErrandOrder::APP_KEY_MYT,
                            'shop_id' => $shopId ?? ''
                        ]);

                        $result = [
                            "recharge_url" => $rechargeUrl,
                            "at_time" => time(),
                        ];

                        Log::channel('maiyatian')->info('生成商家充值链接', [
                            'shop_id' => $shopId,
                            'merchant_id' => $merchantId,
                            'recharge_url' => $rechargeUrl
                        ]);
                    } else {
                        Log::channel('maiyatian')->warning('生成充值链接失败：未找到商家ID');
                        throw new \Exception("生成充值链接失败：未找到商家ID");
                    }
                    break;
                default:
                    return response()->json(["code" => 400, "message" => "The '$command' is not supported.", "data" => ""]);
            }
        } catch (\Exception $e) {
            Log::channel('maiyatian')->error('麦芽田回调异常', [
                'command' => $command ?? 'unknown',
                'shop_id' => $shopId ?? '',
                'merchant_id' => $merchantId ?? 0,
                'user_id' => $userId ?? 0,
                'source_order_no' => $postData['source_order_no'] ?? '',
                'request_data' => $request->all(),
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(["code" => 500, "message" => $e->getMessage(), "data" => ""]);
        }
        return response()->json(["code" => 200, "message" => "SUCCESS", "data" => $result ? json_encode($result) : ""]);
    }

    private function valuating($userId, $order, $postData)
    {
        if ($order) {
            return [
                "pay_amount" => $order->actual_amount + $order->gratuity + $order->goods_protected_price,
                "coupon" => $order->coupon_amount,
                "distance" => $order->distance,
                "premium" => 0,
                "weight" => $order->weight * 1000,
                "overweight" => 0,
                "tips" => $order->gratuity,
                "at_time" => $order->estimated_delivery_time->timestamp - $order->appointment_start_time->timestamp,
                "expect_time" => $order->estimated_delivery_time->timestamp,
                "order_no" => $order->order_no,
                "source_order_no" => $order->out_order_no,
            ];
        }

        $service = new O2oErrandOrderService();
        // 获取预约时间
        $appointmentTime = "NOW";
        if (($postData["expect_finish_time"] ?? 0) > 0) {
            $res = (new GaodeService())->electrobike($postData["sender"]["longitude"], $postData["sender"]["latitude"], $postData["receiver"]["longitude"], $postData["receiver"]["latitude"]);
            $paths = $res["route"]["paths"] ?? [[]];
            $index = 0;
            if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                $index = count($paths) - 1;
            }
            $needSeconds = $service->getSeconds(intval($paths[$index]["duration"] ?? 0));
            $appointmentTime = date("Y-m-d H:i:s", $postData["expect_finish_time"] - $needSeconds) . "|" . date("Y-m-d H:i:s", $postData["expect_finish_time"]);
        } elseif (($postData["delay_delivery_time"] ?? 0) > 0) {
            $appointmentTime = date("Y-m-d H:i:s", $postData["delay_delivery_time"] - 30 * 60) . "|" . date("Y-m-d H:i:s", $postData["delay_delivery_time"]);
        }
        $res = $service->preOrder($userId, [
            "type" => 1,
            "coupon_id" => 0,
            "gratuity" => fentoyuan($postData["tips"] ?? 0),
            "appointment_time" => $appointmentTime,
            "goods_info" => [
                "is_protect_price" => false,
                "price" => fentoyuan($postData["order_info"]["paid_fee"] ?? 0),
            ],
            "start_point" => [
                "mode" => 1,
                "lng" => $postData["sender"]["longitude"],
                "lat" => $postData["sender"]["latitude"],
            ],
            "end_point" => [
                "address_id" => 0,
                "lng" => $postData["receiver"]["longitude"],
                "lat" => $postData["receiver"]["latitude"],
            ],
        ]);
        // 取的普通配送
        return [
            "pay_amount" => yuantofen($res["normal"]["total_amount"]),
            "coupon" => 0,
            "distance" => $res['distance'],
            "premium" => 0,
            "weight" => $postData["order_info"]["weight"] * 1000,
            "overweight" => 0,
            "tips" => $postData["tips"] ?? 0,
            "at_time" => $res["normal"]["spent_time"],
            "expect_time" => strtotime($res["normal"]["estimated_delivery_time"]),
            "order_no" => "", // TODO 订单号 计价未生成订单
            "source_order_no" => $postData["source_order_no"],
        ];
    }

    private function send($userId, $postData, $appKey)
    {
        $service = new O2oErrandOrderService();

        // 获取商家信息
        $merchantInfo = $this->getMerchantInfoForOrder($postData, $appKey);
        $merchantId = $merchantInfo['merchant_id'] ?? 0;

        // 准备预约时间
        $appointmentTime = $this->prepareAppointmentTime($postData, $service);

        // 准备地址信息
        $addressInfo = $this->prepareAddressInfo($userId, $postData);

        // 准备订单数据并创建订单
        $order = $this->createOrderWithPreparedData(
            $userId,
            $postData,
            $appointmentTime,
            $addressInfo['startAddress'],
            $addressInfo['endAddress'],
            $merchantId,
            $service,
            $appKey
        );

        // 返回格式化的结果
        return $this->formatOrderResultData($order);
    }

    /**
     * 获取订单关联的商家信息
     *
     * @param array $postData 请求数据
     * @return array 商家信息
     */
    private function getMerchantInfoForOrder(array $postData, $appKey)
    {
        $shopId = $postData["shop_id"] ?? '';
        $merchantInfo = [];

        if (!empty($shopId)) {
            switch ($appKey){
                case O2oErrandOrder::APP_KEY_MYT:
                    $svc = new MaiYaTianService();
                    $merchantInfo = $svc->getMerchantInfoByShopId($shopId);
                    break;
                case O2oErrandOrder::APP_KEY_WRC:
                    $svc = new WrcService();
                    $merchantInfo = $svc->getMerchantInfoByShopId($shopId);
                    break;
            }

            if ($merchantInfo) {
                Log::channel($appKey)->info('订单创建', [
                    'shop_id' => $shopId,
                    'merchant_id' => $merchantInfo['merchant_id'] ?? 0,
                    'user_id' => $merchantInfo['user_id'] ?? 0
                ]);
            }
        }

        return $merchantInfo;
    }

    /**
     * 准备订单预约时间
     *
     * @param array $postData 请求数据
     * @param O2oErrandOrderService $service 订单服务
     * @return string 格式化的预约时间
     */
    private function prepareAppointmentTime(array $postData, O2oErrandOrderService $service)
    {
        $appointmentTime = "NOW";

        if (($postData["pickup_start_time"] ?? 0) > 0 && ($postData["pickup_end_time"] ?? 0) > 0) {
            // 使用指定的提货时间范围
            $appointmentTime = date("Y-m-d H:i:s", $postData["pickup_start_time"]) . "|" . date("Y-m-d H:i:s", $postData["pickup_end_time"]);
        } elseif (($postData["expect_finish_time"] ?? 0) > 0) {
            // 使用期望完成时间计算
            $res = (new GaodeService())->electrobike(
                $postData["sender"]["longitude"],
                $postData["sender"]["latitude"],
                $postData["receiver"]["longitude"],
                $postData["receiver"]["latitude"]
            );

            $paths = $res["route"]["paths"] ?? [[]];
            $index = 0;
            if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                $index = count($paths) - 1;
            }

            $needSeconds = $service->getSeconds(intval($paths[$index]["duration"] ?? 0));
            $appointmentTime = date("Y-m-d H:i:s", $postData["expect_finish_time"] - $needSeconds) . "|" . date("Y-m-d H:i:s", $postData["expect_finish_time"]);
        } elseif (($postData["delay_delivery_time"] ?? 0) > 0) {
            // 使用延迟配送时间
            $appointmentTime = date("Y-m-d H:i:s", $postData["delay_delivery_time"] - 30 * 60) . "|" . date("Y-m-d H:i:s", $postData["delay_delivery_time"]);
        }

        return $appointmentTime;
    }

    /**
     * 准备订单地址信息
     *
     * @param int $userId 用户ID
     * @param array $postData 请求数据
     * @return array 起点和终点地址信息
     */
    private function prepareAddressInfo($userId, array $postData)
    {
        // 获取地区映射
        $regionMap = $this->getRegionMap($postData);

        // 处理起点地址
        $startAddress = $this->getOrCreateAddress(
            $userId,
            $postData["sender"],
            $regionMap,
            "sender"
        );

        // 处理终点地址
        $endAddress = $this->getOrCreateAddress(
            $userId,
            $postData["receiver"],
            $regionMap,
            "receiver"
        );

        return [
            'startAddress' => $startAddress,
            'endAddress' => $endAddress
        ];
    }

    /**
     * 获取地区映射数据，并尝试补充缺失的地区代码
     *
     * @param array $postData 请求数据
     * @return array 地区映射数据
     */
    private function getRegionMap(array $postData)
    {
        // 尝试补充缺失的地区代码
        $this->fillMissingRegionCodes($postData);

        // 收集所有非空的地区代码
        $regionCodes = array_filter([
            $postData["sender"]["province_code"] ?? '',
            $postData["sender"]["city_code"] ?? '',
            $postData["sender"]["district_code"] ?? '',
            $postData["receiver"]["province_code"] ?? '',
            $postData["receiver"]["city_code"] ?? '',
            $postData["receiver"]["district_code"] ?? ''
        ]);

        if (empty($regionCodes)) {
            Log::channel('maiyatian')->warning('所有地区代码均为空，无法创建地址', [
                'sender' => $postData["sender"] ?? [],
                'receiver' => $postData["receiver"] ?? []
            ]);
            throw new \Exception("无法识别收发地址的地区信息");
        }

        $regions = Region::query()->whereIn('code', $regionCodes)->get();

        $regionMap = [];
        foreach ($regions as $region) {
            $regionMap[$region->code] = $region;
        }

        return $regionMap;
    }

    /**
     * 尝试通过经纬度填充缺失的地区代码
     *
     * @param array &$postData 请求数据（引用传递，会直接修改）
     */
    private function fillMissingRegionCodes(array &$postData)
    {
        // 检查并填充发送方地区代码
        $this->fillAddressRegionCodes($postData['sender'], 'sender');

        // 检查并填充接收方地区代码
        $this->fillAddressRegionCodes($postData['receiver'], 'receiver');
    }

    /**
     * 为单个地址填充缺失的地区代码
     *
     * @param array &$address 地址数据（引用传递，会直接修改）
     * @param string $type 地址类型（发送方/接收方）
     */
    private function fillAddressRegionCodes(array &$address, string $type)
    {
        // 检查是否有缺失的地区代码
        if (empty($address['province_code']) || empty($address['city_code']) || empty($address['district_code'])) {
            // 确保有经纬度信息
            if (!empty($address['longitude']) && !empty($address['latitude'])) {
                try {
                    Log::channel('maiyatian')->info("{$type}地址缺少地区代码，尝试通过经纬度识别", [
                        'longitude' => $address['longitude'],
                        'latitude' => $address['latitude']
                    ]);

                    // 使用百度地图API获取地区信息
                    $locationInfo = (new GaodeService())->reGeoQuery($address['longitude'], $address['latitude']);

                    // 提取并填充地区代码
                    if (!empty($locationInfo)) {
                        // 这里根据百度地图API的返回结构来提取地区代码
                        // 示例代码，实际实现需要根据API返回结构调整
                        if (empty($address['province_code']) && isset($locationInfo['result']['addressComponent']['province_code'])) {
                            $address['province_code'] = $locationInfo['result']['addressComponent']['province_code'];
                        }

                        if (empty($address['city_code']) && isset($locationInfo['result']['addressComponent']['city_code'])) {
                            $address['city_code'] = $locationInfo['result']['addressComponent']['city_code'];
                        }

                        if (empty($address['district_code']) && isset($locationInfo['result']['addressComponent']['district_code'])) {
                            $address['district_code'] = $locationInfo['result']['addressComponent']['district_code'];
                        }

                        // 如果API没有返回地区代码但返回了地区名称，尝试在数据库中查找匹配的代码
                        $this->findRegionCodesByNames($address, $locationInfo);

                        Log::channel('maiyatian')->info("已通过经纬度补充{$type}地址的地区代码", [
                            'address' => $address
                        ]);
                    }
                } catch (\Exception $e) {
                    Log::channel('maiyatian')->warning("通过经纬度识别{$type}地址地区代码失败", [
                        'message' => $e->getMessage(),
                        'longitude' => $address['longitude'],
                        'latitude' => $address['latitude']
                    ]);
                }
            }
        }
    }

    /**
     * 根据地区名称查找对应的地区代码
     *
     * @param array &$address 地址数据
     * @param array $locationInfo 位置信息
     */
    private function findRegionCodesByNames(array &$address, array $locationInfo)
    {
        // 从位置信息中提取地区名称
        $provinceName = $locationInfo['result']['addressComponent']['province'] ?? '';
        $cityName = $locationInfo['result']['addressComponent']['city'] ?? '';
        $districtName = $locationInfo['result']['addressComponent']['district'] ?? '';

        // 根据名称查找省级代码
        if (empty($address['province_code']) && !empty($provinceName)) {
            $province = Region::query()->where('name', 'like', "%{$provinceName}%")
                ->where('level', 'province')
                ->first();
            if ($province) {
                $address['province_code'] = $province->code;
            }
        }

        // 根据名称查找市级代码
        if (empty($address['city_code']) && !empty($cityName)) {
            $city = Region::query()->where('name', 'like', "%{$cityName}%")
                ->where('level', 'city')
                ->first();
            if ($city) {
                $address['city_code'] = $city->code;
            }
        }

        // 根据名称查找区级代码
        if (empty($address['district_code']) && !empty($districtName)) {
            $district = Region::query()->where('name', 'like', "%{$districtName}%")
                ->where('level', 'district')
                ->first();
            if ($district) {
                $address['district_code'] = $district->code;
            }
        }
    }

    /**
     * 获取或创建地址记录，处理地区代码缺失的情况
     *
     * @param int $userId 用户ID
     * @param array $addressData 地址数据
     * @param array $regionMap 地区映射
     * @param string $type 地址类型 (sender/receiver)
     * @return UserAddress 地址对象
     */
    private function getOrCreateAddress($userId, array $addressData, array $regionMap, $type)
    {
        $provinceCode = $addressData['province_code'] ?? '';
        $cityCode = $addressData['city_code'] ?? '';
        $districtCode = $addressData['district_code'] ?? '';

        // 检查地区码是否在映射中存在
        if (empty($provinceCode) || empty($cityCode) || empty($districtCode) ||
            !isset($regionMap[$provinceCode]) || !isset($regionMap[$cityCode]) || !isset($regionMap[$districtCode])) {
            // 如果地区代码有问题，使用备用方案
            Log::channel('maiyatian')->warning("{$type}地址的地区代码无效或不完整", [
                'address_data' => $addressData
            ]);

            // 使用浙江杭州余杭作为默认地区（根据提供的经纬度，这似乎是在杭州）
            $defaultProvince = Region::query()->where('name', '浙江省')->first();
            $defaultCity = Region::query()->where('name', '杭州市')->first();
            $defaultDistrict = Region::query()->where('name', '余杭区')->first();

            if ($defaultProvince && $defaultCity && $defaultDistrict) {
                // 使用默认值
                $provinceName = $defaultProvince->name;
                $cityName = $defaultCity->name;
                $countyName = $defaultDistrict->name;

                Log::channel('maiyatian')->info("使用默认地区信息", [
                    'type' => $type,
                    'province' => $provinceName,
                    'city' => $cityName,
                    'district' => $countyName
                ]);
            } else {
                throw new \Exception("{$type}地址的地区代码无效，且无法使用默认地区");
            }
        } else {
            // 使用正常的地区信息
            $provinceName = $regionMap[$provinceCode]->name;
            $cityName = $regionMap[$cityCode]->name;
            $countyName = $regionMap[$districtCode]->name;
        }
        $addressDetail = $addressData['address_detail'] ?? '';
        if (empty($addressDetail)) {
            $addressDetail = $addressData['address'] ?? '';
        }

        // 查找已存在的地址
        $address = UserAddress::query()->where("tel", $addressData['phone'])
            ->where("province", $provinceName)
            ->where("city", $cityName)
            ->where("county", $countyName)
            ->where("address_detail", $addressDetail)
            ->where('user_id', $userId)
            ->first();

        // 如果地址不存在，创建新地址
        if (!$address) {
            $address = UserAddress::create([
                "user_id" => $userId,
                "name" => $addressData["name"],
                "tel" => $addressData["phone"],
                "province" => $provinceName,
                "city" => $cityName,
                "county" => $countyName,
                "address_detail" => $addressDetail,
                "address_remark" => "",
                "area_code" => $districtCode ?: ($defaultDistrict->code ?? ''),
                "latitude" => $addressData["latitude"],
                "longitude" => $addressData["longitude"],
            ]);

            Log::channel('maiyatian')->info("已创建新的{$type}地址", [
                'address_id' => $address->id
            ]);
        }

        return $address;
    }

    /**
     * 使用准备好的数据创建订单
     *
     * @param int $userId 用户ID
     * @param array $postData 请求数据
     * @param string $appointmentTime 预约时间
     * @param UserAddress $startAddress 起点地址
     * @param UserAddress $endAddress 终点地址
     * @param int $merchantId 商家ID
     * @param O2oErrandOrderService $service 订单服务
     * @return O2oErrandOrder 创建的订单
     */
    private function createOrderWithPreparedData(
        $userId,
        array $postData,
        $appointmentTime,
        $startAddress,
        $endAddress,
        $merchantId,
        O2oErrandOrderService $service,
        $appKey
    ) {
        // 商品分类映射表
        $goodsCategoryMap = $this->getGoodsCategoryMap();

        // 构建订单附加信息
        $orderInfoText = $this->buildOrderInfoText($postData);

        // 平台名称映射
        $platformMap = [
            'meituan' => '美团',
            'ele' => '饿了么',
            'eleme' => '饿了么'
        ];
        
        $infoArray = [];
        
        // 添加平台信息
        if (!empty($orderInfo["source"])) {
            $platformName = $platformMap[$orderInfo["source"]] ?? $orderInfo["source"];
        }
        
        $orderParams = [
            "appointment_time" => $appointmentTime,
            "type" => O2oErrandOrder::TYPE_SEND,
            "coupon_id" => 0,
            "gratuity" => fentoyuan($postData["tips"] ?? 0),
            "out_order_no" => $postData["source_order_no"],
            "app_key" => $appKey,
            "title" => $postData["order_info"]["sn"] ? platformName . '#' . $postData["order_info"]["sn"] : "",
            "remark" => trim(($postData["remark"] ?? "") . " " . $orderInfoText),
            "hide_address" => false,
            "is_special" => false,
            "need_incubator" => false,
            "goods_info" => [
                "price" => fentoyuan($postData["order_info"]["paid_fee"] ?? 0),
                "is_protect_price" => false,
                "desc" => ($postData["order_info"]["goods_list"][0]["name"] ?? "") . "等",
                "imgs" => [],
                "goods_category_id" => $goodsCategoryMap[$postData["order_info"]["category"] ?? ""] ?? 14,
                "category_id" => 0,
                "weight" => $postData["order_info"]["weight"] ?? 0,
                "volume" => "",
            ],
            "start_point" => [
                "mode" => 0,
                "address_id" => $startAddress->id,
                "pickup_code" => false,
                "pickup_code_mode" => 0,
            ],
            "end_point" => [
                "address_id" => $endAddress->id,
                "receive_code" => false,
                "receive_code_mode" => 0,
            ],
        ];

        return $service->createOrder($userId, $orderParams, true, $merchantId);
    }

    /**
     * 构建订单附加信息文本
     *
     * @param array $postData 订单数据
     * @return string 格式化的订单信息
     */
    private function buildOrderInfoText($postData)
    {
        $orderInfo = $postData["order_info"] ?? [];
        
        // 平台名称映射
        $platformMap = [
            'meituan' => '美团',
            'ele' => '饿了么',
            'eleme' => '饿了么'
        ];
        
        $infoArray = [];
        
        // 添加平台信息
        if (!empty($orderInfo["source"])) {
            $platformName = $platformMap[$orderInfo["source"]] ?? $orderInfo["source"];
            $infoArray[] = "来源：{$platformName}";
        }
        
        // 添加订单编号
        if (!empty($orderInfo["source_no"])) {
            $infoArray[] = "订单号：{$orderInfo["source_no"]}";
        }
        
        // 添加序列号
        if (!empty($orderInfo["sn"])) {
            $infoArray[] = "序号：{$orderInfo["sn"]}";
        }
        
        // 添加渠道名称
        if (!empty($orderInfo["channel_name"])) {
            $infoArray[] = "店铺：{$orderInfo["channel_name"]}";
        }
        
        // 添加商品分类
        if (!empty($orderInfo["category"])) {
            $infoArray[] = "分类：{$orderInfo["category"]}";
        }
        
        return implode(' | ', $infoArray);
    }

    /**
     * 获取商品分类映射表
     *
     * @return array 分类映射
     */
    private function getGoodsCategoryMap()
    {
        return [
            "xiaochi" => 5,  // 小吃美食
            "canyin" => 5,   // 正餐快餐
            "shaokao" => 5,  // 龙虾烧烤
            "dangao" => 8,   // 烘焙蛋糕
            "tianpin" => 5,  // 甜品奶茶
            "liaoli" => 5,   // 西餐料理
            "huoguo" => 5,   // 火锅串串
            "xianhua" => 9,  // 浪漫鲜花
            "shuiguo" => 7,  // 生鲜果蔬
            "yinpin" => 14,  // 酒水零售
            "chaoshi" => 14, // 超市百货
            "chengren" => 14,// 医药成人
        ];
    }

    /**
     * 格式化订单结果数据
     *
     * @param O2oErrandOrder $order 订单对象
     * @return array 格式化的结果数据
     */
    private function formatOrderResultData($order)
    {
        return [
            "pay_amount" => $order->actual_amount + $order->gratuity + $order->goods_protected_price,
            "coupon" => $order->coupon_amount,
            "distance" => $order->distance,
            "premium" => 0,
            "weight" => $order->weight * 1000,
            "overweight" => 0,
            "tips" => $order->gratuity,
            "at_time" => $order->estimated_delivery_time->timestamp - $order->appointment_start_time->timestamp,
            "expect_time" => $order->estimated_delivery_time->timestamp,
            "order_no" => $order->order_no,
            "source_order_no" => $order->out_order_no,
        ];
    }

    private function tips($order, $amount)
    {
        if ($amount <= 0) {
            return [];
        }

        // 基本状态检查
        if ($order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            throw new \Exception("该订单已取消");
        }
        if ($order->order_status < O2oErrandOrder::STATUS_PAID || $order->order_status >= O2oErrandOrder::STATUS_FINISH) {
            throw new \Exception("该订单未支付或已完成");
        }

        // 判断是否为麦芽田订单
        if (in_array($order->app_key, [O2oErrandOrder::APP_KEY_MYT, O2oErrandOrder::APP_KEY_WRC])) {
            // 麦芽田订单 - 从商家余额扣除
            return $this->processMerchantTips($order, $amount);
        } else {
            // 普通订单 - 从用户余额扣除
            return $this->processUserTips($order, $amount);
        }
    }

    /**
     * 处理从商家余额扣除小费
     *
     * @param O2oErrandOrder $order 订单
     * @param int $amount 小费金额
     * @return array 空数组
     * @throws BusinessException 当商家余额不足或处理失败时
     */
    private function processMerchantTips($order, $amount)
    {
        try {
            // 获取麦芽田商家信息
            $userId = $order->user_id ?? '';
            $token = MerchantToken::where('platform', $order->app_key)
                ->where('user_id', $userId)->with('merchant')->first();

            $merchant = $token->merchant;

            if (!$merchant) {
                Log::channel($order->app_key)->warning('未找到商家信息，无法处理商家小费', [
                    'order_no' => $order->order_no,
                    'user_id' => $userId
                ]);
                throw new BusinessException("未找到商家信息，无法处理小费");
            }

            // 检查商家余额是否足够
            if ($amount > $merchant->balance) {
                throw new BusinessException("商家余额不足");
            }

            // 使用数据库原子操作更新余额
            $beforeBalance = $merchant->balance;
            $merchant->decrement('balance', $amount);

            // 记录商家账户变动（使用更新后的余额）
            $afterBalance = $merchant->fresh()->balance;
            MerchantAccountLog::create([
                'merchant_id' => $merchant->id,
                'amount' => -$amount,
                'before_balance' => $beforeBalance,
                'after_balance' => $afterBalance,
                'type' => MerchantAccountLog::TYPE_TIP,
                'order_no' => $order->order_no,
                'remark' => '麦芽田订单加小费'
            ]);

            // 增加订单小费
            O2oErrandOrder::query()->where("id", $order->id)->increment("gratuity", $amount);

            Log::channel($order->app_key)->info('商家支付小费成功', [
                'merchant_id' => $merchant->id,
                'order_no' => $order->order_no,
                'amount' => $amount
            ]);

            return [];

        } catch (BusinessException $e) {
            // 业务异常直接向上抛出
            throw $e;
        } catch (\Exception $e) {
            // 其他异常记录日志并转为业务异常
            Log::channel($order->app_key)->error('商家扣款过程出错', [
                'order_no' => $order->order_no,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new BusinessException("商家扣款处理失败：" . $e->getMessage());
        }
    }

    /**
     * 处理从用户余额扣除小费
     *
     * @param O2oErrandOrder $order 订单
     * @param int $amount 小费金额
     * @return array 空数组
     * @throws BusinessException 当用户余额不足或处理失败时
     */
    private function processUserTips($order, $amount)
    {
        try {
            // 获取用户账户
            $account = UserAccount::query()->firstOrCreate(['user_id' => $order->user_id]);

            // 检查用户余额是否足够
            if ($amount > $account->amount) {
                throw new BusinessException("用户余额不足");
            }

            // 单独事务处理用户扣款
            DB::transaction(function () use ($order, $account, $amount) {
                // 扣减用户余额
                $account->decrease($amount, UserAccountFlow::BUSINESS_TYPE_PAYMENT, $order->order_no, "订单加小费");

                // 增加订单小费
                O2oErrandOrder::query()->where("id", $order->id)->increment("gratuity", $amount);

                Log::channel($order->app_key)->info('用户支付小费成功', [
                    'user_id' => $account->user_id,
                    'order_no' => $order->order_no,
                    'amount' => $amount
                ]);
            });

            return [];

        } catch (BusinessException $e) {
            // 业务异常直接向上抛出
            throw $e;
        } catch (\Exception $e) {
            // 其他异常记录日志并转为业务异常
            Log::channel($order->app_key)->error('用户扣款过程出错', [
                'order_no' => $order->order_no,
                'user_id' => $order->user_id,
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            throw new BusinessException("用户扣款处理失败：" . $e->getMessage());
        }
    }


    public function wrcCallback(Request $request, $command)
    {
        Log::channel("wrc")->info("无人仓回调", ['command' => $command, 'data' => $request->all()]);
        try {
            $data = $request->all();

            $client = new WrcService();
            if (($data["sign"] ?? 0) <= 0){
                (new WrcService())->validateSign($request->header("app-key"), $request->header("sign"), $data);
            }

            // TODO 获取商户和user信息
            $merchantId = 0;
            $userId = 0;
            $shopId = $data["shop_id"] ?? '';
            if (!empty($shopId)) {
                $merchantInfo = $client->getMerchantInfoByShopId($shopId);
                if (empty($merchantInfo)) throw new \Exception("该门店未授权");
                $merchantId = $merchantInfo["merchant_id"] ?? 0;
                $userId = $merchantInfo["user_id"] ?? 0;
            }

            $order = null;
            if (($data["source_order_no"] ?? "")) {
                $order = O2oErrandOrder::query()->where("out_order_no", $data["source_order_no"])->where("app_key", O2oErrandOrder::APP_KEY_WRC)->first();
            }

            switch ($command) {
                case "capacity": // 是否开通运力
                    $site = app(CommonService::class)->getPointSite(["lng" => $data["lng"], "lat" => $data["lat"]]);
                    $result = ["status" => $site ? 1 : 0];
                    break;
                case "valuating": // 订单计费接口
                    $result = $this->valuating($userId, $order, $data);
                    break;
                case "send": // 下单接口
                    if ($order) throw new \Exception("订单已存在");
                    $result = $this->send($userId, $data, O2oErrandOrder::APP_KEY_WRC);
                    break;
                case "tips": // 添加小费接口
                    if (!$order) throw new \Exception("订单不存在");
                    $result = $this->tips($order, $data["tips"]);
                    break;
                case "cancel": // 取消配送
                    if (!$order) throw new \Exception("订单不存在");
                    $reason = $data["cancel_reason"];
                    if (empty($reason)) {
                        $reason = [1000 => "订单信息填写错误", 1001 => "已用其他发单工具配送", 1002 => "没有骑手接单", 1005 => "配送方系统超时", 1007 => "订单取消", 1008 => "其他原因"][$data["cancel_reason_type"]] ?? "其他原因";
                    }
                    (new O2oErrandOrderService())->refundOrder($order->order_no, $reason);
                    $result = ["status" => 1];
                    break;
                case "rider_location": // 获取骑手当前位置
                    if (!$order) throw new \Exception("订单不存在");
                    $result = [
                        "order_no" => $order->order_no,
                        "source_order_no" => $order->out_order_no,
                        "rider_name" => "", "rider_phone" => "", "longitude" => "", "latitude" => "",
                        "at_time" => time(),
                    ];
                    if ($order->rider_id) {
                        $rider = Rider::query()->where("id", $order->rider_id)->first();
                        $result["rider_name"] = $rider->name;
                        $result["rider_phone"] = $rider->phone;
                        if ($order->order_status < O2oErrandOrder::STATUS_FINISH) {
                            $riderLocationService = app(RiderLocationService::class);
                            $location = $riderLocationService->getRiderLocation($order->rider_id);
                            if ($location) {
                                $result["longitude"] = $location['lng'] . "";
                                $result["latitude"] = $location['lat'] . "";
                            }
                        }
                    }
                    break;
                case "delivering":
                    if (!$order) throw new \Exception("订单不存在");
                    if($order->refund_status != O2oErrandOrder::REFUND_STATUS_INIT) throw new \Exception("订单已退款");
                    if (!($order->order_status >= O2oErrandOrder::STATUS_PICKUP &&  $order->order_status < O2oErrandOrder::STATUS_DELIVERY)) throw new \Exception("订单状态不正确");
                    $order->order_status = O2oErrandOrder::STATUS_DELIVERY;
                    $order->pickup_at = Carbon::now();
                    $order->save();
                    break;
                default:
                    return response()->json(["code" => 400, "message" => "The '$command' is not supported.", "data" => ""]);
            }
        } catch (\Exception $e) {
            Log::channel("wrc")->error('无人仓回调异常', [
                'command' => $command ?? 'unknown',
                'data' => $request->all(),
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json(["code" => 500, "message" => $e->getMessage(), "data" => ""]);
        }
        return response()->json(["code" => 200, "message" => "SUCCESS", "data" => $result ? json_encode($result) : ""]);
    }
}
