<?php

namespace App\Http\Controllers\Api;

use App\Models\Rider;
use App\Models\RiderSetting;
use Illuminate\Http\Request;

class RiderSettingController extends Controller
{
    public function index(Request $request)
    {
        $user = $request->user();

        $result = [
            'distance' => [
                [
                    'name' => '近距离',
                    'distance' => '0-3km',
                    'flag' => true,
                ],
                [
                    'name' => '中距离',
                    'distance' => '0-5km',
                    'flag' => false,
                ],
                [
                    'name' => '远距离',
                    'distance' => '5-10km',
                    'flag' => false,
                ]
            ],
            'categories' => [
            ],
            'around_push' => true,
        ];

        $riderSetting = RiderSetting::query()->where('user_id', $user->id)->first();
        if ($riderSetting) {
            $result = [
                'distance' => $riderSetting->distance_infos,
                'categories' => [
                ],
                'around_push' => $riderSetting->around_push,
            ];
        }

        return $this->success('success', $result);

    }

    public function update(Request $request)
    {
        $user = $request->user();

        $riderSetting = RiderSetting::query()->where('user_id', $user->id)->first();

        if (!$riderSetting) {
            $riderSetting = new RiderSetting();
            if ($request->distance) {
                $riderSetting->distance = $request->distance;
            } else {
                $riderSetting->distance = '100';
            }
            $riderSetting->cids = [];
            if ($request->has('around_push')) {
                $riderSetting->around_push = $request->around_push;
            } else {
                $riderSetting->around_push = true;
            }
            $riderSetting->save();
        } else {
            if ($request->distance) {
                $riderSetting->distance = $request->distance;
            } else {
                $riderSetting->distance = '100';
            }
            $riderSetting->cids = [];
            if ($request->has('around_push')) {
                $riderSetting->around_push = $request->around_push;
            } else {
                $riderSetting->around_push = true;
            }
            $riderSetting->save();
        }

        $result = [
            'distance' => $riderSetting->distance_infos,
            'categories' => [
            ],
            'around_push' => $riderSetting->around_push,
        ];

        return $this->success('success', $result);
    }
}
