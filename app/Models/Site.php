<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class Site extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'name', 'province', 'city', 'district', 'address', 'contact_name', 'phone', 'latitude', 'longitude', 'farthest'
    ];

    const GEO_KEY = 'site';

    public static function GetSiteFromRedis($lng, $lat, $distance)
    {
        return Redis::georadius(self::GEO_KEY, $lng, $lat, $distance, 'km', 'WITHDIST','WITHCOORD', 'ASC');
    }

    public function regions()
    {
        return $this->hasMany(SiteRegion::class, 'site_id');
    }

    public function getLocation()
    {
        return Redis::geopos(self::GEO_KEY, $this->id)[0];
    }

    public function uploadLocation()
    {
        if (!$this->getLocation()) {
            Redis::geoadd(self::GEO_KEY, $this->longitude, $this->latitude, $this->id);
        }
    }

    public static function PushLocation()
    {
        $sites = Site::query()->get();
        foreach ($sites as $site) {
            $site->uploadLocation();
        }
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::saved(function ($site) {
            Redis::geoadd(self::GEO_KEY, $site->longitude, $site->latitude, $site->id);
        });
        static::deleted(function ($model) {
            Redis::zrem(self::GEO_KEY, $model->id);
        });
    }
}
