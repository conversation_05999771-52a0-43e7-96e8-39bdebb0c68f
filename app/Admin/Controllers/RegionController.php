<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Region;
use App\Models\Common;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RegionController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Region(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableEditButton();
            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('pid');
            $grid->column('level');
            $grid->column('code');
            $grid->column('is_hot')->switch();
            $grid->column('letter');
            $grid->column('pinyin');
            $grid->column('status', '状态')->switch();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand(true);
                $filter->panel();
                $filter->like('name')->width(4);
                $filter->equal('pid', '上级')->select(\App\Models\Region::query()
                    ->where('status', 1)
                    ->where('level', '<', 3)
                    ->pluck('name', 'id'))
                    ->width(4);
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Region(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('pid');
            $show->field('level');
            $show->field('code');
            $show->field('is_hot');
            $show->field('letter');
            $show->field('pinyin');
            $show->field('status');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Region(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->text('pid');
            $form->text('level');
            $form->text('code');
            $form->switch('is_hot');
            $form->text('letter');
            $form->text('pinyin');
            $form->switch('status');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }

    public function provinces(){
        return \App\Models\Region::query()->where("level", Common::REGION_LEVEL_PROVINCE)->get(['id', 'name as text']);
    }

    public function cities(){
        $query = \App\Models\Region::query()->where("level", Common::REGION_LEVEL_CITY);
        if ($q = request()->input('q', '')) {
            $query->where('pid', $q);
        }
        return $query->get(['id', 'name as text']);
    }

    public function districts(){
        $query = \App\Models\Region::query()->where("level", Common::REGION_LEVEL_DISTRICT);
        if ($q = request()->input('q', '')) {
            $query->where('pid', $q);
        }
        return $query->get(['id', 'name as text']);
    }
}
