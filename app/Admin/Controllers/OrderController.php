<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Order;
use App\Models\Common;
use App\Models\Shop;
use App\Models\User;
use Carbon\Carbon;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Table;

class OrderController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Order(), function (Grid $grid) {
            $grid->model()->with(['details', 'user', 'shop'])->orderBy('id', 'desc');

            $grid->disableCreateButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->disableActions();
            $grid->export()->disableExportSelectedRow()->disableExportCurrentPage();
            $grid->fixColumns(2);

            $grid->column("id");
            $grid->column('order_no')->width(200);
            $grid->column("user.nickname", "用户")->width(100);
            $grid->column("shop.name", "店铺")->width(100);
            $grid->column('receiver_info', '收货信息')->display('详情')->modal(function (Grid\Displayers\Modal $modal) {
                $modal->title("收货信息");
                \Admin::style('.table td{padding: .85rem .55rem}');
                $data = [
                    ['name' => '收货人', 'value' => $this->receiver_name],
                    ['name' => '手机号码', 'value' => $this->receiver_tel],
                    ['name' => '收件地址', 'value' => $this->receiver_address],
                ];
                return Table::make(['类目', '值'], $data);
            })->width(100);
            $grid->column('spu_info', '商品信息')->display('详情')->modal(function (Grid\Displayers\Modal $modal) {
                $modal->title("商品信息");
                \Admin::style('.table td{padding: .85rem .55rem}');
                $data = [];
                foreach($this->details as $item){
                    $data[] = [
                        $item->spu_name, fentoyuan($item->price), fentoyuan($item->discount_price), $item->quantity
                    ];
                }
                return Table::make(['商品名称', '原价/元', '优惠价/元', '购买数量'], $data);
            })->width(100);
            $grid->column('pay_amount')->display(function($value){
                return fentoyuan($value);
            });
            $grid->column('pay_method')->using(Common::PayMethodMap)->label();
            $grid->column('paid_at');
            $grid->column('status')->using(\App\Models\Order::StatusMap)->label();
            $grid->column('sending_time', '配送时间')->display(function(){
                return $this->sending_start_time." - ".$this->sending_end_time;
            })->width(200);
            $grid->column('remark')->width(200);
            $grid->column('is_tel_protect')->using([true => "是", false => "否"])->label();
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('order_no')->width(3);
                $filter->like('details.spu_name', "商品名称")->width(3);
                $filter->in('shop_id')->multipleSelect(Shop::query()->pluck("name", "id"))->width(2);
                $filter->in('user_id')->multipleSelect(User::query()->pluck("nickname", "id"))->width(2);
                $filter->where("receiver", function ($query) {
                    $query->where('receiver_name', 'like', "%{$this->input}%")
                        ->orWhere('receiver_tel', 'like', "%{$this->input}%");
                }, "收货人")->width(2);
                $filter->where("status", function ($query){
                    $status = $this->input;
                    switch ($status) {
                        case \App\Models\Order::STATUS_WAITING_PAY:
                            $query->where("closed", false)->whereNull("paid_at");
                            break;
                        case \App\Models\Order::STATUS_PAID:
                            $query->validOrders()->where("paid_at", ">", Carbon::now()->addSeconds(-1*config('app.order_paid_ttl')));
                            break;
                        case \App\Models\Order::STATUS_TAKING:
                            $query->validOrders()->where("paid_at", "<=", Carbon::now()->addSeconds(-1*config('app.order_paid_ttl')))->where("sending_start_time", ">", Carbon::now());
                            break;
                        case \App\Models\Order::STATUS_DELIVERY:
                            $query->validOrders()->where("sending_start_time", "<=", Carbon::now())->where("sending_end_time", ">", Carbon::now());
                            break;
                        case \App\Models\Order::STATUS_FINISHED:
                            $query->validOrders()->where("sending_end_time", "<=", Carbon::now());
                            break;
                        case \App\Models\Order::STATUS_CANCEL:
                            $query->whereRaw("(closed = 1 or refund_type < 0)");
                            break;
                        default:
                            if ($status >= 10) {
                                $query->where("closed", false)->whereNotNull("paid_at")->where("refund_type", $status);
                            }
                    }
                }, "状态")->select(\App\Models\Order::StatusMap)->width(2);
                $filter->in('pay_method')->multipleSelect(Common::PayMethodMap)->width(2);
                $filter->between('created_at')->datetime()->width(3);
                $filter->between('paid_at')->datetime()->width(3);
            });
        });
    }
}
