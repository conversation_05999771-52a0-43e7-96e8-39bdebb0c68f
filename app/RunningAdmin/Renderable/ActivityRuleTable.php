<?php


namespace App\RunningAdmin\Renderable;

use App\RunningAdmin\Repositories\ActivityRule;
use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;

class ActivityRuleTable extends LazyRenderable
{

    public function __construct(array $payload = [])
    {
        parent::__construct($payload);
    }

    public function grid(): Grid
    {
        return Grid::make(new ActivityRule(), function (Grid $grid) {
            $grid->model()->where('activity_id', $this->payload['activity_id']);
            $grid->column('activity_range', '奖励区间');
            $grid->column('order_count', '订单数');
            $grid->column('reward', '奖励金额');
            $grid->disableActions();
            $grid->disableRowSelector();
            $grid->disableRefreshButton();
            $grid->disableCreateButton();
            $grid->disableToolbar();
            $grid->withBorder(false);
        });
    }
}
