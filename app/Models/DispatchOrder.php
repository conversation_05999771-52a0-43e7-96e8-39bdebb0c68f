<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DispatchOrder extends Model
{
    use HasFactory,HasDateTimeFormatter;

    protected $fillable = [
        'order_id', 'status', 'closed', 'rider_id', 'remark', 'operator_id', 'operator_name'
    ];

    public function rider()
    {
        return $this->belongsTo(Rider::class, 'rider_id');
    }

    public function order()
    {
        return $this->belongsTo(O2oErrandOrder::class, 'order_id');
    }
}
