<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\SystemConfig;
use Dcat\Admin\Admin;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class SystemConfigController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new SystemConfig(), function (Grid $grid) {
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $grid->disableEditButton();
            $grid->disableCreateButton();
            $grid->showQuickEditButton();
            $grid->enableDialogCreate();
            $grid->model()->where('platform', \App\Models\SystemConfig::PlatformPT);
            $grid->column("id");
            $grid->column("name");
            $grid->column("remark")->width(200);
            $grid->column("value");

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');

            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new SystemConfig(), function (Show $show) {
            $show->field('id');
            $show->field('platform');
            $show->field('name');
            $show->field('value');
            $show->field('is_json');
            $show->field('last_update_user');
            $show->field('remark');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new SystemConfig(), function (Form $form) {
            $form->display('id');
            $form->hidden('platform');
            $form->hidden('last_update_user');
            $form->select('name')->options(\App\Models\SystemConfig::ParamRemarkMapForPt)->required();
            switch ($form->model()->name) {
                case \App\Models\SystemConfig::PARAM_CANCEL_ORDER_REASON:
                case \App\Models\SystemConfig::PARAM_REFUND_ORDER_REASON:
                case \App\Models\SystemConfig::PARAM_REJECT_REASON:
                case \App\Models\SystemConfig::PARAM_RIDER_REJECT_ORDER_REASON:
                    $form->hidden("is_json")->default(1);
                    $form->list('value')->saving(function ($v) {
                        return json_encode($v, JSON_UNESCAPED_UNICODE);
                    });
                    break;
                case \App\Models\SystemConfig::PARAM_ORDER_QA:
                    $form->hidden("is_json")->default(1);
                    $form->table('value', function ($table) {
                        $table->text("q", "问题");
                        $table->text("a", "客服链接");
                    })->saving(function ($v) {
                        return json_encode($v, JSON_UNESCAPED_UNICODE);
                    });
                    break;
                case \App\Models\SystemConfig::PARAM_OPEN_HEALTHY_CHECK:
                    $form->hidden("is_json")->default(0);
                    $form->switch('value');
                    break;
                case \App\Models\SystemConfig::PARAM_DISTANCE_SCHEME:
                    $form->hidden("is_json")->default(0);
                    $form->select('value')->options([1 => "最短", 2 => "最长"]);
                    break;
                default:
                    $form->hidden("is_json")->default(0);
                    $form->textarea("value")->rows(3);
            }
            $form->hidden("remark");
            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function ($form) {
            $form->remark = \App\Models\SystemConfig::ParamRemarkMapForPt[$form->name];
            $form->platform = \App\Models\SystemConfig::PlatformPT;
            $form->last_update_user = Admin::user()->username;
        });
    }
}
