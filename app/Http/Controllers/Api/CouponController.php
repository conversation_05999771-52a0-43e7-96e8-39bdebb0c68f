<?php

namespace App\Http\Controllers\Api;

use App\Http\Resources\CouponResource;
use App\Models\Common;
use App\Models\Coupon;
use Carbon\Carbon;
use Illuminate\Http\Request;

class CouponController extends Controller
{
    public function index(Request $request)
    {
        $coupons = Coupon::query()
            ->where("status", Common::STATUS_UP)
            ->where('stock', '>', 0)
            ->where(function ($query) {
                $query->where("validity->type", "<>", Coupon::VALIDITY_TYPE_RANGE)->orWhere(function ($query) {
                    $query->where('validity->type', Coupon::VALIDITY_TYPE_RANGE)
                        ->where('validity->end', '>', Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT));
                });
            })
            ->orderBy("sort", "asc")
            ->orderBy("sales", "desc")
            ->paginate($request->get("page_size", $this->pageSize));
        $result = [
            'total' => $coupons->total(),
            'has_next' => $coupons->hasMorePages(),
            'data' => [],
        ];
        if (count($coupons) > 0) {
            foreach ($coupons as $item) {
                $result['data'][] = new CouponResource($item);
            }
        }
        return $this->success('success', $result);
    }
}
