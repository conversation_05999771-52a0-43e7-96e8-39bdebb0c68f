<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ActivityRule extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $appends = ['activity_range'];

    public function getActivityRangeAttribute()
    {
        return '[' . $this->start . ',' . $this->end . ')';
    }

    public function getRewardAttribute($val)
    {
        return fentoyuan($val);
    }
}
