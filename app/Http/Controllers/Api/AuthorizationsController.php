<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\AuthorizationRequest;
use App\Http\Requests\Api\ForgetPasswordRequest;
use App\Models\User;
use App\Models\UserAccount;
use App\Models\UserAuth;
use App\Models\UserAuthRel;
use App\Models\UserProfile;
use App\Services\UserService;
use EasyWeChat\MiniApp\Application;
use Illuminate\Auth\AuthenticationException;
use App\Http\Requests\Api\SocialAuthorizationRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Overtrue\LaravelSocialite\Socialite;
use Overtrue\LaravelWeChat\EasyWeChat;

class AuthorizationsController extends Controller
{
    public function store(AuthorizationRequest $request)
    {
        switch ($authType = $request->auth_type) {
            case 'password':
                $phone = $request->phone;
                $credentials['phone'] = $phone;
                $credentials['password'] = $request->password;
                if (!$token = Auth::guard('api')->attempt($credentials)) {
                    $this->errorResponse(500, '用户名或密码错误');
                }
                $ttl = 365 * 24 * 60;
                $user = User::query()->where('phone', $phone)->first();
                $token = auth('api')->setTTL($ttl)->login($user);
                break;
            case 'smsCode':
                $cacheKey = 'verificationCode_' . $request->verification_key;
                $verifyData = Cache::get($cacheKey);

                if (app()->environment('production')) {
                    if (!$verifyData) {
                        $this->errorResponse(500, '验证码已失效');
                    }

                    if (!hash_equals($verifyData['code'], $request->verification_code)) {
                        // 返回401
                        $this->errorResponse(500, '验证码错误');
                    }
                }

                $user = User::query()->where('phone', $verifyData['phone'])->first();
                if (!$user) {
                    $this->errorResponse(500, '用户不存在');
                }
                // 一年以后过期，单位分钟
                $ttl = 365 * 24 * 60;
                $token = auth('api')->setTTL($ttl)->login($user);
                break;
            default:
                $this->errorResponse(500, '登录方式不合法');
        }

        return $this->respondWithToken($token);
    }

    public function forgetPassword(ForgetPasswordRequest $request)
    {
        $cacheKey = 'verificationCode_' . $request->verification_key;
        $verifyData = Cache::get($cacheKey);
        if (!$verifyData) {
            $this->errorResponse(403, '验证码已失效');
        }
        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(403, '验证码错误');
        }
        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(403, '验证码错误');
        }
        $user = User::query()->where('phone', $verifyData['phone'])->first();
        if (!$user) {
            $this->errorResponse(500, '用户不存在');
        }
        $user->password = Hash::make($request->password);
        $user->save();

        return $this->success("密码修改成功", []);
    }

    public function socialStore($type, SocialAuthorizationRequest $request, UserService $service)
    {
        $driver = Socialite::create($type);

        try {
            if ($code = $request->code) {
                $oauthUser = $driver->userFromCode($code);
            } else {
                $tokenData['access_token'] = $request->access_token;

                // 微信需要增加 openid
                if ($type == 'wechat') {
                    $driver->withOpenid($request->openid);
                }

                $oauthUser = $driver->userFromToken($request->access_token);
            }
        } catch (\Exception $e) {
            throw new AuthenticationException($e->getMessage());
        }

        if (!$oauthUser->getId()) {
            throw new AuthenticationException('参数错误，未获取用户信息');
        }

        switch ($type) {
            case 'wechat':
                $auth = UserAuth::query()->where('union_id', $oauthUser['raw']['unionid'])
                    ->where('login_type', UserAuth::LoginTypeWechat)
                    ->first();
                if (!$auth) {
                    $auth = new UserAuth();
                    $auth->openid = $oauthUser['raw']['openid'];
                    $auth->union_id = $oauthUser['raw']['unionid'];
                    $auth->login_type = UserAuth::LoginTypeWechat;
                    $auth->access_token = $oauthUser['access_token'];
                    $auth->avatar = $oauthUser['raw']['headimgurl'];
                    $auth->nickname = $oauthUser['raw']['nickname'];
                    $auth->save();
                } else {
                    $auth->access_token = $oauthUser['access_token'];
                    $auth->avatar = $oauthUser['raw']['headimgurl'];
                    $auth->nickname = $oauthUser['raw']['nickname'];
                    $auth->save();
                }
                break;
        }

        $rel = UserAuthRel::query()->where('auth_id', $auth->id)->with('user')->first();

        $token = null;

        if ($rel && $rel->user) {
            $ttl = 365 * 24 * 60;
            $token = auth('api')->setTTL($ttl)->login($rel->user);
        }
        $res = [
            'auth_id' => $auth->id,
            'access_token' => $token
        ];

        return $this->success('success', $res);
    }

    public function bindPhone(Request $request, UserService $service)
    {
        $cacheKey = 'verificationCode_' . $request->verification_key;
        $authId = $request->auth_id;
        $verifyData = Cache::get($cacheKey);

        if (!$verifyData) {
            $this->errorResponse(403, '验证码已失效');
        }

        if (!hash_equals($verifyData['code'], $request->verification_code)) {
            // 返回401
            $this->errorResponse(500, '验证码错误');
        }

        $userAuth = UserAuth::query()->find($authId);

        $user = User::query()->where('phone', $verifyData['phone'])->first();
        DB::beginTransaction();
        try {
            if ($user) {
                $relIds = UserAuthRel::query()->where('user_id', $user->id)
                    ->where('auth_id', '!=', $authId)
                    ->pluck('auth_id')->toArray();
                if ($relIds) {
                    $userAuthExisted = UserAuth::query()->whereIn('id', $relIds)
                        ->where('login_type', $userAuth->login_type)->count();
                    if ($userAuthExisted) $this->errorResponse(500, '该手机号已经绑定其它账号');
                }
                $rel = new UserAuthRel();
                $rel->user_id = $user->id;
                $rel->auth_id = $authId;
                $rel->save();
            } else {
                // 注册用户
                $user = $service->registerUser($verifyData['phone'], '123456', $request->invite_code, $this->platform);
                $rel = new UserAuthRel();
                $rel->user_id = $user->id;
                $rel->auth_id = $authId;
                $rel->save();
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage());
            $this->errorResponse(500, $e->getMessage());
        }

        // 清除验证码缓存
        Cache::forget($cacheKey);

        // 一年以后过期，单位分钟
        $ttl = 365 * 24 * 60;
        $token = auth('api')->setTTL($ttl)->login($user);

        return $this->respondWithToken($token);

    }


    protected function respondWithToken($token)
    {
        return $this->success('登录成功', [
            'access_token' => $token,
            'token_type' => 'Bearer',
            'expires_in' => Auth::guard('api')->factory()->getTTL() * 60
        ]);
    }
}
