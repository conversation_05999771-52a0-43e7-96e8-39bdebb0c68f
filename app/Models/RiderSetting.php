<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RiderSetting extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = ['distance', 'cids', 'around_push', 'user_id'];

    protected $appends = ['distance_infos'];

    protected $casts = ['around_push' => 'boolean', 'cids' => 'json'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function getDistanceInfosAttribute()
    {
        $distance = [];
        $strArray = str_split($this->distance);
        foreach ($strArray as $key => $value) {
            if ($key == 0) {
                $info = [
                    'name' => '近距离',
                    'distance' => '0-3km',
                    'flag' => $value > 0,
                ];
            }
            if ($key == 1) {
                $info = [
                    'name' => '中距离',
                    'distance' => '3-5km',
                    'flag' => $value > 0,
                ];
            }
            if ($key == 2) {
                $info = [
                    'name' => '远距离',
                    'distance' => '5-10km',
                    'flag' => $value > 0,
                ];
            }

            $distance[] = $info;
        }
        return $distance;
    }

}
