<?php

namespace App\Listeners;

use App\Events\RechargeOrderPaid;
use App\Models\UserAccountFlow;

class AddUserAmount
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle(RechargeOrderPaid $event)
    {
        $order = $event->order;

        $account = $order->user->userAccount;

        $account->add($order->order_amount, UserAccountFlow::BUSINESS_TYPE_PAYMENT, $order->order_no, '余额充值');
    }
}
