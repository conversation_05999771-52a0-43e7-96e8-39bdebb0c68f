<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Log;

class MerchantRechargeOrder extends Model
{
    use HasFactory, HasDateTimeFormatter, SoftDeletes;

    const RECHARGE_ORDER_PREFIX = '90';

    // 订单状态常量 (通过 closed 和 paid_at 字段组合判断)
    const STATUS_PENDING    = 'pending';    // 待支付
    const STATUS_PAID       = 'paid';       // 已支付
    const STATUS_CLOSED     = 'closed';     // 已关闭

    // 退款状态常量
    const REFUND_STATUS_PENDING    = 'pending';
    const REFUND_STATUS_APPLIED    = 'applied';
    const REFUND_STATUS_PROCESSING = 'processing';
    const REFUND_STATUS_SUCCESS    = 'success';
    const REFUND_STATUS_FAILED     = 'failed';

    // 支付方式常量
    const PAY_METHOD_ALIPAY     = 1;    // 支付宝
    const PAY_METHOD_WECHAT     = 2;    // 微信支付
    const PAY_METHOD_CLOUDFLASH = 3;    // 云闪付

    // 状态映射
    public static $statusMap = [
        self::STATUS_PENDING => '待支付',
        self::STATUS_PAID    => '已支付',
        self::STATUS_CLOSED  => '已关闭',
    ];

    public static $refundStatusMap = [
        self::REFUND_STATUS_PENDING    => '未退款',
        self::REFUND_STATUS_APPLIED    => '已申请退款',
        self::REFUND_STATUS_PROCESSING => '退款中',
        self::REFUND_STATUS_SUCCESS    => '退款成功',
        self::REFUND_STATUS_FAILED     => '退款失败',
    ];

    public static $payMethodMap = [
        self::PAY_METHOD_ALIPAY     => '支付宝',
        self::PAY_METHOD_WECHAT     => '微信支付',
        self::PAY_METHOD_CLOUDFLASH => '云闪付',
    ];

    protected $fillable = [
        'merchant_id',
        'order_no',
        'order_amount',
        'actual_amount',
        'reduce_amount',
        'paid_at',
        'closed',
        'refund_status',
        'refund_no',
        'refund_at',
        'pay_method',
        'payment_no',
    ];

    protected $dates = [
        'paid_at',
        'refund_at',
        'deleted_at',
    ];

    /**
     * 获取订单状态
     *
     * @return string
     */
    public function getStatusAttribute()
    {
        if ($this->closed) {
            return self::STATUS_CLOSED;
        }
        
        return $this->paid_at ? self::STATUS_PAID : self::STATUS_PENDING;
    }

    /**
     * 获取支付方式名称
     *
     * @return string|null
     */
    public function getPaymentMethodNameAttribute()
    {
        return $this->pay_method ? (self::$payMethodMap[$this->pay_method] ?? '未知') : null;
    }

    /**
     * 获取金额（元）
     *
     * @return float
     */
    public function getAmountAttribute()
    {
        return $this->actual_amount / 100;
    }

    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }

    public static function findAvailableNo()
    {
        // 订单流水号前缀
        $prefix = self::RECHARGE_ORDER_PREFIX . date('YmdHis');
        for ($i = 0; $i < 10; $i++) {
            // 随机生成 6 位的数字
            $no = $prefix . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            // 判断是否已经存在
            if (!static::query()->where('order_no', $no)->exists()) {
                return $no;
            }
        }
        Log::warning('find order no failed');

        return false;
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::creating(function ($model) {
            // 如果模型的 no 字段为空
            if (!$model->order_no) {
                // 调用 findAvailableNo 生成订单流水号
                $model->order_no = static::findAvailableNo();
                // 如果生成失败，则终止创建订单
                if (!$model->order_no) {
                    return false;
                }
            }
        });
    }
} 