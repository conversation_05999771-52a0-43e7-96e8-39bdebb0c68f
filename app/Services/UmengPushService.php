<?php

namespace App\Services;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class UmengPushService
{
    private $appKey;
    private $appMasterSecret;
    private $pushUrl = 'https://msgapi.umeng.com/api/send';

    public function __construct()
    {
        $this->appKey = config('umeng.android.app_key');
        $this->appMasterSecret = config('umeng.android.master_secret');
    }

    /**
     * 发送订单消息
     * @param string $deviceToken 设备token
     * @param string $deviceType 设备类型(android/ios)
     * @param array $message 消息内容
     * @return bool
     */
    public function sendOrderMessage($deviceToken, $deviceType, $message)
    {
        try {
            if ($deviceType === 'android') {
                return $this->sendAndroidUnicast($deviceToken, $message);
            } else {
                return $this->sendIOSUnicast($deviceToken, $message);
            }
        } catch (\Exception $e) {
            Log::error('友盟推送失败: ' . $e->getMessage());
            return false;
        }
    }

    /**
     * 发送Android单播消息
     */
    private function sendAndroidUnicast($deviceToken, $message)
    {
        $data = [
            'appkey' => $this->appKey,
            'timestamp' => time(),
            'type' => 'unicast',
            'device_tokens' => $deviceToken,
            'payload' => [
                'display_type' => 'notification',
                'body' => [
                    'ticker' => $message['title'],
                    'title' => $message['title'],
                    'text' => $message['content'],
                    'after_open' => 'go_app',
                    'custom' => $message['extra'] ?? []
                ]
            ]
        ];

        return $this->sendRequest($data);
    }

    /**
     * 发送iOS单播消息
     */
    private function sendIOSUnicast($deviceToken, $message)
    {
        $data = [
            'appkey' => $this->appKey,
            'timestamp' => time(),
            'type' => 'unicast',
            'device_tokens' => $deviceToken,
            'payload' => [
                'aps' => [
                    'alert' => [
                        'title' => $message['title'],
                        'body' => $message['content']
                    ],
                    'sound' => 'default',
                    'badge' => 1
                ],
                'extra' => $message['extra'] ?? []
            ]
        ];

        return $this->sendRequest($data);
    }

    /**
     * 发送HTTP请求
     */
    private function sendRequest($data)
    {
        $sign = $this->generateSign($data);
        $url = $this->pushUrl . '?sign=' . $sign;

        $response = Http::post($url, $data);

        if ($response->successful()) {
            $result = $response->json();
            return $result['ret'] === 'SUCCESS';
        }

        return false;
    }

    /**
     * 生成签名
     */
    private function generateSign($data)
    {
        $string = $data['appkey'] . $data['timestamp'] . $data['type'] . $data['device_tokens'];
        return md5($string . $this->appMasterSecret);
    }
} 