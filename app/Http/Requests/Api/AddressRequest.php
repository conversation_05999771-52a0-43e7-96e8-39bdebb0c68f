<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class AddressRequest extends FormRequest
{
    public function rules()
    {
        $rules = match (request()->method) {
            'POST' => [
                'name' => 'required|string',
                'tel' => 'required|phone:CN,mobile',
                'province' => 'required',
                'city' => 'required',
                'county' => 'required',
                'address_detail' => 'required',
                'latitude' => "required|numeric",
                'longitude' => "required|numeric",
            ],
            default => [],
        };


        return $rules;
    }
}
