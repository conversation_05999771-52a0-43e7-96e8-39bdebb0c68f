<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Community;
use App\Models\Region;
use App\Services\Addres\AddressService;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class CommunityController extends AdminController
{
    /**
     * 自定义地图模型
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function customMap()
    {
        return view('map');
    }

    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Community(), function (Grid $grid) {
            $grid->model()->with('region')->orderByDesc('id');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('region.name', '城市');
            $grid->column('latitude');
            $grid->column('longitude');
            $grid->column('is_open')->switch();
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new Community(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('region_id');
            $show->field('latitude');
            $show->field('longitude');
            $show->field('is_open');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Community(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->select('region_id', '城市')->options(Region::query()->where('status', 1)->pluck('name', 'id'));
            $form->html(view('coordinate'), '地区选择');
            $form->hidden('map_points', '坐标点'); // 隐藏域，用于接收坐标点（这里如果想数据回填可以，->value('49.121221,132.2321312')）
            $form->hidden('map_area', '当前区域'); // 隐藏域，用于接收详细点位地址
            $form->hidden('latitude');
            $form->hidden('longitude');
            $form->switch('is_open');
            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function ($form) {
            if ($form->map_points) {
                $points = $form->map_points;
                $pointArr = explode(',', $points);
                $form->latitude = $pointArr[0];
                $form->longitude = $pointArr[1];
            }
            $form->deleteInput('map_points');
            $form->deleteInput('map_area');
        });
    }
}
