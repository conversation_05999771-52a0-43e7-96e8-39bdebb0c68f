<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\BusinessException;
use App\Http\Requests\Api\CheckCodeRequest;
use App\Jobs\CloseTransOrder;
use App\Jobs\DispatchFinishTask;
use App\Jobs\DispatchHandleNewRiderActivity;
use App\Jobs\DispatchHandleNewUserActivity;
use App\Jobs\DispatchOpenCallback;
use App\Models\DispatchOrder;
use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\TransOrder;
use App\Services\Amap\GaodeService;
use App\Services\CommonService;
use App\Services\LocationService;
use App\Services\O2oErrandOrderService;
use App\Services\RiderOrderService;
use App\Services\TaskService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskController extends Controller
{

    /**
     * 任务列表
     * @param Request $request
     * @param LocationService $locationService
     * @param CommonService $commonService
     * @param TaskService $taskService
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function index(Request $request, LocationService $locationService, CommonService $commonService, TaskService $taskService)
    {
        $results = [];
        $user = $request->user();
        $rider = Rider::query()->where('user_id', $user->id)->first();

        $results['need_upload_health'] = $commonService->getHealthyStatus($user->id);
        if (!$rider) {
            $results['need_cert'] = 0;
            $results['earnest_money'] = true;
            $results['task_list'] = [];
            return $this->success('success', $results);
        } else {
            $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 2)->first();
            $results['need_cert'] = $rider->id_card_certified + $user->face_certified;
            $earnestMoney = true;
            // if ($account && $account->amount > 0) {
            //     $earnestMoney = true;
            // } else {
            //     // 判断骑手订单数量
            //     $count = O2oErrandOrder::query()->where('rider_id', $rider->id)->where('order_status', O2oErrandOrder::STATUS_FINISH)->count();
            //     if ($count < 1000) {
            //         // 新人无需保证金
            //         $earnestMoney = true;
            //     }
            // }
            $results['earnest_money'] = $earnestMoney;
        }

        $lng = $request->lng;
        $lat = $request->lat;
        if (!$lng || !$lat) { //没有传经纬度
            $location = $locationService->getLocationFromIp($request->getClientIp());
            $lng = $location[0];
            $lat = $location[1];
        }

        $orders = $taskService->getTaskList($rider, $lng, $lat, $request->sort);

        $results['task_list'] = $orders;

        return $this->success('success', $results);
    }

    public function running(Request $request, LocationService $locationService, TaskService $taskService)
    {
        $user = $request->user();
        $rider = Rider::query()->where('user_id', $user->id)->first();
        if (!$rider) return $this->success('success', []);
        $lng = $request->lng;
        $lat = $request->lat;
        if (!$lng || !$lat) { //没有传经纬度
            $location = $locationService->getLocationFromIp($request->getClientIp());
            $lng = $location[0];
            $lat = $location[1];
        }

        $orders = $taskService->getOrderList($rider, $lng, $lat);

        return $this->success('success', $orders);
    }

    public function detail(Request $request, LocationService $locationService, TaskService $taskService)
    {
        $orderNo = $request->order_no;
        // 校验参数
        if (!$orderNo) $this->errorResponse(500, '请传入订单号');

        $detail = $taskService->getOrderDetail($orderNo);

        return $this->success('success', $detail);
    }

    public function doTask(Request $request, RiderOrderService $orderService)
    {
        $orderNo = $request->order_no;

        // 校验参数
        if (!$orderNo) $this->errorResponse(500, '请传入订单号');
        $user = $request->user();

        try {
            $order = $orderService->checkOrder($orderNo);

            $rider = $orderService->checkRider($order, $user);

            $orderService->pickupOrder($order, $rider);

            # todo 通知下发
            if ($order->app_key != '') {
                $this->dispatch((new DispatchOpenCallback($order, 1)));
            }

        } catch (BusinessException $e) {
            $this->errorResponse(500, $e->getMessage());
        }

        return $this->success('接单成功', []);

    }

    public function checkCode(CheckCodeRequest $request, CommonService $commonService)
    {
        $orderNo = $request->order_no;

        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if (!$order) $this->errorResponse(500, '订单不存在');

        if ($rider->id != $order->rider_id) {
            $this->errorResponse(500, '这个不是你的订单哦，无法操作');
        }

        if ($request->type == 1) {
            // 取件码
            if ($order->order_status != O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                $this->errorResponse(500, '订单状态异常，无法进行确认取货操作。请确认订单状态');
            }
            if ($order->pickup_code != $request->code) {
                $this->errorResponse(500, '取件码错误，请确认是否输入有误');
            }
        } else {
            // 收货码
            if ($order->order_status != O2oErrandOrder::STATUS_DELIVERY) {
                $this->errorResponse(500, '订单状态异常，无法进行确认收获操作。请确认订单状态');
            }
            if ($order->receive_code != $request->code) {
                $this->errorResponse(500, '收货码错误，请确认是否输入有误');
            }
        }

        return $this->success('success', true);

    }

    public function pickup(Request $request, O2oErrandOrderService $orderService)
    {
        $orderNo = $request->order_no;

        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if ($rider->id != $order->rider_id) {
            $this->errorResponse(500, '这个不是你的订单哦，无法操作');
        }

        if ($order->type == O2oErrandOrder::TYPE_BUY) {
            if (!$request->goods_img || !$request->invoice_img || !$request->shop_img) {
                $this->errorResponse(500, '请上传图片');
            }
            try {
                $images = [
                    "goods_img" => $request->goods_img,
                    "invoice_img" => $request->invoice_img,
                    "shop_img" => $request->shop_img
                ];
                $orderService->purchased($order->order_no, $request->goods_price, $images);
            } catch (\Exception $e) {
                $this->errorResponse(500, $e->getMessage());
            }
        } else {
            // 开箱图m
            if ($request->images) {
                $order->goods_imgs = $request->images;
            }
            $order->order_status = O2oErrandOrder::STATUS_DELIVERY;
            $order->detail = array_merge($order->detail, ['sender' => $request->sender]);
            $order->pickup_at = Carbon::now();
            $order->save();
        }

        if ($order->app_key != '') {
            $this->dispatch((new DispatchOpenCallback($order, 1)));
        }

        return $this->success('success', []);
    }

    public function receive(Request $request, GaodeService $gaodeService)
    {
        $orderNo = $request->order_no;

        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        $lng = $request->lng;
        $lat = $request->lat;

        // $distance = $gaodeService->distanceQuery("$order->deliver_lng,$order->deliver_lat", "$lng,$lat");
        // if (app()->environment('production')) {
        //     if (isset($distance[0]['distance']) && $distance[0]['distance'] > 1500) {
        //         Log::info("传入经纬度 $lng,$lat  目的地经纬度 $order->deliver_lng,$order->deliver_lat");
        //         $this->errorResponse(500, '你还未到达收货地址，请到达目的地后再试');
        //     }
        // }

        if ($rider->id != $order->rider_id) {
            $this->errorResponse(500, '这个不是你的订单哦，无法操作');
        }

        if ($order->type == O2oErrandOrder::TYPE_BUY) {
            $order->order_status = O2oErrandOrder::STATUS_FINISH;
            $order->finish_time = Carbon::now();
            $order->save();
        } else {
            // 开箱图
            $order->buy_imgs = $request->images;
            $order->order_status = O2oErrandOrder::STATUS_FINISH;
            $order->finish_time = Carbon::now();
            $order->detail = array_merge($order->detail, ['receiver' => $request->receiver]);
            $order->save();
        }

        $this->dispatch(new DispatchFinishTask($order));

        $this->dispatch(new DispatchHandleNewUserActivity($order->user, $order));
        $this->dispatch(new DispatchHandleNewRiderActivity($user, $order));
        if ($order->app_key != '') {
            $this->dispatch((new DispatchOpenCallback($order, 1)));
        }

        return $this->success('success', []);
    }

    public function arrive(Request $request, GaodeService $gaodeService)
    {
        $orderNo = $request->order_no;

        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if ($rider->id != $order->rider_id) {
            $this->errorResponse(500, '这个不是你的订单哦，无法操作');
        }

        if ($order->pickup_lng) {
            $lng = $request->lng;
            $lat = $request->lat;

            // $distance = $gaodeService->distanceQuery("$order->pickup_lng,$order->pickup_lat", "$lng,$lat");
            // if (app()->environment('production')) {
            //     if (isset($distance[0]['distance']) && $distance[0]['distance'] > 1500) {
            //         Log::info("传入经纬度 $lng,$lat  目的地经纬度 $order->pickup_lng,$order->pickup_lat");
            //         $this->errorResponse(500, '你还未到达收货地址，请到达目的地后再试');
            //     }
            // }
        }

        $order->order_status = O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT;
        $order->arrive_at = Carbon::now();
        $order->save();
        if ($order->app_key != '') {
            $this->dispatch((new DispatchOpenCallback($order, 1)));
        }

        return $this->success('success', []);
    }

    public function dispatchOrder(Request $request, TaskService $taskService)
    {
        $orderNo = $request->get('order_no');
        $riderId = $request->get('rider_id');
        if (!$orderNo || !$riderId) $this->errorResponse(500, "参数错误");

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) $this->errorResponse(500, '订单不存在');

        if ($order->order_status != O2oErrandOrder::STATUS_PAID) {
            $this->errorResponse(500, '订单不是待接单状态哦');
        }

        if (!in_array($order->dispatch_status, [O2oErrandOrder::DISPATCH_STATUS_PADDING, O2oErrandOrder::DISPATCH_STATUS_FAILED])) {
            $this->errorResponse(500, '派单状态不正确');
        }

        $taskService->dispatchOrder($order, $riderId);

        return $this->success('success', []);
    }

    public function agreeDispatch(Request $request, TaskService $taskService)
    {
        $dispatchOrderId = $request->get('dispatch_order_id');

        if (!$dispatchOrderId) $this->errorResponse(500, "参数错误");

        $dispatchOrder = DispatchOrder::query()->where('id', $dispatchOrderId)->with('order')->first();

        if ($dispatchOrder->order->order_status != O2oErrandOrder::STATUS_PAID) {
            $this->errorResponse(500, '订单不是待接单状态哦');
        }

        if (!in_array($dispatchOrder->order->dispatch_status, [O2oErrandOrder::DISPATCH_STATUS_SENDING, O2oErrandOrder::DISPATCH_STATUS_FAILED])) {
            $this->errorResponse(500, '派单状态不正确');
        }

        $taskService->agreeDispatchOrder($dispatchOrder);

        if ($dispatchOrder->order->app_key != '') {
            $order = O2oErrandOrder::query()->where('id', $dispatchOrder->order_id)->first();
            $this->dispatch((new DispatchOpenCallback($order, 1)));
        }

        return $this->success('success', []);

    }

    public function rejectDispatch(Request $request, TaskService $taskService)
    {
        $dispatchOrderId = $request->get('dispatch_order_id');

        if (!$dispatchOrderId) $this->errorResponse(500, "参数错误");

        $dispatchOrder = DispatchOrder::query()->where('id', $dispatchOrderId)->with('order')->first();
        if (!$dispatchOrder->order) $this->errorResponse(500, '订单不存在');

        if ($dispatchOrder->order->order_status != O2oErrandOrder::STATUS_PAID) {
            $this->errorResponse(500, '订单不是待接单状态哦');
        }

        if (!in_array($dispatchOrder->order->dispatch_status, [O2oErrandOrder::DISPATCH_STATUS_SENDING, O2oErrandOrder::DISPATCH_STATUS_FAILED])) {
            $this->errorResponse(500, '派单状态不正确');
        }

        $taskService->rejectDispatch($dispatchOrder);

        return $this->success('success', []);
    }

    public function queryTransTimes(Request $request)
    {
        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        if (!$rider) {
            return $this->success('success', ['limit' => 0]);
        }

        $count = TransOrder::query()->where('trans_rider_id', $rider->id)
            ->where('created_at', [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()])
            ->where('status', 1)
            ->count();

        return $this->success('success', ['limit' => $rider->trans_limit - $count]);

    }

    public function trans(Request $request, TaskService $service)
    {

        $orderNo = $request->order_no;
        $reason = $request->reason;

        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        if (!$rider) $this->errorResponse(500, '请登录后使用');

        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if (!$order) $this->errorResponse(500, '订单不存在');

        if (!in_array($order->order_status, [O2oErrandOrder::STATUS_PICKUP])) {
            $this->errorResponse(500, '只有待取货的订单才可以转单哦');
        }


        $transOrder = TransOrder::query()->where('order_no', $orderNo)
            ->where('trans_rider_id', $rider->id)
            ->where('status', 0)->first();
        if ($transOrder) $this->errorResponse(500, '已创建转单订单，请耐心等待');

        DB::beginTransaction();
        try {
            // 转单记录创建
            $transOrder = new TransOrder();
            $transOrder->order_no = $orderNo;
            $transOrder->trans_rider_id = $rider->id;
            $transOrder->reason = $reason;
            $transOrder->status = 0;
            $transOrder->target_rider_id = 0;
            $transOrder->save();

            $order->is_trans = 1;
            $order->ori_rider_id = $order->rider_id;
            $order->rider_id = 0;
            $order->order_status = O2oErrandOrder::STATUS_PAID;
            $order->save();

            dispatch(new CloseTransOrder($transOrder->id, 60 * 5));
            DB::commit();
            return $this->success('success', []);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->errorResponse(500, $e->getMessage());
        }
    }

    public function taskOrder(Request $request, TaskService $service)
    {
        $status = $request->input('status', 1);
        $startDate = $request->start_date;
        $endDate = $request->end_date;
        $keyword = $request->input('keyword', '');
        $pageSize = $request->input('page_size', 20);
        $user = $request->user();
        $rider = Rider::query()->where('user_id', $user->id)->first();

        if (!$rider) {
            return $this->success('success', []);
        }

        $res = $service->queryTask($rider, $status, $startDate, $endDate, $keyword, $pageSize);

        return $this->success('success', $res);
    }

    public function transOrderList(Request $request, TaskService $service)
    {
        $user = $request->user();
        $lng = $request->lng;
        $lat = $request->lat;
        $rider = Rider::query()->where('user_id', $user->id)->first();
        if (!$rider) {
            $this->success('success', []);
        }
        $res = $service->queryTransOrder($rider->id, $lng, $lat);

        return $this->success('success', $res);
    }
}
