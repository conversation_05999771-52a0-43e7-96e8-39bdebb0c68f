<?php

namespace App\Models;

use Carbon\Carbon;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Redis;

class O2oErrandOrder extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const APP_KEY_MYT = "maiyatian"; // 麦芽田
    const APP_KEY_WRC = "wrc"; // 无人仓

    const GEO_KEY = 'o2o_order';

    const ORDER_PREFIX = '30';

    // 麦芽田状态常量
    const MYT_STATUS_UNPROGRESS = "UNPROGRESS";
    const MYT_STATUS_CREATED = "CREATED";
    const MYT_STATUS_CONFIRM = "CONFIRM";
    const MYT_STATUS_DELIVERY = "DELIVERY";
    const MYT_STATUS_PICKUP = "PICKUP";
    const MYT_STATUS_GRABBED = "GRABBED";
    const MYT_STATUS_DELIVERING = "DELIVERING";
    const MYT_STATUS_DONE = "DONE";
    const MYT_STATUS_CANCEL = "CANCEL";
    const MYT_STATUS_DELETE = "DELETE";
    const MYT_STATUS_EXPECT = "EXPECT";

    // 订单状态到麦芽田状态的映射
    const StatusToMytMap = [
        self::STATUS_CANCEL => self::MYT_STATUS_CANCEL,
        self::STATUS_PAID => self::MYT_STATUS_DELIVERY,
        self::STATUS_PICKUP => self::MYT_STATUS_GRABBED,
        self::STATUS_ARRIVE_PICKUP_POINT => self::MYT_STATUS_PICKUP,
        self::STATUS_DELIVERY => self::MYT_STATUS_DELIVERING,
        self::STATUS_FINISH => self::MYT_STATUS_DONE,
    ];

    // 麦芽田状态到中文描述的映射
    const MytStatusMap = [
        self::MYT_STATUS_UNPROGRESS => "未处理",
        self::MYT_STATUS_CREATED => "待确认",
        self::MYT_STATUS_CONFIRM => "已确认",
        self::MYT_STATUS_DELIVERY => "待抢单",
        self::MYT_STATUS_PICKUP => "已到店",
        self::MYT_STATUS_GRABBED => "已抢单",
        self::MYT_STATUS_DELIVERING => "配送中",
        self::MYT_STATUS_DONE => "已完成",
        self::MYT_STATUS_CANCEL => "已取消",
        self::MYT_STATUS_DELETE => "已删除",
        self::MYT_STATUS_EXPECT => "配送异常",
    ];

    //1--帮我送 2--帮我取 3--帮我买
    const TYPE_SEND = 1;
    const TYPE_TAKE = 2;
    const TYPE_BUY = 3;

    const TYPE_HELP = 24;
    const TypeMap = [
        self::TYPE_SEND => '帮我送',
        self::TYPE_TAKE => '帮我取',
        self::TYPE_BUY => '帮我买',
        self::TYPE_HELP => '全能帮',
    ];

    //1-口头验证 2-输入验证
    const VALIDATE_SPEAK = 1;
    const VALIDATE_INPUT = 2;
    const ValidateMap = [
        self::VALIDATE_SPEAK => '口头验证',
        self::VALIDATE_INPUT => '输入验证',
    ];

    //0-已取消 10-待付款 20-待接单 26-待取货 28-已到取货点 30-派送中 40-已完成
    const STATUS_CANCEL = 0;
    const STATUS_WAITING_PAY = 10;
    const STATUS_PAID = 20;
    const STATUS_PICKUP = 26;
    const STATUS_ARRIVE_PICKUP_POINT = 28;
    const STATUS_DELIVERY = 30;
    const STATUS_FINISH = 40;
    const StatusMap = [
        self::STATUS_CANCEL => "已取消",
        self::STATUS_WAITING_PAY => "待支付",
        self::STATUS_PAID => "待接单",
        self::STATUS_PICKUP => "待取货",
        self::STATUS_ARRIVE_PICKUP_POINT => "到达取货点",
        self::STATUS_DELIVERY => "派送中",
        self::STATUS_FINISH => "已完成",
    ];

    const REFUND_STATUS_INIT = 0;
    const REFUND_STATUS_ING = 1;
    const REFUND_STATUS_SUCCESS = 2;
    const REFUND_STATUS_FAIL = 3;
    const RefundStatusMap = [
        self::REFUND_STATUS_INIT => "未发起退款",
        self::REFUND_STATUS_ING => "退款中",
        self::REFUND_STATUS_SUCCESS => "退款成功",
        self::REFUND_STATUS_FAIL => "退款失败",
    ];

    const DISPATCH_STATUS_PADDING = 0;
    const DISPATCH_STATUS_SENDING = 1;
    const DISPATCH_STATUS_CONFIRM = 2;
    const DISPATCH_STATUS_FAILED = 3;

    const DispatchStatusMap = [
        self::DISPATCH_STATUS_PADDING => '未派单',
        self::DISPATCH_STATUS_SENDING => '派单确认中',
        self::DISPATCH_STATUS_CONFIRM => '派单确认',
        self::DISPATCH_STATUS_FAILED => '派单失败',
    ];

    protected $casts = ['hide_address' => 'boolean', 'is_special' => 'boolean', 'need_incubator' => 'boolean',
        'is_trans' => 'boolean', 'goods_imgs' => 'json', 'buy_imgs' => 'json', 'detail' => 'json'];

    protected $dates = ['paid_at', "refund_at", 'create_time', 'appointment_start_time', 'appointment_end_time',
        'pickup_at', 'receipt_time', 'finish_time', 'estimated_delivery_time', 'arrive_at'];

    protected $fillable = [
        "out_order_no", "app_key", "title",
        'type', 'order_no', 'user_id', 'rider_id', 'order_status', 'dispatch_status', 'coupon_id', 'gratuity', 'reward', 'remark', 'hide_address', 'is_special', 'need_incubator', 'close_reason', 'detail', 'is_trans', 'ori_rider_id',
        'distance', 'site_id', //距离
        'pickup_name', 'pickup_address_id', 'pickup_address', 'pickup_phone', 'pickup_lng', 'pickup_lat', 'pickup_region_id', 'pickup_code', 'pickup_code_mode', //取货点信息
        'deliver_name', 'deliver_address_id', 'deliver_address', 'deliver_phone', 'deliver_lng', 'deliver_lat', 'deliver_region_id', 'receive_code', 'receive_code_mode', //收货点信息
        'goods_desc', 'goods_imgs', 'goods_price', 'goods_protected_price', 'goods_category_id', 'category_id', 'weight', 'volume', 'buy_imgs', //商品信息
        'order_amount', 'actual_amount', 'coupon_amount', 'freight', 'distance_price', 'time_price', 'weather_price', 'weight_price', 'reward_amount_full', 'reward_amount_part', 'reward_amount', //费用
        'pay_method', 'payment_no', 'paid_at', 'refund_amount', 'refund_status', 'refund_no', 'refund_at', //支付和退款信息
        'create_time', 'appointment_start_time', 'appointment_end_time', 'arrive_at', 'pickup_at', 'receipt_time', 'finish_time', 'estimated_delivery_time' //各种时间
    ];

    protected $appends = ['timeline', "order_status_desc"];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public static function GetOrderFromRedis($lng, $lat, $distance, $sort)
    {
        return Redis::georadius(self::GEO_KEY, $lng, $lat, $distance, 'km', 'WITHDIST', $sort);
    }

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function rider()
    {
        return $this->belongsTo(Rider::class, 'rider_id');
    }

    public function category()
    {
        return $this->belongsTo(Category::class, 'category_id');
    }

    public function site() {
        return $this->belongsTo(Site::class, 'site_id');
    }

    public function goodsCategory()
    {
        return $this->belongsTo(Category::class, 'goods_category_id');
    }

    public function payRecs()
    {
        return $this->hasMany(OrderPayRec::class, "order_id");
    }

    public function remindLogs()
    {
        return $this->hasMany(OrderRemindLog::class, "order_id");
    }

    public function evaluate()
    {
        return $this->hasOne(OrderEvaluateRec::class, "order_id");
    }

    public function getTimelineAttribute()
    {
        $timeline = [
            ['time' => $this->create_time->format(Carbon::DEFAULT_TO_STRING_FORMAT), "remark" => '订单已提交'],
        ];
        if ($this->order_status == self::STATUS_CANCEL) {
            $timeline[] = ['time' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT), "remark" => '订单已取消'];
        } else {
            $keys = ['paid_at' => "支付成功", 'arrive_at' => '到达取货点', 'pickup_at' => "订单已取货", 'receipt_time' => "订单已接单", 'finish_time' => "订单已完成"];
            if ($this->refund_status != self::REFUND_STATUS_INIT) $keys["updated_at"] = "订单已取消";
            $data = [];
            foreach ($keys as $k => $v) {
                if (isset($this->$k) && $this->$k) {
                    if ($this->$k) {
                        $data[$this->$k->format(Carbon::DEFAULT_TO_STRING_FORMAT)] = $v;
                    }
                }
            }
            if ($data) {
                ksort($data);
                foreach ($data as $key => $item) {
                    $timeline[] = ['time' => $key, "remark" => $item];
                }
            }
        }
        return $timeline;
    }

    public function getOrderStatusDescAttribute()
    {
        if ($this->order_status == self::STATUS_CANCEL || $this->refund_status != self::REFUND_STATUS_INIT) return "订单已被您取消";
        $desc = "";
        switch ($this->order_status) {
            case self::STATUS_WAITING_PAY:
                $desc = sprintf("请在%s内支付，超时将自动取消订单", Carbon::now()->startOfDay()->addSeconds($this->created_at->addMinutes(15)->timestamp - Carbon::now()->timestamp)->format("H:i"));
                break;
            case self::STATUS_PAID:
                $desc = sprintf("正在为您寻找附近跑腿，预计%s送达", $this->estimated_delivery_time->format("H:i"));
                break;
            case self::STATUS_PICKUP:
                $desc = sprintf("跑腿将于<span style='color: #F56D0A;'>%s-%s</span>上门取件，预计%s送达", $this->appointment_start_time->format("H:i"), $this->appointment_end_time->format("H:i"), $this->estimated_delivery_time->format("H:i"));
                if($this->type == self::TYPE_BUY) $desc = "跑腿正在前往购买地";
                break;
            case self::STATUS_ARRIVE_PICKUP_POINT:
                $desc = "跑腿取件中";
                if($this->type == self::TYPE_BUY) $desc = "跑腿购买中";
                break;
            case self::STATUS_DELIVERY:
                $desc = sprintf("跑腿预计%s送达", $this->estimated_delivery_time->format("H:i"));
                if($this->type == self::TYPE_BUY) $desc = "跑腿正在赶往收货地";
                break;
            case self::STATUS_FINISH:
                $desc = "您的订单已完成，期待再次光临";
                break;
        }
        return $desc;
    }

    public static function findAvailableNo()
    {
        // 订单流水号前缀
        $prefix = self::ORDER_PREFIX . date('YmdHis');
        for ($i = 0; $i < 10; $i++) {
            // 随机生成 6 位的数字
            $no = $prefix . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            // 判断是否已经存在
            if (!static::query()->where('order_no', $no)->exists()) {
                return $no;
            }
        }
        return false;
    }


    public static function generateCode()
    {
        return str_pad(random_int(0, 9999), 4, '0', STR_PAD_LEFT);
    }

    public static function PushLocation()
    {
        $orders = O2oErrandOrder::query()->get();
        foreach ($orders as $order) {
            if($order->refund_status != self::REFUND_STATUS_INIT){
                Redis::zrem(self::GEO_KEY, $order->id);
            }else {
                switch ($order->order_status) {
                    case 0:
                        // 已取消，挪出redis
                        Redis::zrem(self::GEO_KEY, $order->id);
                        break;
                    case 20:
                        // 待接单
                        if ($order->pickup_region_id) {
                            Redis::geoadd(self::GEO_KEY, $order->pickup_lng, $order->pickup_lat, $order->id);
                        } else {
                            Redis::geoadd(self::GEO_KEY, $order->deliver_lng, $order->deliver_lat, $order->id);
                        }
                        break;
                    case 40:
                        // 已完成
                        Redis::zrem(self::GEO_KEY, $order->id);
                        break;
                }
            }
        }
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::creating(function ($model) {
            // 如果模型的 no 字段为空
            if (!$model->order_no) {
                // 调用 findAvailableNo 生成订单流水号
                $model->order_no = static::findAvailableNo();
                // 如果生成失败，则终止创建订单
                if (!$model->order_no) {
                    return false;
                }
            }
        });

        static::saved(function ($order) {
            if($order->refund_status != self::REFUND_STATUS_INIT){
                Redis::zrem(self::GEO_KEY, $order->id);
            }else{
                switch ($order->order_status) {
                    case 0:
                        // 已取消，挪出redis
                        Redis::zrem(self::GEO_KEY, $order->id);
                        break;
                    case 20:
                        // 待接单
                        if ($order->pickup_region_id) {
                            Redis::geoadd(self::GEO_KEY, $order->pickup_lng, $order->pickup_lat, $order->id);
                        } else {
                            Redis::geoadd(self::GEO_KEY, $order->deliver_lng, $order->deliver_lat, $order->id);
                        }
                        break;
                    case 40:
                        // 已完成
                        Redis::zrem(self::GEO_KEY, $order->id);
                        break;
                }
            }
        });
    }

    /**
     * 获取麦芽田状态
     *
     * @return string 麦芽田状态
     */
    public function getMytStatus()
    {
        $orderStatus = $this->order_status;
        if ($this->refund_status != self::REFUND_STATUS_INIT) {
            $orderStatus = self::STATUS_CANCEL;
        }
        return self::StatusToMytMap[$orderStatus] ?? self::MYT_STATUS_UNPROGRESS;
    }

    /**
     * 获取麦芽田状态描述
     *
     * @return string 状态描述
     */
    public function getMytStatusText()
    {
        return self::MytStatusMap[$this->getMytStatus()] ?? "未知状态";
    }
}
