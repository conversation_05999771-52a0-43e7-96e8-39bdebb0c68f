<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class MerchantAccountLog extends Model
{
    use HasFactory;
    
    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'merchant_id',
        'amount',
        'before_balance',
        'after_balance',
        'type',
        'order_no',
        'remark'
    ];
    
    /**
     * 变动类型常量
     */
    const TYPE_RECHARGE = 'recharge';  // 充值
    const TYPE_ORDER = 'order';        // 订单支付
    const TYPE_REFUND = 'refund';      // 退款
    const TYPE_TIP = 'tips';           // 订单小费
    
    /**
     * 变动类型映射
     */
    const TypeMap = [
        self::TYPE_RECHARGE => '充值',
        self::TYPE_ORDER => '订单支付',
        self::TYPE_REFUND => '退款',
        self::TYPE_TIP => '订单小费',
    ];
    
    /**
     * 关联商家
     */
    public function merchant()
    {
        return $this->belongsTo(Merchant::class);
    }
    
    /**
     * 获取变动类型文本
     */
    public function getTypeTextAttribute()
    {
        return self::TypeMap[$this->type] ?? '未知类型';
    }
    
    /**
     * 格式化金额(分转元)
     */
    public function getAmountYuanAttribute()
    {
        return number_format($this->amount / 100, 2);
    }
    
    /**
     * 格式化变动前余额(分转元)
     */
    public function getBeforeBalanceYuanAttribute()
    {
        return number_format($this->before_balance / 100, 2);
    }
    
    /**
     * 格式化变动后余额(分转元)
     */
    public function getAfterBalanceYuanAttribute()
    {
        return number_format($this->after_balance / 100, 2);
    }
}
