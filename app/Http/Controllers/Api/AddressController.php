<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\AddressRequest;
use App\Models\Common;
use App\Models\Region;
use App\Models\UserAddress;
use App\Services\Addres\AddressService;
use App\Services\Amap\GaodeService;
use App\Services\MapService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;
use Illuminate\Support\Facades\Http;
use Illuminate\Http\JsonResponse;

class AddressController extends Controller
{
    public function smart(Request $request)
    {
        return $this->success('识别完成', AddressService::smart($request->address));
    }

    public function index(Request $request)
    {
        $addressList = UserAddress::query()->where('user_id', $request->user()->id)->orderBy('is_default', 'desc')
            ->orderBy('id', 'desc')->get();
        return $this->success('success', $addressList);
    }

    public function store(AddressRequest $request)
    {
        $user = $request->user();

        $inputs = $request->only(['name', 'tel', 'province', 'city', 'county', 'address_detail', 'is_default', 'area_code', 'longitude', 'latitude', 'address_remark']);

        Log::info('address store', $inputs);

        $address = UserAddress::query()->where("tel", $inputs['tel'])
            ->where("province", $inputs['province'])
            ->where("city", $inputs['city'])
            ->where("county", $inputs['county'])
            ->where("address_detail", $inputs['address_detail'])
            ->where('address_remark', $inputs['address_remark'] ??'')
            ->where('user_id', $user->id)
            ->first();

        if (!$address) {
            $inputs['user_id'] = $user->id;
            $address = UserAddress::create($inputs);
        }

        return $this->success('success', $address);

    }

    public function update($id, AddressRequest $request)
    {

        $user = $request->user();

        $address = UserAddress::find($id);

        if ($user->id != $address->user_id) {
            $this->errorResponse(403, '非法请求');
        }

        $inputs = $request->only(['name', 'tel', 'province', 'city', 'county', 'address_detail', 'is_default', 'area_code', 'longitude', 'latitude', 'address_remark']);

        $address->update($inputs);

        return $this->success('success', $address);

    }

    public function destroy($id, AddressRequest $request)
    {

        $user = $request->user();

        $address = UserAddress::find($id);

        if ($user->id != $address->user_id) {
            $this->errorResponse(403, '非法请求');
        }

        $address->delete();

        return $this->success('success', []);
    }

    public function commonAddress(Request $request, GaodeService $gaodeService)
    {
        $lng = $request->lng;
        $lat = $request->lat;
        if (!($lat && $lng)) $this->errorResponse(500, "请提供经纬度");
        $res = [
            "available" => [],
            "unavailable" => []
        ];
        $addressList = UserAddress::query()->where('user_id', $request->user()->id)->get();
        if ($addressList->count() > 0) {
            $cities = [];
            foreach ($addressList as $v) {
                if (!in_array($v->city, $cities)) {
                    $cities[] = $v->city;
                }
            }
            $availableCities = Region::query()->whereIn("name", $cities)->where("level", Common::REGION_LEVEL_CITY)->where("status", 1)->pluck("name")->toArray();
            $indexMap = [];
            $start = "";
            foreach ($addressList as $k => $v) {
                if (!in_array($v->city, $availableCities)) {
                    $address = $v;
                    $address->reason = "所在城市暂未开通服务";
                    $res["unavailable"][] = $address;
                } else {
                    if ($start) $start .= "|";
                    $start .= "$v->longitude,$v->latitude";
                    $indexMap[] = $k;
                }
            }
            if ($start) {
                $distance = $gaodeService->distanceQuery($start, "$lng,$lat");
                foreach ($distance as $fk => $fv) {
                    foreach ($fv as $sk => $sv) {
                        if (in_array($sk, ["origin_id", "distance"])) {
                            $distance[$fk][$sk] = intval($sv);
                        }
                    }
                }
                $sorted = collect($distance)->sortBy([
                    ['distance', 'asc'],
                    ['origin_id', 'asc'],
                ])->values()->all();
                foreach ($sorted as $v) {
                    $address = $addressList[$indexMap[$v["origin_id"] - 1]];
                    if ($v["distance"] <= 100000) {
                        $address->reason = "";
                        $address->distance = $v["distance"];
                        $res["available"][] = $address;
                    } else {
                        $address->reason = "配送距离过远";
                        $res["unavailable"][] = $address;
                    }
                }
            }
        }
        return $this->success('success', $res);
    }

    public function search(Request $request, MapService $mapService, GaodeService $gaodeService)
    {
        $keyword = $request->keyword;
        $page = $request->page ?? 1;
        $pageSize = $request->input('page_size', 20);
        $city = $request->city;
        $category = $request->category;
        $result = [];

        try {
            if (!$keyword) {
                // 搜索附近POI
                $result = $this->searchNearbyPOI($request, $gaodeService, $category);
            } else {
                // 根据关键词搜索
                $result = $this->searchByKeyword($request, $gaodeService, $category);
            }

            return $this->success('success', $result);
        } catch (\Exception $e) {
            Log::error('地址搜索失败', [
                'message' => $e->getMessage(),
                'keyword' => $keyword,
                'category' => $category
            ]);
            return $this->errorResponse(500, '搜索失败：' . $e->getMessage());
        }
    }

    /**
     * 搜索附近POI
     */
    private function searchNearbyPOI(Request $request, GaodeService $gaodeService, $category)
    {
        $result = [];
        $lat = $request->lat;
        $lng = $request->lng;
        
        // 如果搜索城市与当前城市不同，使用目标城市的中心点
        if ($request->current_city && $request->city && ($request->current_city != $request->city)) {
            $region = Region::query()->where('name', $request->city)->first();
            if (!$region) {
                throw new \Exception('未找到目标城市');
            }
            $lat = $region->lat;
            $lng = $region->lng;
        }

        // 调用高德地图周边搜索
        $response = $gaodeService->queryPoi($lat, $lng, $request->page ?? 1, $request->input('page_size', 20), $category);
        
        if ($response && isset($response['pois'])) {
            foreach ($response['pois'] as $poi) {
                $result[] = $this->formatPoiToAddress($poi);
            }
        }

        return $result;
    }

    /**
     * 根据关键词搜索
     */
    private function searchByKeyword(Request $request, GaodeService $gaodeService, $category)
    {
        $result = [];
        $keyword = $request->keyword;
        
        try {
            // 构建输入提示参数
            $params = [
                'keywords' => $keyword,
                'city' => $request->city ?: '',
                'citylimit' => 'true',
                'datatype' => 'poi'
            ];

            // 如果提供了经纬度，添加到请求中用于排序
            if ($request->lat && $request->lng) {
                $params['location'] = $request->lng . ',' . $request->lat;
            }

            // 根据场景设置POI类型
            if ($category) {
                $params['type'] = $this->getPoiTypes($category);
            }

            // 调用输入提示API
            $response = $gaodeService->inputTips($params);
            
            if ($response && isset($response['tips']) && !empty($response['tips'])) {
                foreach ($response['tips'] as $tip) {
                    // 如果返回结果包含地址，说明是POI数据
                    if (isset($tip['address'])) {
                        $result[] = [
                            'name' => $tip['name'],
                            'province' => explode(',', $tip['district'])[0] ?? '',
                            'city' => explode(',', $tip['district'])[1] ?? '',
                            'county' => explode(',', $tip['district'])[2] ?? '',
                            'address_detail' => $tip['address'],
                            'latitude' => explode(',', $tip['location'])[1] ?? '',
                            'longitude' => explode(',', $tip['location'])[0] ?? '',
                            'area_code' => $tip['adcode'] ?? '',
                            'distance' => 0,
                            'distance_text' => '',
                            'poi_type' => $tip['type'] ?? ''
                        ];
                    }
                }
            }

            // 如果结果为空，尝试使用Place API搜索
            if (empty($result)) {
                $result = $this->searchByPlaceAPI($request, $gaodeService, $category);
            }

            return $result;
        } catch (\Exception $e) {
            Log::error('地址搜索失败', [
                'keyword' => $keyword,
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }

    /**
     * 使用Place API进行搜索（作为备选方案）
     */
    private function searchByPlaceAPI(Request $request, GaodeService $gaodeService, $category)
    {
        $result = [];
        // 构建搜索参数
        $params = [
            'keywords' => $request->keyword,
            'city' => $request->city ?: '',
            'types' => $this->getPoiTypes($category),
            'page' => $request->page ?? 1,
            'offset' => $request->input('page_size', 20),
            'extensions' => 'all'
        ];

        if ($request->lat && $request->lng) {
            $params['location'] = $request->lng . ',' . $request->lat;
        }

        $response = $gaodeService->searchText($params);
        
        if ($response && isset($response['pois'])) {
            foreach ($response['pois'] as $poi) {
                $result[] = $this->formatPoiToAddress($poi);
            }
        }

        return $result;
    }

    /**
     * 根据场景获取POI类型
     */
    private function getPoiTypes($category)
    {
        switch ($category) {
            case 1: // 跑腿场景 - 小区和办公楼
                return '120000|120300|120302|120304'; // 居民区|小区|住宅区|住宅小区|办公楼
            case 2: // 帮买场景 - 商超便利店等
                return '060000|061000|061100|060400|060200'; // 购物|商场|超市|便利店|药店
            default: // 默认场景 - 综合
                return '120000|060000|050000'; // 居住区|购物|餐饮
        }
    }

    /**
     * 格式化POI数据为统一地址格式
     */
    private function formatPoiToAddress($poi)
    {
        // 解析地址组件
        $addressComponents = explode(',', $poi['address'] ?? '');
        $fullAddress = $poi['name'];
        if (!empty($addressComponents)) {
            $fullAddress .= ' ' . $addressComponents[0];
        }

        return [
            'name' => $poi['name'],
            'province' => $poi['pname'] ?? '',
            'city' => $poi['cityname'] ?? '',
            'county' => $poi['adname'] ?? '',
            'address_detail' => $fullAddress,
            'latitude' => explode(',', $poi['location'])[1] ?? '',
            'longitude' => explode(',', $poi['location'])[0] ?? '',
            'area_code' => $poi['adcode'] ?? '',
            'distance' => isset($poi['distance']) ? intval($poi['distance']) : 0,
            'distance_text' => isset($poi['distance']) ? $this->formatDistance($poi['distance']) : '',
            'poi_type' => $poi['type'] ?? ''
        ];
    }

    /**
     * 格式化距离显示
     */
    private function formatDistance($distance)
    {
        if (!$distance) return '';
        $distance = intval($distance);
        if ($distance < 1000) {
            return $distance . '米';
        }
        return round($distance/1000, 1) . '公里';
    }

    public function geoCoder(Request $request, MapService $mapService) {
        $lng = $request->lng;
        $lat = $request->lat;
        if (!($lat && $lng)) $this->errorResponse(500, "请提供经纬度");

        $result = $mapService->geocoder($lng, $lat, 1);
        
        $res = [
            'has_result' => false,
            'location' => []
        ];
        
        if ($result['status'] == 1) {
            // 成功
            $res['has_result'] = true;
            $res['location'] = [
                'county' => $result['result']['address_component']['district'] ?? '',
                'city' => $result['result']['address_component']['city'] ?? '',
                'longitude' => $result['result']['location']['lng'] ?? '',
                'latitude' => $result['result']['location']['lat'] ?? '',
                'province' => $result['result']['address_component']['province'] ?? '',
                'name' => $result['result']['address'] ?? '', // 使用完整地址作为name
                'address_detail' => $result['result']['address'] ?? '', // 使用完整地址作为address_detail
                'area_code' => '', // 高德API返回中没有adcode,如果需要可以通过district API获取
                'distance' => 0,
                'distance_text' => '',
            ];
        }
        
        return $this->success('success', $res);
    }

    public function getBaiduPoi($query, $city) {
        $url = 'https://api.map.baidu.com/place/v2/suggestion?query=%s&region=%s&city_limit=true&output=json&ak=QyDt9UULFw27D1Mmq6sBPG72YAgs0iXv&ret_coordtype=gcj02ll';

        $res = Curl::to(sprintf($url, $query, $city))->get();

        return json_decode($res, true);
    }

    /**
     * 从图片提取地址信息并创建新地址
     * @param Request $request
     * @return JsonResponse
     */
    public function extractAndCreate(Request $request)
    {
        try {
            // 验证请求参数
            $request->validate([
                'image_url' => 'required|url'
            ]);

            // 调用本地OCR服务提取文本
            $response = Http::post(env('LLM_EXTRACT_TEXT'), [
                'image_path' => $request->image_url
            ]);

            if (!$response->successful()) {
                $this->errorResponse(500, '文字识别服务异常');
            }

            $resp = $response->json();
            $addressInfo = $resp['data']??[];

            // 检查关键字段是否为空
            if (empty($addressInfo['contacts']) || empty($addressInfo['latitude']) || empty($addressInfo['longitude'])) {
                $this->errorResponse(500, '文字识别服务异常：关键信息缺失');
            }

            // 构建创建地址的请求参数
            $addressData = [
                'name' => $addressInfo['contact_name']??'',
                'tel' => empty($addressInfo['contacts']) ? '' : $addressInfo['contacts'][0],
                'province' => $addressInfo['province']??'',
                'city' => $addressInfo['city']??'',
                'county' => $addressInfo['area']??'',
                'address_detail' => $addressInfo['address'] ?? '未知',
                'is_default' => 0,
                'latitude' => $addressInfo['latitude']??0,
                'longitude' => $addressInfo['longitude']??0,
            ];

            // 创建新地址
            $user = $request->user();
            $address = UserAddress::create(array_merge($addressData, [
                'user_id' => $user->id
            ]));

            // 查询用户默认地址
            $defaultAddress = UserAddress::query()
                ->where('user_id', $user->id)
                ->where('is_default', 1)
                ->first();

            // 如果存在默认地址,将其合并到返回数据中
            $address = [
                'current' => $address,
                'default' => $defaultAddress
            ];

            return $this->success('地址解析成功', $address);
        } catch (\Exception $e) {
            Log::error('Extract address failed: ' . $e->getMessage());
            $this->errorResponse(500, '地址解析失败: ' . $e->getMessage());
        }
    }
}
