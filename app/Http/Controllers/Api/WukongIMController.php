<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Services\WukongIMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WukongIMController extends Controller
{
    /**
     * 获取IM连接信息
     */
    public function getToken(Request $request)
    {
        try {
            $user = $request->user();
            $wukongService = app(WukongIMService::class);

            // 确保用户已注册到悟空IM
            if (!$wukongService->registerUser($user)) {
                return $this->errorResponse(500, 'IM注册失败');
            }
            // 获取连接地址
            $uid = $wukongService->formatAlias($user);
            $response = Http::get($wukongService->getBaseUrl() . '/route', [
                'uid' => $uid
            ]);

            if (!$response->successful()) {
                Log::error('获取悟空IM连接地址失败', [
                    'user_id' => $user->id,
                    'uid' => $uid,
                    'response' => $response->json()
                ]);
                return $this->errorResponse(500, 'IM服务异常');
            }

            $routeInfo = $response->json();
            
            return $this->success('success', [
                'uid' => $uid,
                'token' => $user->recommend_code,
                'tcp_addr' => $routeInfo['tcp_addr'] ?? '',
                'ws_addr' => $routeInfo['ws_addr'] ?? '',
            ]);

        } catch (\Exception $e) {
            Log::error('获取悟空IM连接信息异常', [
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(500, 'IM服务异常');
        }
    }
} 