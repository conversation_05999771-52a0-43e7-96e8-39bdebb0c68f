<?php

namespace App\Console\Commands;

use App\Services\EasemobService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class InitCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'APP初始化';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->initEasemobSystemAccount();
        return Command::SUCCESS;
    }

    /**
     * 初始化环信系统账号
     */
    private function initEasemobSystemAccount()
    {
        // 社区食堂系统用户
        try {
            app('easemob_sq')->authorizationRegistration(EasemobService::SQ_SYSTEM_USER);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }

        // 雨骑士系统用户
        try {
            app('easemob_pt')->authorizationRegistration(EasemobService::PT_SYSTEM_USER);
        } catch (\Exception $e) {
            Log::error($e->getMessage());
        }
    }
}
