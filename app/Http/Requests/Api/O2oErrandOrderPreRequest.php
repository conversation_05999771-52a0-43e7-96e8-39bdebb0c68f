<?php

namespace App\Http\Requests\Api;

use App\Models\O2oErrandOrder;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class O2oErrandOrderPreRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            "type" => ['required', 'integer', Rule::in([O2oErrandOrder::TYPE_SEND, O2oErrandOrder::TYPE_TAKE, O2oErrandOrder::TYPE_BUY, O2oErrandOrder::TYPE_HELP])],
            "gratuity" => "numeric|min:0",
            "appointment_time" => "required|string",
            "coupon_id" => "integer|min:0",
            "goods_info.price" => "numeric|min:0",
            "goods_info.is_protect_price" => "required|boolean",
            "goods_info.weight" => "nullable|numeric|min:0",
            "is_bad_weather" => "boolean",
            "start_point.mode" => ['required', 'integer', Rule::in([0, 1, 2])], //0-id,1-指定，2-就近
            "end_point.address_id" => "required|integer|min:1",
        ];
        $startPoint = $this->input("start_point");
        if ($startPoint["mode"] == 0) {
            $rules["start_point.address_id"] = "required|integer|min:1";
        } elseif ($startPoint["mode"] == 1) {
            $rules["start_point.lat"] = "required|numeric";
            $rules["start_point.lng"] = "required|numeric";
        }
        return $rules;
    }

    public function attributes()
    {
        return [
            "type" => "类型",
            'gratuity' => "小费",
            'appointment_time' => "预约时间",
            'coupon_id' => "优惠券",

            "goods_info" => "商品信息",
            "goods_info.price" => "商品预估价",
            "goods_info.is_protect_price" => "是否保价",
            "goods_info.weight" => "商品重量(kg)",
            "is_bad_weather" => "是否恶劣天气",

            "start_point" => "起点信息",
            "start_point.mode" => "起点类型",
            "start_point.address_id" => "起点地址",
            "start_point.lat" => "指定店铺纬度",
            "start_point.lng" => "指定店铺经度",

            "end_point" => "终点信息",
            "end_point.address_id" => "终点地址",
        ];
    }
}
