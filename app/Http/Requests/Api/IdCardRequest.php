<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class IdCardRequest extends FormRequest
{

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'id_card_image' => 'required|URL',
            'id_card_image_over' => 'required|URL',
            'name' => 'required',
            'nation' => 'required',
            'address' => 'required',
            'id_card' => 'required',
            'issuing_authority' => 'required',
            'birth' => 'required',
            'issuing_date' => 'required',
            'expiry_date' => 'required',
            'sex' => 'required'
        ];
    }
}
