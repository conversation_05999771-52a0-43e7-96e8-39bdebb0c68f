<?php

namespace App\Http\Controllers\Merchant;

use App\Http\Controllers\Controller;
use App\Models\Merchant;
use App\Models\MerchantRechargeOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class RechargeController extends Controller
{
    /**
     * 创建一个新的控制器实例
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth:merchant');
    }

    /**
     * 显示充值页面
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $merchant = Auth::guard('merchant')->user();
        
        return view('merchant.recharge', [
            'merchant' => $merchant,
        ]);
    }

    /**
     * 创建充值订单
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $request->validate([
            'amount' => 'required|numeric|min:1',
        ]);
        
        $merchant = Auth::guard('merchant')->user();
        $amount = $request->input('amount');
        $amountInCents = (int)($amount * 100); // 转换为分
        
        try {
            DB::beginTransaction();
            
            // 创建充值订单
            $order = new MerchantRechargeOrder();
            $order->merchant_id = $merchant->id;
            $order->order_amount = $amountInCents;
            $order->actual_amount = $amountInCents;
            $order->reduce_amount = 0; // 无优惠
            $order->closed = false;
            $order->refund_status = MerchantRechargeOrder::REFUND_STATUS_PENDING;
            $order->save();
            
            DB::commit();
            
            // 跳转到支付页面
            return redirect()->route('merchant.recharge.pay', ['order_no' => $order->order_no]);
            
        } catch (\Exception $e) {
            DB::rollBack();
            return redirect()->back()->with('error', '创建充值订单失败：' . $e->getMessage());
        }
    }

    /**
     * 显示支付页面
     *
     * @param  string  $order_no
     * @return \Illuminate\View\View
     */
    public function pay($order_no)
    {
        $merchant = Auth::guard('merchant')->user();
        $order = MerchantRechargeOrder::where('order_no', $order_no)
                                     ->where('merchant_id', $merchant->id)
                                     ->where('closed', false)
                                     ->whereNull('paid_at')
                                     ->firstOrFail();
        
        return view('merchant.recharge-pay', [
            'merchant' => $merchant,
            'order' => $order,
        ]);
    }

    /**
     * 处理支付请求
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $order_no
     * @return \Illuminate\Http\RedirectResponse
     */
    public function doPay(Request $request, $order_no)
    {
        $request->validate([
            'payment_method' => 'required|in:1,2',
            'pay_way' => 'required|in:web,wap',
        ]);
        
        $merchant = Auth::guard('merchant')->user();
        $order = MerchantRechargeOrder::where('order_no', $order_no)
                                     ->where('merchant_id', $merchant->id)
                                     ->where('closed', false)
                                     ->whereNull('paid_at')
                                     ->firstOrFail();
        
        $payMethod = $request->input('payment_method');
        $payWay = $request->input('pay_way');
        
        try {
            // 更新订单支付方式
            $order->pay_method = $payMethod;
            $order->save();
            
            // 配置支付参数
            $config = config('pay');
            
            // 调用支付服务
            $payService = app(\App\Services\PayService::class);
            $result = $payService->pay($order->order_no, $payMethod, $payWay, $request->ip());
            
            // 根据支付方式返回不同的结果
            if ($payMethod == \App\Models\Common::PAY_METHOD_ALIPAY && $payWay == 'web') {
                // 支付宝网页支付，直接返回HTML
                return response($result);
            } elseif ($payMethod == \App\Models\Common::PAY_METHOD_WECHAT && $payWay == 'wap') {
                // 微信H5支付，重定向到支付URL
                return redirect($result->url);
            } else {
                // 其他支付方式，返回JSON
                return response()->json($result);
            }
            
        } catch (\Exception $e) {
            return redirect()->back()->with('error', '支付处理失败：' . $e->getMessage());
        }
    }

    /**
     * 显示充值记录
     *
     * @return \Illuminate\View\View
     */
    public function records()
    {
        $merchant = Auth::guard('merchant')->user();
        $records = MerchantRechargeOrder::where('merchant_id', $merchant->id)
                                       ->orderBy('created_at', 'desc')
                                       ->paginate(15);
        
        return view('merchant.recharge-records', [
            'merchant' => $merchant,
            'records' => $records,
        ]);
    }

    /**
     * 生成支付二维码
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  string  $order_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateQrcode(Request $request, $order_no)
    {
        $paymentMethod = $request->input('payment_method', \App\Models\Common::PAY_METHOD_ALIPAY);
        
        $merchant = Auth::guard('merchant')->user();
        $order = MerchantRechargeOrder::where('order_no', $order_no)
                                     ->where('merchant_id', $merchant->id)
                                     ->where('closed', false)
                                     ->whereNull('paid_at')
                                     ->firstOrFail();
        
        try {
            // 更新订单支付方式
            $order->pay_method = $paymentMethod;
            $order->save();
            
            // 配置支付参数
            $config = config('pay');

            
            // 调用支付服务
            $payService = app(\App\Services\PayService::class);
            $result = $payService->pay($order->order_no, $paymentMethod, 'scan', $request->ip());
            
            // 根据支付方式返回不同的结果
            if ($paymentMethod == \App\Models\Common::PAY_METHOD_ALIPAY) {
                // 支付宝扫码支付
                return response()->json([
                    'success' => true,
                    'code_url' => $result['qr_code'],
                ]);
            } else {
                // 微信扫码支付
                return response()->json([
                    'success' => true,
                    'code_url' => $result['code_url'],
                ]);
            }
            
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => '生成支付二维码失败：' . $e->getMessage(),
            ], 500);
        }
    }
    
    /**
     * 检查订单支付状态
     *
     * @param  string  $order_no
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkStatus($order_no)
    {
        $merchant = Auth::guard('merchant')->user();
        $order = MerchantRechargeOrder::where('order_no', $order_no)
                                     ->where('merchant_id', $merchant->id)
                                     ->first();
        
        if (!$order) {
            return response()->json([
                'success' => false,
                'message' => '订单不存在',
            ], 404);
        }
        
        return response()->json([
            'success' => true,
            'paid' => !is_null($order->paid_at),
            'status' => $order->status,
        ]);
    }
} 