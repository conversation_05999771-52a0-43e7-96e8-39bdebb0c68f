<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\UserIdCard;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class UserIdCardController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new UserIdCard(), function (Grid $grid) {
            $grid->column('id')->sortable();
            $grid->column('user_id');
            $grid->column('id_card_image');
            $grid->column('id_card_image_over');
            $grid->column('name');
            $grid->column('nation');
            $grid->column('address');
            $grid->column('id_card');
            $grid->column('issuing_authority');
            $grid->column('sex');
            $grid->column('birth');
            $grid->column('issuing_date');
            $grid->column('expiry_date');
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
        
            });
        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new UserIdCard(), function (Show $show) {
            $show->field('id');
            $show->field('user_id');
            $show->field('id_card_image');
            $show->field('id_card_image_over');
            $show->field('name');
            $show->field('nation');
            $show->field('address');
            $show->field('id_card');
            $show->field('issuing_authority');
            $show->field('sex');
            $show->field('birth');
            $show->field('issuing_date');
            $show->field('expiry_date');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new UserIdCard(), function (Form $form) {
            $form->display('id');
            $form->text('user_id');
            $form->text('id_card_image');
            $form->text('id_card_image_over');
            $form->text('name');
            $form->text('nation');
            $form->text('address');
            $form->text('id_card');
            $form->text('issuing_authority');
            $form->text('sex');
            $form->text('birth');
            $form->text('issuing_date');
            $form->text('expiry_date');
        
            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
