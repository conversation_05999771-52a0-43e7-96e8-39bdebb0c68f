<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\IdCardRequest;
use App\Http\Requests\Api\UserCouponRequest;
use App\Http\Resources\UserCouponResource;
use App\Models\Common;
use App\Models\Coupon;
use App\Models\Rider;
use App\Models\UserCoupon;
use App\Models\UserIdCard;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class UserCouponController extends Controller
{
    public function index(Request $request)
    {
        $query = UserCoupon::query()->with(['coupon'])->where('user_id', $request->user()->id);
        if ($request->status) {
            $statusArr = explode(",", $request->status);
            $query->where(function ($query) use ($statusArr) {
                foreach ($statusArr as $status) {
                    switch ($status) {
                        case 1: //可使用
                            $query->orWhere(function ($query) {
                                $query->where("start_time", "<=", Carbon::now())->where("end_time", ">", Carbon::now())->where("order_id", 0);
                            });
                            break;
                        case 2: //待生效
                            $query->orWhere(function ($query) {
                                $query->where("start_time", ">", Carbon::now())->where("order_id", 0);
                            });
                            break;
                        case 3: //已使用
                            $query->orWhere("order_id", ">", 0);
                            break;
                        case 4: //已过期
                            $query->orWhere(function ($query) {
                                $query->where("end_time", "<=", Carbon::now())->where("order_id", 0);
                            });
                            break;
                    }
                }
            });
        }
        $coupons = $query->orderBy("created_at", "desc")->paginate($request->get("page_size", $this->pageSize));
        $result = [
            'total' => $coupons->total(),
            'has_next' => $coupons->hasMorePages(),
            'data' => [],
        ];
        if (count($coupons) > 0) {
            foreach ($coupons as $item) {
                $result['data'][] = new UserCouponResource($item);
            }
        }
        return $this->success('success', $result);
    }

    public function store(UserCouponRequest $request)
    {
        $userId = $request->user()->id;
        $has = UserCoupon::query()->where("user_id", $userId)->where("coupon_id", $request->coupon_id)->where("end_time", ">", Carbon::now())->where("order_id", 0)->first();
        if ($has) {
            $this->errorResponse(500, '您已领取过该券，不可重复领取～');
        }
        $coupon = Coupon::query()->findOrFail($request->coupon_id);
        if ($coupon->status != Common::STATUS_UP) {
            $this->errorResponse(500, '该优惠券未上架～');
        }
        if ($coupon->validity["type"] == Coupon::VALIDITY_TYPE_RANGE && $coupon->validity["end"] <= Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT)) {
            $this->errorResponse(500, '该优惠券已过期～');
        }
        if ($coupon->stock <= 0) {
            $this->errorResponse(500, '该优惠券已领完～');
        }
        //领券
        {
            //领取时间
            if ($coupon->validity['type'] == Coupon::VALIDITY_TYPE_RANGE) {
                $start = $coupon->validity['start'];
                $end = $coupon->validity['end'];
            } else {
                $start = Carbon::now();
                $end = Carbon::now()->addDays($coupon->validity["day"]);
            }
            try {
                DB::beginTransaction();
                //创建领取记录、增加销量、较少库存
                $rowsAffected = Coupon::query()->where("id", $coupon->id)->where("stock", ">", 0)
                    ->update(["stock" => DB::raw("stock-1"), "sales" => DB::raw("sales+1")]);
                if ($rowsAffected <= 0) {
                    throw new \Exception("该优惠券已领完～");
                }
                UserCoupon::query()->create([
                    'coupon_id' => $coupon->id,
                    'user_id' => $userId,
                    'start_time' => $start,
                    'end_time' => $end,
                ]);
                DB::commit();
            } catch (\Exception $e) {
                DB::rollBack();
                $this->errorResponse(500, $e->getMessage());
            }
            $this->success('领取成功', null);
        }
    }
}
