<?php

namespace App\Http\Controllers\Api;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RiderAccountController extends Controller
{
    public function account(Request $request)
    {
        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();

        if (!$rider) {
            $result = [
                'amount' => 0,
                'income' => '0',
                'count' => 0,
                'out' => 0,
                'flows' => [],
            ];
            return $this->success('success', $result);
        }

        $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 1)->first();

        $flows = RiderAccountFlow::query()->where('user_account_id', $account->id)
            ->whereBetween('created_at', [Carbon::now()->startOfDay(), Carbon::now()])->orderByDesc('id')->get();
        $flowList = [];
        foreach ($flows as $flow) {
            $flowList[] = [
                'chg_amount' => fentoyuan($flow->chg_amount),
                'type' => $flow->type,
                'remark' => $flow->remark
            ];
        }

        // 预计收入 查询未结算订单
        $income = O2oErrandOrder::query()->where('rider_id', $rider->id)
            ->whereNull('settle_time')
            ->where('pickup_at', '>=', Carbon::now()->startOfDay())
            ->sum(DB::raw('reward_amount + gratuity + reward'));

        $count = O2oErrandOrder::query()->where('rider_id', $rider->id)
            ->where('order_status', O2oErrandOrder::STATUS_FINISH)
            ->where('pickup_at', '>=', Carbon::now()->startOfDay())
            ->whereNotNull('refund_no')
            ->count();

        $result = [
            'amount' => fentoyuan($account->amount),
            'income' => '0',
            'count' => $count,
            'out' => 0,
            'flows' => $flowList,
        ];

        return $this->success('success', $result);
    }

    public function earnestAccount(Request $request)
    {
        $user = $request->user();

        $rider = Rider::query()->where('user_id', $user->id)->first();
        if (!$rider) $this->errorResponse(500, "请先进行实名认证成为骑手");

        $account = RiderAccount::query()->where('user_id', $user->id)->where('account_type', 2)->first();
        $result = [
            'amount' => fentoyuan($account->amount),
            'recharge_amount' => [100, 200, 500],
            'recharge_agreement' => 'https://www.baidu.com',
        ];
        return $this->success('success', $result);
    }
}
