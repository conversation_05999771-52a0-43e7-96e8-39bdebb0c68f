<?php

namespace App\Console\Commands;

use App\Models\Region;
use Illuminate\Console\Command;
use Ixudra\Curl\Facades\Curl;
use Overtrue\Pinyin\Pinyin;

class GenCity extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'gen:city';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '国内城市数据生成';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $url = "https://www.mxnzp.com/api/address/list?app_id=qkmswenpnexljrow&app_secret=Z2dQdmZUWHI2Y3ZyUWdXcWgrL3lxZz09&=";
        $res = Curl::to($url)->asJson(true)->get();
        foreach ($res['data'] as $city) {
            $region = Region::query()->where('code', $city['code'])->first();
            $level = 1;
            if (!$region) {
                $region = new Region();
                $region->name = $city['name'];
                $region->pid = 0;
                $region->level = $level;
                $region->code = $city['code'];
                $region->is_hot = 0;
                $region->letter = strtoupper(Pinyin::nameAbbr($city['name'])[0]);
                $region->pinyin = Pinyin::sentence($city['name'], 'none');
                $region->status = 1;
                $region->save();
            }
            if (isset($city['pchilds']) && $city['pchilds']) {
                foreach ($city['pchilds'] as $pchild) {
                    $pregion = Region::query()->where('code', $pchild['code'])->first();
                    $level = 2;
                    if (!$pregion) {
                        $pregion = new Region();
                        $pregion->name = $pchild['name'];
                        $pregion->pid = $region->id;
                        $pregion->level = $level;
                        $pregion->code = $pchild['code'];
                        $pregion->is_hot = 0;
                        $pregion->letter = strtoupper(Pinyin::nameAbbr($pchild['name'])[0]);
                        $pregion->pinyin = Pinyin::sentence($pchild['name'], 'none');
                        $pregion->status = 1;
                        $pregion->save();
                    }
                    if (isset($pchild['cchilds']) && $pchild['cchilds']) {
                        foreach ($pchild['cchilds'] as $cchild) {
                            $cregion = Region::query()->where('code', $cchild['code'])->first();
                            $level = 3;
                            if (!$cregion) {
                                $cregion = new Region();
                                $cregion->name = $cchild['name'];
                                $cregion->pid = $pregion->id;
                                $cregion->level = $level;
                                $cregion->code = $cchild['code'];
                                $cregion->is_hot = 0;
                                $cregion->letter = strtoupper(Pinyin::nameAbbr($cchild['name'])[0]);
                                $cregion->pinyin = Pinyin::sentence($cchild['name'], 'none');
                                $cregion->status = 1;
                                $cregion->save();
                            }
                        }
                    }
                }
            }
        }
        return Command::SUCCESS;
    }
}
