<?php

namespace App\Admin\Controllers;

use App\Admin\Repositories\Shop;
use App\Models\Common;
use App\Models\Community;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Support\Facades\Config;
use function Psy\debug;

class ShopController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Shop(), function (Grid $grid) {
            $grid->model()->with(['community'])->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();

            $grid->column("id");
            $grid->column('name');
            $grid->column('logo')->image(Config::get('app.img_url_prefix'), 80);
            $grid->column("community.name", "社区");
            $grid->column("address_detail")->display(function ($value) {
                return implode("", $this->regions) . $value;
            });
            $grid->column('status')->select(Common::StatusMap);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->like('name')->width(3);
                $filter->in('community_id')->multipleSelect(Community::query()->pluck("name", "id"))->width(2);
                $filter->in('status')->multipleSelect(Common::StatusMap)->width(2);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Shop(), function (Form $form) {
            $form->display('id');
            $form->text('name')->required();
            $form->image('logo')->required()->autoUpload()->autoSave(false)->uniqueName();
            $form->image('cover')->required()->autoUpload()->autoSave(false)->uniqueName();
            $form->select('community_id')->options(Community::query()->pluck("name", "id"))->required();
            $form->select('province')->options('/options/provinces')->load('city', '/options/cities')->required();
            $form->select('city')->load('district', '/options/district')->required();
            $form->select('district')->required();
            $form->text('address_detail')->required();
            $form->html(view('coordinate'), '位置选择');
            $form->hidden('map_points', '坐标点'); // 隐藏域，用于接收坐标点（这里如果想数据回填可以，->value('49.121221,132.2321312')）
            $form->hidden('map_area', '当前区域'); // 隐藏域，用于接收详细点位地址
            $form->hidden('lng');
            $form->hidden('lat');
            $form->text('promotion_info');
            $form->text('tel');
            $form->select('status')->options(Common::StatusMap)->default(Common::STATUS_INIT)->required();
//            $form->number('sort')->default(0)->attribute('min', 0)->required();
            $form->display('created_at');
            $form->display('updated_at');
        })->saving(function (Form $form) {
            if ($form->map_points) {
                $points = $form->map_points;
                $pointArr = explode(',', $points);
                $form->lat = $pointArr[0];
                $form->lng = $pointArr[1];
            }
            $form->deleteInput('map_points');
            $form->deleteInput('map_area');
        });
    }
}
