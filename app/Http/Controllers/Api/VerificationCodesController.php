<?php

namespace App\Http\Controllers\Api;

use App\Models\BanIp;
use App\Models\User;
use App\Services\ChuanglanSmsApi;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\Cache;
use App\Http\Requests\Api\VerificationCodeRequest;

class VerificationCodesController extends Controller
{
    public function store(VerificationCodeRequest $request)
    {
        $phone = $request->phone;
        $type = $request->type;
        // 获取请求的IP地址
        $ipAddress = $request->getClientIp();
        switch ($type) {
            case 'register':
                // 判断手机号是否注册
                $user = User::query()->where('phone', $phone)->first();
                if ($user) {
                    $this->errorResponse(500, '手机号已注册,直接去登录吧～');
                }
                break;
            case 'change':
                // 判断手机号是否注册
                $user = User::query()->where('phone', $phone)->first();
                if ($user) {
                    $this->errorResponse(500, '新手机号已注册，无法更换。请换个手机号换绑吧');
                }
                break;
            default:
        }

        // 生成4位随机数，左侧补0
        if (app()->environment() != 'production') {
            $code = '1234';
        } else {
            if (in_array($request->userAgent(), ['HanjuTV/6.2.5 (BLN-AL10; Android 7.0; Scale/2.00)', 'okhttp/3.10.0', 'LangShen'])) {
                Log::warning('sms warning');
                $this->errorResponse(500, '请求过于频繁，请稍后再试');
            }
            $ipBan = BanIp::query()->where('ip', $ipAddress)->orderBy('expired_at', 'desc')->first();
            if ($ipBan && Carbon::parse($ipBan->expired_at)->gt(now())) {
                $this->errorResponse(500, '请求过于频繁，请稍后再试');
            }
            // 增加ip限制逻辑
            $ipLimitKey = 'ip_limit_' . $ipAddress;
            $ipLimitCount = Cache::get($ipLimitKey, 0);
            $ipLimitMaxAttempts = 5; // 最大尝试次数
            $ipLimitExpiration = now()->addMinutes(60); // 限制过期时间（例如60分钟）
            // 检查IP限制
            if ($ipLimitCount >= $ipLimitMaxAttempts) {
                if (!$ipBan) {
                    $ipBan = new BanIp();
                    $ipBan->ip = $ipAddress;
                    $ipBan->reason = '短信接口请求过于频繁';
                    $ipBan->expired_at = now()->addHour(24);
                    $ipBan->save();
                } else {
                    $ipBan->expired_at = now()->addDay(30);
                    $ipBan->save();
                }
                $this->errorResponse(500, '请求过于频繁，请稍后再试');
            }

            $code = mt_rand(1000, 9999) . '';
            $smsService = app(ChuanglanSmsApi::class);
            $msg = '【雨骑士】您的验证码是：{$var}，如非本人操作，请忽略本短信。	';
            $params = "$phone,$code";
            $result = $smsService->sendVariableSMS($msg, $params);
            Log::info('send sms result: ' . json_encode($result));

            // 增加IP限制计数
            Cache::put($ipLimitKey, $ipLimitCount + 1, $ipLimitExpiration);
        }
        $key = Str::random(15);
        $cacheKey = 'verificationCode_' . $key;
        $expiredAt = now()->addMinutes(5);
        // 缓存验证码 5 分钟过期。
        Cache::put($cacheKey, ['phone' => $phone, 'code' => $code], $expiredAt);

        return $this->success('验证码获取成功', [
            'key' => $key,
            'expired_at' => $expiredAt->toDateTimeString(),
        ]);
    }
}

