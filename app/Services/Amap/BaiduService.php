<?php


namespace App\Services\Amap;


use Exception;
use Illuminate\Support\Facades\Log;
use Ixudra\Curl\Facades\Curl;

class BaiduService
{
    const RidingUrl = "https://api.map.baidu.com/direction/v2/riding";

    protected $appKey;

    public function __construct()
    {
        $this->appKey = config('services.map_key.baidu');
    }

    /**
     * 发起http请求
     * @param string $requestUrl 请求url
     * @param array $params 请求参数
     * @return mixed
     * @throws Exception
     */
    private function do(string $requestUrl, array $params = []): mixed
    {
        try {
            $params["ak"] = $this->appKey;
            $res = Curl::to($requestUrl . "?" . http_build_query($params))->asJsonResponse(true)->get();
        } catch (Exception $e) {
            throw new Exception($requestUrl . "：" . $e->getMessage());
        }
        if ($res["status"] != 0) {
            throw new Exception($requestUrl . "：" . $res["message"]);
        }
        return $res;
    }

    /**
     * 电动车路线
     * @param string $fromLng 起点经度
     * @param string $fromLat 起点纬度
     * @param string $toLng 终点经度
     * @param string $toLat 终点纬度
     * @return mixed
     * @throws Exception
     */
    public function riding(string $fromLng, string $fromLat, string $toLng, string $toLat): mixed
    {
        $arr = explode(".", $fromLng);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $fromLng = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $fromLat);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $fromLat = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $toLng);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $toLng = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $toLat);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $toLat = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        try {
            $res = $this->do(self::RidingUrl, ["origin" => $fromLat . ',' . $fromLng, "destination" => $toLat . ',' . $toLng, "coord_type" => "gcj02", "ret_coordtype" => "gcj02", "riding_type" => 1]);
            return $res;
        } catch (Exception $e) {
            Log::error($e->getMessage());
        }
        return [];
    }

    /**
     * 逆地理编码，根据经纬度获取地址信息
     * @param string $lng 经度
     * @param string $lat 纬度
     * @return array 地址信息
     */
    public function reverseGeocoding(string $lng, string $lat): array
    {
        // 处理经纬度的精度
        $arr = explode(".", $lng);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $lng = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        $arr = explode(".", $lat);
        if (count($arr) > 1) {
            if (strlen($arr[1]) > 6) $lat = $arr[0] . "." . substr($arr[1], 0, 6);
        }
        
        try {
            // 百度地图逆地理编码API
            $apiUrl = "https://api.map.baidu.com/reverse_geocoding/v3";
            $params = [
                "location" => $lat . ',' . $lng,
                "output" => "json",
                "coordtype" => "gcj02ll", // 坐标系类型
                "ret_coordtype" => "gcj02ll",
                "extensions_town" => "true" // 返回乡镇信息
            ];
            
            $res = $this->do($apiUrl, $params);
            return $res;
        } catch (Exception $e) {
            Log::error("逆地理编码失败: " . $e->getMessage(), [
                'lng' => $lng,
                'lat' => $lat
            ]);
        }
        
        return [];
    }
}
