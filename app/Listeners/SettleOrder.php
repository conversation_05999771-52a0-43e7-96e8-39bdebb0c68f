<?php

namespace App\Listeners;

use App\Events\AfterFinishO2oErrandOrder;
use App\Events\AfterRegister;
use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use App\Services\EasemobService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SettleOrder
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\AfterRegister $event
     * @return void
     */
    public function handle(AfterFinishO2oErrandOrder $event)
    {
        $order = $event->order;

        DB::beginTransaction();
        try {
            $riderAccount = RiderAccount::query()->where('user_id', $order->rider->user_id)
                ->where('account_type', 1)->first();

            $riderAccount->add($order->reward_amount, RiderAccountFlow::BUSINESS_TYPE_BUY_FEE, $order->order_no, '帮买垫付金额结算');

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error($e->getMessage() . PHP_EOL . $e->getFile() . $e->getLine() . PHP_EOL . $e->getTraceAsString());
        }


    }
}
