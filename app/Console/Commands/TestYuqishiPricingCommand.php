<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Pricing;
use App\Models\Site;
use Illuminate\Support\Facades\DB;

class TestYuqishiPricingCommand extends Command
{
    protected $signature = 'test:yuqishi-pricing {--site-id=1 : 站点ID} {--create-test-data : 创建测试数据}';
    protected $description = '测试雨骑士定价策略功能';

    public function handle()
    {
        $this->info('🚀 开始测试雨骑士定价策略...');
        
        $siteId = $this->option('site-id');
        
        // 如果指定了创建测试数据选项，则先创建测试配置
        if ($this->option('create-test-data')) {
            $this->createTestPricingData($siteId);
        }
        
        // 显示当前配置信息
        $this->showCurrentPricingConfig($siteId);
        
        // 测试四种场景
        $this->testNormalPricing($siteId);
        $this->testNightPricing($siteId);
        $this->testWeatherNormalPricing($siteId);
        $this->testWeatherNightPricing($siteId);
        
        // 测试重量计费
        $this->testWeightPricing($siteId);
        
        // 测试边界条件
        $this->testBoundaryConditions($siteId);
        
        // 测试长距离配送
        $this->testLongDistanceDelivery($siteId);
        
        // 测试新版本雨骑士定价数据格式
        $this->testNewVersionFormat();
        
        // 测试API兼容性
        $this->testAPICompatibility();
        
        // 测试无重量参数场景
        $this->testNoWeightScenario();
        
        // 测试恶劣天气加收费用计算
        $this->testWeatherPriceCalculation();
        
        $this->info('✅ 雨骑士定价策略测试完成！');
    }
    
    /**
     * 显示当前定价配置信息
     */
    private function showCurrentPricingConfig($siteId)
    {
        $this->info('');
        $this->info('📋 当前雨骑士定价配置信息');
        $this->line('==============================================');
        
        $pricing = Pricing::where('site_id', $siteId)
            ->where('use_yuqishi_pricing', true)
            ->first();
            
        if (!$pricing) {
            $this->error('❌ 未找到雨骑士定价配置！');
            return;
        }
        
        $this->info("配置标题：{$pricing->title}");
        $this->info("站点ID：{$pricing->site_id}");
        $this->info("是否启用雨骑士定价：" . ($pricing->use_yuqishi_pricing ? '是' : '否'));
        $this->line('');
        
        // 显示四种定价策略
        $strategies = [
            'normal_pricing' => '🌞 正常时间+正常天气定价',
            'night_pricing' => '🌙 夜间时间+正常天气定价', 
            'weather_normal_pricing' => '🌧️ 正常时间+恶劣天气定价',
            'weather_night_pricing' => '🌙🌧️ 夜间时间+恶劣天气定价'
        ];
        
        foreach ($strategies as $field => $title) {
            $this->info($title);
            $this->line('----------------------------------------------');
            
            $strategy = $pricing->$field;
            if (!$strategy) {
                $this->warn('  未配置');
                continue;
            }
            
            foreach ($strategy as $index => $rule) {
                $this->line("  距离区间 {$rule['distance_range']}：");
                $this->line("    基础价格：{$rule['base_price']}元");
                if (isset($rule['extra_price']) && $rule['extra_price'] > 0) {
                    $this->line("    超出单价：{$rule['extra_price']}元/公里");
                }
                
                // 显示重量定价
                if (isset($rule['weight_pricing']) && is_array($rule['weight_pricing'])) {
                    $this->line("    重量定价：");
                    foreach ($rule['weight_pricing'] as $weightRule) {
                        $weightDesc = "      {$weightRule['weight_range']}kg: {$weightRule['base_price']}元";
                        if (isset($weightRule['extra_price']) && $weightRule['extra_price'] > 0) {
                            $weightDesc .= " + {$weightRule['extra_price']}元/kg（超出部分）";
                        }
                        $this->line($weightDesc);
                    }
                } else {
                    $this->line("    重量定价：未配置");
                }
                $this->line('');
            }
        }
        
        $this->line('==============================================');
    }
    
    /**
     * 创建测试用的定价配置数据
     */
    private function createTestPricingData($siteId)
    {
        $this->info('📝 创建测试用定价配置数据...');
        
        // 先检查是否已存在测试配置
        $existingPricing = Pricing::where('site_id', $siteId)
            ->where('use_yuqishi_pricing', true)
            ->where('title', 'LIKE', '%雨骑士测试%')
            ->first();
            
        if ($existingPricing) {
            $this->warn('测试配置已存在，跳过创建');
            return;
        }
        
        $pricingData = [
            'type' => Pricing::TYPE_USER,
            'title' => '雨骑士测试配置-杭州闪电仓',
            'site_id' => $siteId,
            'base_price' => 0, // 不使用原有字段
            'use_yuqishi_pricing' => true,
            'normal_pricing' => [
                ['distance_range' => '0-1', 'base_price' => 4.0, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '1-2', 'base_price' => 4.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '2-3', 'base_price' => 5.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '3+', 'base_price' => 6.3, 'extra_price' => 2.0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
            ],
            'night_pricing' => [
                ['distance_range' => '0-1', 'base_price' => 5.0, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '1-2', 'base_price' => 5.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '2-3', 'base_price' => 6.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '3+', 'base_price' => 7.3, 'extra_price' => 2.5, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
            ],
            'weather_normal_pricing' => [
                ['distance_range' => '0-1', 'base_price' => 5.0, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '1-2', 'base_price' => 5.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '2-3', 'base_price' => 6.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '3+', 'base_price' => 7.3, 'extra_price' => 2.5, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
            ],
            'weather_night_pricing' => [
                ['distance_range' => '0-1', 'base_price' => 6.0, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '1-2', 'base_price' => 6.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '2-3', 'base_price' => 7.3, 'extra_price' => 0, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
                ['distance_range' => '3+', 'base_price' => 8.3, 'extra_price' => 3.5, 'weight_pricing' => [
                    ['weight_range' => '0-3', 'base_price' => 0],
                    ['weight_range' => '3-5', 'base_price' => 2.0],
                    ['weight_range' => '5+', 'base_price' => 5.0, 'extra_price' => 1.0]
                ]],
            ],
        ];
        
        Pricing::create($pricingData);
        $this->info('✅ 测试配置创建成功');
    }
    
    /**
     * 测试正常时间+正常天气
     */
    private function testNormalPricing($siteId)
    {
        $this->info('');
        $this->info('🌞 测试场景1：正常时间(9:00-22:00) + 正常天气');
        $this->line('-----------------------------------------------');
        
        $testCases = [
            ['distance' => 500, 'time' => '10:30', 'weight' => 2.0, 'expected' => '0-1公里，4.0元，重量免费'],
            ['distance' => 1500, 'time' => '14:20', 'weight' => 4.0, 'expected' => '1-2公里，4.3元，重量+2元'],
            ['distance' => 2500, 'time' => '18:15', 'weight' => 6.0, 'expected' => '2-3公里，5.3元，重量+6元'],
            ['distance' => 3500, 'time' => '12:00', 'weight' => 1.5, 'expected' => '3公里以上，6.3元基础价，重量免费'],
            ['distance' => 5000, 'time' => '16:45', 'weight' => 8.5, 'expected' => '5公里，6.3+2*2.0=10.3元，重量+8.5元'],
        ];
        
        foreach ($testCases as $case) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $case['distance'],
                "2023-01-18 {$case['time']}",
                false,
                false,
                $case['weight']
            );
            
            $basicAmount = ($result['freight'] + $result['distance_price']) / 100;
            $weightAmount = $result['weight_price'] / 100;
            $totalAmount = ($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100;
            $this->line("距离：{$case['distance']}米 | 重量：{$case['weight']}kg | 时间：{$case['time']}");
            $this->line("  基础费：{$basicAmount}元 | 重量费：{$weightAmount}元 | 总计：{$totalAmount}元");
            $this->line("  预期：{$case['expected']}");
            $this->line('');
        }
    }
    
    /**
     * 测试夜间时间+正常天气
     */
    private function testNightPricing($siteId)
    {
        $this->info('');
        $this->info('🌙 测试场景2：夜间时间(22:00-8:00) + 正常天气');
        $this->line('-----------------------------------------------');
        
        $testCases = [
            ['distance' => 500, 'time' => '23:30', 'weight' => 1.0, 'expected' => '0-1公里，5.0元，重量免费'],
            ['distance' => 1500, 'time' => '02:20', 'weight' => 3.5, 'expected' => '1-2公里，5.3元，重量+2元'],
            ['distance' => 2500, 'time' => '06:15', 'weight' => 7.0, 'expected' => '2-3公里，6.3元，重量+7元'],
            ['distance' => 4000, 'time' => '01:00', 'weight' => 2.5, 'expected' => '4公里，7.3+1*2.5=9.8元，重量免费'],
        ];
        
        foreach ($testCases as $case) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $case['distance'],
                "2023-01-18 {$case['time']}",
                false,
                false,
                $case['weight']
            );
            
            $basicAmount = ($result['freight'] + $result['distance_price']) / 100;
            $weightAmount = $result['weight_price'] / 100;
            $totalAmount = ($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100;
            $this->line("距离：{$case['distance']}米 | 重量：{$case['weight']}kg | 时间：{$case['time']}");
            $this->line("  基础费：{$basicAmount}元 | 重量费：{$weightAmount}元 | 总计：{$totalAmount}元");
            $this->line("  预期：{$case['expected']}");
            $this->line('');
        }
    }
    
    /**
     * 测试正常时间+恶劣天气
     */
    private function testWeatherNormalPricing($siteId)
    {
        $this->info('');
        $this->info('🌧️ 测试场景3：正常时间(9:00-22:00) + 恶劣天气');
        $this->line('-----------------------------------------------');
        
        $testCases = [
            ['distance' => 500, 'time' => '10:30', 'weight' => 2.5, 'expected' => '0-1公里，5.0元，重量免费'],
            ['distance' => 1500, 'time' => '14:20', 'weight' => 4.5, 'expected' => '1-2公里，5.3元，重量+2元'],
            ['distance' => 2500, 'time' => '18:15', 'weight' => 8.0, 'expected' => '2-3公里，6.3元，重量+8元'],
            ['distance' => 4500, 'time' => '12:00', 'weight' => 1.0, 'expected' => '4.5公里，7.3+1.5*2.5=11.05元，重量免费'],
        ];
        
        foreach ($testCases as $case) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $case['distance'],
                "2023-01-18 {$case['time']}",
                false,
                true, // 恶劣天气
                $case['weight']
            );
            
            $basicAmount = ($result['freight'] + $result['distance_price']) / 100;
            $weightAmount = $result['weight_price'] / 100;
            $totalAmount = ($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100;
            $this->line("距离：{$case['distance']}米 | 重量：{$case['weight']}kg | 时间：{$case['time']} | 恶劣天气");
            $this->line("  基础费：{$basicAmount}元 | 重量费：{$weightAmount}元 | 总计：{$totalAmount}元");
            $this->line("  预期：{$case['expected']}");
            $this->line('');
        }
    }
    
    /**
     * 测试夜间时间+恶劣天气
     */
    private function testWeatherNightPricing($siteId)
    {
        $this->info('');
        $this->info('🌙🌧️ 测试场景4：夜间时间(22:00-8:00) + 恶劣天气');
        $this->line('-----------------------------------------------');
        
        $testCases = [
            ['distance' => 500, 'time' => '23:30', 'weight' => 0.5, 'expected' => '0-1公里，6.0元，重量免费'],
            ['distance' => 1500, 'time' => '02:20', 'weight' => 4.0, 'expected' => '1-2公里，6.3元，重量+2元'],
            ['distance' => 2500, 'time' => '06:15', 'weight' => 9.5, 'expected' => '2-3公里，7.3元，重量+9.5元'],
            ['distance' => 4500, 'time' => '01:00', 'weight' => 3.0, 'expected' => '4.5公里，8.3+1.5*3.5=13.55元，重量免费'],
        ];
        
        foreach ($testCases as $case) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $case['distance'],
                "2023-01-18 {$case['time']}",
                false,
                true, // 恶劣天气
                $case['weight']
            );
            
            $basicAmount = ($result['freight'] + $result['distance_price']) / 100;
            $weightAmount = $result['weight_price'] / 100;
            $totalAmount = ($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100;
            $this->line("距离：{$case['distance']}米 | 重量：{$case['weight']}kg | 时间：{$case['time']} | 恶劣天气");
            $this->line("  基础费：{$basicAmount}元 | 重量费：{$weightAmount}元 | 总计：{$totalAmount}元");
            $this->line("  预期：{$case['expected']}");
            $this->line('');
        }
    }
    
    /**
     * 测试重量计费
     */
    private function testWeightPricing($siteId)
    {
        $this->info('');
        $this->info('⚖️ 测试场景5：重量计费测试');
        $this->line('-----------------------------------------------');
        $this->line('重量计费规则：0-3kg免费，3-5kg加2元，5kg以上基础5元+每kg加1元');
        $this->line('');
        
        $testCases = [
            ['distance' => 1500, 'time' => '10:30', 'weight' => 1.5, 'weather' => false, 'expected' => '1.5kg，无重量费'],
            ['distance' => 1500, 'time' => '10:30', 'weight' => 4.0, 'weather' => false, 'expected' => '4.0kg，重量费2元'],
            ['distance' => 1500, 'time' => '10:30', 'weight' => 6.5, 'weather' => false, 'expected' => '6.5kg，重量费5+1.5*1=6.5元'],
            ['distance' => 1500, 'time' => '10:30', 'weight' => 10.0, 'weather' => false, 'expected' => '10kg，重量费5+5*1=10元'],
            ['distance' => 1500, 'time' => '23:30', 'weight' => 8.0, 'weather' => false, 'expected' => '夜间+8kg，重量费5+3*1=8元'],
        ];
        
        foreach ($testCases as $case) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $case['distance'],
                "2023-01-18 {$case['time']}",
                false,
                $case['weather'],
                $case['weight']
            );
            
            $basicAmount = ($result['freight'] + $result['distance_price']) / 100;
            $weightAmount = $result['weight_price'] / 100;
            $totalAmount = ($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100;
            
            $this->line("重量：{$case['weight']}kg | 时间：{$case['time']} | 基础：{$basicAmount}元 | 重量费：{$weightAmount}元 | 总计：{$totalAmount}元");
            $this->line("    预期：{$case['expected']}");
        }
    }
    
    /**
     * 测试边界条件
     */
    private function testBoundaryConditions($siteId)
    {
        $this->info('');
        $this->info('⚠️ 测试边界条件');
        $this->line('-----------------------------------------------');
        
        $boundaryTests = [
            ['distance' => 1000, 'time' => '08:00', 'weather' => false, 'desc' => '正好8点（夜间结束边界）'],
            ['distance' => 1000, 'time' => '22:00', 'weather' => false, 'desc' => '正好22点（夜间开始边界）'],
            ['distance' => 3000, 'time' => '10:00', 'weather' => false, 'desc' => '正好3公里（距离边界）'],
            ['distance' => 0, 'time' => '10:00', 'weather' => false, 'desc' => '0距离测试'],
        ];
        
        foreach ($boundaryTests as $test) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $test['distance'],
                "2023-01-18 {$test['time']}",
                false,
                $test['weather']
            );
            
            $totalAmount = ($result['freight'] + $result['distance_price']) / 100;
            $this->line("{$test['desc']} | 距离：{$test['distance']}米 | 时间：{$test['time']} | 金额：{$totalAmount}元");
        }
    }
    
    /**
     * 测试长距离配送
     */
    private function testLongDistanceDelivery($siteId)
    {
        $this->info('');
        $this->info('🚚 测试长距离配送');
        $this->line('-----------------------------------------------');
        
        $longDistanceTests = [
            ['distance' => 8000, 'time' => '10:00', 'weather' => false, 'desc' => '8公里正常配送'],
            ['distance' => 10000, 'time' => '23:00', 'weather' => false, 'desc' => '10公里夜间配送'],
            ['distance' => 15000, 'time' => '23:00', 'weather' => true, 'desc' => '15公里夜间恶劣天气'],
        ];
        
        foreach ($longDistanceTests as $test) {
            $result = Pricing::calculateFreight(
                Pricing::TYPE_USER,
                $siteId,
                $test['distance'],
                "2023-01-18 {$test['time']}",
                false,
                $test['weather']
            );
            
            $baseAmount = $result['freight'] / 100;
            $extraAmount = $result['distance_price'] / 100;
            $totalAmount = $baseAmount + $extraAmount;
            
            $this->line("{$test['desc']} | 基础费：{$baseAmount}元 | 超距费：{$extraAmount}元 | 总计：{$totalAmount}元");
        }
    }
    
    /**
     * 测试新版本雨骑士定价数据格式
     */
    private function testNewVersionFormat()
    {
        $this->info("=== 测试新版本数据格式 ===");
        
        // 新版本数据格式
        $newVersionData = [
            [
                "distance_range" => "0-3",
                "base_price" => 2,
                "extra_price" => 1,
                "weight_pricing" => [
                    [
                        "weight_range" => "0-3",
                        "base_price" => 0,
                        "extra_price" => 0
                    ],
                    [
                        "weight_range" => "3-5",
                        "base_price" => 2,
                        "extra_price" => 0
                    ],
                    [
                        "weight_range" => "5+",
                        "base_price" => 5,
                        "extra_price" => 1
                    ]
                ]
            ],
            [
                "distance_range" => "3+",
                "base_price" => 5,
                "extra_price" => 2,
                "weight_pricing" => [
                    [
                        "weight_range" => "0-3",
                        "base_price" => 0,
                        "extra_price" => 0
                    ],
                    [
                        "weight_range" => "3-5",
                        "base_price" => 2,
                        "extra_price" => 0
                    ],
                    [
                        "weight_range" => "5+",
                        "base_price" => 5,
                        "extra_price" => 1
                    ]
                ]
            ]
        ];
        
        // 创建测试定价策略
        $pricing = new \App\Models\Pricing();
        $pricing->use_yuqishi_pricing = 1;
        $pricing->normal_pricing = $newVersionData;
        $pricing->night_pricing = $newVersionData;
        $pricing->weather_normal_pricing = $newVersionData;
        $pricing->weather_night_pricing = $newVersionData;
        
        $testCases = [
            // 距离 0-3公里测试
            ['distance' => 500, 'weight' => 2, 'desc' => '0.5公里，2kg'],
            ['distance' => 1500, 'weight' => 2, 'desc' => '1.5公里，2kg'],
            ['distance' => 2500, 'weight' => 2, 'desc' => '2.5公里，2kg'],
            ['distance' => 3000, 'weight' => 2, 'desc' => '3公里，2kg'],
            
            // 距离 3公里以上测试
            ['distance' => 4000, 'weight' => 2, 'desc' => '4公里，2kg'],
            ['distance' => 5500, 'weight' => 2, 'desc' => '5.5公里，2kg'],
            
            // 重量测试
            ['distance' => 2000, 'weight' => 1, 'desc' => '2公里，1kg (0-3kg范围)'],
            ['distance' => 2000, 'weight' => 4, 'desc' => '2公里，4kg (3-5kg范围)'],
            ['distance' => 2000, 'weight' => 6, 'desc' => '2公里，6kg (5+kg范围)'],
            ['distance' => 2000, 'weight' => 8, 'desc' => '2公里，8kg (5+kg范围)'],
        ];
        
        // 使用反射调用私有方法
        $reflection = new \ReflectionClass(\App\Models\Pricing::class);
        $method = $reflection->getMethod('calculateYuqishiFreight');
        $method->setAccessible(true);
        
        foreach ($testCases as $case) {
            $result = $method->invoke(null, $pricing, $case['distance'], '2023-12-01 14:00', false, $case['weight']);
            
            $this->info("测试: {$case['desc']}");
            $this->info("  基础费用: " . ($result['freight'] / 100) . "元");
            $this->info("  距离费用: " . ($result['distance_price'] / 100) . "元");
            $this->info("  重量费用: " . ($result['weight_price'] / 100) . "元");
            $this->info("  总费用: " . (($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100) . "元");
            $this->info("");
        }
    }
    
    /**
     * 测试API兼容性
     */
    private function testAPICompatibility()
    {
        $this->info("=== 测试API兼容性 ===");
        
        // 模拟API请求参数
        $apiParams = [
            "type" => 1,
            "coupon_id" => 0,
            "gratuity" => 2.0,
            "appointment_time" => "2023-12-01 14:30|2023-12-01 15:00",
            "is_bad_weather" => true, // 恶劣天气
            "goods_info" => [
                "price" => 50.0,
                "is_protect_price" => false,
                "weight" => 6.5, // 重量6.5kg
                "desc" => "测试商品",
                "imgs" => [],
                "goods_category_id" => 14,
                "category_id" => 0,
                "volume" => ""
            ],
            "start_point" => [
                "mode" => 2, // 就近
            ],
            "end_point" => [
                "address_id" => 1,
            ]
        ];
        
        try {
            // 测试预下单服务
            $service = new \App\Services\O2oErrandOrderService();
            
            $this->info("测试参数：");
            $this->info("- 恶劣天气：" . ($apiParams['is_bad_weather'] ? '是' : '否'));
            $this->info("- 商品重量：{$apiParams['goods_info']['weight']}kg");
            $this->info("- 预约时间：{$apiParams['appointment_time']}");
            $this->info("");
            
            // 使用反射调用私有方法进行测试
            $reflection = new \ReflectionClass($service);
            
            // 测试prepareGoodsInfo方法
            $prepareGoodsInfoMethod = $reflection->getMethod('prepareGoodsInfo');
            $prepareGoodsInfoMethod->setAccessible(true);
            $goodsInfo = $prepareGoodsInfoMethod->invoke($service, $apiParams, []);
            
            $this->info("✅ 商品信息处理测试通过");
            $this->info("- 重量字段：" . (isset($goodsInfo['weight']) ? $goodsInfo['weight'] . 'kg' : '未设置'));
            $this->info("");
            
            // 测试价格计算（需要创建测试站点）
            $siteId = 1; // 假设站点ID为1
            
            // 创建测试定价数据
            $this->createTestPricingData($siteId);
            
                         // 测试无重量场景的API调用
             $this->info("测试无重量场景：");
             $apiParamsNoWeight = $apiParams;
             unset($apiParamsNoWeight['goods_info']['weight']); // 移除重量参数
             
             $goodsInfoNoWeight = $prepareGoodsInfoMethod->invoke($service, $apiParamsNoWeight, []);
             $this->info("- 无重量字段：" . (isset($goodsInfoNoWeight['weight']) ? $goodsInfoNoWeight['weight'] . 'kg' : '未设置，默认为0'));
             $this->info("");
             
             $this->info("✅ API兼容性测试完成");
             $this->info("- 新版本雨骑士定价策略已正确集成到API中");
             $this->info("- 支持恶劣天气和重量计费参数");
             $this->info("- 正确处理重量参数可选的场景");
             
         } catch (\Exception $e) {
             $this->error("❌ API兼容性测试失败：" . $e->getMessage());
             $this->error("文件：" . $e->getFile() . ":" . $e->getLine());
                  }
     }
     
     /**
      * 测试无重量参数场景
      */
     private function testNoWeightScenario()
     {
         $this->info("=== 测试无重量参数场景 ===");
         $this->info("模拟用户刚选择起点终点，还未填写物品重量的情况");
         $this->info("");
         
         $siteId = 1;
         
         // 创建测试定价数据
         $this->createTestPricingData($siteId);
         
         $testCases = [
             ['distance' => 1500, 'time' => '10:30', 'weather' => false, 'weight' => 0, 'desc' => '正常时间，无重量'],
             ['distance' => 1500, 'time' => '23:30', 'weather' => false, 'weight' => null, 'desc' => '夜间时间，重量为null'],
             ['distance' => 1500, 'time' => '10:30', 'weather' => true, 'weight' => 0, 'desc' => '恶劣天气，无重量'],
             ['distance' => 4000, 'time' => '23:30', 'weather' => true, 'weight' => 0, 'desc' => '夜间+恶劣天气+长距离，无重量'],
         ];
         
         foreach ($testCases as $case) {
             $result = \App\Models\Pricing::calculateFreight(
                 \App\Models\Pricing::TYPE_USER,
                 $siteId,
                 $case['distance'],
                 "2023-12-01 {$case['time']}",
                 false,
                 $case['weather'],
                 $case['weight'] ?? 0
             );
             
             $basicAmount = ($result['freight'] + $result['distance_price']) / 100;
             $weightAmount = $result['weight_price'] / 100;
             $totalAmount = ($result['freight'] + $result['distance_price'] + $result['weight_price']) / 100;
             
             $this->info("测试: {$case['desc']}");
             $this->info("  距离: {$case['distance']}米");
             $this->info("  重量: " . ($case['weight'] === null ? 'null' : $case['weight'] . 'kg'));
             $this->info("  基础费: {$basicAmount}元");
             $this->info("  重量费: {$weightAmount}元 (应该为0)");
             $this->info("  总费用: {$totalAmount}元");
             
             // 验证重量费用为0
             if ($weightAmount == 0) {
                 $this->info("  ✅ 重量费用正确为0");
             } else {
                 $this->error("  ❌ 重量费用应该为0，实际为{$weightAmount}元");
             }
             $this->info("");
         }
         
         $this->info("✅ 无重量参数场景测试完成");
         $this->info("系统能正确处理未填写重量的情况，只计算基础运费和距离费");
     }
     
     /**
      * 测试恶劣天气加收费用计算
      */
     private function testWeatherPriceCalculation()
     {
         $this->info("=== 测试恶劣天气加收费用计算 ===");
         $this->info("验证weather_price = 恶劣天气总价 - 正常天气总价");
         $this->info("");
         
         $siteId = 1;
         
         // 创建测试定价数据
         $this->createTestPricingData($siteId);
         
         $testCases = [
             ['distance' => 1500, 'time' => '10:30', 'weight' => 4, 'desc' => '正常时间，4kg'],
             ['distance' => 1500, 'time' => '23:30', 'weight' => 4, 'desc' => '夜间时间，4kg'],
             ['distance' => 4000, 'time' => '10:30', 'weight' => 6, 'desc' => '正常时间，长距离，6kg'],
             ['distance' => 4000, 'time' => '23:30', 'weight' => 6, 'desc' => '夜间时间，长距离，6kg'],
         ];
         
         foreach ($testCases as $case) {
             $this->info("测试场景: {$case['desc']}");
             $this->info("距离: {$case['distance']}米, 重量: {$case['weight']}kg");
             
             // 计算正常天气价格
             $normalResult = \App\Models\Pricing::calculateFreight(
                 \App\Models\Pricing::TYPE_USER,
                 $siteId,
                 $case['distance'],
                 "2023-12-01 {$case['time']}",
                 false,
                 false, // 正常天气
                 $case['weight']
             );
             
             // 计算恶劣天气价格
             $badWeatherResult = \App\Models\Pricing::calculateFreight(
                 \App\Models\Pricing::TYPE_USER,
                 $siteId,
                 $case['distance'],
                 "2023-12-01 {$case['time']}",
                 false,
                 true, // 恶劣天气
                 $case['weight']
             );
             
             $normalTotal = ($normalResult['freight'] + $normalResult['distance_price'] + $normalResult['weight_price']) / 100;
             $badWeatherTotal = ($badWeatherResult['freight'] + $badWeatherResult['distance_price'] + $badWeatherResult['weight_price']) / 100;
             $weatherPrice = $badWeatherResult['weather_price'] / 100;
             
             $this->info("  正常天气总价: {$normalTotal}元");
             $this->info("  恶劣天气总价: {$badWeatherTotal}元");
             $this->info("  天气加收费用: {$weatherPrice}元");
             
             // 验证计算逻辑
             $expectedWeatherPrice = $badWeatherTotal - $normalTotal;
             if (abs($weatherPrice - $expectedWeatherPrice) < 0.01) {
                 $this->info("  ✅ 天气加收费用计算正确");
             } else {
                 $this->error("  ❌ 天气加收费用计算错误，期望: {$expectedWeatherPrice}元，实际: {$weatherPrice}元");
             }
             
             // 验证恶劣天气价格应该大于等于正常天气价格
             if ($badWeatherTotal >= $normalTotal) {
                 $this->info("  ✅ 恶劣天气价格大于等于正常天气价格");
             } else {
                 $this->error("  ❌ 恶劣天气价格应该大于等于正常天气价格");
             }
             
             $this->info("");
         }
         
         $this->info("✅ 恶劣天气加收费用计算测试完成");
     }
 }  