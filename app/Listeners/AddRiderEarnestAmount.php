<?php

namespace App\Listeners;

use App\Events\EarnestRechargeOrderPaid;
use App\Events\RechargeOrderPaid;
use App\Models\EarnestRechargeOrder;
use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use App\Models\UserAccountFlow;

class AddRiderEarnestAmount
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param object $event
     * @return void
     */
    public function handle(EarnestRechargeOrderPaid $event)
    {
        $order = $event->order;

        $account = RiderAccount::query()->where('user_id', $order->user_id)->where('account_type', 2)->first();

        $account->add($order->order_amount, RiderAccountFlow::BUSINESS_TYPE_EARNEST_ORDER, $order->order_no, '缴纳保证金');
    }
}
