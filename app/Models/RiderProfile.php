<?php

namespace App\Models;

use App\Models\DataModel\Option;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class RiderProfile extends Model
{
    use HasFactory;

    protected $fillable = [
        'user_id',
        'transport_type',
        'edu_background',
        'address',
        'job',
        'marriage',
        'children_count',
        'health_cert_no',
        'health_cert_publisher',
        'health_cert_expire_date',
        'health_cert_cover',
        'health_cert_obverse',
        'emergency_contact',
        'emergency_mobile'
    ];

    protected $appends = [
        'transport_type_text'
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function getTransportTypeTextAttribute() {
        return Option::TypeTransportOptions[$this->transport_type] ?? '';
    }


}
