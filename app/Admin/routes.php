<?php

use App\Admin\Controllers\RegionController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Dcat\Admin\Admin;

Admin::routes();

Route::group([
    'prefix' => config('admin.route.prefix'),
    'namespace' => config('admin.route.namespace'),
    'middleware' => config('admin.route.middleware'),
], function (Router $router) {

    $router->get('/', 'HomeController@index');
    $router->resource('/users', 'UserController');
    $router->resource('/white_list', 'UserWhiteListController');
    $router->resource('/communities', 'CommunityController');
    $router->resource('/regions', 'RegionController');
    $router->resource('/articles', 'ArticleController');
    $router->resource('/configs', 'SystemConfigController');

    $router->resource('/shops', 'ShopController');
    $router->resource('/shop_spus', 'ShopSpuController');
    $router->resource('/shop_spu_cats', 'ShopSpuCatController');

    $router->resource('/orders', 'OrderController');
    $router->resource('/refunds', 'OrderRefundController');
    $router->resource('/versions', 'VersionController');
    // 派单
    Route::post('dispatch_order', [\App\Http\Controllers\Api\TaskController::class, 'dispatchOrder']);

    $router->group(['prefix' => "options"], function (Router $router) {
        $router->get('provinces', [RegionController::class, 'provinces']);
        $router->get('cities', [RegionController::class, 'cities']);
        $router->get('district', [RegionController::class, 'districts']);
    });
});
