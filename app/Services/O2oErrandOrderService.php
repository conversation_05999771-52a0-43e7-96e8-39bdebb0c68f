<?php

namespace App\Services;

use App\Exceptions\BusinessException;
use App\Jobs\CloseO2oErrandOrder;
use App\Jobs\DispatchOrderNotify;
use App\Jobs\RefundO2oErrandOrder;
use App\Models\Common;
use App\Models\Coupon;
use App\Models\O2oErrandOrder;
use App\Models\OpenApiAccount;
use App\Models\OrderEvaluateRec;
use App\Models\OrderPayRec;
use App\Models\OrderRemindLog;
use App\Models\Pricing;
use App\Models\Region;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use App\Models\SystemConfig;
use App\Models\UserAccount;
use App\Models\UserAccountFlow;
use App\Models\UserAddress;
use App\Models\UserCoupon;
use App\Services\Amap\BaiduService;
use App\Services\Amap\GaodeService;
use App\Services\WeatherService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Yansongda\Pay\Pay;
use App\Models\Merchant;

class O2oErrandOrderService
{
    public function preOrder($userId, array $params)
    {
        $type = $params["type"];
        $couponId = $params["coupon_id"];
        $gratuity = yuantofen($params["gratuity"]);
        $weight = $params["goods_info"]["weight"] ?? 0; // 商品重量
        
        // 预约时间
        if (strtoupper($params["appointment_time"]) == "NOW") {
            $appointmentTimes = [Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT), Carbon::now()->addMinutes(30)->format(Carbon::DEFAULT_TO_STRING_FORMAT)];
        } else {
            $appointmentTimes = explode("|", $params["appointment_time"]);
        }
        // 物品保费
        $protectPrice = 0;
        if ($params["goods_info"]["is_protect_price"]) { //计算保费 1/200
            $goodsPrice = yuantofen($params["goods_info"]["price"]);
            $protectPrice = intval(round($goodsPrice / 200));
        }
        try {
            // 起点
            $hasStartPoint = true;
            $pickupLat = 0;
            $pickupLng = 0;
            switch ($params["start_point"]["mode"]) {
                case 2: //就近
                    $hasStartPoint = false;
                    break;
                case 1: //指定
                    $pickupLng = $params["start_point"]["lng"];
                    $pickupLat = $params["start_point"]["lat"];
                    break;
                default:
                    $address = UserAddress::query()->where("user_id", $userId)->where("id", $params["start_point"]["address_id"])->first();
                    if (!$address) throw new \Exception("未找到该取件地址");
                    $pickupLng = $address->longitude;
                    $pickupLat = $address->latitude;
            }
            // 终点
            if ($params["end_point"]["address_id"] > 0) {
                $address = UserAddress::query()->where("user_id", $userId)->where("id", $params["end_point"]["address_id"])->first();
                if (!$address) throw new \Exception("未找到该收货地址");
                $deliverLng = $address->longitude;
                $deliverLat = $address->latitude;
            }else{
                $deliverLng = $params["end_point"]["lng"];
                $deliverLat = $params["end_point"]["lat"];
            }

            // 获取两点距离
            {
                //获取站点（帮取帮送-起点，帮买-终点）
                $point = ["lng" => $deliverLng, "lat" => $deliverLat];
                if (!in_array($type, [O2oErrandOrder::TYPE_BUY, O2oErrandOrder::TYPE_HELP])) {
                    $point = ["lng" => $pickupLng, "lat" => $pickupLat];
                }
                Log::info(json_encode($point));
                $site = app(CommonService::class)->getPointSite($point);
                if (!$site) throw new \Exception("该地址所在区域暂未开通服务");
            }
            if ($hasStartPoint) {
                $res = (new GaodeService())->bicycling(strval($pickupLng), strval($pickupLat), strval($deliverLng), strval($deliverLat));
            } else {
                $res = (new GaodeService())->bicycling(strval($site->longitude), strval($site->latitude), strval($deliverLng), strval($deliverLat));
            }
            $paths = $res["data"]['paths'] ?? [[]];
            $index = 0;
            if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
                $index = count($paths) - 1;
            }
            $distance = intval($paths[$index]["distance"] ?? 0);
            if ($distance > $site->farthest) throw new \Exception(sprintf('暂不支持超过%s公里的配送(订单距离:%s公里)', $site->farthest / 1000, $distance / 1000));

            $needSeconds = intval($paths[$index]["duration"] ?? 0);

            $result = [
                'distance' => $distance,
                'distance_text' => number_format($distance / 1000, 1, '.', '') . "公里",
                'normal' => [],
                'special' => [],
            ];

            $keyMap = ["freight" => "基础配送费", "distance_price" => "距离附加费", 'time_price' => "特殊时段费", 'weather_price' => "恶劣天气费", 'weight_price' => "重量附加费"];
            //获取费用

            if ($type == O2oErrandOrder::TYPE_HELP) {
                // 全能帮1元
                $couponAmount = 0;
                $total = 0;
                $priceRes = ["freight" => 100, "distance_price" => 0, 'time_price' => 0, 'weather_price' => 0, 'weight_price' => 0];
                foreach ($priceRes as $k => $v) {
                    $total += $v;
                    if ($v > 0) {
                        $result["normal"]["amount_info"][] = ['label' => $keyMap[$k], 'amount' => fentoyuan($v)];
                    }
                    if ($v > 0) {
                        $result["special"]["amount_info"][] = ['label' => $keyMap[$k], 'amount' => fentoyuan($v)];
                    }
                    $result["normal"]["total_amount"] = fentoyuan($total - $couponAmount + $gratuity + $protectPrice);
                }
                $result["special"]['freight'] = fentoyuan($priceRes['freight']);
                $result["normal"]["total_amount"] = fentoyuan($total - $couponAmount + $gratuity + $protectPrice);

                //通过距离算预计送达时间（普通配送是大概一个小时）
                $needSecondsForNormal = $this->getSeconds($needSeconds, false);
                $result["normal"]["delivery_time_text"] = "";
                $result["normal"]["spent_time"] = $needSecondsForNormal;
                $result["normal"]['estimated_delivery_time'] = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $appointmentTimes[0])->addSeconds($needSecondsForNormal)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                //优惠券
                $result["special"]["total_amount"] = fentoyuan($total - $couponAmount + $gratuity + $protectPrice);

                //通过距离算预计送达时间（专人直送45分钟）
                $needSecondsForSpecial = $this->getSeconds($needSeconds, true);
                $result["special"]["delivery_time_text"] = "快15分钟";
                $result["special"]["spent_time"] = $needSecondsForSpecial;
                $result["special"]['estimated_delivery_time'] = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $appointmentTimes[0])->addSeconds($needSecondsForSpecial)->format(Carbon::DEFAULT_TO_STRING_FORMAT);

                $result["normal"]['freight'] = fentoyuan($priceRes['freight']);

            } else {
                {
                    //普通 - 先判断天气，再计算价格
                    $isBadWeather = $this->checkWeatherCondition($deliverLng, $deliverLat);
                    $priceRes = Pricing::calculateFreight(Pricing::TYPE_USER, $site->id, $distance, $appointmentTimes[0], false, $isBadWeather, $weight);
                    $total = 0;
                    foreach ($priceRes as $k => $v) {
                        $total += $v;
                        if ($v > 0) {
                            $result["normal"]["amount_info"][] = ['label' => $keyMap[$k], 'amount' => fentoyuan($v)];
                        }
                    }
                    $result["normal"]['freight'] = fentoyuan($priceRes['freight']);

                    //验证优惠券
                    $couponAmount = 0;
                    if ($couponId > 0) {
                        $couponAmount = $this->validateCoupon($userId, $couponId, $total);
                    }
                    $result["normal"]["total_amount"] = fentoyuan($total - $couponAmount + $gratuity + $protectPrice);

                    //通过距离算预计送达时间（普通配送是大概一个小时）
                    $needSecondsForNormal = $this->getSeconds($needSeconds, false);
                    $result["normal"]["delivery_time_text"] = "";
                    $result["normal"]["spent_time"] = $needSecondsForNormal;
                    $result["normal"]['estimated_delivery_time'] = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $appointmentTimes[0])->addSeconds($needSecondsForNormal)->format(Carbon::DEFAULT_TO_STRING_FORMAT);

                    //专送 - 使用相同的天气判断结果
                    $priceRes = Pricing::calculateFreight(Pricing::TYPE_USER, $site->id, $distance, $appointmentTimes[0], true, $isBadWeather, $weight);
                    $total = 0;
                    foreach ($priceRes as $k => $v) {
                        $total += $v;
                        if ($v > 0) {
                            $result["special"]["amount_info"][] = ['label' => $keyMap[$k], 'amount' => fentoyuan($v)];
                        }
                    }
                    $result["special"]['freight'] = fentoyuan($priceRes['freight']);

                    //优惠券
                    $result["special"]["total_amount"] = fentoyuan($total - $couponAmount + $gratuity + $protectPrice);

                    //通过距离算预计送达时间（专人直送45分钟）
                    $needSecondsForSpecial = $this->getSeconds($needSeconds, true);
                    $result["special"]["delivery_time_text"] = "快15分钟";
                    $result["special"]["spent_time"] = $needSecondsForSpecial;
                    $result["special"]['estimated_delivery_time'] = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $appointmentTimes[0])->addSeconds($needSecondsForSpecial)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                }
            }
        } catch (\Exception $e) {
            throw $e;
        }
        if ($couponAmount > 0) {
            $result["normal"]["amount_info"][] = ['label' => "优惠券", 'amount' => "-" . fentoyuan($couponAmount)];
            $result["special"]["amount_info"][] = ['label' => "优惠券", 'amount' => "-" . fentoyuan($couponAmount)];
        }
        if ($gratuity > 0) {
            $result["normal"]["amount_info"][] = ['label' => "小费", 'amount' => fentoyuan($gratuity)];
            $result["special"]["amount_info"][] = ['label' => "小费", 'amount' => fentoyuan($gratuity)];
        }
        if ($protectPrice > 0) {
            $result["normal"]["amount_info"][] = ['label' => "保费", 'amount' => fentoyuan($protectPrice)];
            $result["special"]["amount_info"][] = ['label' => "保费", 'amount' => fentoyuan($protectPrice)];
        }

        return $result;
    }

    //预下单
    public function createOrder($userId, array $params, $userBalancePay = false, $merchantId = null)
    {
        try {
            // 准备订单数据
            $orderData = $this->prepareOrderData($userId, $params);

            // 检查支付条件
            if ($userBalancePay) {
                $this->validateBalancePayment($merchantId, $orderData);
            }

            // 创建订单记录并处理支付
            $order = $this->createOrderRecord($orderData, $userBalancePay, $merchantId);

            // 创建订单后的任务处理
            $this->handlePostOrderTasks($order, $userBalancePay);

            return $order;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 准备订单数据
     * @param int $userId 用户ID
     * @param array $params 订单参数
     * @return array 处理后的订单数据
     * @throws \Exception
     */
    protected function prepareOrderData($userId, array $params)
    {
        $orderData = [];

        // 1. 基础信息填充
        $orderData = $this->prepareBasicInfo($userId, $params, $orderData);

        // 2. 商品信息处理
        $orderData = $this->prepareGoodsInfo($params, $orderData);

        // 3. 起点信息完善
        $orderData = $this->prepareStartPointInfo($userId, $params, $orderData);

        // 4. 终点信息完善
        $orderData = $this->prepareEndPointInfo($userId, $params, $orderData);

        // 5. 获取两点距离和估算送达时间
        $orderData = $this->calculateDistanceAndTime($params, $orderData);

        // 6. 计算费用
        $orderData = $this->calculateOrderPrice($params, $orderData);

        // 7. 验证优惠券
        $orderData = $this->handleCouponDiscount($userId, $orderData);

        return $orderData;
    }

    /**
     * 准备订单基础信息
     */
    protected function prepareBasicInfo($userId, array $params, array $orderData)
    {
        if (strtoupper($params["appointment_time"]) == "NOW") {
            $timeArr = [Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT), Carbon::now()->addMinutes(30)->format(Carbon::DEFAULT_TO_STRING_FORMAT)];
        } else {
            $timeArr = explode("|", $params["appointment_time"]);
        }

        return array_merge($orderData, [
            "out_order_no" => $params["out_order_no"] ?? "",
            "app_key" => $params["app_key"] ?? "",
            "title" => $params["title"] ?? "",
            'type' => $params["type"],
            'user_id' => $userId,
            'order_status' => O2oErrandOrder::STATUS_WAITING_PAY,
            'coupon_id' => $params["coupon_id"],
            "coupon_amount" => 0,
            'gratuity' => yuantofen($params["gratuity"]),
            'remark' => $params["remark"],
            'hide_address' => $params["hide_address"],
            'is_special' => $params["is_special"],
            'need_incubator' => $params["need_incubator"],
            'appointment_start_time' => $timeArr[0],
            'appointment_end_time' => $timeArr[1],
            'create_time' => Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ]);
    }

    /**
     * 准备商品信息
     */
    protected function prepareGoodsInfo(array $params, array $orderData)
    {
        $params["goods_info"]["price"] = yuantofen($params["goods_info"]["price"]);
        $protectPrice = 0;
        if ($params["goods_info"]["is_protect_price"]) { //计算保费 1/200
            $protectPrice = intval(round($params["goods_info"]["price"] / 200));
        }

        return array_merge($orderData, [
            'goods_desc' => $params["goods_info"]["desc"],
            'goods_imgs' => $params["goods_info"]["imgs"],
            'goods_price' => $params["goods_info"]["price"],
            'goods_protected_price' => $protectPrice,
            'goods_category_id' => $params["goods_info"]["goods_category_id"],
            'category_id' => $params["goods_info"]["category_id"],
            'weight' => $params["goods_info"]["weight"],
            'volume' => $params["goods_info"]["volume"]
        ]);
    }

    /**
     * 准备起点信息
     */
    protected function prepareStartPointInfo($userId, array $params, array $orderData)
    {
        $hasStartPoint = true;
        switch ($params["start_point"]["mode"]) {
            case 2: //就近
                $hasStartPoint = false;
                break;
            case 1: //指定
                $areas = explode("-", $params["start_point"]["area"]);
                $region = Region::query()->where('level', Common::REGION_LEVEL_DISTRICT)->where("name", $areas[2])->first();
                $orderData = array_merge($orderData, [
                    'pickup_address' => implode(" ", [$params["start_point"]["area"], $params["start_point"]["address"]]),
                    'pickup_lng' => $params["start_point"]["lng"],
                    'pickup_lat' => $params["start_point"]["lat"],
                    'pickup_region_id' => $region->id ?? 0,
                ]);
                break;
            default:
                $address = UserAddress::query()->where("user_id", $orderData["user_id"])->where("id", $params["start_point"]["address_id"])->first();
                if (!$address) throw new \Exception("未找到该取件地址");
                $region = Region::query()->where('level', Common::REGION_LEVEL_DISTRICT)->where("name", $address->county)->first();
                $orderData = array_merge($orderData, [
                    "pickup_address_id" => $address->id,
                    'pickup_name' => $address->name,
                    'pickup_address' => implode("", [$address->province, $address->city, $address->county, $address->address_detail, $address->address_remark]),
                    'pickup_phone' => $address->tel,
                    'pickup_lng' => $address->longitude,
                    'pickup_lat' => $address->latitude,
                    'pickup_region_id' => $region->id ?? 0,
                    'pickup_code' => $params["start_point"]["pickup_code"] ? O2oErrandOrder::generateCode() : "",
                    'pickup_code_mode' => $params["start_point"]["pickup_code"] ? $params["start_point"]["pickup_code_mode"] : 0,
                ]);
        }
        $orderData['hasStartPoint'] = $hasStartPoint;
        return $orderData;
    }

    /**
     * 准备终点信息
     */
    protected function prepareEndPointInfo($userId, array $params, array $orderData)
    {
        $address = UserAddress::query()->where("user_id", $orderData["user_id"])->where("id", $params["end_point"]["address_id"])->first();
        if (!$address) throw new \Exception("未找到该收货地址");
        $region = Region::query()->where('level', Common::REGION_LEVEL_DISTRICT)->where("name", $address->county)->first();

        return array_merge($orderData, [
            "deliver_address_id" => $address->id,
            'deliver_name' => $address->name,
            'deliver_address' => implode("", [$address->province, $address->city, $address->county, $address->address_detail, $address->address_remark]),
            'deliver_phone' => $address->tel,
            'deliver_lng' => $address->longitude,
            'deliver_lat' => $address->latitude,
            'deliver_region_id' => $region->id ?? 0,
            'receive_code' => $params["end_point"]["receive_code"] ? O2oErrandOrder::generateCode() : "",
            'receive_code_mode' => $params["end_point"]["receive_code"] ? $params["end_point"]["receive_code_mode"] : 0,
        ]);
    }

    /**
     * 计算配送距离及预计送达时间
     */
    protected function calculateDistanceAndTime(array $params, array $orderData)
    {
        // 获取站点
        $point = ["lng" => $orderData['deliver_lng'], "lat" => $orderData['deliver_lat']];
        if (!in_array($orderData["type"], [O2oErrandOrder::TYPE_BUY, O2oErrandOrder::TYPE_HELP])) {
            $point = ["lng" => $orderData['pickup_lng'], "lat" => $orderData['pickup_lat']];
        }
        $site = app(CommonService::class)->getPointSite($point);
        if (!$site) throw new \Exception("不在配送范围内");
        $orderData["site_id"] = $site->id;

        // 计算两点距离
        $hasStartPoint = $orderData['hasStartPoint'] ?? true;
        if ($hasStartPoint) {
            $res = (new GaodeService())->bicycling(strval($orderData["pickup_lng"]), strval($orderData["pickup_lat"]), strval($orderData["deliver_lng"]), strval($orderData["deliver_lat"]));
        } else {
            $res = (new GaodeService())->bicycling(strval($site->longitude), strval($site->latitude), strval($orderData["deliver_lng"]), strval($orderData["deliver_lat"]));
        }

        $paths = $res["data"]['paths'] ?? [[]];

        $index = 0;
        if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
            $index = count($paths) - 1;
        }
        $orderData["distance"] = intval($paths[$index]["distance"] ?? 0);
        if ($orderData["distance"] > $site->farthest) throw new \Exception("配送距离超出限制");

        // 计算预计送达时间
        $needSeconds = intval($paths[$index]["duration"] ?? 0);
        $needSeconds = $this->getSeconds($needSeconds, $orderData["is_special"]);
        $orderData['estimated_delivery_time'] = Carbon::createFromFormat(Carbon::DEFAULT_TO_STRING_FORMAT, $orderData["appointment_start_time"])->addSeconds($needSeconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);

        unset($orderData['hasStartPoint']);
        return $orderData;
    }

    /**
     * 计算订单价格
     */
    protected function calculateOrderPrice(array $params, array $orderData)
    {
        if ($orderData["type"] == O2oErrandOrder::TYPE_HELP) {
            // 全能帮价格计算逻辑
            $priceRes = ["freight" => 100, "distance_price" => 0, 'time_price' => 0, 'weather_price' => 0, 'weight_price' => 0];
            $orderData = array_merge($orderData, $priceRes);
            $total = 0;
            foreach ($priceRes as $v) {
                $total += $v;
            }
            $orderData["order_amount"] = $total;
            $detail["part"] = $priceRes;
            $total = 0;
            foreach ($detail["part"] as $v) {
                $total += $v;
            }
            $orderData["reward_amount_part"] = $total;
            $detail["full"] = $priceRes;
            $total = 0;
            foreach ($detail["full"] as $v) {
                $total += $v;
            }
            $orderData["reward_amount_full"] = $total;
            $orderData["detail"] = $detail;
        } else {
            // 普通订单价格计算 - 先判断天气，再计算价格
            $isBadWeather = $this->checkWeatherCondition($orderData["deliver_lng"] ?? null, $orderData["deliver_lat"] ?? null);
            $priceRes = Pricing::calculateFreight(Pricing::TYPE_USER, $orderData["site_id"], $orderData["distance"], $orderData["appointment_start_time"], $orderData["is_special"], $isBadWeather, $orderData["weight"] ?? 0);
            $orderData = array_merge($orderData, $priceRes);
            $total = 0;
            foreach ($priceRes as $v) {
                $total += $v;
            }
            $orderData["order_amount"] = $total;
            $detail["part"] = Pricing::calculateFreight(Pricing::TYPE_RIDER, $orderData["site_id"], $orderData["distance"], $orderData["appointment_start_time"], false, $isBadWeather, $orderData["weight"] ?? 0);
            $total = 0;
            foreach ($detail["part"] as $v) {
                $total += $v;
            }
            $orderData["reward_amount_part"] = $total;
            $detail["full"] = Pricing::calculateFreight(Pricing::TYPE_RIDER, $orderData["site_id"], $orderData["distance"], $orderData["appointment_start_time"], true, $isBadWeather, $orderData["weight"] ?? 0);
            $total = 0;
            foreach ($detail["full"] as $v) {
                $total += $v;
            }
            $orderData["reward_amount_full"] = $total;
            $orderData["detail"] = $detail;
        }

        return $orderData;
    }

    /**
     * 处理优惠券折扣
     */
    protected function handleCouponDiscount($userId, array $orderData)
    {
        if ($orderData["coupon_id"] > 0) {
            $orderData["coupon_amount"] = $this->validateCoupon($userId, $orderData["coupon_id"], $orderData["order_amount"]);
        }
        $orderData["actual_amount"] = $orderData["order_amount"] - $orderData["coupon_amount"];

        return $orderData;
    }

    /**
     * 验证余额支付条件
     */
    protected function validateBalancePayment($merchantId, array $orderData)
    {
        // 获取商家信息并检查余额
        $merchant = Merchant::query()->find($merchantId ?? 0);
        if (!$merchant) {
            throw new \Exception("商家不存在");
        }

        $amount = $orderData["actual_amount"] + $orderData["gratuity"] + $orderData["goods_protected_price"];
        if ($amount > $merchant->balance) {
            throw new \Exception("商家余额不足");
        }

        return $merchant;
    }

    /**
     * 创建订单记录并处理支付
     */
    protected function createOrderRecord(array $orderData, $userBalancePay, $merchantId)
    {
        DB::beginTransaction();

        // 创建订单
        $order = O2oErrandOrder::query()->create($orderData);

        // 关联优惠券
        if ($order->coupon_id > 0) {
            UserCoupon::query()->where("id", $order->coupon_id)->update(["order_id" => $order->id]);
        }

        // 处理余额支付
        if ($userBalancePay) {
            $this->processBalancePayment($order, $merchantId);
        }

        DB::commit();
        return $order;
    }

    /**
     * 处理余额支付
     */
    protected function processBalancePayment($order, $merchantId)
    {
        // 计算订单总金额（分）
        $amountInFen = $order->actual_amount + $order->gratuity + $order->goods_protected_price;

        // 使用悲观锁和事务处理并发问题
        DB::beginTransaction();
        try {
            // 使用悲观锁锁定商家记录，避免并发更新
            $merchant = Merchant::query()->lockForUpdate()->find($merchantId);
            if (!$merchant) {
                throw new \Exception("商家不存在");
            }

            // 商家余额转换为分
            $balanceInFen = yuantofen($merchant->balance);

            // 再次验证余额是否足够（防止锁之前金额变化）
            if ($amountInFen > $balanceInFen) {
                throw new \Exception("商家余额不足");
            }

            // 记录扣减前的余额（元）
            $beforeBalance = $merchant->balance;

            // 扣减商家余额（这里需要将分转换为元再扣减）
            $merchant->balance -= $amountInFen;
            $merchant->save();

            // 记录商家余额流水
            DB::table('merchant_account_logs')->insert([
                'merchant_id' => $merchant->id,
                'amount' => -$amountInFen, // 存储分为单位的金额
                'before_balance' => $beforeBalance,
                'after_balance' => $merchant->balance,
                'type' => 'order',
                'order_no' => $order->order_no,
                'remark' => '订单支付',
                'created_at' => now(),
                'updated_at' => now()
            ]);

            $updateData = [
                'paid_at' => Carbon::now(),
                'pay_method' => Common::PAY_METHOD_BALANCE,
                'payment_no' => $order->order_no . $order->id,
                "order_status" => O2oErrandOrder::STATUS_PAID,
            ];

            // 如果预约时间已过，更新预约时间
            if ($order->appointment_start_time->timestamp <= Carbon::now()->timestamp) {
                $seconds = Carbon::now()->timestamp - $order->appointment_start_time->timestamp + 10;
                $updateData['appointment_start_time'] = $order->appointment_start_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                $updateData['appointment_end_time'] = $order->appointment_end_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                $updateData['estimated_delivery_time'] = $order->estimated_delivery_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
            }

            $order->update($updateData);

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * 处理订单创建后的任务
     */
    protected function handlePostOrderTasks($order, $userBalancePay)
    {
        if ($userBalancePay) {
            if (app()->environment() == 'production') {
                ding()->text(sprintf("新订单提醒 - 订单号：%s，请及时处理", $order->order_no));
                dispatch(new DispatchOrderNotify($order));
                // 发送订单通知到订单频道
                $this->sendOrderNotification($order);
            }
            # 无骑手接单 超时关闭订单 30分钟
            dispatch(new RefundO2oErrandOrder($order->order_no, config('app.order_refund_ttl')));
        } else {
            dispatch(new CloseO2oErrandOrder($order->order_no, config('app.order_ttl')));
        }
    }

    /**
     * 发送订单通知到订单频道
     * @param O2oErrandOrder $order 订单对象
     * @return bool
     */
    protected function sendOrderNotification($order)
    {
        try {
            $wukongService = app(WukongIMService::class);
            
            // 订单频道ID
            $channelId = 'order_notification';
            
            // 准备订单数据
            $orderData = [
                'order_id' => $order->id,
                'order_no' => $order->order_no,
                'type' => $order->type,
                'type_text' => isset($order::TypeMap[$order->type]) ? $order::TypeMap[$order->type] : '未知类型',
                'status' => 'created',
                'amount' => fentoyuan($order->actual_amount),
                'freight' => fentoyuan($order->freight),
                'user_id' => $order->user_id,
                'pickup_address' => $order->pickup_address,
                'deliver_address' => $order->deliver_address,
                'distance' => $order->distance,
                'distance_text' => number_format($order->distance / 1000, 1, '.', '') . "公里",
                'created_at' => $order->create_time
            ];
            
            // 消息内容
            $message = sprintf(
                "新订单通知: 订单号 %s, 类型 %s, 金额 ¥%.2f, 距离 %s", 
                $order->order_no,
                isset($order::TypeMap[$order->type]) ? $order::TypeMap[$order->type] : '未知类型',
                fentoyuan($order->actual_amount),
                number_format($order->distance / 1000, 1, '.', '') . "公里"
            );
            
            // 发送通知
            $result = $wukongService->sendOrderNotification($channelId, $orderData, $message);
            
            return $result;
        } catch (\Exception $e) {
            \Log::error('发送订单通知失败: ' . $e->getMessage(), [
                'order_no' => $order->order_no,
                'exception' => $e
            ]);
            return false;
        }
    }

    public function createOrderFromOpen($userId, $outOrderNo, $params, $appKey)
    {
        $order = O2oErrandOrder::query()->where('out_order_no', $outOrderNo)->first();
        if ($order) {
            throw new BusinessException('订单已存在,无法重复创建');
        }

        $order = new O2oErrandOrder();
        $order->out_order_no = $outOrderNo;
        $order->type = O2oErrandOrder::TYPE_SEND; // 开放接口仅开通帮送服务
        $order->user_id = $userId;
        $order->rider_id = 0;
        $order->order_status = O2oErrandOrder::STATUS_PAID;
        $order->coupon_id = 0;
        $order->gratuity = 0;
        $order->hide_address = 0;
        $order->is_special = 0;
        $order->need_incubator = 0;
        $order->reward = 0;

        if (isset($params['pickup_address']) && $params['pickup_address']) {
            // 查询收获地址是否保存到我的收货地址
            $pickupAddress = UserAddress::query()->where("tel", $params['pickup_phone'])
                ->where("province", $params['pickup_province'])
                ->where("city", $params['pickup_city'])
                ->where("county", $params['pickup_county'])
                ->where("address_detail", $params['pickup_address'])
                ->where('address_remark', $params['pickup_address_remark'])
                ->where('user_id', $userId)
                ->first();
            if (!$pickupAddress) {
                $pickupAddress = new UserAddress();
                $pickupAddress->name = $params['pickup_name'];
                $pickupAddress->user_id = $userId;
                $pickupAddress->province = $params['pickup_province'];
                $pickupAddress->city = $params['pickup_city'];
                $pickupAddress->county = $params['pickup_county'];
                $pickupAddress->address_detail = $params['pickup_address'];
                $pickupAddress->address_remark = $params['pickup_address_remark'];
                $pickupAddress->tel = $params['pickup_phone'];
                $pickupAddress->latitude = $params['pickup_lat'];
                $pickupAddress->longitude = $params['pickup_lng'];
                $pickupAddress->save();
            }
            $order->pickup_address = $pickupAddress->full_address;
            $order->pickup_name = $pickupAddress->name;
            $order->pickup_phone = $pickupAddress->tel;
            $order->pickup_address_id = $pickupAddress->id;
            $region = Region::query()->where('level', Common::REGION_LEVEL_DISTRICT)->where("name", $pickupAddress->county)->first();
            $order->pickup_region_id = $region->id;
            $order->pickup_lng = $pickupAddress->longitude;
            $order->pickup_lat = $pickupAddress->latitude;
        } else {
            $openApiAccount = OpenApiAccount::query()->where('app_key', $appKey)->with('pickupAddress')->first();

            // 发货地址处理
            $order->pickup_address = $openApiAccount->pickupAddress->full_address;
            $order->pickup_name = $openApiAccount->pickupAddress->name;
            $order->pickup_phone = $openApiAccount->pickupAddress->tel;
            $order->pickup_address_id = $openApiAccount->address_id;
            $region = Region::query()->where('level', Common::REGION_LEVEL_DISTRICT)->where("name", $openApiAccount->pickupAddress->county)->first();
            $order->pickup_region_id = $region->id;
            $order->pickup_lng = $openApiAccount->pickupAddress->longitude;
            $order->pickup_lat = $openApiAccount->pickupAddress->latitude;
        }

        $order->create_time = Carbon::now();

        // 收货地址处理
        $order->deliver_name = $params['deliver_name'];
        $order->deliver_phone = $params['deliver_phone'];

        // 查询收获地址是否保存到我的收货地址
        $address = UserAddress::query()->where("tel", $params['deliver_phone'])
            ->where("province", $params['province'])
            ->where("city", $params['city'])
            ->where("county", $params['county'])
            ->where("address_detail", $params['address'])
            ->where('address_remark', $params['address_remark'])
            ->where('user_id', $userId)
            ->first();
        if (!$address) {
            $address = new UserAddress();
            $address->name = $params['deliver_name'];
            $address->user_id = $userId;
            $address->province = $params['province'];
            $address->city = $params['city'];
            $address->county = $params['county'];
            $address->address_detail = $params['address'];
            $address->address_remark = $params['address_remark'];
            $address->tel = $params['deliver_phone'];
            $address->latitude = $params['lat'];
            $address->longitude = $params['lng'];
            $address->save();
        }

        $order->deliver_address_id = $address->id;
        $order->deliver_address = $params['province'] . $params['city'] . $params['county'] . $params['address'] . $params['address_remark'];
        $region = Region::query()->where('level', Common::REGION_LEVEL_DISTRICT)->where("name", $params['county'])->first();
        $order->deliver_region_id = $region->id;
        $order->deliver_lng = $params['lng'];
        $order->deliver_lat = $params['lat'];
        $order->goods_imgs = [];
        $order->order_amount = yuantofen($params['amount']);
        $order->actual_amount = yuantofen($params['amount']);
        $order->coupon_amount = 0;
        $order->freight = yuantofen($params['amount']);
        $order->distance_price = 0;
        $order->time_price = 0;
        $order->weather_price = 0;
        $order->weight = 0;
        $order->reward_amount_full = 0;
        $order->reward_amount_part = 0;
        $order->pay_method = 5; // 外部订单
        $order->payment_no = $outOrderNo;
        $order->refund_amount = 0;
        $order->refund_status = 0;
        $order->paid_at = Carbon::now();
        $order->reward_amount = 0;
        $order->app_key = $appKey;
        
        $order->detail = [
            'part' => [
                'freight' => yuantofen($params['amount']),
                'distance_price' => 0,
                'time_price' => 0,
                'weather_price' => 0,
                'weight' => 0,
            ],
            'full' => [
                'freight' => yuantofen($params['amount']),
                'distance_price' => 0,
                'time_price' => 0,
                'weather_price' => 0,
                'weight' => 0,
            ]
        ];
        $res = (new GaodeService())->bicycling(strval($order->pickup_lng), strval($order->pickup_lat), strval($order->deliver_lng), strval($order->deliver_lat));

        $paths = $res["data"]['paths'] ?? [[]];
        $index = 0;
        if (SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) == 2) {
            $index = count($paths) - 1;
        }
        $distance = intval($paths[$index]["distance"] ?? 0);
        $needSeconds = intval($paths[$index]["duration"] ?? 0);
        // 距离处理
        $order->distance = $distance;
        $needSecondsForNormal = $this->getSeconds($needSeconds, false);
        if ($params['time_mode'] == 1) {
            // 立即送达
            $order->estimated_delivery_time = Carbon::now()->addSeconds($needSecondsForNormal)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
            $order->appointment_start_time = $order->estimated_delivery_time;
            $order->appointment_end_time = Carbon::parse($order->estimated_delivery_time)->addMinutes(30)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
        } else {
            $order->estimated_delivery_time = Carbon::parse($params['start_time'])->addSeconds($needSecondsForNormal)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
            $order->appointment_start_time = $order->estimated_delivery_time;
            $order->appointment_end_time = Carbon::parse($order->estimated_delivery_time)->addMinutes(30)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
        }
        // 站点处理
        $point = ["lng" => $order->pickup_lng, "lat" => $order->pickup_lat];
        $site = app(CommonService::class)->getPointSite($point);
        if (!$site) throw new \Exception("该地址所在区域暂未开通服务");
        $order->site_id = $site->id;
        $order->save();

        if (app()->environment() == 'production') {
            ding()->text(sprintf("新订单提醒 - 订单号：%s，请及时处理", $order->order_no));
            dispatch(new DispatchOrderNotify($order));
        }

        return $order;

    }

    public function getSeconds($needSeconds, $isSpecial = false)
    {
        // 普通配送是大概一个小时 专人直送45分钟
        $minutes = $isSpecial ? 30 : 30;
        if ($needSeconds > $minutes * 60) {
            $needSeconds += 60 * ((SystemConfig::getCacheConfigValue(SystemConfig::PlatformPT, SystemConfig::PARAM_ORDER_REDUNDANT_TIME) ?? 0) + ($isSpecial ? 0 : 15));
        } else {
            $needSeconds = $minutes * 60;
        }
        return $needSeconds;
    }

    /**
     * 验证优惠券
     * @throws \Exception
     */
    private function validateCoupon($userId, $couponId, $orderAmount)
    {
        $userCoupon = UserCoupon::query()->with(['coupon'])->find($couponId);
        if (!$userCoupon) {
            throw new \Exception("未找到该优惠券");
        }
        if ($userCoupon->user_id != $userId) {
            throw new \Exception("未找到该优惠券");
        }
        if ($userCoupon->order_id > 0) {
            throw new \Exception("该优惠券已使用");
        }
        if ($userCoupon->start_time > Carbon::now() || $userCoupon->end_time <= Carbon::now()) {
            throw new \Exception("该优惠券未生效或已过期");
        }
        if ($orderAmount < yuantofen($userCoupon->coupon->start_price)) {
            throw new \Exception("未达到优惠券门槛金额");
        }
        $couponAmount = 0;
        switch ($userCoupon->coupon->type) {
            case Coupon::TYPE_MANJIAN:
                $couponAmount = yuantofen($userCoupon->coupon->discount_price);
                break;
            case Coupon::TYPE_ZHEKOU:
                $couponAmount = $orderAmount * (100 - $userCoupon->coupon->discount_price) / 100;
                break;
        }
        return $couponAmount;
    }

    /**
     * 取消订单
     * @param $orderNo
     * @param null $userId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws BusinessException
     */
    public function cancelOrder($orderNo, $userId = null)
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) throw new BusinessException("该订单不存在");

        if ($userId && $order->user_id != $userId) throw new BusinessException("该订单不存在");

        if ($order->order_status == O2oErrandOrder::STATUS_CANCEL) throw new BusinessException("该订单已取消");

        if ($order->paid_at) throw new BusinessException("该订单已支付");

        try {
            DB::beginTransaction();
            $order->update(["order_status" => O2oErrandOrder::STATUS_CANCEL]);
            UserCoupon::query()->where('order_id', $order->id)->update(["order_id" => 0]);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        return $order;
    }

    /**
     * 已支付订单取消
     * @param $orderNo
     * @param $refundReason
     * @param null $userId
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws BusinessException
     */
    public function refundOrder($orderNo, $refundReason, $userId = null, $isPlat = false)
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) throw new \Exception("该订单不存在");
        if ($userId && $order->user_id != $userId) throw new BusinessException("该订单不存在");
        if (!in_array($order->refund_status, [O2oErrandOrder::REFUND_STATUS_INIT, O2oErrandOrder::REFUND_STATUS_FAIL])) {
            throw new \Exception("该订单已发起退款");
        }
        if ($isPlat) {
            $refundAmount = $order->actual_amount + $order->gratuity + $order->goods_protected_price;
        } else {
            switch ($order->order_status) {
                case O2oErrandOrder::STATUS_PAID: //待接单
                    $refundAmount = $order->actual_amount + $order->gratuity + $order->goods_protected_price;
                    break;
                case O2oErrandOrder::STATUS_PICKUP: //已接单
                    $refundAmount = round($order->actual_amount) + $order->gratuity + $order->goods_protected_price;
                    break;
                case O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT: //已到达取货点
                    $refundAmount = round($order->actual_amount) + $order->gratuity + $order->goods_protected_price;
                    break;
                case O2oErrandOrder::STATUS_DELIVERY: //配送中
                    $refundAmount = round($order->actual_amount) + $order->gratuity + $order->goods_protected_price;
                    break;
                default:
                    throw new \Exception("该订单不能取消");
            }
        }

        $order->update(["close_reason" => $refundReason, 'refund_amount' => $refundAmount, 'refund_status' => O2oErrandOrder::REFUND_STATUS_ING]);
        $this->refundToUser($order);
        return $order;
    }

    //退款给用户
    private function refundToUser(O2oErrandOrder $order)
    {
        if ($order->refund_status != O2oErrandOrder::REFUND_STATUS_ING) return;
        $refundAmount = $order->refund_amount;
        $payAmount = $order->actual_amount + $order->gratuity + $order->goods_protected_price;
        if (app()->environment() != 'production' && $order->pay_method != Common::PAY_METHOD_BALANCE) {
            $refundAmount = 1;
            $payAmount = 1;
        }
        Pay::config(config("pay"));

        $refundNo = "";
        $refundAt = Carbon::now();
        try {
            switch ($order->pay_method) {
                case Common::PAY_METHOD_ALIPAY:
                    $result = Pay::alipay()->refund([
                        'out_trade_no' => $order->order_no,
                        'refund_amount' => fentoyuan($refundAmount),
                        'out_request_no' => "T" . $order->order_no
                    ]);
                    if ($result->code != 10000) {
                        throw new \Exception($result->code . "-" . $result->msg . "(" . $result->sub_code . "-" . $result->sub_msg . ")");
                    }
                    $refundNo = $result->trade_no;
                    $refundAt = $result->gmt_refund_pay ?? Carbon::now();
                    break;
                case Common::PAY_METHOD_WECHAT:
                    $result = Pay::wechat()->refund([
                        'out_trade_no' => $order->order_no,
                        'out_refund_no' => "T" . $order->order_no,
                        'amount' => [
                            'refund' => $refundAmount,
                            'total' => $payAmount,
                            'currency' => 'CNY',
                        ],
                    ]);
                    if ($result->has("code")) {
                        throw new \Exception($result->code . "-" . $result->message);
                    }
                    $refundNo = $result->refund_id;
                    $refundAt = $result->success_time ?? Carbon::now();
                    break;
                case Common::PAY_METHOD_CLOUD_FLASH:
                    //TODO 云闪付退款
                    break;
                case Common::PAY_METHOD_BALANCE:
                    if (in_array($order->app_key, [O2oErrandOrder::APP_KEY_MYT, O2oErrandOrder::APP_KEY_WRC])) {
                        // 麦芽田订单退款给商家账号
                        $merchant = Merchant::query()->where('user_id', $order->user_id)->first();
                        if (!$merchant) {
                            throw new \Exception("未找到商家账号");
                        }
                        DB::transaction(function () use ($order, $merchant) {
                            $beforeBalance = $merchant->balance;
                            $merchant->balance += $order->refund_amount;
                            $merchant->save();

                            // 记录商家余额流水
                            DB::table('merchant_account_logs')->insert([
                                'merchant_id' => $merchant->id,
                                'amount' => $order->refund_amount,
                                'before_balance' => $beforeBalance,
                                'after_balance' => $merchant->balance,
                                'type' => 'refund',
                                'order_no' => $order->order_no,
                                'remark' => '订单退款',
                                'created_at' => now(),
                                'updated_at' => now()
                            ]);

                            $order->update([
                                "refund_status" => O2oErrandOrder::REFUND_STATUS_SUCCESS,
                                "refund_at" => Carbon::now(),
                                "refund_no" => ""
                            ]);
                        });
                    } else {
                        // 普通订单退款给用户账号
                        $account = UserAccount::query()->firstOrCreate(['user_id' => $order->user_id]);
                        DB::transaction(function () use ($order, $account) {
                            $account->add($order->refund_amount, UserAccountFlow::BUSINESS_TYPE_REFUND, $order->order_no, '订单退款');
                            $order->update([
                                "refund_status" => O2oErrandOrder::REFUND_STATUS_SUCCESS,
                                "refund_at" => Carbon::now(),
                                "refund_no" => ""
                            ]);
                        });
                    }
                    return;
            }
            $order->update(["refund_status" => O2oErrandOrder::REFUND_STATUS_SUCCESS, "refund_at" => $refundAt, "refund_no" => $refundNo]);
        } catch (\Exception $e) {
            $order->update(["refund_status" => O2oErrandOrder::REFUND_STATUS_FAIL]);
            Log::error("帮我*订单（{$order->id}）退款失败：" . json_encode(["code" => $e->getCode(), "message" => $e->getMessage(), "file" => $e->getFile(), "line" => $e->getLine()]));
        }

        $recs = OrderPayRec::query()->where("order_id", $order->id)->whereNotNull("paid_at")->whereNull("refund_at")->get();
        if ($recs) {
            foreach ($recs as $rec) {
                $this->antherRefundToUser($rec);
            }
        }
    }

    private function antherRefundToUser(OrderPayRec $rec)
    {
        if ($rec->refund_at) return;
        if (!$rec->paid_at) return;
        $refundAmount = $rec->amount;
        if (app()->environment() != 'production') {
            $refundAmount = 1;
        }
        Pay::config(config("pay"));

        $refundNo = "";
        $refundAt = Carbon::now();
        try {
            switch ($rec->pay_method) {
                case Common::PAY_METHOD_ALIPAY:
                    $result = Pay::alipay()->refund([
                        'out_trade_no' => $rec->order_no,
                        'refund_amount' => fentoyuan($refundAmount),
                        'out_request_no' => "T" . $rec->order_no
                    ]);
                    if ($result->code != 10000) {
                        throw new \Exception($result->code . "-" . $result->msg . "(" . $result->sub_code . "-" . $result->sub_msg . ")");
                    }
                    $refundNo = $result->trade_no;
                    $refundAt = $result->gmt_refund_pay ?? Carbon::now();
                    break;
                case Common::PAY_METHOD_WECHAT:
                    $result = Pay::wechat()->refund([
                        'out_trade_no' => $rec->order_no,
                        'out_refund_no' => "T" . $rec->order_no,
                        'amount' => [
                            'refund' => $refundAmount,
                            'total' => $refundAmount,
                            'currency' => 'CNY',
                        ],
                    ]);
                    if ($result->has("code")) {
                        throw new \Exception($result->code . "-" . $result->message);
                    }
                    $refundNo = $result->refund_id;
                    $refundAt = $result->success_time ?? Carbon::now();
                    break;
                case Common::PAY_METHOD_CLOUD_FLASH:
                    //TODO 云闪付退款
                    break;
                case Common::PAY_METHOD_BALANCE:
                    $account = UserAccount::query()->firstOrCreate(['user_id' => $rec->user_id]);
                    DB::transaction(function () use ($rec, $account) {
                        $account->add($rec->amount, UserAccountFlow::BUSINESS_TYPE_REFUND, $rec->order_no, OrderPayRec::TypeMap[$rec->type] . "退款");
                        $rec->update(["refund_at" => Carbon::now(), "refund_no" => ""]);
                    });
                    return;
            }
            $rec->update(["refund_at" => $refundAt, "refund_no" => $refundNo]);
        } catch (\Exception $e) {
            Log::error(OrderPayRec::TypeMap[$rec->type] . "订单（{$rec->id}）退款失败：" . json_encode(["code" => $e->getCode(), "message" => $e->getMessage(), "file" => $e->getFile(), "line" => $e->getLine()]));
        }
    }

    //加小费
    public function addGratuity($orderNo, $amount)
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if (!$order) throw new \Exception("该订单不存在");

        if ($order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            throw new \Exception("该订单已取消");
        }
        if ($order->order_status < O2oErrandOrder::STATUS_PAID || $order->order_status >= O2oErrandOrder::STATUS_FINISH) {
            throw new \Exception("该订单未支付或已完成");
        }

        return OrderPayRec::query()->create([
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'type' => OrderPayRec::TYPE_GRATUITY,
            'amount' => yuantofen($amount),
        ]);
    }

    /**
     * 催单
     * @param $orderNo
     * @return mixed
     * @throws \Exception
     */
    public function remind($orderNo)
    {
        $cacheKey = 'remind_order_' . $orderNo;
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();

        if (!$order) throw new \Exception("该订单不存在");

        if ($order->order_status == O2oErrandOrder::STATUS_CANCEL) {
            throw new \Exception("该订单已取消");
        }
        if ($order->order_status < O2oErrandOrder::STATUS_PICKUP || $order->order_status >= O2oErrandOrder::STATUS_FINISH) {
            throw new \Exception("该订单未接单或已完成");
        }

        // 10分钟内不能重复催单
        if (Cache::has($cacheKey)) throw new \Exception("您已催过单了，稍后再试");

        $log = OrderRemindLog::create([
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'rider_id' => $order->rider_id,
        ]);

        $message = "用户发起催单，请加紧处理 订单号：{$order->order_no} 订单类型 " . O2oErrandOrder::TypeMap[$order->type] . " 目的地：{$order->deliver_address} 收货人：{$order->deliver_name} {$order->deliver_phone}";

        app(WukongIMService::class)->sendSystemMessage($order->rider->user, $message, ['msg_type' => 2]);

        Cache::put($cacheKey, $orderNo, Carbon::now()->addMinutes(10));

        return $log;
    }

    /**
     * 验证取货码/收货码
     * @param $orderNo
     * @param $codeType
     * @param $code
     * @return bool
     * @throws \Exception
     */
    public function validateCode($orderNo, $codeType, $code): bool
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) throw new \Exception("该订单不存在");
        switch ($codeType) {
            case 1:
                if ($order->order_status != O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                    throw new \Exception("该订单状态不合法");
                }
                break;
            case 2:
                if ($order->order_status != O2oErrandOrder::STATUS_DELIVERY) {
                    throw new \Exception("该订单状态不合法");
                }
                break;
            default:
                throw new \Exception("码类型错误");
        }
        $codeMap = [
            1 => $order->pickup_code, //取货码
            2 => $order->receive_code, //收货码
        ];
        $nameMap = [
            1 => "取货码",
            2 => "收货码",
        ];
        if ($code != $codeMap[$codeType]) throw new \Exception($nameMap[$codeType] . "错误");

        return true;
    }

    /**
     * 骑手买到商品，修改价格上传凭证
     * @param $orderNo
     * @param $goodsPrice
     * @param array $imgs
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws \Exception
     */
    public function purchased($orderNo, $goodsPrice, $imgs = [])
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) throw new \Exception("该订单不存在");
        if ($order->order_status != O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
            throw new \Exception("该订单状态不合法");
        }
        $rec = OrderPayRec::query()->where('order_id', $order->id)->where("type", OrderPayRec::TYPE_GOODS)->first();
        try {
            $updateOrder = ["goods_price" => yuantofen($goodsPrice), "buy_imgs" => $imgs];
            if (!isset($order->detail["goods_price"])) {
                $detail = $order->detail;
                $detail['goods_price'] = $order->goods_price;
                $updateOrder["detail"] = $detail;
            }
            DB::beginTransaction();
            if ($rec) {
                if ($rec->paid_at) throw new \Exception("用户已支付商品费，不能重复操作");
                $rec->update(['amount' => yuantofen($goodsPrice)]);
            } else {
                $rec = OrderPayRec::query()->create([
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'type' => OrderPayRec::TYPE_GOODS,
                    'amount' => yuantofen($goodsPrice),
                ]);
                $updateOrder["order_status"] = O2oErrandOrder::STATUS_DELIVERY;
                $updateOrder["pickup_at"] = Carbon::now()->format(Carbon::DEFAULT_TO_STRING_FORMAT);
            }
            $order->update($updateOrder);
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
        return $order;
    }

    /**
     * 打赏
     * @param $orderNo
     * @param $amount
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws \Exception
     */
    public function reward($orderNo, $amount)
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
        if (!$order) throw new \Exception("该订单不存在");

        if ($order->order_status != O2oErrandOrder::STATUS_FINISH) throw new \Exception("请在订单完成后进行大赏");

        $rec = OrderPayRec::query()->where('order_id', $order->id)->where("type", OrderPayRec::TYPE_REWARD)->first();
        try {
            if ($rec) {
                if ($rec->paid_at) throw new \Exception("您已打赏过了");
                $rec->update(['amount' => yuantofen($amount)]);
            } else {
                $rec = OrderPayRec::query()->create([
                    'order_id' => $order->id,
                    'user_id' => $order->user_id,
                    'type' => OrderPayRec::TYPE_REWARD,
                    'amount' => yuantofen($amount),
                ]);
            }
        } catch (\Exception $e) {
            throw $e;
        }
        return $rec;
    }

    /**
     * 支付成功，累加到订单表对应字段里
     * @param OrderPayRec $rec
     */
    public function afterPaid(OrderPayRec $rec)
    {
        switch ($rec->type) {
            case OrderPayRec::TYPE_GRATUITY:
                O2oErrandOrder::query()->where("id", $rec->order_id)->increment("gratuity", $rec->amount);
                break;
            case OrderPayRec::TYPE_REWARD:
                O2oErrandOrder::query()->where("id", $rec->order_id)->increment("reward", $rec->amount);
                break;
            case OrderPayRec::TYPE_GOODS:
                $order = O2oErrandOrder::query()->with(["rider"])->find($rec->order_id);
                DB::transaction(function () use ($order, $rec) {
                    $riderAccount = RiderAccount::query()->where('user_id', $order->rider->user_id)->where('account_type', 1)->first();
                    $riderAccount->add($rec->amount, RiderAccountFlow::BUSINESS_TYPE_BUY_FEE, $rec->order_no, '商品垫付金额结算');
                });
                break;
        }
    }

    /**
     * 评价
     * @param array $params
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model
     * @throws \Exception
     */
    public function evaluate(array $params)
    {
        $order = O2oErrandOrder::query()->where('order_no', $params["order_no"])->first();
        if (!$order) throw new \Exception("该订单不存在");
        if ($order->order_status != O2oErrandOrder::STATUS_FINISH) throw new \Exception("该订单未完成");

        $rec = OrderEvaluateRec::query()->where("order_id", $order->id)->first();
        if ($rec) throw new \Exception("您已评价过了，无需再次评价");

        $data = [
            'order_id' => $order->id,
            'user_id' => $order->user_id,
            'rider_id' => $order->rider_id,
            'is_anonymous' => $params["is_anonymous"],
            'is_satisfied' => $params["is_satisfied"],
        ];
        if ($params["is_satisfied"]) {
            $data = array_merge($data, [
                "pszs_star" => $params["pszs_star"],
                'cdgf_star' => $params["cdgf_star"],
                'ybzj_star' => $params["ybzj_star"],
                'hpwh_star' => $params["hpwh_star"],
                'lmrq_star' => $params["lmrq_star"]
            ]);

        } else {
            $data = array_merge($data, [
                'reason' => $params["reason"],
                'remark' => $params["remark"],
                'imgs' => $params["imgs"]
            ]);
        }

        return OrderEvaluateRec::query()->create($data);
    }

    /**
     * 检查天气条件
     * @param float|null $lng 经度
     * @param float|null $lat 纬度
     * @return bool 是否为恶劣天气
     */
    private function checkWeatherCondition($lng = null, $lat = null): bool
    {
        // 如果没有提供经纬度，默认为正常天气
        if ($lng === null || $lat === null) {
            return false;
        }

        try {
            $weatherService = new WeatherService();
            return $weatherService->isBadWeatherByLocation((float)$lng, (float)$lat);
        } catch (\Exception $e) {
            Log::warning('天气判断失败，使用默认值', [
                'lng' => $lng,
                'lat' => $lat,
                'error' => $e->getMessage()
            ]);
            return false; // 异常时默认为正常天气
        }
    }
}
