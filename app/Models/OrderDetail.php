<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderDetail extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = [
        'order_id', 'spu_id', 'spu_name', 'spu_cover', 'price', "discount_price", 'quantity'
    ];

    public function order(){
        return $this->belongsTo(Order::class, "order_id");
    }
}
