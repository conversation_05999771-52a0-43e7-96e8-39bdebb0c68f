# 骑手多路径规划API文档

本文档详细说明了骑手APP多路径规划功能相关的API接口。

## 基础信息

- 接口基础路径: `/api/v1`
- 认证方式: Bearer Token

## 接口列表

### 1. 获取骑手多任务的最优路径规划

系统自动根据时间窗口和位置关联性来确定需要进行路径优化的任务，计算最优配送路径。

**请求**

```
POST /rider/route/multi_task
```

**请求参数**

| 参数名       | 类型   | 必填 | 说明                       | 示例值    |
|-------------|--------|-----|----------------------------|-----------|
| lat         | number | 是   | 骑手当前纬度                | 30.282412 |
| lng         | number | 是   | 骑手当前经度                | 120.128412|
| time_window | integer| 否   | 时间窗口大小(分钟)，默认60   | 60        |
| max_distance| integer| 否   | 最大距离(米)，默认5000      | 5000      |

**成功响应 (200)**

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "route_id": 123,
    "total_distance": 7500,
    "estimated_time": 1800,
    "waypoints": [...],
    "savings": {
      "time_savings": 900,
      "distance_savings": 2300,
      "percentage_time": 25,
      "percentage_distance": 20
    },
    "tasks": [...],
    "task_count": 3,
    "total_task_count": 5
  }
}
```

**失败响应: 任务数量不足 (400)**

```json
{
  "code": 400,
  "message": "当前任务数量不足，无需路径优化",
  "data": {
    "has_optimization": false,
    "task_count": 1
  }
}
```

### 2. 获取路径规划详情

根据路径规划ID获取详细信息。

**请求**

```
GET /rider/route/detail/{id}
```

**路径参数**

| 参数名 | 类型    | 必填 | 说明       | 示例值 |
|-------|---------|-----|------------|-------|
| id    | integer | 是   | 路径规划ID | 123    |

**成功响应 (200)**

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "id": 123,
    "rider_id": 456,
    "total_distance": 7500,
    "estimated_time": 1800,
    "time_savings": 900,
    "distance_savings": 2300,
    "percentage_time": 25,
    "percentage_distance": 20,
    "status": "pending",
    "route": [...],
    "waypoints": [...],
    "tasks": [...]
  }
}
```

**失败响应: 未找到资源 (404)**

```json
{
  "code": 404,
  "message": "未找到路径规划信息",
  "data": null
}
```

### 3. 保存用户选择的路径

骑手确认接受或拒绝优化路径。

**请求**

```
POST /rider/route/save
```

**请求参数**

| 参数名      | 类型    | 必填 | 说明             | 示例值 |
|------------|---------|-----|------------------|-------|
| route_id   | integer | 是   | 路径规划ID        | 123   |
| is_accepted| boolean | 是   | 是否接受优化路径   | true  |

**成功响应 (200)**

```json
{
  "code": 0,
  "message": "保存成功",
  "data": null
}
```

**失败响应: 未找到资源 (404)**

```json
{
  "code": 404,
  "message": "未找到路径规划信息或无权操作",
  "data": null
}
```

### 4. 开始导航

获取导航信息并开始导航。

**请求**

```
POST /rider/route/navigate
```

**请求参数**

| 参数名    | 类型    | 必填 | 说明         | 示例值     |
|----------|---------|-----|--------------|-----------|
| route_id | integer | 是   | 路径规划ID    | 123       |
| lat      | number  | 是   | 骑手当前纬度  | 30.282412 |
| lng      | number  | 是   | 骑手当前经度  | 120.128412|

**成功响应 (200)**

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "current_location": {
      "lat": 30.282412,
      "lng": 120.128412
    },
    "nearest_point": {...},
    "distance_to_nearest": 350,
    "duration_to_nearest": 180,
    "remaining_route": [...],
    "remaining_distance": 5000,
    "remaining_time": 1200,
    "task_info": {
      "id": 789,
      "order_no": "ORD2023120112345",
      "type": "pickup",
      "status": "accepted",
      "address": "浙江省杭州市临安区滨湖天地",
      "name": "张三",
      "phone": "13812345678"
    },
    "amap_navigation": {
      "origin": "120.128412,30.282412",
      "destination": "120.135678,30.287654",
      "waypoints": "120.145678,30.297654;120.155678,30.307654"
    }
  }
}
```

### 5. 查询路径优化节省信息

查询当前任务的路径优化可能节省的时间和距离。

**请求**

```
GET /rider/route/savings
```

**成功响应 (200)**

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "has_optimization": true,
    "task_count": 3,
    "time_savings": 900,
    "distance_savings": 2300
  }
}
```

**失败响应: 任务数量不足 (400)**

```json
{
  "code": 400,
  "message": "当前任务数量不足，无需路径优化",
  "data": {
    "has_optimization": false,
    "task_count": 1
  }
}
```

## 通用错误响应

### 权限不足 (403)

```json
{
  "code": 403,
  "message": "您不是骑手，无法使用此功能",
  "data": null
}
```

### 参数错误 (422)

```json
{
  "code": 422,
  "message": "参数错误",
  "data": {
    "lat": ["lat字段是必须的"]
  }
}
``` 