<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Activity extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const ActivityStatusMap = [
        0 => '下架',
        1 => '上架'
    ];

    const ActivityTypeMap = [
        1 => '用户拉新',
        2 => '骑手拉新',
        3 => '用户订单',
        4 => '骑手订单'
    ];

    const ActivityTypeMessage = [
        1 => '%s天内每邀的第%s-%s名顾客且下单%s单成功完成后，奖励%s元',
        2 => '邀请%s人：完成任务双方都有奖励',
        3 => '邀请的顾客在%s天内下单完成量≥%s单，奖励%s元'
    ];

    const TimeType = [
        1 => '日期区间',
        2 => '注册后X天'
    ];

    public function rules()
    {
        return $this->hasMany(ActivityRule::class, 'activity_id');
    }

    public function ruleMessage($day)
    {
        $message = [];
        foreach ($this->rules as $rule) {
            $messageTpl = "";
            switch ($this->type) {
                case 1:
                    $messageTpl = sprintf(self::ActivityTypeMessage[$this->type], $day,$rule->start,$rule->end-1, $rule->order_count, floatval($rule->reward));
                    break;
                case 2:
                    $messageTpl = sprintf(self::ActivityTypeMessage[$this->type], $rule->start . '-' . ($rule->end -1) );
                    break;
                case 3:
                    $messageTpl = sprintf(self::ActivityTypeMessage[$this->type], $day, $rule->order_count, floatval($rule->reward));
                    break;
            }
            $message[] = $messageTpl;
        }
        return $message;
    }

}
