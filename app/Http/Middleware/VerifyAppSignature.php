<?php

namespace App\Http\Middleware;

use App\Models\OpenApiAccount;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpKernel\Exception\HttpException;

class VerifyAppSignature 
{
    const MIN_VERSION = '1.4.10';

    public function handle(Request $request, Closure $next)
    {
        $version = $request->header('app-version');

        if (!$version) {
            return $next($request);
        }

        if (version_compare($version, self::MIN_VERSION, '>')) {
            $appKey = $request->header('app-key');
            $account = OpenApiAccount::where('app_key', $appKey)->first();
            if (!$account) {
                return $this->errorResponse(401, '应用不存在');
            }

            $signature = $request->header('sign');
            if (!$signature) {
                return $this->errorResponse(401, '签名错误');
            }

            $data = $this->getSortedData($request);
            if ($this->isValidSignature($data, $signature, $account->app_secret)) {
                return $next($request);
            }

            return $this->errorResponse(401, '签名错误');
        }

        return $next($request);
    }

    private function isValidSignature($data, $signature, $secret)
    {
        $expectedSignature = hash_hmac('sha256', $data, $secret);
        return hash_equals($expectedSignature, $signature);
    }

    private function getSortedData(Request $request)
    {
        // 获取所有参数
        $params = $request->all();
        
        // 递归处理数组参数
        $params = $this->normalizeParams($params);
        
        // 按键名排序
        ksort($params);

        // 构建签名字符串
        $stringToSign = '';
        foreach ($params as $key => $value) {
            if (!is_null($value) && $value !== '') {
                $stringToSign .= $key . $this->normalizeValue($value);
            }
        }

        return $stringToSign;
    }

    /**
     * 规范化参数,将多维数组转为一维
     */
    private function normalizeParams($params, $prefix = '')
    {
        $result = [];
        
        foreach ($params as $key => $value) {
            $newKey = $prefix ? "{$prefix}.{$key}" : $key;
            
            if (is_array($value)) {
                // 递归处理数组
                $result = array_merge($result, $this->normalizeParams($value, $newKey));
            } else {
                $result[$newKey] = $value;
            }
        }
        
        return $result;
    }

    /**
     * 规范化值的处理
     */
    private function normalizeValue($value)
    {
        if (is_bool($value)) {
            return $value ? 'true' : 'false';
        }
        
        if (is_null($value)) {
            return '';
        }
        
        if (is_array($value)) {
            return implode(',', array_map([$this, 'normalizeValue'], $value));
        }
        
        // 统一使用 RFC 3986 编码
        return rawurlencode((string)$value);
    }

    public function errorResponse($statusCode, $message = null, $code = 0)
    {
        throw new HttpException($statusCode, $message, null, [], $code);
    }
} 