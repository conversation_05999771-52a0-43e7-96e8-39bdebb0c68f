<?php

namespace App\Http\Controllers\Api;

use App\Models\User;
use App\Services\EasemobService;
use App\Services\WukongIMService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class EasemobController extends Controller
{
    public function getToken(Request $request)
    {
        try {
            $user = $request->user();
            $wukongService = app(WukongIMService::class);
            
            // 确保用户已注册到悟空IM
            if (!$wukongService->registerUser($user)) {
                return $this->errorResponse(500, 'IM注册失败');
            }

            $uid = $wukongService->formatAlias($user);

            // 获取连接地址
            $response = Http::get($wukongService->getBaseUrl() . '/route', [
                'uid' => $uid
            ]);
            if (!$response->successful()) {
                Log::error('获取悟空IM连接地址失败', [
                    'user_id' => $user->id,
                    'uid' => $uid,
                    'response' => $response->json()
                ]);
                return $this->errorResponse(500, 'IM服务异常');
            }

            $routeInfo = $response->json();
            
            return $this->success('success', [
                'token' => $user->recommend_code,
                'uid' => $uid,
                'tcp_addr' => $routeInfo['tcp_addr'] ?? '',
                'ws_addr' => $routeInfo['ws_addr'] ?? '',
            ]);

        } catch (\Exception $e) {
            Log::error('获取悟空IM连接信息异常', [
                'user_id' => $user->id ?? null,
                'error' => $e->getMessage()
            ]);
            return $this->errorResponse(500, 'IM服务异常');
        }
    }

    public function fetchUserInfo(Request $request)
    {
        $ids = $request->get('ids');

        $result = [];
        $codes = [];

        foreach ($ids as $id) {
            $code = str_replace('u_', '', $id);
            $code = str_replace('r_', '', $code);
            $codes[] = $code;
        }

        $users = User::query()->whereIn('recommend_code', $codes)->get()->groupBy('recommend_code');
        foreach ($ids as $id) {
            $code = str_replace('u_', '', $id);
            $code = str_replace('r_', '', $code);
            if ($id == EasemobService::SQ_SYSTEM_USER) {
                $result[$id] = [
                    'nickname' => '系统信息',
                    'avatar' => 'https://storage.fengqishi.com.cn/resource/logo.jpeg'
                ];
            } elseif ($id == EasemobService::PT_SYSTEM_USER) {
                $result[$id] = [
                    'nickname' => '系统信息',
                    'avatar' => 'https://storage.fengqishi.com.cn/resource/yuqishi_logo_108.png'
                ];
            } else {
                $result[$id] = [
                    'nickname' => isset($users[$code][0]['nickname']) ? $users[$code][0]['nickname'] : "用户" . $id,
                    'avatar' => isset($users[$code][0]['avatar']) ? $users[$code][0]['avatar'] : "",
                ];
            }
        }

        return $this->success("success", $result);

    }
}
