<?php

namespace App\RunningAdmin\Forms;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use Carbon\Carbon;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Facades\DB;

class OrderDispatchForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        return $this->response()->error("TODO");
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $riders = Rider::query()->pluck('name', 'id');
        $this->select('rider_id', "骑手")->options($riders)->required();
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'rider_id' => 0,
        ];
    }
}
