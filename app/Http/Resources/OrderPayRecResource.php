<?php

namespace App\Http\Resources;

use App\Models\OrderPayRec;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderPayRecResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'user_id' => $this->user_id,
            'order_no' => $this->order_no,
            'type' => $this->type,
            'type_text' => OrderPayRec::TypeMap[$this->type],
            'amount' => fentoyuan($this->amount),
            'paid_at' => $this->paid_at ? $this->paid_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'pay_method' => $this->pay_method,
            "payment_no" => $this->payment_no,
            'refund_no' => $this->refund_no,
            'refund_at' => $this->refund_at ? $this->refund_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
