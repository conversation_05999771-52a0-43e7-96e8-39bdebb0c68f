<?php

namespace App\Http\Controllers;

use App\Events\EarnestRechargeOrderPaid;
use App\Events\RechargeOrderPaid;
use App\Jobs\DispatchHandleNewUserActivity;
use App\Jobs\DispatchOrderNotify;
use App\Jobs\RefundO2oErrandOrder;
use App\Models\Common;
use App\Models\O2oErrandOrder;
use App\Models\EarnestRechargeOrder;
use App\Models\Order;
use App\Models\OrderPayRec;
use App\Models\RechargeOrder;
use App\Services\O2oErrandOrderService;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Yansongda\Pay\Pay;
use Illuminate\Support\Facades\DB;
use App\Models\MerchantRechargeOrder;

class PaymentCallbackController extends Controller
{
    public function wechatNotify()
    {
        Pay::config(config("pay"));
        $result = Pay::wechat()->callback();
        Log::info("微信支付回调", $result->toArray());
        $ciphertext = $result['resource']['ciphertext'];

        $orderNo = $ciphertext['out_trade_no'];
        $orderPrefix = substr($orderNo, 0, 2);
        // 找到对应的订单
        switch ($orderPrefix) {
            case RechargeOrder::RECHARGE_ORDER_PREFIX:
                $order = RechargeOrder::query()->where("order_no", $orderNo)->first();
                break;
            case Order::ORDER_PREFIX:
                $order = Order::query()->where('order_no', $orderNo)->first();
                break;
            case O2oErrandOrder::ORDER_PREFIX:
                $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
                break;
            case OrderPayRec::ORDER_PREFIX:
                $order = OrderPayRec::query()->where('order_no', $orderNo)->first();
                break;
            case EarnestRechargeOrder::EARNEST_RECHARGE_ORDER_PREFIX:
                $order = EarnestRechargeOrder::query()->where("order_no", $orderNo)->first();
                break;
        }
        // 订单不存在则告知微信支付
        if (!$order) {
            return 'fail';
        }
        // 订单已支付
        if ($order->paid_at) {
            // 告知微信支付此订单已处理
            return Pay::wechat()->success();
        }
        // 将订单标记为已支付
        $updateData = [
            'paid_at' => Carbon::now(),
            'pay_method' => Common::PAY_METHOD_WECHAT,
            'payment_no' => $ciphertext['transaction_id'],
        ];
        if ($orderPrefix == O2oErrandOrder::ORDER_PREFIX) {
            $updateData['order_status'] = O2oErrandOrder::STATUS_PAID;
            if ($order->appointment_start_time->timestamp <= Carbon::now()->timestamp) {
                $seconds = Carbon::now()->timestamp - $order->appointment_start_time->timestamp + 10;
                $updateData['appointment_start_time'] = $order->appointment_start_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                $updateData['appointment_end_time'] = $order->appointment_end_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                $updateData['estimated_delivery_time'] = $order->estimated_delivery_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
            }
        }
        $order->update($updateData);

        $this->afterOrderPaid($order);

        return Pay::wechat()->success();
    }

    public function alipayNotify()
    {
        try {
            // 记录原始请求数据用于调试
            Log::info("支付宝回调开始", [
                'ip' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'raw_data' => request()->all()
            ]);

            Pay::config(config("pay"));
            $result = Pay::alipay()->callback();
            Log::info("支付宝支付回调", $result->toArray());

            $orderNo = $result->out_trade_no;
            $orderPrefix = substr($orderNo, 0, 2);
            
            // 记录订单号和前缀用于调试
            Log::info("支付宝回调订单信息", [
                'order_no' => $orderNo,
                'order_prefix' => $orderPrefix
            ]);

            // 找到对应的订单
            $order = null;
            switch ($orderPrefix) {
                case RechargeOrder::RECHARGE_ORDER_PREFIX:
                    $order = RechargeOrder::query()->where("order_no", $orderNo)->first();
                    break;
                case Order::ORDER_PREFIX:
                    $order = Order::query()->where('order_no', $orderNo)->first();
                    break;
                case O2oErrandOrder::ORDER_PREFIX:
                    $order = O2oErrandOrder::query()->where('order_no', $orderNo)->first();
                    break;
                case OrderPayRec::ORDER_PREFIX:
                    $order = OrderPayRec::query()->where('order_no', $orderNo)->first();
                    break;
                case EarnestRechargeOrder::EARNEST_RECHARGE_ORDER_PREFIX:
                    $order = EarnestRechargeOrder::query()->where("order_no", $orderNo)->first();
                    break;
                case MerchantRechargeOrder::RECHARGE_ORDER_PREFIX:
                    $order = MerchantRechargeOrder::query()->where("order_no", $orderNo)->first();
                    break;
                default:
                    Log::warning("支付宝回调：未知的订单前缀", [
                        'order_no' => $orderNo,
                        'order_prefix' => $orderPrefix
                    ]);
                    break;
            }

            // 订单不存在的处理
            if (!$order) {
                Log::warning("支付宝回调：订单不存在", [
                    'order_no' => $orderNo,
                    'order_prefix' => $orderPrefix
                ]);
                return 'fail';
            }

            // 订单已支付的处理
            if ($order->paid_at) {
                Log::info("支付宝回调：订单已支付", [
                    'order_no' => $orderNo,
                    'paid_at' => $order->paid_at
                ]);
                return Pay::alipay()->success();
            }

            // 更新订单状态
            $updateData = [
                'paid_at' => Carbon::now(),
                'pay_method' => Common::PAY_METHOD_ALIPAY,
                'payment_no' => $result->trade_no,
            ];
            
            if ($orderPrefix == O2oErrandOrder::ORDER_PREFIX) {
                $updateData['order_status'] = O2oErrandOrder::STATUS_PAID;
                if ($order->appointment_start_time->timestamp <= Carbon::now()->timestamp) {
                    $seconds = Carbon::now()->timestamp - $order->appointment_start_time->timestamp + 10;
                    $updateData['appointment_start_time'] = $order->appointment_start_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                    $updateData['appointment_end_time'] = $order->appointment_end_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                    $updateData['estimated_delivery_time'] = $order->estimated_delivery_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                }
            }
            
            $order->update($updateData);

            Log::info("支付宝回调：订单状态更新成功", [
                'order_no' => $orderNo,
                'update_data' => $updateData
            ]);

            // 执行支付后处理逻辑
            $this->afterOrderPaid($order);

            Log::info("支付宝回调处理完成", ['order_no' => $orderNo]);
            return Pay::alipay()->success();

        } catch (\Yansongda\Pay\Exception\InvalidArgumentException $e) {
            // 支付SDK参数异常
            Log::error("支付宝回调：SDK参数异常", [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_data' => request()->all()
            ]);
            return response('fail', 500);
            
        } catch (\Yansongda\Pay\Exception\InvalidSignException $e) {
            // 签名验证失败
            Log::error("支付宝回调：签名验证失败", [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'request_data' => request()->all()
            ]);
            return response('fail', 500);
            
        } catch (\Yansongda\Pay\Exception\InvalidConfigException $e) {
            // 配置异常
            Log::error("支付宝回调：配置异常", [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'config' => config('pay.alipay'),
                'request_data' => request()->all()
            ]);
            return response('fail', 500);
            
        } catch (\Exception $e) {
            // 其他异常
            Log::error("支付宝回调：未知异常", [
                'message' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
                'trace' => $e->getTraceAsString(),
                'request_data' => request()->all()
            ]);
            return response('fail', 500);
        }
    }

    private function afterOrderPaid($order)
    {
        $orderPrefix = substr($order->order_no, 0, 2);
        // 找到对应的订单
        switch ($orderPrefix) {
            case RechargeOrder::RECHARGE_ORDER_PREFIX:
                event(new RechargeOrderPaid($order));
                break;
            case Order::ORDER_PREFIX:
                # todo
                break;
            case EarnestRechargeOrder::EARNEST_RECHARGE_ORDER_PREFIX:
                event(new EarnestRechargeOrderPaid($order));
                break;
            case OrderPayRec::ORDER_PREFIX:
                app(O2oErrandOrderService::class)->afterPaid($order);
                break;
            case O2oErrandOrder::ORDER_PREFIX:
                if (app()->environment() == 'production') {
                    // ding()->text(sprintf("新订单提醒 - 订单号：%s，请及时处理", $order->order_no));
                    // 订单APP推送
                    dispatch(new DispatchOrderNotify($order));
                }
                # 无骑手接单 超时关闭订单 30分钟
                dispatch(new RefundO2oErrandOrder($order->order_no, config('app.order_refund_ttl')));
                break;
            case MerchantRechargeOrder::RECHARGE_ORDER_PREFIX:
                try {
                    // 处理商家充值，增加商家余额
                    DB::transaction(function () use ($order) {
                        // 获取商家ID和充值金额
                        $merchantId = $order->merchant_id;
                        // 由于从数据库读取的值会自动转换为元，需要乘以100转回分
                        $amount = $order->actual_amount;
                        
                        // 查找商家记录
                        $merchant = \App\Models\Merchant::query()->find($merchantId);
                        if (!$merchant) {
                            Log::error('商家充值失败：商家不存在', [
                                'order_no' => $order->order_no,
                                'merchant_id' => $merchantId,
                                'amount' => $amount
                            ]);
                            throw new \Exception("商家不存在: {$merchantId}");
                        }
                        
                        // 获取更新前的余额（用于记录）
                        $beforeBalance = $merchant->balance;
                        
                        // 使用原子操作更新余额，避免并发问题
                        $updated = DB::table('merchants')
                            ->where('id', $merchantId)
                            ->update([
                                'balance' => DB::raw("balance + {$amount}"),
                                'updated_at' => now()
                            ]);
                            
                        if (!$updated) {
                            Log::error('商家充值失败：余额更新失败', [
                                'order_no' => $order->order_no,
                                'merchant_id' => $merchantId,
                                'amount' => $amount
                            ]);
                            throw new \Exception("余额更新失败");
                        }
                        
                        // 获取更新后的余额
                        $merchant = \App\Models\Merchant::query()->find($merchantId);
                        $afterBalance = $merchant->balance;
                        
                        // 记录商家账户流水
                        $logResult = DB::table('merchant_account_logs')->insert([
                            'merchant_id' => $merchantId,
                            'amount' => $amount,
                            'before_balance' => $beforeBalance,
                            'after_balance' => $afterBalance,
                            'type' => 'recharge',
                            'order_no' => $order->order_no,
                            'remark' => '商家余额充值',
                            'created_at' => now(),
                            'updated_at' => now()
                        ]);
                        
                        if (!$logResult) {
                            Log::warning('商家充值流水记录失败', [
                                'order_no' => $order->order_no,
                                'merchant_id' => $merchantId,
                                'amount' => $amount
                            ]);
                        }
                        
                        Log::info('商家充值成功', [
                            'order_no' => $order->order_no,
                            'merchant_id' => $merchantId,
                            'amount' => $amount,
                            'before_balance' => $beforeBalance,
                            'after_balance' => $afterBalance
                        ]);
                    });
                } catch (\Exception $e) {
                    Log::error('商家充值处理异常', [
                        'order_no' => $order->order_no,
                        'message' => $e->getMessage(),
                        'file' => $e->getFile(),
                        'line' => $e->getLine(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    // 即使充值处理失败，也不要让整个回调失败
                    // 因为支付已经成功，只是后续处理有问题
                }
                break;
        }

    }

}
