<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class UserIdCard extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $fillable = ['user_id', 'id_card_image', 'id_card_image_over', 'name', 'nation', 'address', 'id_card',
        'issuing_authority', 'birth', 'issuing_date', 'expiry_date', 'sex'];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id');
    }
}
