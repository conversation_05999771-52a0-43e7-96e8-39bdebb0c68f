<?php

namespace App\Services;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

class VerificationService
{
    /**
     * 发送验证码
     *
     * @param string $phone 手机号
     * @return array 发送结果
     */
    public function send(string $phone): array
    {
        // 生成6位随机验证码
        $code = mt_rand(100000, 999999);
        
        // 在实际应用中，这里应该调用短信服务商的API发送验证码
        // 这里仅作为示例，将验证码存储在缓存中
        Cache::put("verification_code:{$phone}", $code, now()->addMinutes(10));
        
        // 记录日志（开发环境使用）
        Log::info("向手机号 {$phone} 发送验证码: {$code}");
        
        return [
            'success' => true,
            'message' => '验证码发送成功',
            'code' => $code, // 仅在开发环境返回，生产环境应移除
        ];
    }
    
    /**
     * 验证验证码
     *
     * @param string $phone 手机号
     * @param string $code 验证码
     * @return bool 验证结果
     */
    public function verify(string $phone, string $code): bool
    {
        $cachedCode = Cache::get("verification_code:{$phone}");
        
        if (!$cachedCode) {
            return false; // 验证码不存在或已过期
        }
        
        // 验证成功后删除缓存中的验证码，防止重复使用
        if ($cachedCode == $code) {
            Cache::forget("verification_code:{$phone}");
            return true;
        }
        
        return false;
    }
} 