<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ShopSpu extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $casts = ["is_signboard" => 'boolean', 'sell_tags' => 'json'];

    protected $fillable = [
        'shop_id', 'cat_id',
        'name', 'cover', 'description', 'price', 'discount_price', 'sell_tags', 'stock', 'sales',
        'is_signboard', 'sort', 'status',
    ];

    protected function asJson($value)
    {
        return json_encode($value, JSON_UNESCAPED_UNICODE);
    }

    public function shop(){
        return $this->belongsTo(Shop::class, "shop_id");
    }

    public function cat(){
        return $this->belongsTo(ShopSpuCat::class, "cat_id");
    }
}
