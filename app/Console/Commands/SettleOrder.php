<?php

namespace App\Console\Commands;

use App\Models\O2oErrandOrder;
use App\Models\Rider;
use App\Models\RiderAccount;
use App\Models\RiderAccountFlow;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class SettleOrder extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'settle {mode=daily} {time=1} {start?} {end?}';

    /**
     * The console command description.
     *0
     * @var string
     */
    protected $description = '系统结算进程 可以处理订单结算和活动结算';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $params = $this->arguments();
        if ($params['mode'] == 'daily') {
            // 日结
            $this->riderOrderDailySettle($params['time']);
        } else {
            // 月结
        }
        return Command::SUCCESS;
    }

    private function riderOrderDailySettle($time)
    {
        // 查询订单
        switch ($time) {
            case 1:
                $start = Carbon::yesterday()->startOfDay();
                $end = Carbon::yesterday()->endOfDay();
                break;
            case 2:
                $start = Carbon::now()->startOfMonth();
                $end = Carbon::now()->endOfMonth();
                break;
            case 3:
                $start = Carbon::now()->startOfMonth()->addMonths(-1);
                $end = Carbon::now()->startOfMonth()->addMonths(-1)->endOfMonth();
                break;
            case 4:
                $start = Carbon::parse($this->option('start'))->startOfDay();
                $end = Carbon::parse($this->option('end'))->endOfDay();
                break;
            default:
                $start = Carbon::yesterday()->startOfDay();
                $end = Carbon::yesterday()->endOfDay();
        }

        O2oErrandOrder::query()->where('order_status', O2oErrandOrder::STATUS_FINISH)
            ->whereBetween('finish_time', [$start, $end])
            ->whereNull('settle_time')
            ->with('rider')
            ->chunkById(100, function ($orders) {
                DB::beginTransaction();
                try {
                    foreach ($orders as $order) {
                        $riderAccount = RiderAccount::query()->where('user_id', $order->rider->user_id)
                            ->where('account_type', 1)->first();
                        $riderAccount->add($order->reward_amount, RiderAccountFlow::BUSINESS_TYPE_ORDER, $order->order_no, '跑腿订单结算');
                        $order->settle_time = Carbon::now();
                        // 小费，打赏
                        if ($order->gratuity > 0) {
                            $riderAccount->add($order->gratuity, RiderAccountFlow::BUSINESS_TYPE_ORDER_TIP, $order->order_no, '小费结算');
                        }
                        if ($order->reward > 0) {
                            $riderAccount->add($order->reward, RiderAccountFlow::BUSINESS_TYPE_ORDER_REWARD, $order->order_no, '打赏');
                        }
                        DB::commit();
                    }
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error($e->getMessage() . PHP_EOL . $e->getFile() . $e->getLine() . PHP_EOL . $e->getTraceAsString());
                }
            });
    }

    private function riderOrderMonthlySettle()
    {

    }
}
