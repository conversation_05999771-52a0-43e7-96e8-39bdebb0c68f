<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\Merchant;
use App\Models\User;
use App\Models\MerchantToken;

class HaiboControllerTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    /**
     * 测试创建新门店
     */
    public function test_create_new_store()
    {
        $storeData = [
            'shop_name' => '测试门店',
            'phone' => '13800138000',
            'address' => '测试地址123号',
            'province' => '浙江省',
            'city' => '杭州市',
            'district' => '余杭区',
            'city_code' => '330100',
            'shop_id' => 'haibo_test_001',
            'contact_name' => '张三',
            'email' => '<EMAIL>',
        ];

        $response = $this->postJson('/api/haibo/store', $storeData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => '门店创建成功'
                ]);

        // 验证数据库中是否创建了商家记录
        $this->assertDatabaseHas('merchants', [
            'shop_name' => '测试门店',
            'phone' => '13800138000',
        ]);

        // 验证是否创建了用户记录
        $this->assertDatabaseHas('users', [
            'phone' => '13800138000',
        ]);

        // 验证是否创建了token记录
        $this->assertDatabaseHas('merchant_tokens', [
            'platform' => 'haibo',
            'shop_id' => 'haibo_test_001',
        ]);
    }

    /**
     * 测试更新现有门店
     */
    public function test_update_existing_store()
    {
        // 先创建一个用户和商家
        $user = User::factory()->create([
            'phone' => '13800138001',
        ]);

        $merchant = Merchant::factory()->create([
            'phone' => '13800138001',
            'user_id' => $user->id,
            'shop_name' => '原门店名称',
        ]);

        MerchantToken::create([
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'platform' => 'haibo',
            'shop_id' => 'haibo_test_002',
            'access_token' => 'test_token',
            'refresh_token' => 'test_refresh_token',
        ]);

        $updateData = [
            'shop_name' => '更新后的门店名称',
            'phone' => '13800138001',
            'address' => '更新后的地址456号',
            'province' => '浙江省',
            'city' => '杭州市',
            'district' => '西湖区',
            'city_code' => '330100',
            'shop_id' => 'haibo_test_002',
        ];

        $response = $this->postJson('/api/haibo/store', $updateData);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => '门店更新成功'
                ]);

        // 验证商家信息是否更新
        $this->assertDatabaseHas('merchants', [
            'id' => $merchant->id,
            'shop_name' => '更新后的门店名称',
            'district' => '西湖区',
        ]);
    }

    /**
     * 测试参数验证
     */
    public function test_validation_errors()
    {
        $invalidData = [
            'shop_name' => '', // 空的门店名称
            'phone' => '123', // 无效的手机号
        ];

        $response = $this->postJson('/api/haibo/store', $invalidData);

        $response->assertStatus(400)
                ->assertJson([
                    'code' => 400,
                ]);
    }

    /**
     * 测试回调接口 - ping
     */
    public function test_callback_ping()
    {
        $response = $this->postJson('/api/haibo/callback/ping', []);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => 'SUCCESS'
                ]);
    }

    /**
     * 测试回调接口 - 查询门店余额
     */
    public function test_callback_balance()
    {
        // 创建测试数据
        $user = User::factory()->create();
        $merchant = Merchant::factory()->create([
            'user_id' => $user->id,
            'balance' => 100.50,
        ]);

        MerchantToken::create([
            'merchant_id' => $merchant->id,
            'user_id' => $user->id,
            'platform' => 'haibo',
            'shop_id' => 'haibo_test_balance',
            'access_token' => 'test_token',
            'refresh_token' => 'test_refresh_token',
        ]);

        $response = $this->postJson('/api/haibo/callback/balance', [
            'shop_id' => 'haibo_test_balance'
        ]);

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => 'SUCCESS'
                ]);

        $responseData = json_decode($response->json('data'), true);
        $this->assertEquals('haibo_test_balance', $responseData['shop_id']);
        $this->assertEquals(100.50, $responseData['balance']);
    }

    /**
     * 测试授权回调
     */
    public function test_auth_callback()
    {
        $response = $this->getJson('/api/haibo/auth/callback?code=test_code&redirect_uri=http://example.com&state=test_state');

        $response->assertStatus(200)
                ->assertJson([
                    'code' => 200,
                    'message' => '授权回调处理成功'
                ]);
    }
}
