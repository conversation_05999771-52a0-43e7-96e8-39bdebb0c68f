<?php


namespace App\Services;


use App\Exceptions\BusinessException;
use App\Jobs\CloseDispathOrder;
use App\Jobs\CloseTransOrder;
use App\Models\DispatchOrder;
use App\Models\O2oErrandOrder;
use App\Models\OrderPayRec;
use App\Models\Rider;
use App\Models\RiderSetting;
use App\Models\TransOrder;
use App\Services\Amap\GaodeService;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskService
{
    protected int $distance = 5;

    private $commonService;

    private $gaodeService;

    private $orderService;

    public function __construct()
    {
        $this->commonService = app(CommonService::class);
        $this->gaodeService = app(GaodeService::class);
        $this->orderService = app(O2oErrandOrderService::class);
    }

    /**
     * @param $rider
     * @param $lng
     * @param $lat
     * @param string $sort
     * @return \Illuminate\Database\Eloquent\Builder[]|\Illuminate\Database\Eloquent\Collection
     */
    public function getTaskList($rider, $lng, $lat, $sort = 'ASC')
    {
        $orderList = [];

        if (!in_array($sort, ['ASC', 'DESC'])) {
            $sort = 'ASC';
        }

        $minDistance = [];
        $maxDistance = [];

        $setting = RiderSetting::query()->where('user_id', $rider->user_id)->first();
        if ($setting) {
            $infos = $setting->distance_infos;
            foreach ($infos as $index => $info) {
                if ($info['flag']) {
                    if ($index == 0) {
                        $minDistance[] = 0;
                        $maxDistance[] = 3;
                    }
                    if ($index == 1) {
                        $minDistance[] = 3;
                        $maxDistance[] = 5;
                    }
                    if ($index == 2) {
                        $minDistance[] = 5;
                        $maxDistance[] = 10;
                    }
                }
            }
        } else {
            $minDistance[] = 0;
            $maxDistance[] = 5;
        }
        if (app()->environment() != 'production') {
            $maxDistance[] = 5000;
            $minDistance[] = 0;
        }
        $redisLocations = O2oErrandOrder::GetOrderFromRedis($lng, $lat, max($maxDistance), $sort);
        $md = min($minDistance);
        foreach ($redisLocations as $index => $redisLocation) {
            if ($redisLocation[1] < $md) {
                unset($redisLocations[$index]);
            }
        }
        $orderIdList = [];
        $orderDistance = [];
        foreach ($redisLocations as $location) {
            $orderIdList[] = $location[0];
            $orderDistance[$location[0]] = $location[1];
        }
        $query = O2oErrandOrder::query()->whereIn('id', $orderIdList)
            ->where('order_status', O2oErrandOrder::STATUS_PAID)
            ->where('site_id', $rider->site_id)
            ->where('refund_status', O2oErrandOrder::REFUND_STATUS_INIT)
            ->with(['category', 'goodsCategory']);

        $query->orderByRaw(DB::raw("FIND_IN_SET(id, '" . implode(',', $orderIdList) . "'" . ')')); //按照指定顺序排序

        $orders = $query->get();


        foreach ($orders as $order) {
            $orderList[] = $this->formatTask($order, $orderDistance, $rider->role);
        }

        return $orderList;

    }

    public function getOrderList($rider, $lng, $lat)
    {
        $orderList = [];

        $orders = O2oErrandOrder::query()
            ->with([
                'payRecs' => function ($query) { //支付商品费订单信息
                    $query->where('type', OrderPayRec::TYPE_GOODS);
                }
            ])
            ->where('rider_id', $rider->id)
            ->whereIn('order_status', [O2oErrandOrder::STATUS_PICKUP, O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, O2oErrandOrder::STATUS_DELIVERY])
            ->where('refund_status', O2oErrandOrder::REFUND_STATUS_INIT)
            ->get();

        $target = "";
        foreach ($orders as $order) {
            if ($order->pickup_region_id == 0) {
                $target .= $order->deliver_lng . ',' . $order->deliver_lat . '|';
            } else {
                if ($order->order_status == O2oErrandOrder::STATUS_PICKUP || $order->order_status == O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT) {
                    $target .= $order->pickup_lng . ',' . $order->pickup_lat . '|';
                } else {
                    $target .= $order->deliver_lng . ',' . $order->deliver_lat . '|';
                }
            }



        }
        $target = substr($target, 0, strlen($target) - 1);
        $orderDistance = [];
        if ($target) {
            Log::debug("距离请求 $target , $lng,$lat");
            $distanceList = $this->gaodeService->distanceQuery($target, "$lng,$lat");
            foreach ($distanceList as $item) {
                $order = $orders[intval($item["origin_id"]) - 1];
                $orderDistance[$order->id] = $item['distance'] / 1000;
            }
        }

        foreach ($orders as $order) {
            $orderList[] = $this->formatTask($order, $orderDistance, $rider->role);
        }

        return $orderList;
    }

    public function getOrderDetail($orderNo)
    {
        $order = O2oErrandOrder::query()->where('order_no', $orderNo)->with('rider')->first();

        $riderRole = $order->rider->role ?? Rider::RolePartTime;
        $result = $this->formatTask($order, [], $riderRole);

        $arrayText = "到店";
        if (in_array($order->type, [O2oErrandOrder::TYPE_SEND, O2oErrandOrder::TYPE_TAKE])) {
            $arrayText = "到达取货点";
        }
        $detail = [
            'pickup_lng' => $order->pickup_lng,
            'pickup_lat' => $order->pickup_lat,
            'deliver_lng' => $order->deliver_lng,
            'deliver_lat' => $order->deliver_lat,
            'estimated_delivery_time' => $order->estimated_delivery_time->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'timeline' => [
                [
                    'name' => '接单',
                    'time' => $order->receipt_time ? $order->receipt_time->format('H:i') : ''
                ],
                [
                    'name' => $arrayText,
                    'time' => $order->arrive_at ? $order->arrive_at->format('H:i') : ''
                ],
                [
                    'name' => '取货',
                    'time' => $order->pickup_time ? $order->pickup_time->format('H:i') : ''
                ],
                [
                    'name' => '送达',
                    'time' => $order->finish_time ? $order->finish_time->format('H:i') : ''
                ]
            ],
            'buy_imgs' => $this->formatBuyImage($order->buy_imgs),
        ];
        if ($riderRole == Rider::RolePartTime) {
            // 兼职
            $detail['income_details'] = $this->transIncomeDetail($order->detail['part']);
        } else {
            // 全职
            $detail['income_details'] = $this->transIncomeDetail($order->detail['full']);
        }

        return array_merge($result, $detail);
    }

    /**
     * @param $order
     * @param $riderId
     * @return mixed
     * @throws \Exception
     */
    public function dispatchOrder($order, $riderId)
    {
        DB::beginTransaction();
        try {
            $dispatchOrder = new DispatchOrder();
            $dispatchOrder->order_id = $order->id;
            $dispatchOrder->status = 0;
            $dispatchOrder->closed = 0;
            $dispatchOrder->rider_id = $riderId;
            $dispatchOrder->remark = "";
            $dispatchOrder->operator_id = 0;
            $dispatchOrder->operator_name = "管理员";
            $dispatchOrder->save();

            $order->dispatch_status = O2oErrandOrder::DISPATCH_STATUS_SENDING;
            $order->save();

            $rider = Rider::query()->with('user')->find($riderId);

            // 发送派单确认提醒
            app(WukongIMService::class)->sendCustomMessage($rider->user, [
                'type' => 9999,
                'cmd' => 'dispatch_order',
                'param' => [
                    'order_no' => $order->order_no,
                    'dispatch_order_id' => $dispatchOrder->id,
                    'create_time' => Carbon::parse($dispatchOrder->created_at)->format('Y-m-d H:i:s'),
                    'close_time' => $dispatchOrder->created_at->addMinutes(5)->format('Y-m-d H:i:s')
                ]
            ]);
            DB::commit();
            dispatch(new CloseDispathOrder($dispatchOrder->id, 60 * 5));
            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function agreeDispatchOrder($dispatchOrder)
    {
        DB::beginTransaction();
        try {
            $service = app(RiderOrderService::class);
            $service->pickupOrder($dispatchOrder->order, $dispatchOrder->rider, O2oErrandOrder::DISPATCH_STATUS_CONFIRM);

            $dispatchOrder->status = 1;
            $dispatchOrder->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function rejectDispatch($dispatchOrder)
    {

        DB::beginTransaction();
        try {
            $dispatchOrder->status = 2;
            $dispatchOrder->save();

            $order = $dispatchOrder->order;
            $order->dispatch_status = O2oErrandOrder::DISPATCH_STATUS_FAILED;
            $order->save();

            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * @param $rider
     * @param $status
     * @param $startDate
     * @param $endDate
     * @param $keyword
     * @param $pageSize
     * @return array
     */
    public function queryTask($rider, $status, $startDate, $endDate, $keyword, $pageSize)
    {
        switch ($status) {
            case 1:
                $query = O2oErrandOrder::query()
                    ->where('rider_id', $rider->id)
                    ->where('order_status', O2oErrandOrder::STATUS_FINISH)
                    ->where('refund_status', 0);
                break;
            case 2:
                $query = O2oErrandOrder::query()
                    ->where('rider_id', $rider->id)
                    ->whereNotNull('refund_no');
                break;
            case 3:
                $query = O2oErrandOrder::query()
                    ->where('ori_rider_id', $rider->id)
                    ->where('is_trans', 1);
                break;
            default:
                $query = O2oErrandOrder::query()
                    ->where('rider_id', $rider->id)
                    ->where('order_status', O2oErrandOrder::STATUS_FINISH)
                    ->where('refund_status', 0);
        }

        if ($startDate) {
            $query->whereBetween('create_time', [$startDate, $endDate]);
        }

        if ($keyword) {
            $query->where(function ($subQuery) use ($keyword) {
                return $subQuery->where('deliver_phone', 'like', '%' . $keyword . '%')
                    ->orWhere('pickup_phone', 'like', '%' . $keyword . '%');
            });
        }
        $orderList = [];
        $taskList = $query->orderBy('create_time', 'desc')->paginate($pageSize);
        foreach ($taskList as $task) {
            $orderList[] = $this->formatTask($task, '', $rider->role);
        }

        return [
            'current_page' => $taskList->currentPage(),
            'total' => $taskList->total(),
            'data' => $orderList
        ];
    }


    public function transTask($order, $reason, $targetRiderId = 0)
    {
        DB::beginTransaction();
        try {
            // 转单记录创建
            $transOrder = new TransOrder();
            $transOrder->order_no = $order->order_no;
            $transOrder->trans_rider_id = $order->rider->id;
            $transOrder->reason = $reason;
            $transOrder->status = $targetRiderId > 0 ? 1 : 0;
            $transOrder->target_rider_id = $targetRiderId;
            $transOrder->save();

            $order->is_trans = 1;
            $order->ori_rider_id = $order->rider_id;
            $order->rider_id = $targetRiderId;
            if ($targetRiderId == 0) {
                $order->status = O2oErrandOrder::STATUS_PAID;
            }
            $order->save();
            if (!$targetRiderId) {
                dispatch(new CloseTransOrder($transOrder->id, 60 * 5));
            }
            DB::commit();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    public function formatTask($order, $orderDistance, $role)
    {
        $minute = Carbon::parse($order->estimated_delivery_time)->diffInMinutes(Carbon::now());
        if ($minute > 90) {
            $timeText = Carbon::parse($order->estimated_delivery_time)->diffInHours(Carbon::now()) . '小时内';
        } else {
            $timeText = $minute . '分钟内';
        }
        if (Carbon::parse($order->estimated_delivery_time)->lt(Carbon::now())) {
            if ($minute > 90) {
                $hours = Carbon::parse($order->estimated_delivery_time)->diffInHours(Carbon::now());
                $timeText = '已超时' . $hours . '小时';
            } else {
                $timeText = '已超时' . $minute . '分钟';
            }
        }

        // if (gtVersion('1.0.3')) {
        //     $orderPrice = '';
        // } else {
            $orderPrice = $role == Rider::RolePartTime ? fentoyuan($order->reward_amount_part + $order->gratuity) : fentoyuan($order->reward_amount_full + $order->gratuity);
        // }

        $goodsPricePaidAt = "";
        if ($order->payRecs && $order->payRecs->count() > 0) {
            foreach ($order->payRecs as $v) {
                if ($v->type == OrderPayRec::TYPE_GOODS) { //获取骑手垫付的商品费支付单号及是否支付标识
                    $goodsPricePaidAt = $v->paid_at ? $v->paid_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "";
                    break;
                }
            }
        }

        if ($order->type == O2oErrandOrder::TYPE_HELP || ($order->type == O2oErrandOrder::TYPE_BUY && $order->pickup_lng == 0 && $order->pickup_lat == 0)) {
            $order->pickup_lng = request()->get('lng');
            $order->pickup_lat = request()->get('lat');
        }

        return [
            'order_no' => $order->order_no,
            'title' => $order->title,
            'time' => Carbon::parse($order->estimated_delivery_time)->diffInMinutes(Carbon::now()),
            'time_text' => $timeText,
            'order_price' => $orderPrice,
            'pickup_address' => $order->pickup_address,
            'pickup_distance' => formatDistance($orderDistance[$order->id] ?? ''),
            'pickup_lng' => $order->pickup_lng,
            'pickup_lat' => $order->pickup_lat,
            'pickup_phone' => $order->pickup_phone,
            'pickup_photo' => 0,
            'deliver_address' => $order->deliver_address,
            'deliver_distance' => $order->pickup_address_id > 0 ? formatDistance($order->distance / 1000) : formatDistance($orderDistance[$order->id] ?? ''),
            'distance' => formatDistance($orderDistance[$order->id] ?? ''),
            'deliver_lng' => $order->deliver_lng,
            'deliver_lat' => $order->deliver_lat,
            'deliver_phone' => $order->deliver_phone,
            'deliver_photo' => 1,
            'type' => $order->type,
            'type_text' => O2oErrandOrder::TypeMap[$order->type],
            'labels' => $this->getOrderLable($order),
            'remark' => $order->remark,
            'appointment' => is_null($order->appointment_end_time),
            'goods_desc' => $order->goods_desc,
            "goods_pre_price" => fentoyuan($order->detail["goods_price"] ?? $order->goods_price),
            'goods_price' => fentoyuan($order->goods_price),
            'goods_protected_price' => fentoyuan($order->goods_protected_price),
            'goods_price_paid_at' => $goodsPricePaidAt,
            'order_status' => $order->order_status,
            'order_status_text' => O2oErrandOrder::StatusMap[$order->order_status],
            'refund_status' => $order->refund_status,
            'refund_status_text' => O2oErrandOrder::RefundStatusMap[$order->refund_status],
            'is_special' => $order->is_special,
            'gratuity' => fentoyuan($order->gratuity),
            'is_trans' => $order->is_trans,
            'dispatch_status' => $order->dispatch_status,
            'dispatch_status_text' => O2oErrandOrder::DispatchStatusMap[$order->dispatch_status],
            'pickup_code_mode' => $order->pickup_code_mode,
            'receive_code_mode' => $order->receive_code_mode,
            'create_time' => Carbon::parse($order->create_time)->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }

    public function queryTransOrder($riderId, $lng, $lat)
    {
        $rider = Rider::query()->find($riderId);
        $dispatchOrders = DispatchOrder::query()->where('rider_id', $riderId)
            ->where('status', 0)
            ->where('closed', 0)
            ->with('order')
            ->get();
        $orderList = [];

        $redisLocations = O2oErrandOrder::GetOrderFromRedis($lng, $lat, 100, 'ASC');

        foreach ($redisLocations as $location) {
            $orderDistance[$location[0]] = $location[1];
        }
        foreach ($dispatchOrders as $dispatchOrder) {
            $orderTask = $this->formatTask($dispatchOrder->order, $orderDistance, $rider->role);
            $orderList[] = [
                'task' => $orderTask,
                'order_no' => $dispatchOrder->order->order_no,
                'dispatch_order_id' => $dispatchOrder->id,
                'create_time' => $dispatchOrder->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
                'close_time' => $dispatchOrder->created_at->addMinutes(5)->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            ];
        }

        return $orderList;
    }

    private function getOrderLable($order)
    {
        $labels = [];
        if ($order->goodsCategory) {
            $labels[] = $order->goodsCategory->title;
        }

        if ($order->weight < 5 && $order->type != O2oErrandOrder::TYPE_HELP) $labels[] = '小于5kg';

        if ($order->type == O2oErrandOrder::TYPE_BUY) {
            $labels[] = "预计垫付¥" . fentoyuan($order->detail["goods_price"] ?? $order->goods_price);
        }

        return $labels;

    }

    private function formatBuyImage($images)
    {
        if (!$images) return [];
        if (isset($images['invoice_img'])) {
            // 帮买图片
            return array_values($images);
        } else {
            return $images;
        }
    }

    private function transIncomeDetail($detail)
    {
        if (gtVersion('1.0.3')) {
            return [];
        }
        $incomeDetail = [];

        $incomeDetail[] = [
            'label' => '配送费',
            'amount' => '¥' . fentoyuan($detail['freight'] + $detail['distance_price']),
        ];

        if ($detail['time_price']) {
            $incomeDetail[] = [
                'label' => '特殊时段补贴',
                'amount' => '¥' . fentoyuan($detail['time_price']),
            ];
        }

        if ($detail['weather_price']) {
            $incomeDetail[] = [
                'label' => '恶劣天气补贴',
                'amount' => '¥' . fentoyuan($detail['weather_price']),
            ];
        }

        return $incomeDetail;
    }
}
