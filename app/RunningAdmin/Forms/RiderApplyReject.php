<?php

namespace App\RunningAdmin\Forms;

use App\Models\RiderApply;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;

class RiderApplyReject extends Form implements LazyRenderable
{
    use LazyWidget;

    // 使用异步加载功能

    /**
     * Handle the form request.
     *
     * @param array $input
     *
     * @return mixed
     */
    public function handle(array $input)
    {
        $apply = RiderApply::find($this->payload['id']);
        if ($apply) {
            $apply->status = 2;
            $apply->remark = $input['remark'];
            $apply->save();
        }
        return $this->response()->success("处理成功")->refresh();
    }

    /**
     * Build a form here.
     */
    public function form()
    {
        $this->textarea('remark', '拒绝理由')->required();
    }

    /**
     * The data of the form.
     *
     * @return array
     */
    public function default()
    {
        return [
            'remark' => '',
        ];
    }
}
