<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;

class CartResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $res = [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'shop_id' => $this->shop_id,
            'spu_id' => $this->spu_id,
            'quantity' => $this->quantity,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
        if (!($this->whenLoaded('spu') instanceof MissingValue)) {
            $res["spu"] = new ShopSpuResource($this->spu);
        }
        return $res;
    }
}
