<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\Notify;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class NotifyController extends AdminController
{
    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new Notify(), function (Grid $grid) {
            $grid->model()->orderBy('id', 'desc');
            $grid->disableViewButton();
            $grid->disableRowSelector();

            $grid->column("id");
            $grid->column('type')->using(\App\Models\Notify::TypeMap)->label();
            $grid->column('title');
            $grid->column('is_important')->switch();
            $grid->column('created_at');
            $grid->column('updated_at');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->expand();
                $filter->panel();
                $filter->in('type')->multipleSelect(\App\Models\Notify::TypeMap)->width(3);
                $filter->like('title')->width(3);
            });
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new Notify(), function (Form $form) {
            $form->disableViewButton();
            $form->disableViewCheck();
            $form->display('id');
            $form->select('type')->options(\App\Models\Notify::TypeMap)->required();
            $form->text('title')->required();
            $form->image('cover')->autoUpload()->autoSave(false)->uniqueName();
            $form->editor("content")->required();
            $form->display('created_at');
            $form->switch('is_important')->default(false);
            $form->display('updated_at');
        });
    }
}
