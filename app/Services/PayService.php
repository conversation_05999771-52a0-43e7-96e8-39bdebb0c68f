<?php


namespace App\Services;


use App\Exceptions\BusinessException;
use App\Jobs\DispatchOrderNotify;
use App\Jobs\RefundO2oErrandOrder;
use App\Models\Common;
use App\Models\O2oErrandOrder;
use App\Models\EarnestRechargeOrder;
use App\Models\MerchantRechargeOrder;
use App\Models\Order;
use App\Models\OrderPayRec;
use App\Models\RechargeOrder;
use App\Models\UserAccount;
use App\Models\UserAccountFlow;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Monolog\Logger;
use Yansongda\Pay\Pay;

class PayService
{
    public function pay($orderNo, $payMethod, $payWay, $ip = "")
    {
        // 查询订单 判断订单合法性以及获取金额
        $orderPrefix = substr($orderNo, 0, 2);
        $amount = 0;
        $title = '';
        switch ($orderPrefix) {
            case RechargeOrder::RECHARGE_ORDER_PREFIX:
                $order = RechargeOrder::query()->where('order_no', $orderNo)->first();
                if (!$order) throw new BusinessException("订单不存在");
                if ($order->paid_at != null && !$order->closed) throw new BusinessException("订单状态不合法");
                $title = "钱包充值";
                $amount = fentoyuan($order->actual_amount);
                break;
            case Order::ORDER_PREFIX:
                $order = Order::query()->where("order_no", $orderNo)->first();
                if (!$order) throw new BusinessException("订单不存在");
                if ($order->closed || $order->paid_at) throw new BusinessException("订单状态不合法");
                $title = "订单支付";
                $amount = fentoyuan($order->pay_amount);
                break;
            case O2oErrandOrder::ORDER_PREFIX:
                $order = O2oErrandOrder::query()->where("order_no", $orderNo)->first();
                if (!$order) throw new BusinessException("订单不存在");
                if ($order->order_status != O2oErrandOrder::STATUS_WAITING_PAY) throw new BusinessException("订单状态不合法");
                $title = "订单支付";
                $amount = fentoyuan($order->actual_amount + $order->gratuity + $order->goods_protected_price);
                break;
            case OrderPayRec::ORDER_PREFIX:
                $order = OrderPayRec::query()->where("order_no", $orderNo)->first();
                if (!$order) throw new BusinessException("订单不存在");
                if ($order->paid_at) throw new BusinessException("订单已支付");
                $title = "订单" . OrderPayRec::TypeMap[$order->type];
                $amount = fentoyuan($order->amount);
                break;
            case EarnestRechargeOrder::EARNEST_RECHARGE_ORDER_PREFIX:
                $order = EarnestRechargeOrder::query()->where('order_no', $orderNo)->first();
                if (!$order) throw new BusinessException("订单不存在");
                if ($order->paid_at != null && !$order->closed) throw new BusinessException("订单状态不合法");
                $title = "缴纳保证金";
                $amount = fentoyuan($order->actual_amount);
                break;
            case MerchantRechargeOrder::RECHARGE_ORDER_PREFIX:
                $order = MerchantRechargeOrder::query()->where('order_no', $orderNo)->first();
                if (!$order) throw new BusinessException("订单不存在");
                if ($order->paid_at != null && !$order->closed) throw new BusinessException("订单状态不合法");
                $title = "商户充值";
                $amount = fentoyuan($order->actual_amount);
                break;
        }
        if (app()->environment() != 'production') {
            if ($payMethod != Common::PAY_METHOD_BALANCE) {
                $amount = 0.01;
            }
        }
        if ($amount > 0) {
            Pay::config(config("pay"));
            // 调用支付SDK获取支付参数
            try {
                switch ($payMethod) {
                    case Common::PAY_METHOD_ALIPAY:
                        // alipay
                        switch ($payWay) {
                            case 'app':
                                $response = Pay::alipay()->app([
                                    'out_trade_no' => $orderNo,
                                    'total_amount' => $amount,
                                    'subject' => $title,
                                ]);
                                return (string)$response->getBody();
                            case 'web':
                                $response = Pay::alipay()->web([
                                    'out_trade_no' => $orderNo,
                                    'total_amount' => $amount,
                                    'subject' => $title,
                                ]);
                                return (string)$response->getBody();
                            case 'scan':
                                $response = Pay::alipay()->scan([
                                    'out_trade_no' => $orderNo,
                                    'total_amount' => $amount,
                                    'subject' => $title,
                                ]);
                                return $response;
                            default:
                                throw new BusinessException("支付方式暂不支持");
                        }
                    case Common::PAY_METHOD_WECHAT:
                        // wexin
                        switch ($payWay) {
                            case 'app':
                                return Pay::wechat()->app([
                                    'out_trade_no' => $orderNo,
                                    'description' => $title,
                                    'amount' => [
                                        'total' => yuantofen($amount),
                                    ],
                                ]);
                            case "wap":
                                $res = Pay::wechat()->wap([
                                    'out_trade_no' => $orderNo,
                                    'description' => $title,
                                    'amount' => [
                                        'total' => yuantofen($amount),
                                    ],
                                    'scene_info' => [
                                        'payer_client_ip' => $ip,
                                        'h5_info' => [
                                            'type' => 'Wap',
                                        ]
                                    ],
                                ]);
                                return $res;
                            case 'mini':
                                $order = [
                                    'out_trade_no' => $orderNo,
                                    'description' => $title,
                                    'amount' => [
                                        'total' => yuantofen($amount),
                                    ],
                                    'payer' => [
                                        'openid' => $order->user->mp_openid,
                                    ],
                                ];
                                $res = Pay::wechat()->mini($order);
                                return $res;
                            case 'scan':
                                $res = Pay::wechat()->scan([
                                    'out_trade_no' => $orderNo,
                                    'description' => $title,
                                    'amount' => [
                                        'total' => yuantofen($amount),
                                    ],
                                ]);
                                return $res;
                            default:
                                throw new BusinessException("支付方式暂不支持");
                        }
                    case Common::PAY_METHOD_CLOUD_FLASH: // 云闪付
                        throw new BusinessException("支付方式暂不支持");
                    case Common::PAY_METHOD_BALANCE: //余额抵扣
                        if (in_array($orderPrefix, [Order::ORDER_PREFIX, O2oErrandOrder::ORDER_PREFIX, OrderPayRec::ORDER_PREFIX])) {
                            $account = UserAccount::query()->firstOrCreate(['user_id' => $order->user_id]);
                            if ($account->amount < yuantofen($amount)) {
                                throw new BusinessException("余额不足");
                            }
                            DB::transaction(function () use ($order, $account, $amount, $title, $orderPrefix) {
                                $account->decrease(yuantofen($amount), UserAccountFlow::BUSINESS_TYPE_PAYMENT, $order->order_no, $title);
                                $updateData = [
                                    'paid_at' => Carbon::now(),
                                    'pay_method' => Common::PAY_METHOD_BALANCE,
                                    'payment_no' => $order->order_no . $order->id,
                                ];
                                if ($orderPrefix == O2oErrandOrder::ORDER_PREFIX) {
                                    $updateData['order_status'] = O2oErrandOrder::STATUS_PAID;
                                    if ($order->appointment_start_time->timestamp <= Carbon::now()->timestamp) {
                                        $seconds = Carbon::now()->timestamp - $order->appointment_start_time->timestamp + 10;
                                        $updateData['appointment_start_time'] = $order->appointment_start_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                                        $updateData['appointment_end_time'] = $order->appointment_end_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                                        $updateData['estimated_delivery_time'] = $order->estimated_delivery_time->addSeconds($seconds)->format(Carbon::DEFAULT_TO_STRING_FORMAT);
                                    }
                                }
                                $order->update($updateData);
                                if ($orderPrefix == OrderPayRec::ORDER_PREFIX) {
                                    app(O2oErrandOrderService::class)->afterPaid($order);
                                }
                            });
                            if ($orderPrefix == O2oErrandOrder::ORDER_PREFIX) {
                                if (app()->environment() == 'production') {
                                    ding()->text(sprintf("新订单提醒 - 订单号：%s，请及时处理", $order->order_no));
                                    dispatch(new DispatchOrderNotify($order));
                                }
                                # 无骑手接单 超时关闭订单 30分钟
                                dispatch(new RefundO2oErrandOrder($order->order_no, config('app.order_refund_ttl')));
                            }
                        }
                }
            } catch (\Exception $e) {
                throw new BusinessException($e->getMessage());
            }
        }
    }


    public function riderTransfer($apply)
    {
        $amount = $apply->amount;
        if (app()->environment() != 'production') {
            $amount = '0.1';
        }
        Pay::config(config("pay"));

        $result = Pay::alipay()->transfer([
            'out_biz_no' => $apply->order_no,
            'trans_amount' => $amount,
            'product_code' => 'TRANS_ACCOUNT_NO_PWD',
            'biz_scene' => 'DIRECT_TRANSFER',
            'order_title' => '余额提现',
            'payee_info' => [
                'identity' => $apply->bank_account,
                'identity_type' => 'ALIPAY_LOGON_ID',
                'name' => $apply->real_name,
            ],
        ]);

        if ($result['code'] != 10000) {
            Log::error("TRANSFER ERROR" . json_encode($result));
            throw new BusinessException($result['msg']);
        }
    }
}
