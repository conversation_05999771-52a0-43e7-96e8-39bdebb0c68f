<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\UserAccountFlowRequest;
use App\Models\UserAccountFlow;
use Carbon\Carbon;
use Illuminate\Http\Request;

class UserAccountFlowController extends Controller
{
    public function index(UserAccountFlowRequest $request)
    {
        $query = UserAccountFlow::query()
            ->where('user_id', $request->user()->id);
        if ($request->type) {
            $query->where('type', $request->type);
        }
        if ($request->bill_month) {
            $start = Carbon::createFromFormat('Ym', $request->bill_month)->startOfMonth();
        } else {
            $start = Carbon::now()->startOfMonth();
        }

        $flows = $query->where('created_at', '>', $start)
            ->orderBy('id', 'desc')
            ->paginate($request->input('page_size', $this->pageSize));

        $result = [];

        foreach ($flows as $flow) {
            $result[] = [
                'id' => $flow->id,
                'user_id' => $flow->user_id,
                'chg_amount' => fentoyuan($flow->chg_amount),
                'type' => $flow->type,
                'subject' => UserAccountFlow::BUSINESS_TYPE_MAP[$flow->business_type] ?? '',
                'logo' => '',
                'no' => $flow->no,
                'created_at' => $flow->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT)
            ];
        }

        return $this->success('success', $result);
    }

    public function show($id, Request $request)
    {
        $flow = UserAccountFlow::query()->where('user_id', $request->user()->id)->where('id', $id)->first();
        if (!$flow) {
            $this->errorResponse(404, '流水记录不存在');
        }
        $result = [
            'id' => $flow->id,
            'user_id' => $flow->user_id,
            'chg_amount' => fentoyuan($flow->chg_amount),
            'type' => $flow->type,
            'subject' => UserAccountFlow::BUSINESS_TYPE_MAP[$flow->business_type] ?? '',
            'logo' => '',
            'no' => $flow->no,
            'created_at' => $flow->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT)
        ];

        return $this->success('success', $result);
    }
}
