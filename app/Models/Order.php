<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Order extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const ORDER_PREFIX = '20';

    const STATUS_WAITING_PAY = 1;
    const STATUS_PAID = 2;
    const STATUS_TAKING = 3;
    const STATUS_DELIVERY = 4;
    const STATUS_FINISHED = 5;
    const STATUS_CANCEL = 6;
    const StatusMap = [
        self::STATUS_WAITING_PAY => "待支付",
        self::STATUS_PAID => "已支付",
        self::STATUS_TAKING => "已接单",
        self::STATUS_DELIVERY => "配送中",
        self::STATUS_FINISHED => "已完成",
        self::STATUS_CANCEL => "已取消",
        10 => "申请退款",
        11 => "审核通过",
        12 => "审核拒绝",
        13 => "退款成功",
        14 => "退款失败",
    ];

    protected $casts = ['closed' => 'boolean', 'is_tel_protect' => 'boolean'];

    protected $dates = ['paid_at', "sending_start_time", "sending_end_time"];

    protected $appends = ['status', 'timeline'];

    protected $fillable = [
        'user_id', 'shop_id', 'order_no', 'closed',
        'order_amount', 'reduce_amount', "pay_amount",
        'pay_method', 'paid_at', 'payment_no',
        'receiver_name', 'receiver_tel', 'receiver_area', 'receiver_address',
        'sending_start_time', 'sending_end_time', 'remark', 'is_tel_protect', 'refund_type',
    ];

    public function scopeValidOrders($query)
    {
        return $query->where("closed", 0)->whereNotNull("paid_at")->whereIn("refund_type", [0, OrderRefund::REFUND_STATUS_REJECT + 10, OrderRefund::REFUND_STATUS_FAIL + 10]);
    }

    public function user()
    {
        return $this->belongsTo(User::class, "user_id");
    }

    public function shop()
    {
        return $this->belongsTo(Shop::class, "shop_id");
    }

    public function details()
    {
        return $this->hasMany(OrderDetail::class, "order_id");
    }

    public function refunds()
    {
        return $this->hasMany(OrderRefund::class, "order_id");
    }

    public function getStatusAttribute()
    {
        if ($this->closed || $this->refund_type < 0) {
            $status = self::STATUS_CANCEL;
        } else {
            if ($this->paid_at) {
                if ($this->refund_type >= 10) {
                    $status = $this->refund_type;
                } else {
                    $status = self::STATUS_PAID;
                    if ($this->paid_at->timestamp + config('app.order_paid_ttl') <= time()) { //支付后10分钟接单
                        $status = self::STATUS_TAKING;
                    }
                    if ($this->sending_start_time->timestamp <= time()) { //到达配送开始时间为配送中
                        $status = self::STATUS_DELIVERY;
                        if ($this->sending_end_time->timestamp <= time()) { //到达配送结束时间为已完成
                            $status = self::STATUS_FINISHED;
                        }
                    }
                }
            } else {
                $status = self::STATUS_WAITING_PAY;
            }
        }
        return $status;
    }

    public function getTimelineAttribute()
    {
        $timeline = [
            ['time' => $this->created_at->format("H:i"), "remark" => '订单已提交'],
        ];

        if ($this->closed || $this->refund_type < 0) {
            if ($this->paid_at) {
                $timeline[] = ['time' => $this->paid_at->format("H:i"), "remark" => '支付成功'];
            }
            $timeline[] = ['time' => $this->updated_at->format("H:i"), "remark" => '订单已取消'];
        } else {
            if ($this->paid_at) {
                $timeline[] = ['time' => $this->paid_at->format("H:i"), "remark" => '支付成功'];
                if ($this->paid_at->timestamp + config('app.order_paid_ttl') <= time()) {
                    $timeline[] = ['time' => $this->paid_at->addSeconds(config('app.order_paid_ttl'))->format("H:i"), "remark" => '已接单'];
                }
                $isFinished = false;
                if ($this->sending_start_time->timestamp <= time()) { //到达配送开始时间为配送中
                    $timeline[] = ['time' => $this->sending_start_time->format("H:i"), "remark" => '配送中'];
                    if ($this->sending_end_time->timestamp <= time()) { //到达配送结束时间为已完成
                        $timeline[] = ['time' => $this->sending_end_time->format("H:i"), "remark" => '商品已送达'];
                        $isFinished = true;
                    }
                }
                if ($this->refund_type >= 10) {
                    $timeline[] = ['time' => $this->updated_at->format("H:i"), "remark" => '申请售后'];
                } elseif ($isFinished) {
                    $timeline[] = ['time' => $this->sending_end_time->format("H:i"), "remark" => '订单已完成'];
                }
            }
        }
        return $timeline;
    }

    public static function findAvailableNo()
    {
        // 订单流水号前缀
        $prefix = self::ORDER_PREFIX . date('YmdHis');
        for ($i = 0; $i < 10; $i++) {
            // 随机生成 6 位的数字
            $no = $prefix . str_pad(random_int(0, 999999), 6, '0', STR_PAD_LEFT);
            // 判断是否已经存在
            if (!static::query()->where('order_no', $no)->exists()) {
                return $no;
            }
        }
        return false;
    }

    protected static function boot()
    {
        parent::boot();
        // 监听模型创建事件，在写入数据库之前触发
        static::creating(function ($model) {
            // 如果模型的 no 字段为空
            if (!$model->order_no) {
                // 调用 findAvailableNo 生成订单流水号
                $model->order_no = static::findAvailableNo();
                // 如果生成失败，则终止创建订单
                if (!$model->order_no) {
                    return false;
                }
            }
        });
    }
}
