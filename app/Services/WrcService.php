<?php

namespace App\Services;

use App\Models\O2oErrandOrder;
use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Ixudra\Curl\Facades\Curl;
use App\Models\MerchantToken;
use App\Models\Merchant;
use Illuminate\Support\Facades\Log;

class WrcService
{
    protected string $appKey;
    protected string $appSecret;
    protected string $host;

    public function __construct(){
        $this->appKey = "xgd4jd7k";
        $this->appSecret = "570b58a525dfcf6e06b46b0f248d37b6";
        if (app()->environment() == 'production') {
            $this->host = "https://api-pro.xiaoduniot.com/nms/dubbo/awsOrderCenter/webApi/yqsOrder";
        }else{
            $this->host = "https://api-test.xiaoduniot.com/nms/dubbo/awsOrderCenter/webApi/yqsOrder";
        }
    }

    public function validateSign($appKey, $sign, $data)
    {
        if (empty($appKey) || $appKey != $this->appKey) throw new \Exception("appkey无效");
        if (empty($sign) || $sign != $this->getSignature($data)) throw new \Exception("签名错误");
        return true;
    }

    private function getSignature(array $data){
        $newData = [];
        foreach ($data as $k => $v){
            if (is_array($v)){
                continue;
//                $newData[$k] = $v ? json_encode($v, JSON_UNESCAPED_UNICODE) : "";
            }elseif(is_bool($v)){
                $newData[$k] = $v ? 'true' : 'false';
            }else{
                $newData[$k] = $v;
            }
        }
        ksort($newData);
        $str = '';
        foreach ($newData as $k => $v) {
            if ($str) $str .= ",";
            $str .= $k . '=' . $v;
        }
        return md5($str.$this->appSecret);
    }

    private function post(string $command, array $data = []){
        $result = Curl::to($this->host . '/'. $command)
            ->asJson(true)
            ->withHeaders(["app_key" => $this->appKey, "sign" => $this->getSignature($data)])
            ->withData($data)
            ->post();

        if(($result["code"]?? 0) == 200){
            return json_decode($result["data"], true);
        }
        throw new \Exception($result["msg"]?? "未知错误", $result["code"]?? 0);
    }

    public function deliveryChange($userId, $orderNo, $thirdOrderNo, $status, $otherData = [])
    {
        // 通过userId获取门店ID
        $shopId = "";
        $merchantToken = MerchantToken::where('user_id', $userId)
            ->where('platform', O2oErrandOrder::APP_KEY_WRC)
            ->whereNotNull('shop_id')
            ->first();

        if ($merchantToken && !empty($merchantToken->shop_id)) {
            $shopId = $merchantToken->shop_id;
        }

        // 必填信息没有需要初始化
        foreach (["rider_name", "rider_phone", "longitude", "latitude"] as $key){
            if (isset($otherData[$key])){
                $otherData[$key] .= "";
            }else{
                $otherData[$key] = "";
            }
        }

        Log::channel('wrc')->info('无人仓配送状态变更', [
            'user_id' => $userId,
            'shop_id' => $shopId,
            'order_no' => $orderNo,
            'source_order_no' => $thirdOrderNo,
            'status' => $status
        ]);

        return $this->post("deliveryChange", array_merge([
            "order_no" => $orderNo,
            "source_order_no" => $thirdOrderNo,
            "shop_id" => $shopId,
            "status" => $status,
            "at_time" => time()
        ], $otherData));
    }

    public function locationChange($userId, $orderNo, $thirdOrderNo, $status, $longitude, $latitude) {
        return true;
        // 通过userId获取门店ID
        $shopId = "";
        $merchantToken = MerchantToken::where('user_id', $userId)
            ->where('platform', O2oErrandOrder::APP_KEY_WRC)
            ->whereNotNull('shop_id')
            ->first();

        if ($merchantToken && !empty($merchantToken->shop_id)) {
            $shopId = $merchantToken->shop_id;
        }

        Log::channel('wrc')->info('无人仓位置变更', [
            'user_id' => $userId,
            'shop_id' => $shopId,
            'order_no' => $orderNo,
            'source_order_no' => $thirdOrderNo,
            'status' => $status,
            'longitude' => $longitude,
            'latitude' => $latitude
        ]);

        return $this->post("locationChange", [
            "order_no" => $orderNo,
            "source_order_no" => $thirdOrderNo,
            "shop_id" => $shopId,
            "locations" => [
                [
                    "longitude" => "$longitude",
                    "latitude" => "$latitude",
                    "status" => $status,
                    "update_time" => time()
                ]
            ]
        ]);
    }

    public function getMerchantInfoByShopId(string $shopId): ?array
    {
        $token = MerchantToken::where('platform', O2oErrandOrder::APP_KEY_WRC)->where('shop_id', $shopId)->first();
        if (!$token) {
            return null;
        }
        $merchantId = $token->merchant_id;
        $userId = $token->user_id;

        // 如果token记录中没有user_id，尝试从商家表获取
        if (!$userId && $merchantId) {
            $merchant = Merchant::find($merchantId);
            $userId = $merchant ? $merchant->user_id : null;
        }

        return [
            'merchant_id' => $merchantId,
            'user_id' => $userId,
            'token' => $token
        ];
    }
}
