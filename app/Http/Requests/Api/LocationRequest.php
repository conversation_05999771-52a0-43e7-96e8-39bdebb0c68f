<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LocationRequest extends FormRequest
{
    public function rules()
    {
        $rules = match (request()->method) {
            'POST' => [
                'type' => ['required', 'integer', Rule::in([1, 2])],
                'lng' => 'required',
                'lat' => 'required',
            ],
            default => [],
        };


        return $rules;
    }
}
