<?php

namespace App\Http\Controllers\Api;

use App\Models\Community;
use App\Services\LocationService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CommunityController extends Controller
{
    protected int $distance = 5000;

    public function __construct()
    {
        if (app()->environment() != 'production') {
            $this->distance = 5000;
        }
    }

    public function index(Request $request, LocationService $locationService)
    {
        $lng = $request->lng;
        $lat = $request->lat;
        if (!$lng || !$lat) { //没有传经纬度
            $location = $locationService->getLocationFromIp($request->getClientIp());
            $lng = $location[0];
            $lat = $location[1];
        }

        $redisLocations = Community::GetCommunityFromRedis($lng, $lat, $this->distance);
        $communitiesIdList = [];
        $communitiesDistance = [];
        foreach ($redisLocations as $location) {
            $communitiesIdList[] = $location[0];
            $communitiesDistance[$location[0]] = $location[1];
        }

        $communities = Community::query()->whereIn('id', $communitiesIdList)
            ->orderByRaw(DB::raw("FIND_IN_SET(id, '" . implode(',', $communitiesIdList) . "'" . ')'))
            ->get();

        $result = [];
        foreach ($communities as $community) {
            $result[] = [
                'id' => $community->id,
                'name' => $community->name,
                'distance' => $communitiesDistance[$community->id]
            ];
        }

        return $this->success('success', $result);
    }
}
