<?php

namespace App\RunningAdmin\Controllers;

use App\RunningAdmin\Repositories\RiderLevelSetting;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Show;
use Dcat\Admin\Http\Controllers\AdminController;

class RiderLevelSettingController extends AdminController
{
    protected $title = '骑手等级管理';


    /**
     * Make a grid builder.
     *
     * @return Grid
     */
    protected function grid()
    {
        return Grid::make(new RiderLevelSetting(), function (Grid $grid) {
            $grid->enableDialogCreate();
            $grid->disableDeleteButton();
            $grid->disableViewButton();
            $grid->disableEditButton();
            $grid->showQuickEditButton();

            $grid->column('id')->sortable();
            $grid->column('name');
            $grid->column('level');
            $grid->column('max_score');
            $grid->column('card_background', '卡片背景')->image();
            $grid->column('order_limit');
            $grid->column('trans_limit');

        });
    }

    /**
     * Make a show builder.
     *
     * @param mixed $id
     *
     * @return Show
     */
    protected function detail($id)
    {
        return Show::make($id, new RiderLevelSetting(), function (Show $show) {
            $show->field('id');
            $show->field('name');
            $show->field('level');
            $show->field('max_score');
            $show->field('card_background');
            $show->field('order_limit');
            $show->field('trans_limit');
            $show->field('created_at');
            $show->field('updated_at');
        });
    }

    /**
     * Make a form builder.
     *
     * @return Form
     */
    protected function form()
    {
        return Form::make(new RiderLevelSetting(), function (Form $form) {
            $form->display('id');
            $form->text('name');
            $form->number('level');
            $form->number('max_score');
            $form->image('card_background', '卡片背景')->autoUpload()->uniqueName();
            $form->number('order_limit');
            $form->number('trans_limit');

            $form->display('created_at');
            $form->display('updated_at');
        });
    }
}
