<?php

namespace App\Http\Resources;

use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderEvaluateRecResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'order_id' => $this->order_id,
            'user_id' => $this->user_id,
            'rider_id' => $this->rider_id,
            'is_anonymous' => $this->is_anonymous,
            'is_satisfied' => $this->is_satisfied,
            'reason' => $this->reason,
            'remark' => $this->remark,
            'imgs' => img_url_array($this->imgs),
            "pszs_star" => $this->pszs_star,
            'cdgf_star' => $this->cdgf_star,
            'ybzj_star' => $this->ybzj_star,
            'hpwh_star' => $this->hpwh_star,
            'lmrq_star' => $this->lmrq_star,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
    }
}
