<?php

namespace App\Http\Controllers\Api;

use App\Models\Common;
use App\Models\Region;
use App\Services\Amap\GaodeService;
use App\Services\MapService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use function PHPUnit\Framework\isNull;
use Illuminate\Support\Facades\File;

class RegionController extends Controller
{
    public function index(Request $request)
    {
        $level = $request->level ?? Common::REGION_LEVEL_CITY;
        //城市列表
        $regions = Region::query()->where('level', $level)->orderBy('letter')->where('status', 1)->get();
        //热门城市
        $hotRegions = Region::query()->where('is_hot', true)->get();;

        $userRegions = null; //用户定位
        $weather = null; //天气

        $mapService = new MapService();
        $res = $mapService->geocoder($request->lng, $request->lat, 0);
        if ($res['status'] == 1) {
            $userRegions = [$res['result']['address_component']['province'], $res['result']['address_component']['city'], $res['result']['address_component']['district']];
            $userRegions = Region::getRegions($userRegions[0], $userRegions[1], $userRegions[2], "name", "");
        }
        if (!is_null($userRegions) && !isset($userRegions['province']) && !$userRegions['province']) {
            $userRegions = Region::getRegions('浙江省', '宁波市', '余姚市', "name", "");
        }
        return $this->success("success", [
            "cities" => $regions->groupBy('letter'),
            "hot_cities" => $hotRegions,
            'user_regions' => $userRegions,
            'weather' => $weather,
        ]);
    }

    /**
     * 获取所有省份
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function provinces()
    {
        $provinces = \App\Models\Region::where('level', 1)
            ->orderBy('letter')
            ->get(['code', 'name']);

        return response()->json($provinces);
    }

    /**
     * 获取指定省份的城市
     *
     * @param  string  $provinceCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function cities($provinceCode)
    {
        // 先获取省份ID
        $province = \App\Models\Region::where('code', $provinceCode)
            ->first();

        if (!$province) {
            return response()->json([]);
        }

        // 获取该省份下的所有城市
        $cities = \App\Models\Region::where('pid', $province->id)
            ->where('level', 2)
            ->orderBy('letter')
            ->get(['code', 'name']);

        return response()->json($cities);
    }

    /**
     * 获取指定城市的区县
     *
     * @param  string  $provinceCode
     * @param  string  $cityCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function districts($provinceCode, $cityCode)
    {
        // 获取城市ID
        $city = \App\Models\Region::where('code', $cityCode)
            ->first();

        if (!$city) {
            return response()->json([]);
        }

        // 获取该城市下的所有区县
        $districts = \App\Models\Region::where('pid', $city->id)
            ->where('level', 3)
            ->orderBy('letter')
            ->get(['code', 'name']);

        return response()->json($districts);
    }

    /**
     * 获取区域数据
     *
     * @return array
     */
    protected function getRegionData()
    {
        $path = public_path('data/regions.json');
        $json = File::get($path);
        return json_decode($json, true);
    }
}
