<?php

namespace App\Http\Resources;

use App\Models\Common;
use App\Models\Order;
use Carbon\Carbon;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\MissingValue;

class OrderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $sendingTime = $this->sending_start_time->format("H:i") . "-" . $this->sending_end_time->format("H:i");
        if (Carbon::now()->addDays(1)->format("Y-m-d") == $this->sending_start_time->format("Y-m-d")) {
            $sendingTime = "明天 " . $sendingTime;
        }

        $res = [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'shop_id' => $this->shop_id,
            'order_no' => $this->order_no,
            'order_amount' => fentoyuan($this->order_amount),
            'reduce_amount' => fentoyuan($this->reduce_amount),
            'pay_amount' => fentoyuan($this->pay_amount),
            'receiver_name' => $this->receiver_name,
            'receiver_tel' => $this->receiver_tel,
            'receiver_address' => $this->receiver_address,
            'sending_start_time' => $this->sending_start_time ? $this->sending_start_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'sending_end_time' => $this->sending_end_time ? $this->sending_end_time->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'sending_time' => $sendingTime,
            'pay_method' => $this->pay_method,
            'pay_method_text' => Common::PayMethodMap[$this->pay_method] ?? "",
            'paid_at' => $this->paid_at ? $this->paid_at->format(Carbon::DEFAULT_TO_STRING_FORMAT) : "",
            'payment_no' => $this->payment_no,
            'remark' => $this->remark,
            'is_tel_protect' => $this->is_tel_protect,
            'status' => $this->status,
            'status_text' => Order::StatusMap[$this->status] ?? "",
            'timeline' => $this->timeline,
            'created_at' => $this->created_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
            'updated_at' => $this->updated_at->format(Carbon::DEFAULT_TO_STRING_FORMAT),
        ];
        if (!($this->whenLoaded('shop') instanceof MissingValue)) {
            $res["shop"] = new ShopResource($this->shop);
        }
        if (!($this->whenLoaded('details') instanceof MissingValue)) {
            $res["details"] = OrderDetailResource::collection($this->details);
        }
        if (!($this->whenLoaded('refunds') instanceof MissingValue)) {
            $res["refunds"] = OrderRefundResource::collection($this->refunds);
        }
        return $res;
    }
}
