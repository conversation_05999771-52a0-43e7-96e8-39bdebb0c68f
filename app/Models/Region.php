<?php

namespace App\Models;

use Carbon\Carbon;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;

class Region extends Model
{
    use HasFactory, HasDateTimeFormatter;

    protected $casts = ['is_hot' => 'boolean'];

    protected $fillable = [
        'pid', 'name', 'level', 'code', 'lng', "lat", 'is_hot', 'letter', 'pinyin', 'status'
    ];

    public function parent()
    {
        return $this->belongsTo(Region::class, "pid");
    }

    public function children()
    {
        return $this->hasMany(Region::class, "pid");
    }

    public static function getRegions($province, $city, $district, $key = "id", $value = "name"): array
    {
        $res = ["province" => null, "city" => null, "district" => null];
        $arr = [];
        if ($province) $arr[] = $province;
        if ($city) $arr[] = $city;
        if ($district) $arr[] = $district;
        if (empty($arr)) return $res;
        $cacheKey = sprintf("regions_%s_%s", $key, implode("-", $arr));
        if (Cache::has($cacheKey)) {
            $data = Cache::get($cacheKey);
        } else {
            $data = Region::query()->whereIn($key, $arr)->get();
            if ($data) {
                Cache::put($cacheKey, $data, Carbon::now()->addMinutes(15));
            }
        }
        if ($value) {
            $data = $data->pluck($value, $key)->all();
            if ($province) $res["province"] = $data[$province] ?? "";
            if ($city) $res["city"] = $data[$city] ?? "";
            if ($district) $res["district"] = $data[$district] ?? "";
        } else {
            $data = $data->groupBy($key)->all();
            if ($province) $res["province"] = $data[$province][0] ?? null;
            if ($city) $res["city"] = $data[$city][0] ?? null;
            if ($district) $res["district"] = $data[$district][0] ?? null;
        }
        return $res;
    }
}
