<?php

namespace App\Listeners;

use App\Events\AfterRegister;
use App\Jobs\DispatchHandleNewUserActivity;
use App\Models\UserAccount;
use App\Models\UserProfile;
use Illuminate\Contracts\Queue\ShouldQueue;

class HandleUserInfo implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param \App\Events\AfterRegister $event
     * @return void
     */
    public function handle(AfterRegister $event)
    {
        $user = $event->user;

        if (!$user->profile) {
            // 创建用户基本信息
            UserProfile::create([
                'user_id' => $user->id,
            ]);
        }

        if (!$user->userAccount) {
            // 创建用户账户
            UserAccount::create(['user_id' => $user->id, 'amount' => 0]);
        }

        if ($user->parent_id > 0) {
            dispatch(new DispatchHandleNewUserActivity($user, null));
        }
    }
}
