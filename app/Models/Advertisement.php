<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;


class Advertisement extends Model implements Sortable
{
    use HasFactory, HasDateTimeFormatter, SortableTrait;

    const TYPE_BANNER = 1;
    const TYPE_POPUP = 2;
    const TYPE_ZONE_SEND = 11;
    const TYPE_ZONE_TAKE = 12;
    const TYPE_ZONE_BUY = 13;
    const TypeMap = [
        self::TYPE_BANNER => '轮播',
//        self::TYPE_POPUP => '弹窗',
        self::TYPE_ZONE_SEND => "帮我送-专区",
        self::TYPE_ZONE_TAKE => "帮我取-专区",
        self::TYPE_ZONE_BUY => "帮我买-专区",
    ];

    const TARGET_TYPE_WEB = 1;
    const TARGET_TYPE_APP = 2;
    const TargetTypeMap = [
        self::TARGET_TYPE_WEB => '网页',
        self::TARGET_TYPE_APP => 'APP内页面'
    ];

    protected $casts = ['target' => 'json',];

    protected $dates = ['start_time', "end_time"];

    protected $fillable = [
        'title', 'cover', 'type', "start_time", 'end_time', 'target_type', 'target', 'status', "sort"
    ];

    protected $sortable = [
        'order_column_name' => 'sort',
        'sort_when_creating' => true,
    ];
}
