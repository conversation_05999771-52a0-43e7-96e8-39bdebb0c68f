<?php

namespace App\Http\Requests\Api;

class AuthorizationRequest extends FormRequest
{
    public function rules()
    {
        $rules = [
            'auth_type' => 'required|in:password,smsCode,flash,mp',
        ];

        if ($this->auth_type == 'password') {
            $rules['password'] = 'required|alpha_dash|min:6';
        }

        if ($this->auth_type == 'smsCode') {
            $rules['verification_key'] = 'required|string';
            $rules['verification_code'] = 'required|string';
        }

        return $rules;
    }
}
