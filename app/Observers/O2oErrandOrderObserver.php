<?php

namespace App\Observers;

use App\Models\O2oErrandOrder;
use Illuminate\Support\Facades\Redis;

class O2oErrandOrderObserver
{
    /**
     * 更新骑手的push状态缓存
     */
    private function updateRiderPushStatus($order)
    {
        if (!$order->rider_id) {
            return;
        }

        // 检查骑手是否有需要push位置的订单
        $hasActiveOrder = O2oErrandOrder::query()
            ->whereIn("app_key", [O2oErrandOrder::APP_KEY_MYT, O2oErrandOrder::APP_KEY_WRC])
            ->where("rider_id", $order->rider_id)
            ->whereIn("order_status", [O2oErrandOrder::STATUS_PICKUP, O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, O2oErrandOrder::STATUS_DELIVERY])
            ->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT)
            ->exists();

        if ($hasActiveOrder) {
            Redis::sadd('myt_push_riders', $order->rider_id);
        } else {
            Redis::srem('myt_push_riders', $order->rider_id);
        }
    }

    /**
     * Handle the O2oErrandOrder "created" event.
     */
    public function created(O2oErrandOrder $order)
    {
        $this->updateRiderPushStatus($order);
    }

    /**
     * Handle the O2oErrandOrder "updated" event.
     */
    public function updated(O2oErrandOrder $order)
    {
        // 如果订单状态或退款状态发生变化,更新骑手push状态
        if ($order->isDirty('order_status') || $order->isDirty('refund_status') || $order->isDirty('rider_id')) {
            $this->updateRiderPushStatus($order);

            // 如果骑手发生变化,还需要更新原骑手的状态
            if ($order->isDirty('rider_id') && $order->getOriginal('rider_id')) {
                $originalRiderId = $order->getOriginal('rider_id');

                $hasActiveOrder = O2oErrandOrder::query()
                    ->whereIn("app_key", [O2oErrandOrder::APP_KEY_MYT, O2oErrandOrder::APP_KEY_WRC])
                    ->where("rider_id", $originalRiderId)
                    ->whereIn("order_status", [O2oErrandOrder::STATUS_PICKUP, O2oErrandOrder::STATUS_ARRIVE_PICKUP_POINT, O2oErrandOrder::STATUS_DELIVERY])
                    ->where("refund_status", O2oErrandOrder::REFUND_STATUS_INIT)
                    ->exists();

                if ($hasActiveOrder) {
                    Redis::sadd('myt_push_riders', $originalRiderId);
                } else {
                    Redis::srem('myt_push_riders', $originalRiderId);
                }
            }
        }
    }

    /**
     * Handle the O2oErrandOrder "deleted" event.
     */
    public function deleted(O2oErrandOrder $order)
    {
        $this->updateRiderPushStatus($order);
    }
}
