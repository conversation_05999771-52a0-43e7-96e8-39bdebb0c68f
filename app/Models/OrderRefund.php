<?php

namespace App\Models;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class OrderRefund extends Model
{
    use HasFactory, HasDateTimeFormatter;

    const REFUND_STATUS_APPLY = 0;
    const REFUND_STATUS_APPROVED = 1;
    const REFUND_STATUS_REJECT = 2;
    const REFUND_STATUS_SUCCESS = 3;
    const REFUND_STATUS_FAIL = 4;
    const RefundStatusMap = [
        self::REFUND_STATUS_APPLY => "申请退款",
        self::REFUND_STATUS_APPROVED => "审核通过",
        self::REFUND_STATUS_REJECT => "审核拒绝",
        self::REFUND_STATUS_SUCCESS => "退款成功",
        self::REFUND_STATUS_FAIL => "退款失败",
    ];

    protected $casts = [
        'refund_pics' => 'json',
        'spu_info' => 'json',
    ];

    protected $dates = ['refund_at', 'verify_at'];

    protected $fillable = [
        'order_id', 'no', 'refund_reason', 'refund_remark', 'refund_pics', 'spu_info', "refund_amount", 'refund_status', 'verify_at', 'reject_reason', 'refund_at', 'refund_no'
    ];

    public function order(){
        return $this->belongsTo(Order::class, "order_id");
    }
}
